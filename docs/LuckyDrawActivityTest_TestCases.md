# 抽奖活动接口测试用例文档

## 概述

本文档描述了抽奖活动相关接口的全面测试用例，基于OpenAPI规范和现有业务逻辑设计。

## 测试接口列表

1. **查询活动信息-根据活动类型** (`POST /luck/draw/activity/query/info`)
2. **查询活动信息-根据活动ID** (`POST /luck/draw/activity/query/detail`)
3. **开始抽奖** (`POST /luck/draw/activity/upsert/start`)
4. **查询活动抽奖记录-根据活动ID** (`POST /luck/draw/activity/query/prize`)

## 测试用例分类

### 1. 功能测试用例

#### 1.1 查询活动信息-根据活动类型

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_QUERY_INFO_001 | 查询红包雨活动信息-正常场景 | P0 | 传入type=1查询红包雨活动 | 返回活动ID=144的红包雨活动信息 |
| TC_API_LUCK_DRAW_QUERY_INFO_002 | 查询每日抽奖活动信息-正常场景 | P0 | 传入type=2查询每日抽奖活动 | 返回活动ID=145的每日抽奖活动信息 |
| TC_API_LUCK_DRAW_QUERY_INFO_003 | 查询活动信息-无效活动类型 | P1 | 传入type=999无效类型 | 返回错误信息或空数据 |
| TC_API_LUCK_DRAW_QUERY_INFO_004 | 查询活动信息-缺少必填参数 | P1 | 不传type参数 | 返回参数错误信息 |
| TC_API_LUCK_DRAW_QUERY_INFO_005 | 查询活动信息-边界值测试 | P2 | 传入边界值(0,-1,MAX,MIN) | 正确处理边界值情况 |

#### 1.2 查询活动信息-根据活动ID

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_QUERY_DETAIL_001 | 根据活动ID查询红包雨详情-正常场景 | P0 | 传入id=144,type=1查询详情 | 返回红包雨活动详细信息 |
| TC_API_LUCK_DRAW_QUERY_DETAIL_002 | 根据活动ID查询每日抽奖详情-正常场景 | P0 | 传入id=145,type=2查询详情 | 返回每日抽奖活动详细信息 |
| TC_API_LUCK_DRAW_QUERY_DETAIL_003 | 查询活动详情-不存在的活动ID | P1 | 传入不存在的活动ID | 返回错误信息或空数据 |

#### 1.3 开始抽奖

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_START_001 | 开始红包雨抽奖-正常场景 | P0 | 传入activityId=144开始抽奖 | 成功抽奖并返回奖品信息 |
| TC_API_LUCK_DRAW_START_002 | 开始每日抽奖-正常场景 | P0 | 传入activityId=145开始抽奖 | 成功抽奖并返回奖品信息 |
| TC_API_LUCK_DRAW_START_003 | 开始抽奖-不存在的活动ID | P1 | 传入不存在的活动ID | 返回错误信息 |
| TC_API_LUCK_DRAW_START_004 | 开始抽奖-缺少必填参数 | P1 | 不传activityId参数 | 返回参数错误信息 |

#### 1.4 查询活动抽奖记录

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_QUERY_PRIZE_001 | 查询红包雨抽奖记录-正常场景 | P0 | 传入id=144,type=1查询记录 | 返回红包雨抽奖记录 |
| TC_API_LUCK_DRAW_QUERY_PRIZE_002 | 查询每日抽奖记录-正常场景 | P0 | 传入id=145,type=2查询记录 | 返回每日抽奖记录 |

### 2. 综合测试用例

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_COMPREHENSIVE_001 | 抽奖活动完整流程测试 | P0 | 完整业务流程测试 | 所有步骤都成功执行 |

### 3. 数据库验证测试用例

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_DATABASE_001 | 抽奖活动数据库验证测试 | P1 | 验证数据库记录一致性 | 数据库记录正确写入 |

### 4. 权限验证测试用例

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_UNAUTHORIZED_001 | 未授权访问测试 | P1 | 不传token访问接口 | 返回授权错误 |
| TC_API_LUCK_DRAW_INVALID_TOKEN_001 | 无效token测试 | P1 | 传入无效token | 返回token错误 |

### 5. 性能测试用例

| 用例ID | 用例名称 | 优先级 | 测试场景 | 预期结果 |
|--------|----------|--------|----------|----------|
| TC_API_LUCK_DRAW_PERFORMANCE_001 | 接口性能测试 | P2 | 测试接口响应时间 | 响应时间<3秒 |

## 测试数据

### 活动类型
- 红包雨活动：type = 1, activityId = 144
- 每日抽奖活动：type = 2, activityId = 145

### 测试用户
- 测试用户：mallAutoUser2 (手机号: 15755642759, mid: 349375)

### 测试环境
- 测试环境：qa
- 基础URL：https://qah5.summerfarm.net

## 数据库表结构

### 相关数据库表
1. `merchant_coupon` - 商户优惠券表
2. `lucky_draw_activity_record` - 抽奖活动记录表
3. `lucky_draw_activity_equity_package` - 抽奖活动权益包表

### 数据清理策略
- 测试前清理：删除测试用户的历史抽奖记录和优惠券
- 测试后清理：删除测试产生的数据，重置权益包数量

## 断言验证点

### 响应结构验证
- 响应不为空
- status字段为0表示成功
- data字段包含预期数据结构

### 业务逻辑验证
- 活动ID匹配
- 活动类型匹配
- 活动名称不为空
- 时间字段格式正确

### 数据库验证
- 抽奖记录正确写入
- 优惠券正确发放
- 权益包数量正确扣减

## 执行说明

### 运行环境要求
- Java 8
- TestNG框架
- Spring Boot测试环境
- MySQL数据库连接

### 执行方式
```bash
# 运行单个测试类
mvn test -Dtest=LuckyDrawActivityTest

# 运行特定测试方法
mvn test -Dtest=LuckyDrawActivityTest#testQueryRedPacketRainInfo
```

### 报告生成
- 使用Allure生成测试报告
- 包含详细的请求响应信息
- 包含数据库验证结果

## 注意事项

1. **数据隔离**：每个测试用例使用独立的测试数据，避免相互影响
2. **环境配置**：确保测试环境配置正确，数据库连接正常
3. **权限管理**：测试用户需要有相应的抽奖权限
4. **并发控制**：避免并发执行可能导致的数据冲突
5. **错误处理**：充分测试各种异常情况的处理

## 维护说明

1. **定期更新**：根据业务变更及时更新测试用例
2. **数据维护**：定期清理测试数据，保持环境整洁
3. **监控告警**：关注测试执行结果，及时发现问题
4. **文档同步**：保持测试文档与代码同步更新
