### SummerFarm-XM Automation Testing Platform  || 自动化测试平台

该项目是一个基于 testng 和 allure 的自动化测试平台。

#### 1.如何使用：

- 新增用例

- 新增场景/集合

- 新增xml用于不同环境不同场景的执行

##### 1.1 项目中使用到的注解：

testng部分

 - `@Test` - 你必须在你的测试用例上添加 `description` 要不然在报告上只会显示你的方法名
 - `@Owner` - 用于标记谁编辑了测试用例

 allure部分，用于编织测试报告
 - `@Epic` - 用于分组测试用例 一级分类
 - `@Feature` - 用于分组测试用例 二级分类
 - `@Story` - 用于分组测试用例 三级分类
 - 如果想在报告中打印你自己编辑的日志，你可以使用以下代码：
>  Allure.addAttachment(String 日志的名字, String 日志的内容);

##### 1.2 使用`Assert`断言来对用例的结果进行判别
>  Assert.assertEquals(实际结果, 预期结果);
 
##### 1.3 新增用例
333
 如果你已经掌握了以上基础， 我们开始新增用例
###### 1.3.1 创建业务域文件夹
在`test`文件夹下新建一个业务域文件夹，例如`CRM`，然后就可以开始场景了。

###### 1.3.2 创建场景/集合
在业务域文件夹下新建一个场景/集合的`**Test.java`文件，例如`LoginTest.java`，然后就可以开始新增基础用例了。
注意需要继承与`BaseTest.java`。
###### 1.3.3 新增用例

在场景/集合的`**Test.java`文件中新增用例，例如：

#### Example
```java
@Epic("一级分类")
@Feature("登录场景，二级分类")
@Owner("xianmu，作者")
@Listeners({XMListener.class})
public class LoginTest extends BaseTest {

    @Resource
    private MerchantDao merchantDao;

    @Story("下单场景，三级分类")
    @Owner("xianmu，作者")
    @Test(description = "商城登录，测试用例描述")
    public void case05() {
        /* 更新余额 */
        int res = merchantDao.updateAmountByMerchantId(merchantId, 10000);
        /* 删除购物车商品 */
        shoppingCartDao.deleteByAccountId(merchantId);
        HttpResponse response = HttpRequest.post(helpUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
        Assert.assertTrue(JSON.parseObject(response.body()).getJSONObject("data").get("orderNo") != null, "下单失败");
    }
}
```

其中，对于Http部分，我们使用了hutool的`HttpRequest`来进行请求，`HttpResponse`是响应，具体使用方法可以参考[HttpRequest](https://doc.hutool.cn/pages/HttpResponse/#%E4%BB%8B%E7%BB%8D)的文档。优点是可以进行链式调用，并且我们重写了它的拦截器可以在请求前后进行一些操作。

- 其次，对于sql操作，我们使用了mybatis-plus的`BaseMapper`来进行操作，具体使用方法可以参考[BaseMapper](URL_ADDRESS
值得注意的点是需要再`model`中新建对应的表实例，如果用‘datagrip’的话可以右键表，选择`Generate`，然后选择`POJO with lombok`，然后就可以获取相应的java代码。

- 然后在`mapper`中新建对应的mapper接口，继承基础mapper。

- 最后在`dao`中新建对应的dao接口，进行sql操作。

其余的部分就是一些testng的特性，例如`@Test`，`@BeforeClass`，`@AfterClass`，`@BeforeMethod`，`@AfterMethod`，`@DataProvider`，`@Factory`，`@Listeners`，`@Parameters`，etc.


 #### 1.4 新增xml用于不同环境不同业务的组合场景的执行
在`test`文件夹的`resources`文件夹，新建一个`**-suite.xml`文件，例如`dev-suite.xml`。
 Example
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="套件">
    <test name="场景">
        <classes>
            <!--      执行类-->
            <class name="mall.net.xianmu.atp.OrderTest" />
        </classes>
    </test> <!-- NewMedia -->
</suite> <!-- Default Suite -->

```
至此，就完成了从用例到场景的新增，接下来就可以执行了。
maven部分
 > mvn clean test "-Dsurefire.suiteXmlFiles=src/test/resources/game-unit-test-suite.xml"
> 
> 或者你已经在pom中配置了相关testng的套件设置或者默认方式可以直接执行：
> 
> mvn clean test
 
如果想要在本地查看报告：
 > allure serve (你存放测试报告的路径)target/allure-results/

## 2.一些其他会用到的的配置
#### 2.1 失败重试
可以在util文件夹中新增自定义的`**RetryAnalyzer.java`，实现`IRetryAnalyzer`接口的`retry`方法就可以自定义重试操作，例如次数和间隔时间etc.。

- Example

```java
public class XMRetryAnalyzer implements IRetryAnalyzer {


    private int retryCount = 0;
    private static final int maxRetryCount = 3;
    @Override
    public boolean retry(ITestResult iTestResult) {
        
        // 重试次数小于最大重试次数时，重试
        if (retryCount < maxRetryCount) {
            retryCount++;
            return true;
        }
        return false;
    }
}
```

- 使用方式：

在需要重试的用例上在`@Test`注解中添加`retryAnalyzer = XMRetryAnalyzer.class`即可。

```java
@Test(description = "代销不入仓&非代销不入仓有多张优惠券下单", retryAnalyzer = XMRetryAnalyzer.class)
    public void case06(){}
```
> [!TIP]

### V1.1 - 2024.11.18
- 1.新增了日志输出到项目目录。使用方式：在用例的类上添加注解`@Slf4j`然后log.info()调用即可，日志存放在项目目录的log文件夹下
- 2.新增了多数据源支持，目前配置了xianmudb和offline。使用方式：直接在对应的mapper中使用@DS注解指定数据源即可。e.g. `@DS("offline")`，不写的话默认使用xianmudb。例子在dal/mapper文件夹下的`ResultMapper.java中`。
- 3.新增了自定义重试次数与时间间隔。使用方式：
- 3-1.在用例方法上：
```java
 @Test(description = "测试用例2",
       retryAnalyzer = XMRetryAnalyzer.class,
       attributes = {
               @CustomAttribute(name = "loop", values = {"3"}),
               @CustomAttribute(name = "interval", values = {"3"})
       })
    // loop是重试次数为3次，interval是间隔时间为3秒
    // retryAnalyzer一定要填 XMRetryAnalyzer.class
```
- 3-2.在xml上：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<suite name="qa自动化">
    <listeners>
        <listener class-name="com.xianmu.atp.util.testng.XMAnnotationTransformer" />
    </listeners>
    <test name="商城测试">
        <classes>
            <class name="com.xianmu.atp.cases.mall.BaseCaseTest" />
        </classes>
    </test>
</suite>
<!--其中listener标签是必须填XMAnnotationTransformer的-->
```

- 4.新增了用例并行执行，以提升执行效率。使用方式：
在xml上：
```xml
<suite name="qa自动化" parallel="methods" thread-count="5">
<!--其中parallel表示是以什么程度进行并行，thread-count表示线程数，默认为5-->
```

- 5.新增了直接以package形式进行执行，避免新增一个用例集就需要变更一次对应xml的问题。使用方式：
  在xml上：
```xml
<test name="test2">
 <packages>
  <package name="com.xianmu.atp.cases.mall" />
 </packages>
</test>
<!--其中parallel表示是以什么程度进行并行，thread-count表示线程数，默认为5-->
```

#### - 2024.11.20
- 1.新增了上下游依赖的心跳检测，使用方式：
  在xml上：
```xml
<suite name="qa自动化" parallel="methods" thread-count="5" skipfailedinvocationcounts="true">
    <listeners>
        <listener class-name="com.xianmu.atp.util.testng.XMAnnotationTransformer" />
        <listener class-name="com.xianmu.atp.util.testng.XMSuiteListener" />
    </listeners>
    <parameters>
        <parameter name="dependence" value="1,2" />
    </parameters>
<!--在suite标签中新增parameters标签，其中name要写dependence，value是之前库里收集的应用心跳地址的id，多个应用id用逗号隔开。-->
```

#### - 2025.03.03
- 1.新增上传文件到七牛云工具，使用方式：
- com.xianmu.atp.util.upload.QiNiuUpload.uploadQiNiuYun
-     /**
    * 上传文件到七牛云
    * 本方法实现将本地文件上传到七牛云存储的流程，包括获取上传凭证和执行上传操作
    * 首先，方法通过发送请求获取七牛云的上传凭证（token），然后使用该凭证进行文件上传
    * @return boolean 上传成功返回true，上传失败返回false
      */
    * 当前为节省资源,只支持上传默认图片