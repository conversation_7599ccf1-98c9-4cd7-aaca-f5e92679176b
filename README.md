### SummerFarm-XM Automation Testing Platform


This project is an automation testing platform which is based on testng and allure that allows users to create and run automated tests on applications. 
The platform provides a user-friendly framework for creating test cases, defining test scenarios, and executing tests.

## Language

- [English](#english)
- [中文](#中文)


<a href="./README_zh.md">中文</a>

如何使用- how to use:
1. 新增用例 -add new test cases：
2. 新增场景/集合 -add new scene；
3. 新增xml用于不同环境不同场景的执行 -add new xml for invoking on the different environment；

In the project, we have used the following usages:

Some annotations of testng:

- `@Test` - you must add `description` to your test case 要不然在报告上只会显示你的方法名
- `@Owner` - for signing who edit the test case 

Annotations of allure:

- `@Epic` - for grouping the test cases 一级分类
- `@Feature` - for grouping the test cases 二级分类
- `@Story` - for grouping the test cases 三级分类
if you want to print the log that edited by yourself in the report, you can use the following code:
>  Allure.addAttachment(String name, String content);

if you have got all bases covered,

` maven` - 

> mvn clean test -Dsurefire.suiteXmlFiles=(你存放测试xml的路径)src/test/java/game-unit-test-suite.xml

And if you have added the `suiteXmlFiles` in your `pom.xml` file, you can run the tests with the following command:

> mvn clean test

However, if you want to see the reports which you executed before, you can run the following command:

> mvn test

Finally, you can see some json files in the `target/allure-report` directory. you execute the following command to generate the report:

> allure serve target/allure-report

<a href="#锚点名称">B</a>