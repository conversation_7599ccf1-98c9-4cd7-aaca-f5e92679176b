
app:
  xm_admin_domain: "https://devadmin.summerfarm.net"
  saas_manage_domain: "https://devmanage.cosfo.cn"
  srm_domain: "https://devsrm.summerfarm.net"
  manage-domain: "https://devadmin.summerfarm.net"
  mall-url:  "devh5.summerfarm.net"
  username: "<EMAIL>"
  password: "hello1234"
  dev-domain: "https://devh5.summerfarm.net"
  wechat-token: "1-6caaf42d442a42e2a1c99e386ee96c63"
  wechat-phone: "78562314569"
  merchantId: 344065
  openapi_domain: "https://qa-openapi.summerfarm.net/api?apiPath="
  appKey: "wanwei_app"
  appSecret: "7443e548f9a5832b0172e297f9578442"
  environment: "dev"
  pic-path: "test/test_upload.jpg"



rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_mall
    secret-key: ''
    sendMsgTimeout: 10000

# DataSource Config
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: true #严格模式
      datasource:
        # 主数据源
        master:
          username: test
          password: xianmu619
          url: *************************************************************************************************
        offline:
          password: xianmu619
          url: *********************************************************************************************************
          username: test
        ofcDb:
          password: xianmu619
          url: *********************************************************************************************
          username: test
        cosfodb:
          password: xianmu619
          url: ***********************************************************************************************
          username: test
        user:
          username: test
          password: xianmu619
          url: ****************************************************************************************************
    # Druid datasource
    druid:
      # 初始化大小
      initial-size: 5
      # 最小连接数
      min-idle: 10
      # 最大连接数
      max-active: 20
      # 获取连接时的最大等待时间
      max-wait: 60000
      # 一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 多久才进行一次检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置扩展插件：stat-监控统计，log4j-日志，wall-防火墙（防止SQL注入），去掉后，监控界面的sql无法统计
      filters: stat,wall
      # 检测连接是否有效的 SQL语句，为空时以下三个配置均无效
      validation-query: SELECT 1
      # 申请连接时执行validationQuery检测连接是否有效，默认true，开启后会降低性能
      test-on-borrow: true
      # 归还连接时执行validationQuery检测连接是否有效，默认false，开启后会降低性能
      test-on-return: true
      # 申请连接时如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效，默认false，建议开启，不影响性能
      test-while-idle: true
      # 是否开启 StatViewServlet
      stat-view-servlet:
        enabled: true
        # 访问监控页面 白名单，默认127.0.0.1
        allow: 127.0.0.1
        login-username: admin
        login-password: admin
      # FilterStat
      filter:
        stat:
          # 是否开启 FilterStat，默认true
          enabled: true
          # 是否开启 慢SQL 记录，默认false
          log-slow-sql: true
          # 慢 SQL 的标准，默认 3000，单位：毫秒
          slow-sql-millis: 5000
          # 合并多个连接池的监控数据，默认false
          merge-sql: false
  # redis配置
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 5
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
    groupId: saas-mall
    appKey: w8Dy/uszPmRlJiFm/P0J5g==
  # auth服务依赖
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 5
        max-idle: 5
        min-idle: 5
        max-wait: 5
  application:
    name: ATP



## 配置中心
#nacos:
#  config:
#    server-addr: test-nacos.summerfarm.net:11000
#    namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
#dubbo:
#  application:
#    name: ATP
#    id: xmTest
#  registry:
#    protocol: nacos
#    #    address: nacos://************:11000
#    address: nacos://test-nacos.summerfarm.net:11000
#    #    address: nacos://*********:11000
#    parameters:
#      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
#  protocol:
#    id: dubbo
#    name: dubbo
#    port: 20880
#  provider:
#    version: 1.0.0
#    group: online
#    timeout: 5000
#    retries: 0
#    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
#    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
#    corethreads: 10 #核心线程数，默认0
#    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
#    queues: 200 #默认0
#    alive: 30000 #默认60 * 1000ms
#  consumer:
#    version: 1.0.0
#    group: online
#    retries: 0
#    check: false
#    timeout: 10000
#    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
#    corethreads: 10 #核心线程数，默认0
#    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
#    queues: 200 #默认0
#    alive: 30000 #默认60 * 1000ms