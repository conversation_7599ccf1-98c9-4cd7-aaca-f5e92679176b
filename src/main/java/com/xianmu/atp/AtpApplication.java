package com.xianmu.atp;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication
@MapperScan("com.xianmu.atp*")
@EnableAspectJAutoProxy
public class AtpApplication {

    public static void main(String[] args) {
        SpringApplication.run(AtpApplication.class, args);
    }

}
