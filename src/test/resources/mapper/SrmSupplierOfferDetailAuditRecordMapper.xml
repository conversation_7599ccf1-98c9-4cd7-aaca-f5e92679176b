<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.pms.SrmSupplierOfferDetailAuditRecordMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.SrmSupplierOfferDetailAuditRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="offerDetailId" column="offer_detail_id" jdbcType="BIGINT"/>
            <result property="editPrice" column="edit_price" jdbcType="VARCHAR"/>
            <result property="result" column="result" jdbcType="TINYINT"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="auditAccount" column="audit_account" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="warehouseNo" column="warehouse_no" jdbcType="BIGINT"/>
            <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
            <result property="priceContent" column="price_content" jdbcType="VARCHAR"/>
            <result property="skuSubType" column="sku_sub_type" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,offer_detail_id,edit_price,
        result,audit_time,audit_account,
        creator,create_time,sku,
        warehouse_no,supplier_id,price_content,
        sku_sub_type,remark
    </sql>
    <select id="selectBySupplierIdAndWarehouseNoAndSkuAndCreateTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from srm_supplier_offer_detail_audit_record
        where
        supplier_id = #{supplierId,jdbcType=NUMERIC}
        AND warehouse_no = #{warehouseNo,jdbcType=NUMERIC}
        AND sku = #{sku,jdbcType=VARCHAR}
        AND create_time >= #{createTime}
        order by id desc
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from srm_supplier_offer_detail_audit_record
        where
        id = #{id,jdbcType=NUMERIC}
    </select>
    <select id="selectBySupplierIdAndWarehouseNoAndSkuAndResult" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from srm_supplier_offer_detail_audit_record
        where
        supplier_id = #{supplierId,jdbcType=NUMERIC}
        AND warehouse_no = #{warehouseNo,jdbcType=NUMERIC}
        AND sku = #{sku,jdbcType=VARCHAR}
        AND result = #{result,jdbcType=NUMERIC}
    </select>
</mapper>
