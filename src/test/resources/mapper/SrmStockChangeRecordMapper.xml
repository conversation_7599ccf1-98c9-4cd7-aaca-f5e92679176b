<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.pms.SrmStockChangeRecordMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.SrmStockChangeRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="BIGINT"/>
            <result property="updater" column="updater" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,supplier_id,type,
        status,create_time,update_time,
        creator,updater
    </sql>
    <delete id="deleteById">
        delete xr,sscrd
        from srm_stock_change_record xr
         left join xianmudb.srm_stock_change_record_detail sscrd on xr.id = sscrd.record_id
        where xr.id = #{id,jdbcType=NUMERIC}
    </delete>
</mapper>
