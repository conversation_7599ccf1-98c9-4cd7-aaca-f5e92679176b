<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.SupplierAccountMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.SupplierAccount">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
            <result property="payType" column="pay_type" jdbcType="INTEGER"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="accountBank" column="account_bank" jdbcType="VARCHAR"/>
            <result property="accountAscription" column="account_ascription" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,supplier_id,pay_type,
        account_name,account_bank,account_ascription,
        account,creator
    </sql>
    <select id="selectBySupplierId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supplier_account
        where
        supplier_id = #{supplierId,jdbcType=NUMERIC}
    </select>
</mapper>
