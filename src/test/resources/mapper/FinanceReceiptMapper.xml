<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.FinanceReceiptMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.FinanceReceipt">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="adminId" column="admin_id" jdbcType="BIGINT"/>
            <result property="invoiceId" column="invoice_id" jdbcType="BIGINT"/>
            <result property="invoiceTitle" column="invoice_title" jdbcType="VARCHAR"/>
            <result property="salerId" column="saler_id" jdbcType="BIGINT"/>
            <result property="receiptAmount" column="receipt_amount" jdbcType="DECIMAL"/>
            <result property="billNumber" column="bill_number" jdbcType="INTEGER"/>
            <result property="receiptVoucher" column="receipt_voucher" jdbcType="VARCHAR"/>
            <result property="writeOffStatus" column="write_off_status" jdbcType="TINYINT"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="salerName" column="saler_name" jdbcType="VARCHAR"/>
            <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
            <result property="nameRemakes" column="name_remakes" jdbcType="VARCHAR"/>
            <result property="customerType" column="customer_type" jdbcType="TINYINT"/>
            <result property="financeBankFlowingWaterId" column="finance_bank_flowing_water_id" jdbcType="BIGINT"/>
            <result property="payType" column="pay_type" jdbcType="INTEGER"/>
            <result property="receiptNo" column="receipt_no" jdbcType="CHAR"/>
            <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="auditorId" column="auditor_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,admin_id,invoice_id,
        invoice_title,saler_id,receipt_amount,
        bill_number,receipt_voucher,write_off_status,
        remarks,create_time,creator,
        update_time,updater,saler_name,
        creator_id,name_remakes,customer_type,
        finance_bank_flowing_water_id,pay_type,receipt_no,
        auditor,audit_time,auditor_id
    </sql>
    <select id="selectAllByFinanceOrderIdWithJoin"
            resultType="com.xianmu.atp.dal.model.FinanceReceipt">
        select
            fr.id
        from finance_receipt fr
        left join finance_receipt_bill fb on fr.id = fb.finance_receipt_id
        where fb.finance_order_id = #{financeOrderId, jdbcType=NUMERIC}
        and fb.source_no = #{sourceNo, jdbcType=VARCHAR}
    </select>
</mapper>
