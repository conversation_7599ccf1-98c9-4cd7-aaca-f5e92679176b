<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.pms.InventorySupplierWarehouseStoreMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.InventorySupplierWarehouseStore">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
            <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
            <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
            <result property="quantity" column="quantity" jdbcType="BIGINT"/>
            <result property="safeQuantity" column="safe_quantity" jdbcType="BIGINT"/>
            <result property="saleableQuantity" column="saleable_quantity" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        creator,updater,warehouse_no,
        supplier_id,sku_code,quantity,
        safe_quantity,saleable_quantity
    </sql>
    <select id="selectBySupplierIdAndWarehouseNoAndSkuCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from inventory_supplier_warehouse_store
        where
        supplier_id = #{supplierId,jdbcType=NUMERIC}
        AND warehouse_no = #{warehouseNo,jdbcType=NUMERIC}
        AND sku_code = #{skuCode,jdbcType=VARCHAR}
    </select>
</mapper>
