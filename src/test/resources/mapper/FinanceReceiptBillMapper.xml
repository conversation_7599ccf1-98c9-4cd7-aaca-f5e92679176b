<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.fms.FinanceReceiptBillMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.FinanceReceiptBill">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="financeReceiptId" column="finance_receipt_id" jdbcType="BIGINT"/>
            <result property="financeOrderId" column="finance_order_id" jdbcType="BIGINT"/>
            <result property="receiptAmount" column="receipt_amount" jdbcType="DECIMAL"/>
            <result property="otherAmount" column="other_amount" jdbcType="DECIMAL"/>
            <result property="receiptVoucher" column="receipt_voucher" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="receivableAmount" column="receivable_amount" jdbcType="DECIMAL"/>
            <result property="sourceNo" column="source_no" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,finance_receipt_id,finance_order_id,
        receipt_amount,other_amount,receipt_voucher,
        create_time,update_time,receivable_amount,
        source_no
    </sql>
    <select id="selectAllByFinanceOrderIdAndSourceNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from finance_receipt_bill
        where
        finance_order_id = #{financeOrderId,jdbcType=NUMERIC}
        AND source_no = #{sourceNo,jdbcType=VARCHAR}
    </select>
</mapper>
