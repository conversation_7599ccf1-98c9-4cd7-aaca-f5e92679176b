<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.pms.SrmStockPackupMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.SrmStockPackup">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="spu" column="spu" jdbcType="VARCHAR"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
            <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
            <result property="productionDate" column="production_date" jdbcType="DATE"/>
            <result property="yesterdayPackupStock" column="yesterday_packup_stock" jdbcType="INTEGER"/>
            <result property="packupStatus" column="packup_status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="BIGINT"/>
            <result property="updater" column="updater" jdbcType="BIGINT"/>
            <result property="todayPackupStock" column="today_packup_stock" jdbcType="INTEGER"/>
            <result property="todayClearStock" column="today_clear_stock" jdbcType="TINYINT"/>
            <result property="subType" column="sub_type" jdbcType="INTEGER"/>
            <result property="supplierPrice" column="supplier_price" jdbcType="DECIMAL"/>
            <result property="supplierWeightPrice" column="supplier_weight_price" jdbcType="DECIMAL"/>
            <result property="skuWeight" column="sku_weight" jdbcType="DECIMAL"/>
            <result property="quoteType" column="quote_type" jdbcType="INTEGER"/>
            <result property="supplierCost" column="supplier_cost" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,spu,sku,
        warehouse_no,supplier_id,production_date,
        yesterday_packup_stock,packup_status,create_time,
        update_time,creator,updater,
        today_packup_stock,today_clear_stock,sub_type,
        supplier_price,supplier_weight_price,sku_weight,
        quote_type,supplier_cost
    </sql>
    <select id="selectBySupplierIdAndWarehouseNoAndSkuCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from srm_stock_packup
        where
        supplier_id = #{supplierId,jdbcType=NUMERIC}
        AND warehouse_no = #{warehouseNo,jdbcType=NUMERIC}
        AND sku = #{sku,jdbcType=VARCHAR}
    </select>
</mapper>
