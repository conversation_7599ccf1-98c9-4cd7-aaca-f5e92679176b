<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.SkuInfoMapper">
    <select id="getSkuInfoBySku" resultType="com.xianmu.atp.dal.model.SkuInfo">
        SELECT
        p.pd_id,
        p.pd_no,
        p.pd_name,
        p.quality_time,
        p.quality_time_unit,
        p.category_id,
        p.storage_location,
        i.weight,
        i.sku_pic,
        i.unit,
        i.type,
        p.storage_location,
        c.type
        FROM
        products p
        LEFT JOIN inventory i ON p.pd_id = i.pd_id
        LEFT JOIN category c ON p.category_id = c.id
        WHERE
        i.sku = #{sku};
    </select>
</mapper>
