<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.DingdingProcessFlowMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.DingdingProcessFlow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processCode" column="process_code" jdbcType="VARCHAR"/>
            <result property="bizType" column="biz_type" jdbcType="INTEGER"/>
            <result property="bizId" column="biz_id" jdbcType="BIGINT"/>
            <result property="processStatus" column="process_status" jdbcType="INTEGER"/>
            <result property="processUrl" column="process_url" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="INTEGER"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="formData" column="form_data" jdbcType="VARCHAR"/>
            <result property="platformType" column="platform_type" jdbcType="VARCHAR"/>
            <result property="env" column="env" jdbcType="VARCHAR"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="autoTerminateTime" column="auto_terminate_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_code,
        biz_type,biz_id,process_status,
        process_url,remark,creator,
        updater,create_time,update_time,
        form_data,platform_type,env,
        uuid,auto_terminate_time
    </sql>
    <select id="selectOneByBizIdAndBizTypeAndProcessStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dingding_process_flow
        where
        biz_id = #{bizId,jdbcType=NUMERIC}
        AND biz_type = #{bizType,jdbcType=NUMERIC}
        AND process_status = #{processStatus,jdbcType=NUMERIC}
    </select>
    <select id="selectOneByBizIdAndBizType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dingding_process_flow
        where
        biz_id = #{bizId,jdbcType=NUMERIC}
        AND biz_type = #{bizType,jdbcType=NUMERIC}
    </select>
</mapper>
