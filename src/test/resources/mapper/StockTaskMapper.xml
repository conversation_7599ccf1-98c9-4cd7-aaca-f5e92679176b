<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.StockTaskMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.StockTask">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="taskNo" column="task_no" jdbcType="VARCHAR"/>
            <result property="areaNo" column="area_no" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="expectTime" column="expect_time" jdbcType="TIMESTAMP"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="addtime" column="addtime" jdbcType="TIMESTAMP"/>
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
            <result property="outStoreNo" column="out_store_no" jdbcType="INTEGER"/>
            <result property="outType" column="out_type" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="dimension" column="dimension" jdbcType="INTEGER"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="mismatchReason" column="mismatch_reason" jdbcType="VARCHAR"/>
            <result property="transitionField" column="transition_field" jdbcType="INTEGER"/>
            <result property="taskType" column="task_type" jdbcType="INTEGER"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="closeReason" column="close_reason" jdbcType="VARCHAR"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="optionFlag" column="option_flag" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="inventoryLocked" column="inventory_locked" jdbcType="TINYINT"/>
            <result property="systemSource" column="system_source" jdbcType="INTEGER"/>
            <result property="outOrderNo" column="out_order_no" jdbcType="VARCHAR"/>
            <result property="outboundCategory" column="outbound_category" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,task_no,area_no,
        type,expect_time,state,
        addtime,admin_id,updatetime,
        out_store_no,out_type,remark,
        dimension,process_state,mismatch_reason,
        transition_field,task_type,category,
        close_reason,updater,option_flag,
        tenant_id,inventory_locked,system_source,
        out_order_no,outbound_category
    </sql>
</mapper>
