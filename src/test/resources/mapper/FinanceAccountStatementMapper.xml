<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.FinanceAccountStatementMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.FinanceAccountStatement">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="estimateAmount" column="estimate_amount" jdbcType="DECIMAL"/>
            <result property="totalBillAmount" column="total_bill_amount" jdbcType="DECIMAL"/>
            <result property="walletsId" column="wallets_id" jdbcType="BIGINT"/>
            <result property="writeOffAmount" column="write_off_amount" jdbcType="DECIMAL"/>
            <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="taxNumber" column="tax_number" jdbcType="VARCHAR"/>
            <result property="supplierAccountId" column="supplier_account_id" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="pdType" column="pd_type" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="advancedOrderId" column="advanced_order_id" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleteReason" column="delete_reason" jdbcType="INTEGER"/>
            <result property="currentProcessor" column="current_processor" jdbcType="VARCHAR"/>
            <result property="creatorAdminId" column="creator_admin_id" jdbcType="INTEGER"/>
            <result property="supplierConfirmStatus" column="supplier_confirm_status" jdbcType="INTEGER"/>
            <result property="confirmUser" column="confirm_user" jdbcType="INTEGER"/>
            <result property="payType" column="pay_type" jdbcType="INTEGER"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="accountBank" column="account_bank" jdbcType="VARCHAR"/>
            <result property="accountAscription" column="account_ascription" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,estimate_amount,total_bill_amount,
        wallets_id,write_off_amount,supplier_id,
        supplier_name,tax_number,supplier_account_id,
        creator,create_time,pd_type,
        remark,status,advanced_order_id,
        update_time,delete_reason,current_processor,
        creator_admin_id,supplier_confirm_status,confirm_user,
        pay_type,account_name,account_bank,
        account_ascription,account
    </sql>
</mapper>
