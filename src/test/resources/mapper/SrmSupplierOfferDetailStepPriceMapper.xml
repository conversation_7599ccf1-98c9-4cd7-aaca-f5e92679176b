<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.pms.SrmSupplierOfferDetailStepPriceMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.SrmSupplierOfferDetailStepPrice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="offerDetailId" column="offer_detail_id" jdbcType="BIGINT"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,offer_detail_id,quantity,
        price,creator,create_time,
        update_time,updater
    </sql>
    <select id="selectStepDetailPrice" resultMap="BaseResultMap">
        select sp.*
        from srm_supplier_offer s
                 left join srm_supplier_offer_detail sd on s.id = sd.offer_id
                 left join srm_supplier_offer_detail_step_price sp on sd.id = sp.offer_detail_id
        where s.sku = #{skuCode,jdbcType=VARCHAR}
          and s.warehouse_no = #{warehouseNo,jdbcType=NUMERIC}
          and s.supplier_id = #{supplierId,jdbcType=NUMERIC}
          and sd.status = #{status,jdbcType=NUMERIC}
        order by sd.end_time desc
            limit 1;
    </select>
</mapper>
