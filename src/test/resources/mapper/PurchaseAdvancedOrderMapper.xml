<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.PurchaseAdvancedOrderMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.PurchaseAdvancedOrder">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
            <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="payType" column="pay_type" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="pdType" column="pd_type" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="supplierAccountId" column="supplier_account_id" jdbcType="INTEGER"/>
            <result property="temporaryRemark" column="temporary_remark" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleteReason" column="delete_reason" jdbcType="INTEGER"/>
            <result property="currentProcessor" column="current_processor" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type,total_amount,
        supplier_id,supplier_name,pay_type,
        create_time,pd_type,remark,
        status,supplier_account_id,temporary_remark,
        state,update_time,delete_reason,
        current_processor
    </sql>
    <select id="selectById" resultType="com.xianmu.atp.dal.model.PurchaseAdvancedOrder">
        SELECT <include refid="Base_Column_List" />
        FROM purchase_advanced_order
        WHERE id = #{id}
    </select>
</mapper>
