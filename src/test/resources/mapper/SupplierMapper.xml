<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xianmu.atp.dal.mapper.SupplierMapper">

    <resultMap id="BaseResultMap" type="com.xianmu.atp.dal.model.Supplier">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="categoryArray" column="category_array" jdbcType="VARCHAR"/>
            <result property="productArray" column="product_array" jdbcType="VARCHAR"/>
            <result property="payType" column="pay_type" jdbcType="VARCHAR"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="accountBank" column="account_bank" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="manager" column="manager" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="invoice" column="invoice" jdbcType="TINYINT"/>
            <result property="contract" column="contract" jdbcType="VARCHAR"/>
            <result property="deliveryFrequent" column="delivery_frequent" jdbcType="VARCHAR"/>
            <result property="settleForm" column="settle_form" jdbcType="INTEGER"/>
            <result property="settleType" column="settle_type" jdbcType="INTEGER"/>
            <result property="customStartDate" column="custom_start_date" jdbcType="DATE"/>
            <result property="customCycle" column="custom_cycle" jdbcType="INTEGER"/>
            <result property="creditDays" column="credit_days" jdbcType="INTEGER"/>
            <result property="supplierType" column="supplier_type" jdbcType="TINYINT"/>
            <result property="taxNumber" column="tax_number" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="auditPassDate" column="audit_pass_date" jdbcType="DATE"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="qrCodeShowSupplierSwitch" column="qr_code_show_supplier_switch" jdbcType="TINYINT"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="businessType" column="business_type" jdbcType="TINYINT"/>
            <result property="customerSupplierId" column="customer_supplier_id" jdbcType="VARCHAR"/>
            <result property="packupFrequent" column="packup_frequent" jdbcType="VARCHAR"/>
            <result property="noPackupFrequentDate" column="no_packup_frequent_date" jdbcType="VARCHAR"/>
            <result property="consignmentPurchaser" column="consignment_purchaser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,address,
        category_array,product_array,pay_type,
        account_name,account_bank,account,
        manager,remark,invoice,
        contract,delivery_frequent,settle_form,
        settle_type,custom_start_date,custom_cycle,
        credit_days,supplier_type,tax_number,
        type,status,audit_pass_date,
        creator,create_time,updater,
        update_time,qr_code_show_supplier_switch,source,
        tenant_id,business_type,customer_supplier_id,
        packup_frequent,no_packup_frequent_date,consignment_purchaser
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supplier
        where
        id = #{id,jdbcType=NUMERIC}
    </select>
    <select id="selectByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supplier
        where
        status = #{status,jdbcType=NUMERIC} limit 10
    </select>
    <update id="updateStatusById">
        update supplier
        set status = #{status,jdbcType=NUMERIC}
        where id = #{id,jdbcType=NUMERIC}
    </update>
</mapper>
