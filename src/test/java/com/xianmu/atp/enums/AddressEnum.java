package com.xianmu.atp.enums;

public enum AddressEnum {


    zylHangzhouAddress("朱永林的杭州地址",350666,350887),
    mallAuto<PERSON>ddress("商城默认地址",351940,350935),
    mallAutoAddress2("第二个默认地址",346919,349375),

    mallAutoAddress3("订单价格地址",352860,352599),
    bigUserAddress("大客户地址",351848,351900),
    popAddress("pop地址",352428,352276),
    deliveryFee<PERSON>ddress("配送费测试地址",354044,353666),

    deliveryBigFeeAddress("大客户配送费测试地址",354045,353667),

    deliveryUniqueBigFeeAddress("大客户地址设置配送费",354049,353667),
    ;

    AddressEnum(String addressName, int contactId,int mid) {
        this.addressName = addressName;
        this.contactId = contactId;
        this.mid = mid;
    }

    public int getMid() {
        return mid;
    }

    public void setMid(int mid) {
        this.mid = mid;
    }

    private int mid;

    private String addressName;

    public String getAddressName() {
        return addressName;
    }

    public void setAddressName(String addressName) {
        this.addressName = addressName;
    }

    public int getContactId() {
        return contactId;
    }

    public void setContactId(int contactId) {
        this.contactId = contactId;
    }

    private int contactId;
}
