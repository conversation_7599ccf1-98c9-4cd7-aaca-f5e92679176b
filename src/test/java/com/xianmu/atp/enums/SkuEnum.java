package com.xianmu.atp.enums;

public enum SkuEnum {
    /*
     * skuName: sku名称,自定义
     * sku: sku编号
     * spuId: spuId,要注意不是spu编号，拿来请求详情页用
     * extraKey: sku额外信息key，与extraValue同时使用，在SkuHelper中用checkSkuExtraInfo方法检查返回结果是否符合预设信息
     * defaultBuyNum: 默认购买数量
     */
    skuForSpuStartNum("spu限购","2225088267551","41221","spuBaseSaleQuantity","5",1),

    skuForActivityLimit("活动限购","2154561007035","42164","","",1),
    skuForAllCategory("全品类越库","************","21835","","",1),

    skuBillAfter("账期大客户售后用","************","40747","price","53.20",1),
    skuNearDate("临保品","************","40747","","",1),

    skuPop("pop基础品","2185235215881","39911","","",1),

    skuTiming("省心送品","2154766511136","2154766511","ruleId","3468",1),

    priceTestA("无优惠品","2154533451774","44685","","",1),

    priceTestB("使用优惠券","2154533451828","44685","","",1),

    priceTestC("本身有特价","2154533451410","44685","","",1),

    priceTestD("底价控制","2154533451851","44685","","",1),

    allCategory("全品类","************","","","",1),

    deliveryFeeSku("运费区测试商品","************","20608","","",1),

    deliveryFeeSku2("全品类运费测试商品","2154376847572","39932","","",1),

    deliveryFeeSku3("运费退款组合品","************","20608","","",1),


    ;
    SkuEnum(String skuName, String sku,String spuId,String extraKey,String extraValue,int defaultBuyNum){
        this.skuName = skuName;
        this.sku = sku;
        this.extraKey = extraKey;
        this.extraValue = extraValue;
        this.defaultBuyNum = defaultBuyNum;
        this.spuId = spuId;
    }

    public String getSpuId() {
        return spuId;
    }

    public void setSpuId(String spu) {
        this.spuId = spuId;
    }

    private String spuId;

    private String skuName;
    private String sku;

    private String extraKey;

    public String getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(String extraKey) {
        this.extraKey = extraKey;
    }

    public String getExtraValue() {
        return extraValue;
    }

    public void setExtraValue(String extraValue) {
        this.extraValue = extraValue;
    }

    public int getDefaultBuyNum() {
        return defaultBuyNum;
    }

    public void setDefaultBuyNum(int defaultBuyNum) {
        this.defaultBuyNum = defaultBuyNum;
    }

    private String extraValue;

    private int defaultBuyNum;

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }
}