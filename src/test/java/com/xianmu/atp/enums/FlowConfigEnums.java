package com.xianmu.atp.enums;

import lombok.Getter;

/**
 * 基于process_config表获取的数据
 * 定义飞书审批流配置枚举类，用于描述不同业务场景下的审批流程配置
 */
@Getter
public enum FlowConfigEnums {
    PAYEE_AUDIT("PAYEE_AUDIT_CODE", 70, "承运商调整"),
    COST_ADJUSTMENT_ROUTE_DETAIL("COST_ADJUSTMENT_AUDIT_CODE", 17, "路线明细调整"),
    COST_ADJUSTMENT_UNLOADING_FEE("COST_ADJUSTMENT_AUDIT_CODE", 16, "卸货费用调整"),
    COST_ADJUSTMENT_BILL_FEE("COST_ADJUSTMENT_AUDIT_CODE", 18, "对账单费用调整"),
    PURCHASE_ADVANCED_ORDER_APPROVAL("PURCHASE_ADVANCED_ORDER_APPROVAL_CODE", 13, "采购预付单审批"),
    FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL("FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_CODE", 14, "采购预付付款单审批"),
    SUPPLIER_AUDIT("SUPPLIER_AUDIT_CODE", 7, "供应商审核审批"),
    MERCHANT_SITUATION("MERCHANT_SITUATION", 10, "客情券审批"),
    MERCHANT_CANCEL("MERCHANT_CANCEL_CODE", 22, "门店注销审批"),
    COUPON_AUDIT("COUPON_AUDIT_CODE", 2, "卡券发起审批"),
    CUSTOMER_FAIL("CUSTOMER_FAIL_CODE", 1, "客户倒闭审批"),
    PURCHASER_ORDER_CREATE("PURCHASER_ORDER_CREATE", 23, "采购单审批"),
    FOLLOW_UP_UPDATE_POI("FOLLOW_UP_UPDATE_POI", 24, "销售拜访修改门店定位"),
    PURCHASE_STATEMENT_APPROVAL("PURCHASE_STATEMENT_APPROVAL_CODE", 11, "采购对账单审批"),
    FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL("FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_CODE", 12, "采购对账单付款审批"),
    SUPPLIER_CONTRACT_AUDIT("SUPPLIER_CONTRACT_AUDIT_CODE", 8, "供应商合同审批"),
    STOCKTAKING_AUDUT("STOCKTAKING_AUDUT_CODE", 5, "盘亏、盘盈审批"),
    STOCKDAMAGE_AUDUT("STOCKDAMAGE_AUDUT_CODE", 4, "货损审批"),
    BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT("BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT_CODE", 19, "BMS发起打款"),
    BMS_RECONCILIATION_PAYMENT_AUDIT("BMS_RECONCILIATION_PAYMENT_AUDIT_CODE", 20, "BMS提交打款"),
    BMS_PAYMENT_ORDER_APPROVAL("BMS_PAYMENT_ORDER_APPROVAL_CODE", 15, "BMS打款单生成付款单"),
    SECURITY_STOCK_AUDIT("SECURITY_STOCK_AUDIT", 21, "安全库存审批"),
    SRM_MODIFY_OFFER_DETAIL("SRM_MODIFY_OFFER_DETAIL_CODE", 9, "供应商报价变更审批"),
    COUPON_AUDIT_NOT_NEED_PRINCIPAL("COUPON_AUDIT_NOT_NEED_PRINCIPAL", 25, "卡劵人工发放不需要负责人审批"),
    COUPON_AUDIT_NEED_PRINCIPAL("COUPON_AUDIT_NEED_PRINCIPAL", 26, "卡劵人工发放需要负责人审批"),
    SAFE_INVENTORY_WARN("SAFE_INVENTORY_WARN", 27, "安全库存预警"),
    TEST("TEST_CODE", 3, "测试审批流"),
    POP_SUPPLIER_PRICE_ADJUST("POP_SUPPLIER_PRICE_ADJUST", 30, "POP供应商售价审批"),
    SUPPLIER_PRICE_AUDIT("SUPPLIER_PRICE_AUDIT", 31, "供应商供货价调整"),
    ONSALE_AUDIT("ONSALE_AUDIT_CODE", 71, "商品上下架");

    private final String processCode;
    private final int processType;
    private final String remark;

    FlowConfigEnums(String processCode, int processType, String remark) {
        this.processCode = processCode;
        this.processType = processType;
        this.remark = remark;
    }
}
