package com.xianmu.atp.enums.api.common;

import lombok.Getter;

@Getter
public enum LoginEnumsInterface {
    xianmuLogin("authentication/auth/username/login", "POST", "application/x-www-form-urlencoded","鲜沐后台登录接口"),
    saasManageLogin("/sdk/user-login/login", "POST", "application/json","saas商家后台登录接口"),
    srmPcLogin("/authentication/auth/phone/login", "POST", "application/json","srm登录接口");


    private final String url;
    private final String method;
    private final String contentType;
    private final String description;

    LoginEnumsInterface(String url, String method, String contentType,String description) {
        this.url = url;
        this.method = method;
        this.contentType = contentType;
        this.description = description;

    }
}
