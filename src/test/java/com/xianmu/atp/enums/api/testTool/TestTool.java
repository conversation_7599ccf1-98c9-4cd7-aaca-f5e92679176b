package com.xianmu.atp.enums.api.testTool;

import lombok.Getter;

public interface TestTool {
    @Getter
    enum TestToolEnum{
        xmPurchaseCreate( "/quantity-service/pms/xmPurchaseCreate", "POST", "application/json","造数工具--鲜沐采购下单");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;
        TestToolEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
}
