package com.xianmu.atp.enums.api.common;

import lombok.Getter;

public interface CommonEnumsInterface {

    @Getter
    enum FlowEnums {
        flow("/common-service/process/upsert/auto/process", "POST", null,"飞书审批流处理接口"),
        get_qi_niu_token("/qiniu/upload-token/one", "POST", null,"获取七牛云key,token"),
        qi_niu_upload("https://up-z0.qiniup.com/", "POST", "multipart/form-data","七牛云上传图片"),
        importResult("/common-service/download-center/query/import-result", "POST", "application/json","excel导入结果");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        FlowEnums(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    @Getter
    enum UtilEnums {
        excelCreate("https://test-tool.summerfarm.net/util/excel/create", "POST", "application/json","excel生成并上传oss");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        UtilEnums(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    @Getter
    enum DownloadEnums {

        importResult("/common-service/download-center/query/import-result", "POST", "application/json","excel导入结果"),
        importOssUrl("/common-service/download-center/import/oss-url", "POST", "application/json","导入excel");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        DownloadEnums(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    @Getter
    enum CommonEnum {

        personalInfo("/admin/personalInfo", "GET", "application/json","当前用户信息详情"),
        selectLikeBySkuOrName("/summerfarm-manage/saas-purchases/query/selectLikeBySkuOrName", "POST", "application/json","查询货品列表"),
        merchantDetail("/sf-mall-manage/merchant/query/detail/", "POST", "application/json","门店详情");


        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        CommonEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
}
