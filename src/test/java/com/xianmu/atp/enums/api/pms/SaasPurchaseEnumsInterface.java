package com.xianmu.atp.enums.api.pms;

import lombok.Getter;

/**
 * SaaS采购相关接口枚举定义
 * 管理采购单、预约单、退货单等API路由信息
 * 
 * <AUTHOR> Test Engineer
 */
public class SaasPurchaseEnumsInterface {

    /**
     * 采购单管理相关接口
     */
    @Getter
    public enum SaasPurchaseEnum {
        // 采购单列表查询接口
        queryPageV2("/pms-service/saas-purchase/query/page/v2", "POST", "application/json", "采购单列表查询接口"),
        // 采购单新增接口
        save("/pms-service/saas-purchase/upsert/save", "POST", "application/json", "采购单新增接口"),
        // 采购单编辑更新接口
        update("/pms-service/saas-purchase/upsert/update", "POST", "application/json", "采购单编辑更新接口"),
        // 采购单删除接口
        delete("/pms-service/saas-purchase/upsert/del", "POST", "application/json", "采购单删除接口"),
        // 采购单批量新增接口
        saveBatch("/pms-service/saas/purchase/save/batch", "POST", "application/json", "采购单批量新增接口"),
        copyCheck("/pms-service/saas/purchase/copy/check", "POST", "application/json", "采购单复制检查接口"),
        // 采购单详情接口
        detail("/pms-service/saas-purchase/query/detail/v2", "POST", "application/json", "采购单详情接口"),
        exportV2("/pms-service/saas-purchase/export/v2", "POST", "application/json", "采购单列表导出接口"),
        exportByPurchaseNo("/pms-service/saas-purchase/export-purchaseNo/v2", "POST", "application/json", "采购单详情页导出"),
        inOutStatistics("/pms-service/po/purchase/query/in/out/statistics", "POST", "application/json", "出入库明细汇总数据"),
        // 采购单出入库明细列表查询
        queryInOutDetail("/pms-service/po/purchase/query/in/out/detail", "POST", "application/json", "采购单出入库明细列表查询");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        SaasPurchaseEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 供应商关系查询相关接口
     */
    @Getter
    public enum SupplyRelationQueryEnum {
        // 查询允供供应商列表接口
        querySkuSupplyList("/pms-service/saas/supply/relation/query/sku-supply/list", "POST", "application/json", "查询允供供应商列表接口");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        SupplyRelationQueryEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 预约单管理相关接口
     */
    @Getter
    public enum SaasArrangeEnum {

        // 预约单列表查询接口
        listArrange("/pms-service/saas-arrange/list/purchasesNo", "POST", "application/json", "预约单列表查询接口"),
        closeArrange("/pms-service/saas-arrange/close/stockArrangeId", "POST", "application/json", "撤销预约单接口"),
        updateArrange("/pms-service/saas-arrange/upsert/update", "POST", "application/json", "更新预约单接口"),
        updateArrangeRemark("/pms-service/saas-arrange/update-remark", "POST", "application/json", "更新预约单备注接口"),
        addArrange("/pms-service/saas-arrange/add", "POST", "application/json", "新增预约单接口"),
        querySkuArrange("/pms-service/saas-arrange/sku/list/purchasesNo", "POST", "application/json", "可预约SKU列表接口"),
        // 预约单详情接口
        detailStockArrangeId("/pms-service/saas-arrange/detail/stockArrangeId", "POST", "application/json", "预约单详情接口");
        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        SaasArrangeEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 仓库管理相关接口
     */
    @Getter
    public enum WarehouseEnum {
        // 查询证件信息接口
        queryBatchStandardBatch("/pms-service/saas-arrange/query/batch/standard-batch", "POST", "application/json", "查询证件信息接口"),
        closeTask("/summerfarm-wms/in-store/close", "POST", "application/json", "关闭入库任务接口"),
        // 批量查询仓库产能接口
        queryWarehouseCapacityBatch("/pms-service/box/warehouse/query/warehouse-capacity/batch", "POST", "application/json", "批量查询仓库产能接口");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        WarehouseEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 采购退货单管理相关接口
     */
    @Getter
    public enum SaasPurchaseBackEnum {
        // 采购退货单列表查询
        queryPage("/pms-service/saas-purchase-back/query/page", "POST", "application/json", "采购退货单列表查询");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        SaasPurchaseBackEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
}