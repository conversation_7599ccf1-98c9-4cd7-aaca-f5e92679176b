package com.xianmu.atp.enums.api.goods;

import lombok.Getter;

//货品相关
public interface GoodsEnumsInterface {

    @Getter
    enum GoodsEnum{
        queryProductInfo( "/sf-mall-manage/product/query/info", "POST", "application/json","查询商品详情"),
        upsetInfo("/sf-mall-manage/product/upset/info", "PUT", "application/json","商品上新保存发布");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        GoodsEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
}
