package com.xianmu.atp.enums.api.pms;

import lombok.Getter;
public interface PmsEnumsInterface {

    @Getter
    enum PurchaseEnum {
        query("/pms-service/purchase/query/page", "POST", "application/json","采购单列表查询接口"),
        uploadV3("/pms-service/saas-purchase/upload/purchases-template/v3", "POST", "application/json","采购单新增--导入接口"),
        exportDetail("/pms-service/po/purchase/query/detail/export", "POST", "application/json","采购单--详情导出接口"),
        exportList("/pms-service/po/export/list", "POST", "application/json","采购单--列表导出接口"),
        queryPurchasePage( "/pms-service/saas-purchase/query/page", "POST", "application/json","查询采购单列表"),
        detail("/pms-service/purchase/query/detail","POST", "application/json","采购单详情接口");


        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        PurchaseEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
     }

    //预付单流程相关接口
    @Getter
    enum PurchaseAdvanceEnum{
        add( "/pms-service/purchase-advance/upsert/add", "POST", "application/json","预付单新增接口"),
        query("/pms-service/purchase-advance/query/page", "POST", "application/json","预付单列表查询接口"),
        advanceRecordQuery("/pms-service/advance/record/query/page", "POST", "application/json","查询预付变动记录列表"),
        checkBySupplier("/pms-service/po/query/check-by-supplier", "POST", "application/json","校验是否处于预付中"),
        advancePaymentBind("/pms-service/purchase/funds/upsert/advance/bind", "POST", "application/json","绑定预付余额");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        PurchaseAdvanceEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
      }
    //采购退款单流程相关接口
    @Getter
    enum PurchaseRefundEnum{
        add( "/pms-service/finance-purchase/upsert/save", "POST", "application/json","采购单退款-新增接口"),
        query("/pms-service/finance-purchase/query/page", "POST", "application/json","采购单退款-列表查询接口"),
        refundDetail("/pms-service/finance-purchase/query/detail","POST", "application/json","采购单退款单-详情接口"),
        financeBankFlowingWaterQuery("financeBankFlowingWater/list/1/10","get",null,"银行退款记录--查询接口"),
        receivableList("/bms-service/query/receivable/page","POST","application/json","供应商退款核销单--列表查询"),
        receivableReject("/bms-service/upsert/receivable/reject","POST","application/json","供应商退款核销单--拒绝"),
        receivableConfirm("/bms-service/upsert/receivable/confirm","POST","application/json","供应商退款核销单--核销通过"),
        receivableDetail("/bms-service/query/receivable/detail","POST","application/json","供应商退款核销单--查看详情");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        PurchaseRefundEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    //供应商管理相关接口
    @Getter
    enum SupplierEnum{
        detail( "/pms-service/supplier/query/detail", "POST", "application/json","供应商--详情接口"),
        queryPage( "pms-service/supplier/query/page", "POST", "application/json","供应商--列表接口"),
        accountDetail( "pms-service/supplier/query/account-detail-list", "POST", "application/json","查询供应商账户接口");
        private final String url;
        private final String method;
        private final String contentType;
        private final String description;
        SupplierEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
    //POP流程相关接口
    @Getter
    enum PopEnum{
        publish_pop_product("/products/upsert/publish_pop_product", "POST", "application/json","POP商品上新-发布商品"),
        categoryIndex( "/pms-service/srm/pop/query/category-index", "POST", "application/json","POP商品管理--查询商品类目接口"),
        priceUpsert( "/pms-service/srm/pop/upsert/sku/stock-change/price", "POST", "application/json","POP商品管理--价格提报"),
        stockUpsert( "/pms-service/srm/pop/upsert/sku/stock-change/stock", "POST", "application/json","POP商品管理--供应商库存提报"),
        stockChangeRecord( "/pms-service/srm/pop/query/sku/stock-change/page", "POST", "application/json","POP商品管理--提报记录"),
        skuIndex( "/pms-service/srm/pop/query/sku-index", "POST", "application/json","POP商品管理--查询商品列表接口");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;
        PopEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
    //SRM-PC端
    @Getter
    enum SrmPcEnum{
        priceQuery( "/pms-service/srm/price/coordination/price/query/page", "POST", "application/json","SRM-代销业务-查询当前报价列表"),
        stockPackupQuery( "/pms-service/srm/stockPackup/query/page", "POST", "application/json","SRM-代销业务-库存管理--列表"),
        stockPackupImport( "/pms-service/srm/stockPackup/import", "POST", "application/json","SRM-代销业务-库存管理--库存提报导入"),
        stockChangeImport( "/pms-service/srm/stockChange/import", "POST", "application/json","SRM-代销业务-库存管理--库存调整导入"),
        relationUpsert( "/pms-service/srm/stockPackup/upsert/sku/relation", "POST", "application/json","SRM-代销业务-库存管理--更新供货编码"),
        productDateUpsert( "/pms-service/srm/stockPackup/upsert/productDate", "POST", "application/json","SRM-代销业务-库存管理--更新生产日期"),
        stockPackupUpsert( "/pms-service/srm/stockChange/upsert/add", "POST", "application/json","SRM-代销业务-库存管理--库存调整"),
        stockChangeQuery( "/pms-service/srm/stockChange/query/page", "POST", "application/json","SRM-代销业务-库存变更记录--列表"),
        priceAdd( "/pms-service/srm/price/coordination/upsert/create", "POST", "application/json","SRM-代销业务-供货价调整--新增供货价"),
        priceAduitQuery( "/pms-service/srm/price/coordination/audit/query/page", "POST", "application/json","SRM-代销业务-供货价调整--供货价审核记录列表"),
        priceTerminate( "/pms-service/srm/price/coordination/upsert/terminate", "POST", "application/json","SRM-代销业务-供货价调整--供货价审批撤销"),
        packupPoQuerg( "pms-service/srm/packupPo/query/page", "POST", "application/json","SRM-代销业务-订单管理--列表"),
        supplySkuListExport( "/pms-service/srm/stockPackup/export/supply-sku-list", "POST", "application/json","SRM-代销业务-库存管理--导出供应商供货商品列表"),
        stockPackupExport( "/pms-service/srm/stockPackup/export", "POST", "application/json","SRM-代销业务-库存管理--导出");



        private final String url;
        private final String method;
        private final String contentType;
        private final String description;
        SrmPcEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
    //价格中心佣金相关接口
    @Getter
    enum PriceCenterCommissionEnum {

        queryPage( "/pms-service/price-config/query/page", "POST", "application/json","价格中心佣金--列表接口"),
        add( "pms-service/price-config/upsert/add", "POST", "application/json","价格中心佣金--列表接口"),
        delete( "pms-service/price-config/upsert/delete", "POST", "application/json","价格中心佣金--作废报价单");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;
        PriceCenterCommissionEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }


    //价格中心相关接口
    @Getter
    enum PriceCenterEnum {

        queryPage( "/pms-service/price-center/query/page", "POST", "application/json","价格中心--查询报价单分页列表"),
        add( "/pms-service/price-center/upsert/add", "POST", "application/json","价格中心--新增报价单"),
        invalid( "/pms-service/price-center/upsert/invalid", "POST", "application/json","价格中心--作废报价单"),
        queryAllPrice( "/pms-service/price-center/query/all-type-price", "POST", "application/json","价格中心--查询商品所有类型的生效价格"),
        queryWithSku( "/pms-service/price-center/query/cost-detail/with-sku", "POST", "application/json","价格中心--根据sku仓库供应商获取成本明细"),
        queryWithId( "/pms-service/price-center/query/cost-detail/with-offerId", "POST", "application/json","价格中心--根据报价单获取成本明细");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;
        PriceCenterEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    //供货目录相关
    @Getter
    enum SupplyListEnum{
        queryPage( "/pms-service/supply-list/query/page", "POST", "application/json","供货目录--列表分页接口"),
        queryList( "/pms-service/supply-list/query/list", "POST", "application/json","供货目录--根据供货目录三要素（或四要素）查询列表"),
        add( "pms-service/supply-list/upsert/add", "POST", "application/json","供货目录--新增接口"),
        upsertUpdate( "/pms-service/supply-list/upsert/update", "POST", "application/json","供货目录--更新"),
        upsertSupplier( "/pms-service/supply-list/update/supplier", "POST", "application/json","供货目录--变更供应商"),
        delete( "/pms-service/supply-list/upsert/delete", "POST", "application/json","供货目录--删除供货配置"),
        supplyListiImport( "/pms-service/supply-list/import", "POST", "application/json","供货目录--导入");



        private final String url;
        private final String method;
        private final String contentType;
        private final String description;
        SupplyListEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 直发采购相关接口枚举
     */
    @Getter
    enum DirectPurchaseEnum {
        // 新建直发采购单
        placeOrder("/directpurchase/order/placeOrder", "POST", "application/json", "新建直发采购单"),
        // 查询采购单详情
        queryDetail("/pms-service/po/query/detail", "POST", "application/json", "查询采购单详情"),
        // 查询直发采购对应直发订单详情
        queryDirectOrderDetail("/directpurchase/detail-info/", "GET", "application/json", "查询直发采购对应直发订单详情"),
        // 绑定预付金额
        advanceBinding("/pms-service/po/advance/binding", "POST", "application/json", "绑定预付金额"),
        // 一键发货
        oneClickDelivery("/pms-service/po/upsert/oneClickDelivery", "POST", "application/json", "一键发货"),
        // 批量获取生效中阶梯价格
        getStepPriceBatch("/pms-service/srm-supplier-offer/select/StepPrice/batch", "POST", "application/json", "批量获取生效中阶梯价格"),
        // 解绑预付金额
        advanceUnbinding("/pms-service/po/advance/unbundling", "POST", "application/json", "解绑预付金额"),
        queryPaymentBySupplier("/pms-service/po/queryPaymentBySupplier", "POST", "application/json", "查询采购单及供应商的付款信息"),
        exportDetail("/pms-service/po/export-detail", "POST", "application/json", "直发采购详情导出"),
        uploadV2("/pms-service/saas-purchase/upload/purchases-template/v2", "POST", "multipart/form-data", "直发采购导入");


        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        DirectPurchaseEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 仓库入库相关接口枚举
     */
    @Getter
    enum WmsInStockEnum {
        createInBound("/summerfarm-wms/inbound/create", "POST", "application/json", "创建入库单接口"),
        queryTaskDetail("/summerfarm-wms/in-store/query/detail", "POST", "application/json", "查询入库任务详情");


        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        WmsInStockEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

}