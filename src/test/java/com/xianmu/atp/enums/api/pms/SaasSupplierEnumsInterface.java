package com.xianmu.atp.enums.api.pms;

import lombok.Getter;

/**
 * SaaS供应商相关接口枚举定义
 * 管理供应商、供应商关系、采购价格等API路由信息
 * 
 * <AUTHOR> Test Engineer
 */
public class SaasSupplierEnumsInterface {

    /**
     * SaaS供应商管理相关接口
     */
    @Getter
    public enum SaasSupplierEnum {
        // 新增&编辑供应商
        saveOrUpdate("/pms-service/saas-supplier/saveOrUpdate", "POST", "application/json", "新增&编辑供应商"),
        // 供应商详情接口
        detail("/pms-service/saas-supplier/detail", "POST", "application/json", "供应商详情接口"),
        // 供应商列表查询接口
        queryPage("/pms-service/saas/supplier/query/page", "POST", "application/json", "供应商列表查询接口"),
        // 导出供应商接口
        export("/pms-service/saas/supplier/query/export", "POST", "application/json", "导出供应商接口"),
        pendingMatter("/order/pending/matter", "GET", "application/json", "工作台--待办事项");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        SaasSupplierEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 供应商关系管理相关接口
     */
    @Getter
    public enum SupplyRelationEnum {
        // 添加合作货品接口
        upsert("/pms-service/saas/supply/relation/upsert", "POST", "application/json", "添加合作货品接口"),
        // 删除合作货品接口
        batchDel("/pms-service/saas/supply/relation/batch/del", "POST", "application/json", "删除合作货品接口"),
        // 查询合作货品列表接口
        queryPage("/pms-service/saas/supply/relation/query/page", "POST", "application/json", "查询合作货品列表接口");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        SupplyRelationEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 采购价格管理相关接口
     */
    @Getter
    public enum PurchasesPriceEnum {
        // 查询采购价格列表
        list("/pms-service/purchases-price/list", "POST", "application/json", "查询采购价格列表"),
        // 获取商品税率接口
        getTaxRate("/pms-service/purchases-price/getTaxRate", "POST", "application/json", "获取商品税率接口"),
        // 获取价格有效期接口
        getTimeList("/pms-service/purchases-price/getTimeList", "POST", "application/json", "获取价格有效期接口"),
        // 新增采购价格接口
        save("/pms-service/purchases-price/save", "POST", "application/json", "新增采购价格接口"),
        // 编辑采购价格接口
        update("/pms-service/purchases-price/update", "POST", "application/json", "编辑采购价格接口"),
        // 导出采购价格接口
        export("/pms-service/purchases-price/export", "POST", "application/json", "导出采购价格接口");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        PurchasesPriceEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

    /**
     * 供应商导入相关接口
     */
    @Getter
    public enum SupplierImportEnum {
        // 导入供应商
        importOssUrl("/common-service/download-center/import/oss-url", "POST", "application/json", "导入供应商"),
        // 获取供应商导入结果
        queryImportResult("/common-service/download-center/query/import-result", "POST", "application/json", "获取供应商导入结果");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        SupplierImportEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
}