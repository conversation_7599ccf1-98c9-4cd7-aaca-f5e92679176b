package com.xianmu.atp.enums.api.bms;

import lombok.Getter;

public interface FmsEnumsInterface {
    @Getter
    enum PaymentEnums {
        upload_payment_voucher("/bms-service/fms/upsert/upload-payment-voucher", "POST", "application/json","付款单上传凭证"),
        saveSettlementConfig("/settlementConfig/saveOrUpdate", "POST", "application/json","设置打款人"),
        payment_list("finance-purchase/purchase/payment/list/1/10", "GET", null,"付款单列表");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        PaymentEnums(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }

}
