package com.xianmu.atp.enums;

public enum MallUserEnum {
    mallAutoUser("商城默认自动化","13732254859",350935),
    mallAutoUser2("第二个自动化用户","15755642759",349375 ),

    mallAutoUser3("订单价格测试自动化用户","13732229911",352599 ),
    bigUser("大客户自动化用户","13018956732",351900),
    popUser("pop自动化用户","13214254364",352276),

    deliveryFeeUser("配送费测试用户","10010011002",353666),

    deliveryFeeBigUser("配送费测试大客户用户","10010001002",353667),
    ;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getMid() {
        return mid;
    }

    public void setMid(int mid) {
        this.mid = mid;
    }

    private String name;
    private String phone;

    private int mid;

    MallUserEnum(String name, String phone, int mid) {
        this.name = name;
        this.phone = phone;
        this.mid = mid;
    }
}
