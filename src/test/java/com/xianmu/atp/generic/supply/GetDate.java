package com.xianmu.atp.generic.supply;

import com.xianmu.atp.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Component
@Slf4j
public class GetDate extends BaseTest {
    public String GetTodayDate(){
        //当天日期
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        Date nextDate = calendar.getTime();
        // 格式化日期为 yyyy-MM-dd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(nextDate);
    }
    public String GetTomorrowDate(){
        //次日日期
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 当前日期加 1 天
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        // 获取加 1 天后的日期
        Date nextDate = calendar.getTime();
        // 格式化日期为 yyyy-MM-dd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(nextDate);
    }

    public String GetDesignatedDate(Integer num){
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 当前日期加 1 天
        calendar.add(Calendar.DAY_OF_MONTH, num);
        // 获取加 1 天后的日期
        Date nextDate = calendar.getTime();
        // 格式化日期为 yyyy-MM-dd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(nextDate);
    }
    public void Sleep(Integer m) {
        try {
            // 使当前线程休眠 2000 毫秒（即 2 秒）
            Thread.sleep(m);
        } catch (InterruptedException e) {
            // 处理线程被中断的情况
            System.err.println("线程被中断: " + e.getMessage());
        }
    }
}
