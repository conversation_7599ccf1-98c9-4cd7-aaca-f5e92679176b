package com.xianmu.atp.generic.supply;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;

import com.alibaba.fastjson.JSONArray;

import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.PurchasesDao;
import com.xianmu.atp.dal.dao.PurchasesDao;
import com.xianmu.atp.dal.dao.WarehouseStorageCenterDao;
import com.xianmu.atp.dal.dao.pms.SupplierDao;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Allure;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


@Component
@Slf4j
public class Purchase extends BaseTest {
    @Value("${app.manage-domain}")
    private String domain;
    @Resource
    private WarehouseStorageCenterDao warehouseStorageCenterDao;
    @Resource
    private LoginHttp login;
    @Resource
    private SupplierDao supplierDao;
    @Resource
    private PurchasesDao purchasesDao;
    public String PurchaseCreate(String skus,String warehouseNo,Integer quantity,String supplierId){
        String selectSkuURL =domain +
                "/inventory/selectLikeBySkuOrName";
        String supplyListURL =domain +
                "/pms-service/supply-list/query/page";
        String token = login.login();
        log.info("采购单创建开始");
        if ((quantity==null)|| quantity <= 0) {
            return "false";
        }
        if (supplierId.isEmpty()){
            supplierId = "4610";
        }
        List<Supplier> supplierInfo =  supplierDao.getSupplierInfo(supplierId);
        String supplierName = supplierInfo.get(0).getName();
        String warehouseName = warehouseStorageCenterDao.getWarehouseName(warehouseNo);
        String[] sku = skus.split(",");
        Date currentDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormat.format(currentDate);
        JSONArray purchasesPlanResultVOS = new JSONArray();
        for (int i = 0; i < sku.length; i++) {
            if (StringUtils.isEmpty(sku[0]) || StringUtils.isEmpty(warehouseNo)) {
                return "false";
            }
            String skuBody = "{\n" +
                    "\"queryStr\":\""+sku[i]+"\",\n" +
                    "\"warehouseNo\":\""+warehouseNo+"\"\n" +
                    "}";
            HttpResponse response = HttpRequest.post(selectSkuURL)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(skuBody)
                    .execute();
            log.info(response.body());
            JSONArray skuInfos =new JSONArray();
            try {
                String listStr = JSON.parseObject(JSON.parseObject(response.body()).get("data").toString()).getString("list");
                skuInfos = JSONArray.parseArray(listStr);
                if (skuInfos.isEmpty()) {
                    return sku[i] + "这个SKU输入有误,未查询到此商品";
                }
            }catch (JSONException e) {
                e.printStackTrace();
            }
            String skuInfo = skuInfos.get(0).toString();
            String pdNo = JSON.parseObject(skuInfo).getString("pdNo");
            String pdName = JSON.parseObject(skuInfo).getString("productName");
            String spuBody = "{\n" +
                    "\t\"pageIndex\": 1,\n" +
                    "\t\"pageSize\": 10,\n" +
                    "\t\"spu\": \"" + pdNo + "\",\n" +
                    "\t\"supplierIds\": ["+supplierId+"],\n" +
                    "\t\"warehouseNos\": [" + warehouseNo + "]\n" +
                    "}";
            HttpResponse supplyListResponse = HttpRequest.post(supplyListURL)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(spuBody)
                    .execute();
            log.info(supplyListResponse.body());
            JSONArray spuInfos = new JSONArray();
            try {
                String spuStr = JSON.parseObject(JSON.parseObject(supplyListResponse.body()).get("data").toString()).getString("list");
                spuInfos = JSONArray.parseArray(spuStr);

            }catch (JSONException e) {
                e.printStackTrace();
            }

            if (spuInfos.size()==0){
                String supplyResult="{\n" +
                        "    \"supplierName\": \""+supplierName+"\",\n" +
                        "    \"supplierId\": "+supplierId+",\n" +
                        "    \"advanceDay\": 1,\n" +
                        "    \"spu\": \""+pdNo+"\",\n" +
                        "    \"defaultSupplier\": 0,\n" +
                        "    \"channelType\": 1,\n" +
                        "    \"pdName\": \""+pdName+"\",\n" +
                        "    \"warehouseName\": \""+warehouseName+"\",\n" +
                        "    \"orderModel\": 1,\n" +
                        "    \"warehouseNo\": \""+warehouseNo+"\"\n" +
                        "}";
                String addSupplyURL =domain + "/pms-service/supply-list/upsert/add";
                HttpResponse addSupplyResponse = HttpRequest.post(addSupplyURL)
                        .header(Header.CONTENT_TYPE, "application/json")
                        .header("token", token)
                        .body(supplyResult)
                        .execute();
                log.info(addSupplyResponse.body());
            }

            if (spuInfos.size() > 0) {
                JSONObject spuInfo = JSON.parseObject(spuInfos.getString(0));
                supplierId = spuInfo.getString("supplierId");
                supplierName = spuInfo.getString("supplierName");
                //获取供货信息 供应商
            }
            log.info("设置供货协同配置");
            String coordinationUrl =domain+
                    "/pms-service/supplier/upsert/update-coordination";
            String coordinationBody ="{\n" +
                    "                \"supplierId\":"+supplierId+",\n" +
                    "                \"poCoordinationTab\": 0,\n" +
                    "                    \"inboundCoordinationTab\": 0,\n" +
                    "                    \"reconciliationCoordinationTab\": 1,\n" +
                    "                    \"invoiceCoordinationTab\": 0\n" +
                    "            }";
            HttpResponse coordinationResponse = HttpRequest.post(coordinationUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(coordinationBody)
                    .execute();
            log.info(coordinationResponse.body());

            log.info("发布采购单");
            String weight = JSON.parseObject(skuInfo).getString("weight");
            String purchasesPlanResult = "{\n" +
                    "            \"latestArrivalDate\": \"" + formattedDate + "\",\n" +
                    "            \"price\": 120,\n" +
                    "            \"checkReport\": \"\",\n" +
                    "            \"qualityTime\": 2,\n" +
                    "            \"qualityTimeUnit\": \"\",\n" +
                    "            \"purchaseNo\": \"\",\n" +
                    "            \"purchasesPlans\": [\n" +
                    "                {\n" +
                    "                    \"checkReport\": \"\",\n" +
                    "                    \"qualityDate\": \"\",\n" +
                    "                    \"productionDate\": \"\",\n" +
                    "                    \"inQuantity\": 0,\n" +
                    "                    \"supplierId\": \"\",\n" +
                    "                    \"priceType\": 0,\n" +
                    "                    \"latestArrivalDate\": \"" + formattedDate + "\"\n" +
                    "                }\n" +
                    "            ],\n" +
                    "            \"quantity\": "+quantity+",\n" +
                    "            \"singlePlice\": 1.2,\n" +
                    "            \"sku\": \"" + sku[i] + "\",\n" +
                    "            \"title\": \"" + pdName + "\",\n" +
                    "            \"weight\": \"" + weight + "\",\n" +
                    "            \"pdName\": \"" + pdName + "\",\n" +
                    "            \"extType\": 0,\n" +
                    "            \"isSave\": false,\n" +
                    "            \"isNew\": true,\n" +
                    "            \"type\": 0,\n" +
                    "            \"advQuantity\": 0,\n" +
                    "            \"minPrice\": 100,\n" +
                    "            \"maxPrice\": 100,\n" +
                    "            \"priceType\": 0,\n" +
                    "            \"priceLapse\": false,\n" +
                    "            \"pdNo\": \"" + pdNo + "\",\n" +
                    "            \"netWeight\": 2,\n" +
                    "            \"supplier\": \"" + supplierName + "\",\n" +
                    "            \"supplierName\": \"" + supplierName + "\",\n" +
                    "            \"supplierId\": " + supplierId + ",\n" +
                    "            \"showTip\": false,\n" +
                    "            \"priceHint\": false,\n" +
                    "            \"roadSaleRatio\": 0\n" +
                    "        }";

            JSONObject purchasesPlanResultVO =JSON.parseObject(purchasesPlanResult);
            purchasesPlanResultVOS.add(purchasesPlanResultVO);
        }

        String remark = String.valueOf(System.currentTimeMillis());
        String addSupply="{\n" +
                "    \"purchaseNo\": \"\",\n" +
                "    \"purchasePlace\": \"\",\n" +
                "    \"purchaser\": \"测试哈哈\",\n" +
                "    \"receiver\": \"测试哈哈\",\n" +
                "    \"remark\": \""+remark+"\",\n" +
                "    \"purchaseTime\": \""+formattedDate+"\",\n" +
                "    \"state\": 1,\n" +
                "    \"purchasesPlanResultVOS\":         "+purchasesPlanResultVOS.toString()+",\n" +
                "    \"purchasesType\": 0,\n" +
                "    \"arrangeTime\": \""+formattedDate+"\",\n" +
                "    \"arrangeRemark\": \"\",\n" +
                "    \"creatorId\": 123,\n" +
                "    \"deliveryTime\": \"\",\n" +
                "    \"tmsDistSiteName\": \"\",\n" +
                "    \"deliveryAddress\": \"\",\n" +
                "    \"tmsDistSiteId\": \"\",\n" +
                "    \"areaNo\": "+warehouseNo+",\n" +
                "    \"operatorId\": 123\n" +
                "}";
        String addPurchaseURL =domain+"/pms-service/po/upsert/add";
        HttpResponse addPurchaseResponse = HttpRequest.post(addPurchaseURL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(addSupply)
                .execute();
        log.info(addPurchaseResponse.body());
        String purchaseNo= purchasesDao.getPurchasesNo(remark);
        log.info("查询是否需要审批");
        if (purchaseNo.isEmpty())
            return null;
        return purchaseNo;
    }
}
