package com.xianmu.atp.generic.supply;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.*;
import com.xianmu.atp.dal.model.*;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Allure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Assert;
import org.testng.Reporter;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


@Component
@Slf4j
public class OfcOrderAssert extends BaseTest {
    @Resource
    private OfcOrderDao ofcOrderDao;
    @Resource
    private DeliveryPlanDao deliveryPlanDao;
    @Resource
    private WmsStockTaskNoticeOrderDao wmsStockTaskNoticeOrderDao;
    @Resource
    private GoodsSupplyOrderDao goodsSupplyOrderDao;
    @Resource
    private GoodsRecycleOrderDao goodsRecycleOrderDao;
    @Resource
    private  WmsStockTaskStorageNoticeOrderDao wmsStockTaskStorageNoticeOrderDao;
    @Resource
    private LoginHttp login;
    @Value("${app.manage-domain}")
    private String domain;
    @Value("${app.environment}")
    private String env;

    @Resource
    private GetDate getDate;
    /**
     * OFC订单的通用断言
     * @param orderNo
     */
    public void ofcOrderAssert(String orderNo){
        getDate.Sleep(2000);
        String orderListUrl =domain+ "/summerfarm-ofc/fulfillment/query/list";
        String token = login.login();
        log.info("token:{}", token);
        //查询履约表
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        getDate.Sleep(3000);
        //查看履约列表数据
        String orderListBody = "{\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 10,\n" +
                "\t\"outOrderNo\": \""+orderNo+"\"\n" +
                "}";
        HttpResponse response = HttpRequest.post(orderListUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(orderListBody)
                .execute();
        Reporter.log("log-test");
        log.info(JSON.parseObject(response.body()).getJSONObject("data").toString());
        Assert.assertEquals(JSON.parseObject(response.body()).getJSONObject("data").get("total").toString() , "1");
        //履约单断言
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getDeliveryTime().toString(),ofcOrders.get(0).getFulfillmentTime().toString());
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),10);
        Assert.assertEquals(ofcOrders.get(0).getOutContactId().toString(),deliveryOrder.get(0).getContactId().toString());
        //货品供应单 //WMS的出库通知单
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getStatus().toString(),"2");
        List<WmsStockTaskNoticeOrder> wmsStockTaskNoticeOrders = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getSupplyOrderNo(),wmsStockTaskNoticeOrders.get(0).getGoodsSupplyNo());
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getIsDeleted().toString(),"0");

        //TMS的承运单状态
    }

    /**
     * 断言关单后的OFC的一些状态
     * @param orderNo
     */
    public void closeOrderAssert(String orderNo){
        getDate.Sleep(2000);
        //OFC 履约单状态断言
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Assert.assertEquals(ofcOrders.get(0).getStatus(),-1);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),51);
        //OFC 货品供应单状态断言
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getDataStatus().toString(),"-1");
        //WMS出库通知单断言
        List<WmsStockTaskNoticeOrder> wmsStockTaskNoticeOrders = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getIsDeleted().toString(),"1");
    }

    /**
     * 断言修改配送日期
     * @param orderNo
     * @param updateDate
     */
    public void updateOrderAssert(String orderNo,String updateDate){
        getDate.Sleep(2000);
        //OFC 履约单状态断言
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),10);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentTime().toString(),updateDate);
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getStatus().toString(),"2");
        Assert.assertEquals(goodsSupplyOrder.get(0).getScheduleDate().toString(),updateDate+"T00:00");
        List<WmsStockTaskNoticeOrder> wmsStockTaskNoticeOrders = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getIsDeleted().toString(),"0");
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getExceptTime().toString(),updateDate+"T00:00");
    }

    /**
     * 订单自提后的断言
     * @param orderNo
     */
    public void shelfPickUpAssert(String orderNo){
        getDate.Sleep(2000);
        Date currentDate = new Date();

        // 格式化日期为 "yyyy-MM-dd"
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormat.format(currentDate);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),44);
        Assert.assertEquals(ofcOrders.get(0).getExpectArriveTime().toString(),formattedDate+" 23:59:59.0");
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getType().toString(),"2");
        Assert.assertEquals(goodsSupplyOrder.get(0).getScheduleDate().toString(),formattedDate+"T00:00");
        List<WmsStockTaskNoticeOrder> wmsStockTaskNoticeOrders = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getIsDeleted().toString(),"0");
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getExceptTime().toString(),formattedDate+"T23:59:59");
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getOutOrderType().toString(),"2");
        }

    /**
     * OFC发起拦截后的断言
     * @param orderNo
     */
    public void interceptAssert(String orderNo){
        getDate.Sleep(2000);
        //OFC履约单的断言
            List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
            Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),52);

            //WMS生成入库通知单的断言
        //入库通知单的断言
        List<WmsStockTaskStorageNoticeOrder> storageNoticeOrder = wmsStockTaskStorageNoticeOrderDao.getStorageNoticeInfo(orderNo);
        Assert.assertEquals(storageNoticeOrder.size(),1);
        }

    /**
     * OFC发起延迟配送后的断言
     * @param orderNo
     * @param updateDate
     */
    public void delayDeliveryAssert(String orderNo,String updateDate){
        getDate.Sleep(2000);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),10);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentTime().toString(),updateDate);
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getStatus().toString(),"2");
        Assert.assertEquals(goodsSupplyOrder.get(0).getScheduleDate().toString(),updateDate+"T00:00");
        List<WmsStockTaskNoticeOrder> wmsStockTaskNoticeOrders = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getIsDeleted().toString(),"0");
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getExceptTime().toString(),updateDate+"T00:00");
        List<GoodsRecycleOrder> goodsRecycleOrder = goodsRecycleOrderDao.getGoodsRecycleOrder(orderNo);
        Assert.assertEquals(goodsRecycleOrder.size(),1);
        //入库通知单的断言
        List<WmsStockTaskStorageNoticeOrder> storageNoticeOrder = wmsStockTaskStorageNoticeOrderDao.getStorageNoticeInfo(orderNo);
        Assert.assertEquals(storageNoticeOrder.size(),1);
    }

    /**
     * 加单断言
     * @param orderNo
     * @param today
     */
    public void addOrderAssert(String orderNo,String today){
        getDate.Sleep(2000);

        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentTime().toString(),today);
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getStatus().toString(),"2");
        Assert.assertEquals(goodsSupplyOrder.get(0).getScheduleDate().toString(),today+"T00:00");
        List<WmsStockTaskNoticeOrder> wmsStockTaskNoticeOrders = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getIsDeleted().toString(),"0");
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getExceptTime().toString(),today+"T00:00");
    }
    public void AfterSaleAssert(String orderNo){
        getDate.Sleep(2000);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),50);
        //OFC 货品供应单状态断言
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getDataStatus().toString(),"-1");
        //WMS出库通知单断言
        List<WmsStockTaskNoticeOrder> wmsStockTaskNoticeOrders = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getIsDeleted().toString(),"1");
        Assert.assertEquals(wmsStockTaskNoticeOrders.get(0).getStatus().toString(),"40");
    }
    /**
     * 出库任务生成后的关单断言，主要有异常订单生成，多出入库任务生成
     * @param orderNo
     */
    public void outTaskOfcCloseAssert(String orderNo){
        getDate.Sleep(2000);
//OFC 履约单状态断言
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Assert.assertEquals(ofcOrders.get(0).getStatus(),-1);
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),51);
        //OFC 货品供应单状态断言
        List<GoodsSupplyOrder> goodsSupplyOrder = goodsSupplyOrderDao.getGoodsSupplyOrder(orderNo);
        Assert.assertEquals(goodsSupplyOrder.get(0).getDataStatus().toString(),"-1");
        log.info("OFC异常订单断言");
    }

    public void ResendOrderAssert(String AfterOrder){
        String orderListUrl =domain+ "/summerfarm-ofc/fulfillment/query/list";
        getDate.Sleep(2000);
        String token = login.login();
        log.info("token:{}", token);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(AfterOrder);
        //查看履约列表数据
        String orderListBody = "{\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 10,\n" +
                "\t\"outOrderNo\": \""+AfterOrder+"\"\n" +
                "}";
        HttpResponse response = HttpRequest.post(orderListUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(orderListBody)
                .execute();
        Reporter.log("log-test");
        log.info(JSON.parseObject(response.body()).getJSONObject("data").toString());
        Assert.assertEquals(JSON.parseObject(response.body()).getJSONObject("data").get("total").toString() , "1");


    }


    /**
     * 下单代销不入仓的SKu并进行转采操作
     * @param phone
     * @param contactId
     * @param sku
     * @param quality
     * @param warehouseNo
     * @return
     */
    public String AgentOrderToOfc(String phone,Integer contactId,String sku,Integer quality,Integer warehouseNo){
        String orderListUrl =domain+ "/summerfarm-ofc/fulfillment/query/list";
        String skuAvailableUrl = domain
                +"/summerfarm-wms/notice/tool/test_query_av";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String skuAvailableBody ="{ \"sku\":\""+sku+"\",\"warehouseNo\":"+warehouseNo+"}";
        HttpResponse skuAvailableResponse = HttpRequest.post(skuAvailableUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(skuAvailableBody)
                .execute();
        Integer num = Integer.valueOf(JSON.parseObject(skuAvailableResponse.body()).getJSONObject("data").get("availableNum").toString());
        if (num>0)
            quality = num +quality;
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        Reporter.log("log-test");
        getDate.Sleep(5000);
        log.info("token:{}", token);
        //查询履约表
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        //查看履约列表数据
        String orderListBody = "{\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 10,\n" +
                "\t\"outOrderNo\": \""+orderNo+"\"\n" +
                "}";
        HttpResponse response = HttpRequest.post(orderListUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(orderListBody)
                .execute();
        Reporter.log("log-test");
        log.info(JSON.parseObject(response.body()).getJSONObject("data").toString());
        Assert.assertEquals(JSON.parseObject(response.body()).getJSONObject("data").get("total").toString() , "1");
        //履约单断言
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getDeliveryTime().toString(),ofcOrders.get(0).getFulfillmentTime().toString());
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),10);
        Assert.assertEquals(ofcOrders.get(0).getOutContactId().toString(),deliveryOrder.get(0).getContactId().toString());

        log.info("OFC销转采购操作");
        String saleToPurchaseUrl =domain+
                "/summerfarm-ofc/saleToPurchase/debug";

        String saleToPurchaseBody = "{\"pickupDate\": \""+getDate.GetTomorrowDate()+"\",\"warehouseNo\": "+warehouseNo+"}";
        HttpResponse saleToPurchaseResponse = HttpRequest.post(saleToPurchaseUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(saleToPurchaseBody)
                .execute();
        Reporter.log("log-test");
        log.info(saleToPurchaseResponse.body());
        Assert.assertEquals(JSON.parseObject(saleToPurchaseResponse.body()).get("status"),200);
        return orderNo;
    }
    }
