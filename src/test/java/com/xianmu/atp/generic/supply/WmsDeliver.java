package com.xianmu.atp.generic.supply;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.WmsStockTaskNoticeOrderDao;
import com.xianmu.atp.dal.model.WmsStockTaskNoticeOrder;
import com.xianmu.atp.util.http.LoginHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import com.alibaba.fastjson.JSONObject;

@Component
@Slf4j
public class WmsDeliver  extends BaseTest {
    @Value("${app.manage-domain}")
    private String domain;
    @Resource
    private LoginHttp login;
    @Resource
    private WmsStockTaskNoticeOrderDao wmsStockTaskNoticeOrderDao;

    /**
     * 出库任务生成
     * @param orderNo
     * @param isWave
     * @return
     */
    public Long outStockTask(String orderNo,Integer isWave,Integer isAgency){
        String OutTaskByNoticeURL =domain
                +"/summerfarm-wms/stock-task/genOutTaskByNotice";
        String token = login.login();
        //1、处理出库通知单的时间为T+1
        wmsStockTaskNoticeOrderDao.updateNoticeTime(orderNo);
        //2、查询出当前订单的城配仓和库存仓以及订单类型
        List<WmsStockTaskNoticeOrder> taskNoticeOrderInfo = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
        Integer warehouseNo = taskNoticeOrderInfo.get(0).getWarehouseNo();
        Integer storeNo = taskNoticeOrderInfo.get(0).getStoreNo();
        Integer outOrderType = taskNoticeOrderInfo.get(0).getOutOrderType();
        String deliveryDate = taskNoticeOrderInfo.get(0).getExceptTime().toString();
        //3、执行出库任务生成
        boolean waveFlag =false;boolean crossFlag =false;
        if (isWave==1){
            waveFlag =true;
        }
        if (isAgency ==1){
            crossFlag =true;
        }
        String noticeJson ="{\n" +
                "  \"warehouseNo\": "+warehouseNo+",\n" +
                "  \"storeNo\": "+storeNo+",\n" +
                "  \"deliveryDate\": \""+deliveryDate+"\",\n" +
                "  \"type\": "+outOrderType+",\n" +
                "  \"crossFlag\": "+crossFlag+",\n" +
                "  \"waveFlag\": "+waveFlag+"\n" +
                "}";
        HttpResponse noticeResponse = HttpRequest.post(OutTaskByNoticeURL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(noticeJson)
                .execute();
        log.info(noticeResponse.body());
        Long taskId = null;
        for (int i = 5; i > 0; i--) {
            try {
                // 使用 TimeUnit 让线程睡眠2秒
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            List<WmsStockTaskNoticeOrder> taskNoticeOrderInfo2 = wmsStockTaskNoticeOrderDao.getNoticeInfo(orderNo);
            taskId = taskNoticeOrderInfo2.get(0).getStockTaskId();
            if (taskId != null) {
                break;
            }
        }
        //4、查询出出库任务并返回
        return taskId;
    }
}
