package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */

@Slf4j
@Component
public class LadderPrice {

    private static final String LOGIN_URL = "https://qamanage.cosfo.cn/tenant/user/query/login";

    private static final String VERIFY_CODE_URL = "https://qamall.cosfo.cn/merchant/store/sendCode?phone=13900099909&tenantId=24514";

    private static final String H5_LOGIN_URL = "https://qamall.cosfo.cn/merchant/store/smsCodeLogin";

    private static final String GET_CODE_URL = "https://qamall.cosfo.cn/merchant/store/sendCode";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    @Value("https://qamanage.cosfo.cn/")
    private String domain2;


    public String update(String token) throws Exception{
        //编辑阶梯价
        String url = domain2 + "/market/item/upsert/upsert";
        String requestBody = "{\"goodsType\":0,\"marketId\":2047330,\"id\":58410,\"amount\":999999,\"onSale\":1,\"specificationUnit\":\"盒\",\"miniOrderQuantity\":1,\"itemCode\":\"11\",\"afterSaleUnit\":\"盒\",\"maxAfterSaleAmount\":1,\"itemSaleMode\":0,\"noGoodsSupplyPrice\":5,\"defaultPrice\":{\"areaItemId\":null,\"createTime\":null,\"differenceValue\":-5,\"id\":null,\"ladderPrices\":[{\"price\":20,\"unit\":1},{\"price\":10,\"unit\":2}],\"mappingNumber\":20,\"maxPrice\":null,\"merchantStoreDtoList\":[],\"minPrice\":null,\"priceType\":1,\"remark\":\"\",\"storeGroupIds\":[],\"storeGroupList\":[],\"storeIds\":[],\"storePriceType\":null,\"tenantId\":24514,\"totalStoreCount\":513,\"type\":2,\"updateTime\":null,\"_SupplyPrice\":5},\"storeGroupPrice\":[],\"storePrice\":[],\"marketItemUnfairPriceStrategyDTO\":{\"defaultFlag\":1,\"strategyType\":0},\"saleLimitQuantity\":null,\"saleLimitRule\":0,\"buyMultiple\":1,\"buyMultipleSwitch\":false,\"presaleSwitch\":0,\"storeInventoryControlFlag\":true,\"storeCostUnit\":\"mL\",\"storeOrderingInventoryUnitMultiple\":1,\"storeInventoryUnit\":\"mL\",\"storeInventoryCostUnitMultiple\":1,\"storeOrderingUnit\":\"盒\",\"standardUnitPrice\":1,\"weight\":11,\"supplierName\":\"测试供应商A\",\"supplierId\":\"3321\",\"specification\":\"0_1mL*1mL\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }


    public String placeOrder(String token, long itemId) throws Exception {
        log.info("无仓阶梯价订单-生成订单");
        String url = domain1 + "order/place-order";
        String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":2,\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":2,\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":null,\"warehouseType\":0,\"remark\":\"\",\"orderType\":0,\"combineMarketId\":null,\"combineItemId\":null}]";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        System.out.println("无仓订单号" + data);
        return data;
    }












}
