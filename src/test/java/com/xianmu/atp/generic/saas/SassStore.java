package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */

@Slf4j
@Component
public class SassStore{

    @Resource
    private TenantAccount tenantAccount;

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    public String save(String token)throws Exception{
        log.info("新增门店");
        //接口地址
        String url = domain + "merchant/store/save";
        String requestBody ="{\"placeOrderPermissionTimeLimited\":0,\"accountName\":\"\",\"address\":\"上城区\",\"area\":\"上城区\",\"city\":\"杭州市\",\"houseNumber\":\"101\",\"province\":\"浙江省\",\"registerTime\":\"\",\"status\":0,\"storeName\":\"" + NumberUtil.getNameRandom("测试门店")+"\",\"storeNo\":\"" + NumberUtil.getTimeNumber(6) + "\",\"accountList\":[{\"accountName\":\"" +
                NumberUtil.getNameRandom("测试员工")+"\",\"type\":0,\"phone\":\"" +
                NumberUtil.getPhoneNumber("134", 8)+"\",\"registerTime\":\"\"}],\"auditRemark\":\"\",\"auditTime\":\"\",\"onlinePayment\":1,\"contactList\":[{\"contactName\":\"" +
                NumberUtil.getNameRandom("测试联系人")+"\",\"defaultFlag\":0,\"phone\":\"" +
                NumberUtil.getPhoneNumber("138", 6)+"\"}],\"poiNote\":\"120.210792,30.246026\",\"groupId\":1063,\"balanceAuthority\":0,\"enableOfflinePayment\":0,\"enableDeliveryNotePrintPrice\":1,\"balance\":0,\"billSwitch\":0,\"type\":0,\"remark\":\"111\",\"auditFlag\":3}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String listAll(String token)throws Exception{
        log.info("查询门店列表");
        //接口地址
        String url = domain + "merchant/store/listAll";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String detail(String token, String storeId)throws Exception{
        log.info("查询门店详情");
        //接口地址
        String url = domain + "merchant/store/detail/"+storeId;
        HttpResponse response = HttpRequest.get(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String update(String token, String requestBody)throws Exception{
        log.info("编辑门店");
        //接口地址
        String url = domain + "merchant/store/update";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String adjustBalance(String token, String storeId)throws Exception{
        log.info("调整余额");
        //接口地址
        String url = domain + "merchant-store-balance/upsert/adjust-balance";
        //随机变动金额
        int changeBalance = NumberUtil.getRandomInt(-5, 5);
        //随机一种账户类型
        int accountType = NumberUtil.getRandomInt(0, 1);
        String requestBody;
        if(accountType == 0){
            requestBody ="{\"changeBalance\":"+changeBalance+",\"remark\":\"111\",\"proof\":\"test/qq0ogw6tkhmxez7vf.jpg\",\"storeId\":"+storeId+",\"accountType\":0}";
        }else{
            //获取一个子类型
            String fundAccountId = NumberUtil.getListFirstId(tenantAccount.fundAccountPage(token));
            requestBody ="{\"changeBalance\":"+changeBalance+",\"remark\":\"111\",\"proof\":\"test/qq0ogw6tkhmxez7vf.jpg\",\"storeId\":"+storeId+",\"fundAccountId\":"+fundAccountId+",\"accountType\":1}";
        }
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String deleteContact(String token, String contactId, String storeId)throws Exception{
        log.info("删除联系人");
        //接口地址
        String url = domain + "merchant/contact/delete?contactId="+contactId+"&storeId="+storeId;
        HttpResponse response = HttpRequest.delete(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String deleteAccount(String token, String accountId)throws Exception{
        log.info("删除员工");
        //接口地址
        String url = domain + "merchant-store-account/upsert/delete-account?id=" + accountId;
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public void addStoreGroup(String token, String storeId)throws Exception{
        log.info("新增门店分组");
        //接口地址
        String url = domain + "merchant/store/group/upsert/add";
        //入参
        String requestBody = "{\"name\":\""+NumberUtil.getNameRandom("测试分组")+"\",\"storeId\":["+storeId+"]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
    }

    public String listAllDefault(String token)throws Exception{
        log.info("可管理分组门店列表");
        //接口地址
        String url = domain + "merchant/store/group/query/list-all-default";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String groupList(String token)throws Exception{
        log.info("门店分组列表");
        //接口地址
        String url = domain + "merchant/store/group/query/list";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String storeListByGroupId(String token, String groupId)throws Exception{
        log.info("门店分组下关联的门店列表");
        //接口地址
        String url = domain + "merchant/store/group/query/list-by-group-id";
        String requestBody ="{\"id\":"+groupId+"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public void updateStoreGroup(String token, long [] storeIds, String groupName, String id)throws Exception{
        log.info("门店分组编辑");
        //接口地址
        String url = domain + "merchant/store/group/upsert/update-by-group-id";
        String requestBody ="{\"id\":"+id+",\"name\":\""+groupName+"\",\"storeId\":"+ Arrays.toString(storeIds) +"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
    }

    public void delStoreGroup(String token, String groupId)throws Exception{
        log.info("门店分组删除");
        //接口地址
        String url = domain + "merchant/store/group/upsert/delete";
        String requestBody ="{\"id\":"+groupId+"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
    }

}
