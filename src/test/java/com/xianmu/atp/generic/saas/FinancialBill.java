package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import com.xianmu.atp.cases.saas.tools.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */

@Slf4j
@Component
public class FinancialBill{


    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    public String list(String token ,String createBillStartTime,String createBillEndTime)throws Exception{
        log.info("账期应收待收款列表");
        //接口地址
        String url = domain + "storeBill/list";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"status\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String addCredentials(String token ,String billId)throws Exception{
        log.info("账期应收待收款列表-收款上传凭证");
        //接口地址
        String url = domain + "storeBill/add/tenant/credentials";
        String requestBody ="{\"remark\":\"44\",\"credentials\":\"test/r8khzmsjjv4iwwx8.jpg\",\"billId\":"+billId+"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String confirm(String token ,String billId)throws Exception{
        log.info("账期应收待收款列表-收款");
        //接口地址
        String url = domain + "storeBill/confirm?billId="+billId;
        String requestBody ="{}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String detail(String token, String billId)throws Exception{
        log.info("账期应收待收款列表-查看明细-订单");
        //接口地址
        String url = domain + "storeBill/order/detail?billId="+billId;
        HttpResponse response = HttpRequest.get(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String orderAfterSaleDetail(String token, String billId)throws Exception{
        log.info("账期应收待收款列表-查看明细-售后");
        //接口地址
        String url = domain + "storeBill/orderAfterSale/detail?billId="+billId;
        HttpResponse response = HttpRequest.get(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String storeBillTotalData(String token)throws Exception{
        log.info("账期应收-数据合计");
        //接口地址
        String url = domain + "storeBill/totalData";
        //入参
        String startTime = TimeUtil.getTimePeriodByDay(-90);
        String endTime = LocalDate.now().toString();
        String requestBody ="{\"createBillStartTime\":\""+startTime+"\",\"createBillEndTime\":\""+endTime+"\",\"status\":1}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportSettlement(String token)throws Exception{
        log.info("结算明细-导出结算明细");
        //接口地址
        String url = domain + "financial/export/export-settlement";
        //入参
        String startTime = TimeUtil.getTimePeriodByMonth(-1);
        String endTime = LocalDate.now().toString();
        String requestBody ="{\"startTime\":\""+startTime+" 00:00:00\",\"endTime\":\""+endTime+" 23:59:59\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String queryProfitCount(String token)throws Exception{
        log.info("查询分账明细-分账金额合计");
        //接口地址
        String url = domain + "financial/query/profit-count";
        //入参
        String startTime = TimeUtil.getTimePeriodByMonth(-1);
        String endTime = LocalDate.now().toString();
        String requestBody ="{\"startTime\":\""+startTime+"\",\"endTime\":\""+endTime+"\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String querySettlementCount(String token)throws Exception{
        log.info("查询结算明细金额合计");
        //接口地址
        String url = domain + "financial/query/settlement-count";
        //入参
        String startTime = TimeUtil.getTimePeriodByMonth(-1);
        String endTime = LocalDate.now().toString();
        String requestBody ="{\"startTime\":\""+startTime+"\",\"endTime\":\""+endTime+"\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String prepaymentList(String token)throws Exception{
        log.info("预付概况列表");
        //接口地址
        String url = domain + "prepayment/record/query/record";
        //入参
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String prepaymentDetail(String token, String id)throws Exception{
        log.info("预付概率详情");
        //接口地址
        String url = domain + "prepayment/record/query/detail";
        //入参
        String requestBody ="{\"id\":\""+id+"\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String prepaymentDownload(String token)throws Exception{
        log.info("导出预付明细");
        //接口地址
        String url = domain + "prepayment/record/query/download";
        String requestBody ="{}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(10000)
                .execute();
        String body = response.body();
        System.out.println(response);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

}
