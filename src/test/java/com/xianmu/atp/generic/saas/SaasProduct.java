package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Assert;


/**
 * Saas商品货品通用方法
 * <AUTHOR>
 * @ClassName SaasProduct
 * @date 2025-1-08
 */
@Slf4j
@Component
public class SaasProduct {


    @Value("https://qamanage.cosfo.cn/")
    private String domain;


    public String addGoods(String token) throws Exception{
        log.info("帆台新建货品");
        String url = domain + "product/upsert/add";
        String requestBody = "{\"title\":\""+ NumberUtil.getNameRandom("自动化商品") +"\",\"categoryId\":\"50\",\"mainPicture\":\"test/dr2j0bjp2erqohnab.png\",\"storageLocation\":\"1\",\"storageTemperature\":\"18-22\",\"brandName\":\"111\",\"guaranteePeriod\":\"100\",\"guaranteeUnit\":\"2\",\"origin\":\"杭州\",\"productSkuAddInputList\":[{\"placeType\":0,\"specificationUnit\":\"瓶\",\"weight\":22,\"specificationType\":0,\"specification\":\"1盒*2箱\",\"volume\":\"0.020*0.020*0.020\",\"volumeUnit\":1000,\"customSkuCode\":\"3\",\"taxRateValue\":0}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        Assert.assertEquals(jsonResponse.getString("status"), "200");
        Assert.assertTrue(body.contains("请求成功"));
        return data;

    }

    public String addMarket(String token) throws Exception{
        log.info("帆台新增spu");
        String url = domain + "market/upsert/add";
        String requestBody = "{\"title\":\""+ NumberUtil.getNameRandom("自动化商品") +"\",\"classificationId\":1829,\"mainPicture\":\"test/dr2j0bjp2erqohnab.png\",\"detailPicture\":\"\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return JSON.parseObject(response.body()).getJSONObject("data").getString("marketId");
    }

    public String addMarketItem(String token, String marketId) throws Exception{
        log.info("帆台新增无仓sku");
        String url = domain + "market/item/upsert/add";
        String requestBody = "{\"goodsType\":0,\"marketId\":"+marketId+",\"amount\":100,\"onSale\":1,\"specificationUnit\":\"盒\",\"miniOrderQuantity\":1,\"afterSaleUnit\":\"盒\",\"maxAfterSaleAmount\":1,\"itemSaleMode\":0,\"noGoodsSupplyPrice\":1,\"defaultPrice\":{\"priceType\":1,\"type\":2,\"mappingNumber\":1,\"totalStoreCount\":4276,\"differenceValue\":0},\"storeGroupPrice\":[],\"storePrice\":[],\"marketItemUnfairPriceStrategyDTO\":{\"defaultFlag\":1,\"strategyType\":1},\"saleLimitRule\":0,\"buyMultiple\":1,\"buyMultipleSwitch\":false,\"presaleSwitch\":0,\"storeInventoryControlFlag\":false,\"storeCostUnit\":\"mL\",\"storeOrderingInventoryUnitMultiple\":1,\"storeInventoryUnit\":\"mL\",\"storeInventoryCostUnitMultiple\":1,\"storeOrderingUnit\":\"盒\",\"standardUnitPrice\":1,\"supplierName\":\"测试供应商A\",\"supplierId\":3321,\"specification\":\"0_1mL*1mL\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        Assert.assertEquals(jsonResponse.getString("status"), "200");
        return data;
    }

    public String addMarket1(String token) throws Exception{
        log.info("帆台新增自营仓sku");
        String url = domain + "market/upsert/add";
        String requestBody = "{\"title\":\""+ NumberUtil.getNameRandom("自动化商品") +"\",\"classificationId\":1830,\"mainPicture\":\"https://azure.cosfo.cn/test/p676c0zrzqgqohnab.png\",\"detailPicture\":\"\",\"MarketItemInput\":{\"goodsType\":2,\"specificationType\":0,\"showWeight\":false,\"specificationUnit\":\"盒\",\"specification\":\"1箱*1盒\",\"miniOrderQuantity\":1,\"itemSaleMode\":0,\"quantityType\":1,\"onSale\":1,\"classificationId\":[],\"skuList\":[{\"afterSaleUnit\":\"盒\",\"agentStatus\":3,\"brandId\":null,\"brandName\":\"喜茶\",\"category\":\"辅料\",\"categoryId\":50,\"cityNum\":null,\"customSkuCode\":\"1\",\"enabledTotalQuantity\":null,\"endTime\":null,\"guaranteePeriod\":100,\"guaranteeUnit\":2,\"id\":123119,\"mainPicture\":\"https://azure.cosfo.cn/test/p676c0zrzqgqohnab.png\",\"maxAfterSaleAmount\":1,\"maxPrice\":null,\"minPrice\":null,\"origin\":\"杭州\",\"price\":null,\"priceStr\":\"\",\"productAgentWarehouseDataVOS\":[],\"productSupplyPriceId\":null,\"secondaryCategory\":\"冻面团\",\"sku\":\"50857440022\",\"specification\":\"1箱*1盒\",\"specificationUnit\":\"盒\",\"spuId\":131207,\"startTime\":null,\"storageLocation\":0,\"storageTemperature\":\"18-22\",\"supplySkuId\":123119,\"tenantId\":24514,\"thirdCategory\":\"辅料的二级类目\"," +
                "\"title\":\""+ NumberUtil.getNameRandom("自营自动化商品") +"\",\"warehouseDataVos\":[{\"cityNames\":[{\"area\":\"富阳区\",\"city\":\"杭州市\",\"province\":\"浙江\"},{\"area\":\"西湖区\",\"city\":\"杭州市\",\"province\":\"浙江\"},{\"area\":\"余杭区\",\"city\":\"杭州市\",\"province\":\"浙江\"}],\"cityNum\":3,\"enabledTotalQuantity\":9990,\"warehouseName\":\"贝塔杭州仓库\",\"warehouseNo\":341,\"warehouseProvider\":\"贝塔有限公司\",\"warehouseTenantId\":24514}]}],\"skuId\":123119,\"afterSaleUnit\":\"盒\",\"maxAfterSaleAmount\":1,\"defaultPriceList\":[{\"priceType\":1,\"type\":2,\"mappingNumber\":1,\"totalStoreCount\":4276}],\"storePrice\":[],\"storeGroupPrice\":[],\"marketItemUnfairPriceStrategyVO\":{\"defaultFlag\":1,\"strategyType\":1},\"buyMultiple\":1,\"buyMultipleSwitch\":false,\"presaleSwitch\":0,\"storeInventoryControlFlag\":false,\"storeCostUnit\":\"箱\",\"storeOrderingInventoryUnitMultiple\":1,\"storeInventoryUnit\":\"盒\",\"storeInventoryCostUnitMultiple\":1,\"storeOrderingUnit\":\"盒\",\"standardUnitPrice\":1,\"saleLimitRule\":0,\"marketItemUnfairPriceStrategyDTO\":{\"defaultFlag\":1,\"strategyType\":1},\"defaultPrice\":{\"priceType\":1,\"type\":2,\"mappingNumber\":1,\"totalStoreCount\":4276}},\"buyMultiple\":1,\"buyMultipleSwitch\":false}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        return jsonResponse.getJSONObject("data").getString("marketId");
    }

    public String detail(String token,String id) throws Exception{
        log.info("货品详情");
        String url = domain + "product/query/detail";
        String requestBody = "{\"id\":"+id+"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        Assert.assertEquals(jsonResponse.getString("status"), "200");
        return data;
    }

    public String list(String token) throws Exception{
        log.info("自营货品列表");
        String url = domain + "product/query/list";
        String requestBody = "{\"pageIndex\":1,\"pageSize\":10,\"useFlag\":1,\"pageNum\":1}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String updateProduct(String token, String requestBody)throws Exception{
        log.info("自营货品编辑");
        String url = domain + "product/upsert/update";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String addProductSku(String token, String requestBody) throws Exception{
        log.info("自营货品新增规格");
        String url = domain + "product/sku/add";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }


    public String updateProductSku(String token) throws Exception{
        log.info("编辑自营货品sku");
        String url = domain + "product/sku/update";
        String requestBody = "{\"id\":127506,\"placeType\":0,\"specificationUnit\":\"瓶\",\"weight\":22,\"specificationType\":0,\"specification\":\"1盒*2箱\",\"volume\":\"0.020*0.020*0.020\",\"volumeUnit\":1000,\"customSkuCode\":\"22\",\"taxRateValue\":\"0.01\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String upsertItemCode(String token) throws Exception{
        log.info("编辑商品自有编码");
        String url = domain + "market/item/upsert/item-code";
        String requestBody = "{\"marketItemId\":37588,\"itemCode\":\"1111\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String saveClassification(String token) throws Exception{
        log.info("新增商品分组");
        String url = domain + "marketClassification/saveClassification";
        //String requestBody = "{\"id\":0,\"uploadIcon\":\"\",\"icon\":\"\",\"parentId\":1393,\"level\":2,\"name\":\"测试322\"}";
        String requestBody = "{\"id\":0,\"uploadIcon\":\"\",\"icon\":\"\",\"parentId\":1393,\"level\":2,\"name\":\"" + NumberUtil.getTimeNumber(6)+"\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String listAll(String token) throws Exception{
        log.info("商品分组列表");
        String url = domain + "marketClassification/listAll";
        HttpResponse response = HttpRequest.get(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        return response.body();
    }

    public String deleteClassification(String token,String id) throws Exception{
        log.info("删除商品分组");
        String url = domain + "marketClassification/deleteClassification";
        String requestBody = "{\"id\":"+id+"}";
        HttpResponse response = HttpRequest.delete(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String combineList(String token) throws Exception{
        log.info("组合包列表");
        String url = domain + "market/combine/query/list";
        String requestBody = "{\"pageIndex\":1,\"pageSize\":10}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String paginationQuery(String token) throws Exception{
        log.info("商品分页查询-运费处使用");
        String url = domain + "market/item/query/page";
        String requestBody = "{\"pageIndex\":1,\"pageSize\":10}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String combineDetail(String token) throws Exception{
        log.info("组合包详情");
        String url = domain + "market/combine/query/detail?combineMarketId=2038742";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String combineUpdate(String token) throws Exception{
        log.info("编辑组合包");
        String url = domain + "market/combine/upsert/update";
        String requestBody = "{\"combineItemId\":31464,\"combineMarketId\":\"2021304\",\"combineMarketSubTitle\":\"\",\"combineMarketTitle\":\"1231\",\"detailPicture\":\"\",\"firstClassificationId\":1393,\"firstClassificationName\":\"测试\",\"mainPicture\":\"test/al01wgl6ia6m1lmj6.jpg\",\"maxTotalPrice\":2,\"minTotalPrice\":2,\"onSale\":1,\"priceType\":5,\"secondClassificationId\":1394,\"secondClassificationName\":\"测试\",\"strategyValue\":null,\"totalPriceStr\":\"2.00\",\"classificationId\":1394,\"marketCombineItemMappingVOList\":[{\"itemId\":49704,\"itemTitle\":\"自动化无仓商品581\",\"mappingId\":1208,\"maxPrice\":1,\"minPrice\":1,\"priceStr\":\"1.00\",\"quantity\":2,\"specification\":\"0_1mL*1mL\",\"specificationUnit\":\"盒\",\"isEdit\":true},{\"itemId\":49705,\"itemTitle\":\"自动化无仓商品444\",\"mappingId\":1209,\"maxPrice\":1,\"minPrice\":1,\"priceStr\":\"1.00\",\"quantity\":1,\"specification\":\"0_1mL*1mL\",\"specificationUnit\":\"盒\",\"isEdit\":true}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String combineItemList(String token) throws Exception{
        log.info("组合包选商品");
        String url = domain + "market/combine/item/list";
        String requestBody = "{\"pageSize\":10,\"pageNum\":1}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }


    public String search(String token) throws Exception{
        log.info("搜索商品分组");
        String url = domain + "marketClassification/listAllById?id=1393";
        HttpResponse response = HttpRequest.get(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        return response.body();
    }




    public String upsertSkuUseFlag(String token) throws Exception{
        log.info("更新sku的停用状态");
        String url = domain + "product/upsert/sku-use-flag";
        String requestBody = "{\"skuId\":127221,\"useFlag\":0}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }


    public String deleteProductSku(String token, String id) throws Exception{
        log.info("删除自营货品sku");
        String url = domain + "product/upsert/delete-sku?id=" + id;
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        return response.body();
    }

    public String deleteProduct(String token, String id) throws Exception{
        log.info("自营货品删除spu");
        String url = domain + "product/upsert/delete-product?id=" + id;
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        return response.body();
    }

    public String agentRecordList(String token) throws Exception{
        log.info("商品代仓记录列表");
        String url = domain + "cosfo-manage/product-agent-application/sku-agent-record/list";
        String requestBody = "{\"sortList\":[{\"orderBy\":\"desc\",\"sortBy\":\"lastUpdateTimeSort\"}],\"pageIndex\":1,\"pageSize\":10,\"status\":0}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String upsertCancel(String token, String id) throws Exception{
        log.info("商品代仓记录取消");
        String url = domain + "product-agent-application/upsert/cancel";
        String requestBody = "{\"skuId\":"+id+"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

    public String bizLog(String token, String id) throws Exception{
        log.info("商品代仓申请记录日志");
        String url = domain + "cosfo-manage/product-agent-application/biz-log";
        String requestBody = "{\"skuId\":"+id+"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        return response.body();
    }

}
