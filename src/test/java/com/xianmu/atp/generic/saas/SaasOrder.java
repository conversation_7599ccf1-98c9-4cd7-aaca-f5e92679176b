package com.xianmu.atp.generic.saas;

import cn.hutool.core.lang.Console;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.Random;

/**
 * 类<code>Doc</code>用于：TODO
 * saas的通用方法订单
 * <AUTHOR>
 * @ClassName SaasOrder
 * @date 2024-12-03
 */
@Slf4j
@Component
public class SaasOrder {

    private static final String LOGIN_URL = "https://qamanage.cosfo.cn/tenant/user/query/login";

    private static final String VERIFY_CODE_URL = "https://qamall.cosfo.cn/merchant/store/sendCode?phone=13900099909&tenantId=24514";

    private static final String H5_LOGIN_URL = "https://qamall.cosfo.cn/merchant/store/smsCodeLogin";

    private static final String GET_CODE_URL = "https://qamall.cosfo.cn/merchant/store/sendCode";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    @Value("https://qamanage.cosfo.cn/")
    private String domain2;


    public String login() {
        log.info("帆台登录");
        String loginData = "{\"phone\":\"13429645111\",\"password\":\"Hello1234\",\"tenantId\":24514}";
        HttpResponse response = HttpRequest.post(LOGIN_URL)
                .body(loginData)
                .execute();
        String token = JSON.parseObject(response.body()).getJSONObject("data").getString("token");
        Console.log(response);
        log.info(response.toString());
        return token;
    }

/*    public String h5Login() throws Exception{
        log.info("登录");
        String h5LoginData = "{\"phone\":\"13900099909\",\"code\":\"643542\",\"H5Request\":true}";
        JSONObject jsonObject = JSON.parseObject(h5LoginData);
        HttpResponse verifyCodeResponse = HttpRequest.get(VERIFY_CODE_URL)
               .execute();
        String code = (String)redisTemplate.opsForValue().get("code_13900099909");
        JSONPath.set(jsonObject, "$.code", code);
        HttpResponse response = HttpRequest.post(H5_LOGIN_URL)
                .header("token", "h5mall_F2GqGw20CaxcgNwbeJOt2g==")
                .header("xm-phone", "13900099909")
                .header("xm-uid", "24514")
              .body(jsonObject.toJSONString())
              .execute();
        String token = JSON.parseObject(response.body()).getJSONObject("data").getString("token");
        log.info("token:{}", token);
        return token;
    }*/

/*    public String h5LoginGet() throws Exception{
        log.info("SaaS商城登录");
        HttpResponse response = HttpRequest.get(GET_CODE_URL + "?phone=13429645097&tenantId=24514")
                .execute();
        String code = JSON.parseObject(response.body()).getJSONObject("data").getString("code");

        String loginData1 = "{\"phone\":\"13429645097\",\"code\":\""+code+"\",\"H5Request\":true}";
        HttpResponse response1 = HttpRequest.post(H5_LOGIN_URL)
                .header("token", "h5mall_F2GqGw20CaxcgNwbeJOt2g==")
                .header("xm-phone", "13429645097")
                .header("xm-uid", "24514")
                .body(loginData1)
                .execute();
        JSONObject jsonObject = JSON.parseObject(response1.body()).getJSONObject("data");
        String token = jsonObject.getString("token");
        log.info("token:{}", token);
        return token;

    }*/


    public String h5LoginGet(String phone) throws Exception {
        log.info("SaaS商城登录, phone={}", phone);
        try {
            HttpResponse response = HttpRequest.get(GET_CODE_URL + "?phone=" + phone + "&tenantId=24514")
                    .timeout(8000)
                    .execute();
            String body1 = response != null ? response.body() : null;
            log.info("发送验证码响应: status={}, body={}", response == null ? -1 : response.getStatus(), body1);

            String code = null;
            if (body1 != null && !body1.isEmpty()) {
                try {
                    JSONObject dataObj = JSON.parseObject(body1).getJSONObject("data");
                    if (dataObj != null) {
                        code = dataObj.getString("code");
                    }
                } catch (Exception ignore) {
                    // 可能不是JSON或没有data/code字段
                }
            }

            // 如果接口未返回验证码，回退从Redis读取（H5发送验证码通常不会直接返回code）
            if (code == null || code.trim().isEmpty()) {
                String redisKey = "code_" + phone;
                for (int i = 0; i < 5 && (code == null || code.trim().isEmpty()); i++) {
                    try {
                        Object v = redisTemplate != null ? redisTemplate.opsForValue().get(redisKey) : null;
                        if (v != null) {
                            code = String.valueOf(v);
                            break;
                        }
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.warn("读取Redis验证码异常: {}", e.getMessage());
                    }
                }
            }

            if (code == null || code.trim().isEmpty()) {
                log.warn("未获取到短信验证码，无法完成H5登录。phone={}", phone);
                return null;
            }

            String loginData1 = "{\"phone\":\"" + phone + "\",\"code\":\"" + code + "\",\"H5Request\":true}";
            HttpResponse response1 = HttpRequest.post(H5_LOGIN_URL)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", "h5mall_F2GqGw20CaxcgNwbeJOt2g==")
                    .header("xm-phone", phone)
                    .header("xm-uid", "24514")
                    .body(loginData1)
                    .timeout(8000)
                    .execute();
            String body2 = response1 != null ? response1.body() : null;
            log.info("H5登录响应: status={}, body={}", response1 == null ? -1 : response1.getStatus(), body2);
            if (body2 == null || body2.isEmpty()) {
                log.warn("H5登录响应为空");
                return null;
            }
            JSONObject jsonObject = null;
            try {
                jsonObject = JSON.parseObject(body2).getJSONObject("data");
            } catch (Exception e) {
                log.warn("H5登录响应解析异常: {}", e.getMessage());
                return null;
            }
            if (jsonObject == null) {
                log.warn("H5登录返回无data: {}", body2);
                return null;
            }
            String token = jsonObject.getString("token");
            if (token == null || token.trim().isEmpty()) {
                log.warn("H5登录未返回token, body={}", body2);
                return null;
            }
            log.info("H5登录成功, token={}");
            return token;
        } catch (Exception e) {
            log.warn("H5登录异常: {}", e.getMessage());
            return null;
        }

    }

    public String placeOrder(String token, long itemId) throws Exception {
        log.info("SaaS商城生成无仓订单");
        String url = domain1 + "order/place-order";
        String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":2,\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":1,\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":null,\"warehouseType\":0,\"remark\":\"\",\"orderType\":0,\"combineMarketId\":null,\"combineItemId\":null}]";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        System.out.println("无仓订单号" + data);
        return data;
    }


    public String placeOrder1(String token, long itemId) throws Exception {
        log.info("SaaS商城生成自营仓订单");
        String url = domain1 + "order/place-order";
        String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":2,\"orderItemDTOS\":[{\"itemId\":49691,\"amount\":2,\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":341,\"warehouseType\":2,\"remark\":\"\",\"orderType\":0,\"combineMarketId\":null,\"combineItemId\":null}]";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        System.out.println("自营仓订单号" + data);
        return data;
    }


    public String placeOrder2(String token, long itemId) throws Exception {
        log.info("SaaS商城生成代仓订单");
        String url = domain1 + "order/place-order";
        String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":2,\"orderItemDTOS\":[{\"itemId\":49692,\"amount\":1,\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":null,\"warehouseType\":1,\"remark\":\"\",\"orderType\":0,\"combineMarketId\":null,\"combineItemId\":null}]";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        System.out.println("代仓订单号" + data);
        return data;
    }


    public String placeOrder3(String token, long itemId) throws Exception {
        log.info("SaaS商城生成鲜沐直供订单");
        String url = domain1 + "order/place-order";
        String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":2,\"orderItemDTOS\":[{\"itemId\":51156,\"amount\":1,\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":null,\"warehouseType\":1,\"remark\":\"\",\"orderType\":0,\"combineMarketId\":null,\"combineItemId\":null}]";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        System.out.println("鲜沐直供订单号" + data);
        return data;
    }


    public String placeOrder4(String token, long itemId) throws Exception {
        log.info("SaaS商城生成组合包订单");
        String url = domain1 + "order/place-order";
        String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":2,\"orderItemDTOS\":[{\"itemId\":51065,\"amount\":1,\"itemType\":2,\"combineItemId\":49891},{\"itemId\":49467,\"amount\":1,\"itemType\":2,\"combineItemId\":49891}],\"warehouseNo\":null,\"warehouseType\":0,\"remark\":\"\",\"orderType\":1,\"combineMarketId\":2038742,\"combineItemId\":49891}]";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        JSONObject jsonResponse = JSON.parseObject(body);
        String data = jsonResponse.getString("data");
        System.out.println("组合包订单号" + data);
        return data;
    }


    public String orderPay(String token, String orderNos) throws Exception {
        log.info("SaaS商城订单进行支付");
        String url = domain1 + "order/pay";
        String requestBody = "{\"payType\":2,\"orderNos\":" + orderNos + ",\"H5Request\":true}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }


    public String quantityRule(String token, String itemId) throws Exception {
        log.info("起订量检测");
        String url = domain1 + "order-rule/query/quantity/rule/v2";
        String requestBody = "{\"itemList\":[{\"itemId\":" + itemId + ",\"quantity\":1,\"amount\":0}],\"checkSource\":2}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String changeOrder(String token, String itemId) throws Exception {
        log.info("帆台订单改单");
        String url = domain2 + "order/upsert/change-order";
        String requestBody = "{\"itemList\":[{\"itemId\":" + itemId + ",\"quantity\":1,\"amount\":1}],\"checkSource\":2}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String schemeList(String token) throws Exception {
        log.info("流程设置列表");
        //接口地址
        String url = domain2 + "tenant/flow/scheme/query/list";
        String requestBody = "{\"pageIndex\":1,\"pageSize\":10000,\"defaultFlag\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }


    public String schemeDetail(String token) throws Exception {
        log.info("流程设置详情");
        //接口地址
        String url = domain2 + "tenant/flow/scheme/query/detail";
        String requestBody = "{\"schemeType\":1,\"defaultFlag\":1,\"schemeId\":190}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String schemeUpdate(String token) throws Exception {
        log.info("编辑流程设置");
        //接口地址
        String url = domain2 + "tenant/flow/scheme/upsert/update";
        String requestBody = "{\"defaultFlag\":1,\"flowRuleAuditDTOList\":[{\"auditAccountIds\":[],\"auditAccountList\":[],\"bizType\":1,\"id\":745,\"switchFlag\":false},{\"auditAccountIds\":[],\"auditAccountList\":[],\"bizType\":2,\"id\":746,\"switchFlag\":false},{\"auditAccountIds\":[],\"auditAccountList\":[],\"bizType\":3,\"id\":747,\"switchFlag\":false},{\"auditAccountIds\":[],\"auditAccountList\":[],\"bizType\":4,\"id\":748,\"switchFlag\":false}],\"schemeType\":1,\"schemeId\":190}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }


    public String agentOrderList(String token) throws Exception {
        log.info("后台代下单列表");
        //接口地址
        String url = domain2 + "agent-order/list";
        String requestBody = "{\"pageIndex\":1,\"pageSize\":10,\"planOrderStatus\":200}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }


    public String agentAgain(String token, String agentOrderNo) throws Exception {
        log.info("再来一单");
        //接口地址
        String url = domain2 + "agent-order/agent-order-again";
        String requestBody = "{\"agentOrderNo\":\"" + agentOrderNo + "\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String agentNotifyAll(String token)throws Exception{
        log.info("代下单一键提醒");
        //接口地址
        String url = domain2 + "agent-order/notify-all";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        System.out.println(response);
        return response.body();
    }



    public String orderList(String token,String orderNo) throws Exception{
        log.info("后台订单列表");
        String url = domain2 + "order/listAll";
        String requestBody = "{\"pageIndex\":1,\"pageSize\":10,\"orderNo\":\""+orderNo+"\",\"timeQueryType\":1}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        Assert.assertEquals(JSON.parseObject(body).getString("status"),"200");
        return body;
    }

    public String mallOrderList(String token) throws Exception{
        log.info("商城订单列表");
        String url = domain1 + "order/list";
        String requestBody = "{\"pageNum\":1,\"pageSize\":6}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        Assert.assertEquals(JSON.parseObject(body).getString("code"),"200");
        return body;
    }

    public String changeOrder(String token,String orderId,String orderItemId) throws Exception{
        log.info("改单");
        String url = domain2 + "order/upsert/change-order";
        String requestBody = "{\"orderId\":"+orderId+",\"changeOrderItemDTOList\":[{\"orderItemId\":"+orderItemId+",\"reduceQuantity\":1,\"enableApplyAmount\":2,\"remainQuantity\":1}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        Assert.assertEquals(JSON.parseObject(body).getString("status"),"200");
        return body;
    }

    /**
     * 查询配送明细接口
     * URL: /order/query/delivery-details
     */
    public String queryDeliveryDetailsNoDB(String token, Long orderId) throws Exception {
        log.info("查询配送明细(不访问数据源)");
        String url = domain2 + "/order/query/delivery-details?orderId="+orderId;
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        String body = response.body();
        System.out.println(body);
        Assert.assertEquals(JSON.parseObject(body).getString("status"),"200");
        return body;
    }

    /**
     * 无仓订单立即配送接口
     * URL: /order/upsert/order-delivery
     */
    public String orderDeliveryNoDB(String token, Long orderId, Long orderItemId) throws Exception {
        log.info("无仓订单立即配送(不访问数据源)");
        String url = domain2 + "order/upsert/order-delivery";
        // 根据要求完善入参结构：包含 deliveryType、remark、deliveryDTOList，并保持 orderId 与上一步一致、orderItemId 来自步骤8
        String requestBody = "{" +
                "\"orderId\":" + orderId + "," +
                "\"deliveryType\":0," +
                "\"remark\":\"总部自行配送\"," +
                "\"deliveryDTOList\":[{" +
                "\"orderItemId\":" + orderItemId + "," +
                // 数量按需求调整为6
                "\"quantity\":6}]}";
        // 新增日志：打印URL与入参，便于排查
        log.info("步骤9-立即配送 URL: {}", url);
        log.info("步骤9-立即配送 入参: {}", requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        String body = response.body();
        System.out.println(body);
        Assert.assertEquals(JSON.parseObject(body).getString("status"),"200");
        return body;
    }

    /**
     * 售后申请接口
     * URL: /order/after/sale/add
     */
    public String afterSaleAddNoDB(String token, Long orderItemId, Integer amount, Double applyPrice, 
                                   String proofPicture, Integer afterSaleType, Integer serviceType) throws Exception {
        log.info("售后申请(不访问数据源)");
        String url = domain2 + "order/after/sale/add";
        String requestBody = "{\"orderItemId\":" + orderItemId + 
                           ",\"amount\":" + amount + 
                           ",\"applyPrice\":" + applyPrice + 
                           ",\"proofPicture\":\"" + proofPicture + "\"" +
                           ",\"afterSaleType\":" + afterSaleType + 
                           ",\"serviceType\":" + serviceType + "}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        String body = response.body();
        System.out.println(body);
        Assert.assertEquals(JSON.parseObject(body).getString("status"),"200");
        return body;
    }
}
