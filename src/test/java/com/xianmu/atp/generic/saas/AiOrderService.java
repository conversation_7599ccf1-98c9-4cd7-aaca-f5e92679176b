package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 订单相关的解析与自提接口服务
 * 提供：
 * - 解析辅助方法：extractOrderId、extractOrderItemPrice、extractOrderItemId
 * - 自提相关接口：querySelfLifting、selfLifting
 *
 * 说明：
 * - 统一添加超时时间 timeout=3000ms
 * - 默认使用后台域名 xm.admin.domain，默认为 https://qamanage.cosfo.cn/
 */
@Slf4j
@Component
public class AiOrderService {

    @Value("${xm.admin.domain:https://qamanage.cosfo.cn/}")
    private String domain;

    /**
     * 从后台订单列表响应中提取 orderId
     * 预期结构：{"status":200, "data": {"list": [ {"orderId": 123, "orderItemVOS": [...] } ] }}
     */
    public Long extractOrderId(JSONObject orderListJson) {
        if (orderListJson == null) return null;
        JSONObject data = toJSONObject(orderListJson.get("data"));
        if (data == null) return null;
        JSONArray list = toJSONArray(data.get("list"));
        if (list == null || list.isEmpty()) return null;
        JSONObject first = list.getJSONObject(0);
        if (first == null) return null;
        Long orderId = first.getLong("orderId");
        if (orderId == null) {
            // 兜底：有些环境可能使用 id 作为订单ID
            orderId = first.getLong("id");
        }
        return orderId;
    }

    /**
     * 从后台订单列表响应中提取第一个订单项的 price
     * 预期结构：... -> list[0] -> orderItemVOS[0].price
     */
    public Double extractOrderItemPrice(JSONObject orderListJson) {
        if (orderListJson == null) return null;
        JSONObject data = toJSONObject(orderListJson.get("data"));
        if (data == null) return null;
        JSONArray list = toJSONArray(data.get("list"));
        if (list == null || list.isEmpty()) return null;
        JSONObject first = list.getJSONObject(0);
        if (first == null) return null;
        JSONArray itemVOS = toJSONArray(first.get("orderItemVOS"));
        if (itemVOS == null || itemVOS.isEmpty()) return null;
        JSONObject firstItem = itemVOS.getJSONObject(0);
        if (firstItem == null) return null;
        Double price = firstItem.getDouble("price");
        if (price == null) {
            // 兜底：可能以字符串形式返回
            String priceStr = firstItem.getString("price");
            try {
                price = priceStr == null ? null : Double.valueOf(priceStr);
            } catch (Exception ignored) {}
        }
        return price;
    }

    /**
     * 从配送明细响应中提取第一个待配送项的 orderItemId
     * 预期结构：{"status":200, "data": {"waitDeliveryItemList": [ {"orderItemId": 456} ] }}
     */
    public Long extractOrderItemId(JSONObject deliveryDetailsJson) {
        if (deliveryDetailsJson == null) return null;
        JSONObject data = toJSONObject(deliveryDetailsJson.get("data"));
        if (data == null) return null;
        // 优先从 waitDeliveryItemList 提取
        JSONArray waitList = toJSONArray(data.get("waitDeliveryItemList"));
        if (waitList != null && !waitList.isEmpty()) {
            JSONObject first = waitList.getJSONObject(0);
            if (first != null) {
                Long id = first.getLong("orderItemId");
                if (id != null) return id;
                // 兜底字段
                id = first.getLong("id");
                if (id != null) return id;
            }
        }
        // 兜底：某些响应可能复用 orderItemVOS 结构
        JSONArray itemVOS = toJSONArray(data.get("orderItemVOS"));
        if (itemVOS != null && !itemVOS.isEmpty()) {
            JSONObject first = itemVOS.getJSONObject(0);
            if (first != null) {
                Long id = first.getLong("orderItemId");
                if (id != null) return id;
                id = first.getLong("id");
                if (id != null) return id;
            }
        }
        return null;
    }

    /**
     * 查询自提信息
     * GET /order/query/self-lifting?orderNo=xxx
     */
    public JSONObject querySelfLifting(String token, String orderNo) {
        String url = domain + "order/query/self-lifting?orderNo=" + orderNo;
        log.info("查询自提信息 URL: {}", url);
        HttpResponse response = HttpRequest.get(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        String body = response.body();
        log.info("查询自提信息 响应: {}", body);
        try {
            return JSON.parseObject(body);
        } catch (Exception e) {
            log.warn("查询自提信息解析失败: {}", e.getMessage());
            return new JSONObject();
        }
    }

    /**
     * 提交订单自提
     * POST /order/self-lifting
     * 请求体示例：{"orderNo":"OR123","orderSelfLiftingDTOS":[{"address":"xxx","expectTime":"2025-01-01 23:00:00","warehouseNo":1}]}
     */
    public JSONObject selfLifting(String token, String orderNo, String address, String expectTime, Integer warehouseNo) {
        String url = domain + "order/self-lifting";
        JSONObject req = new JSONObject();
        req.put("orderNo", orderNo);
        // 按后端期望结构包装为数组字段 orderSelfLiftingDTOS
        JSONArray arr = new JSONArray();
        JSONObject dto = new JSONObject();
        dto.put("address", address);
        dto.put("expectTime", expectTime);
        dto.put("warehouseNo", warehouseNo);
        arr.add(dto);
        req.put("orderSelfLiftingDTOS", arr);
        String bodyStr = req.toJSONString();
        log.info("提交自提 URL: {}", url);
        log.info("提交自提 入参: {}", bodyStr);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(bodyStr)
                .timeout(3000)
                .execute();
        String body = response.body();
        log.info("提交自提 响应: {}", body);
        try {
            return JSON.parseObject(body);
        } catch (Exception e) {
            log.warn("提交自提解析失败: {}", e.getMessage());
            return new JSONObject();
        }
    }

    // 工具方法：兼容字符串/JSONObject
    private static JSONObject toJSONObject(Object obj) {
        if (obj == null) return null;
        if (obj instanceof JSONObject) return (JSONObject) obj;
        if (obj instanceof String) {
            try { return JSON.parseObject((String) obj); } catch (Exception ignored) {}
        }
        return null;
    }

    // 工具方法：兼容字符串/JSONArray
    private static JSONArray toJSONArray(Object obj) {
        if (obj == null) return null;
        if (obj instanceof JSONArray) return (JSONArray) obj;
        if (obj instanceof String) {
            try { return JSON.parseArray((String) obj); } catch (Exception ignored) {}
        }
        return null;
    }
}