package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */

@Slf4j
@Component
public class SaasWarehouse{


    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    public String list(String token)throws Exception{
        log.info("实时库存-查询仓库数据");
        //接口地址
        String url = domain + "product/agent/query/warehouse-data-list";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"onSale\":1,\"inOutRecord\":1,\"pageNum\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String list1(String token)throws Exception{
        log.info("实时库存-查询仓库列表");
        //接口地址
        String url = domain + "product/agent/query/warehouse-list";
        String requestBody ="{}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }


    public String list2(String token)throws Exception{
        log.info("实时库存-查询仓库库存总览信息");
        //接口地址
        String url = domain + "product/agent/query/warehouse-overview";
        String requestBody ="{\"onSale\":1,\"inOutRecord\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String list3(String token)throws Exception{
        log.info("货品维度-查询仓库-货品聚合数据");
        //接口地址
        String url = domain + "product/agent/query/warehouse-aggregation-list";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"saleOut\":false,\"onSale\":1,\"inOutRecord\":1,\"pageNum\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();

    }


    public String exportWarehousing(String token)throws Exception{
        log.info("导出出入库记录");
        //接口地址
        String url = domain + "product/export-warehousing-record";
        String requestBody ="{\"outOfStockType\":51,\"startTime\":\"2024-07-01\",\"endTime\":\"2024-07-31\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();

    }

    public String exportDeliveryPlan(String token)throws Exception{
        log.info("导出配送记录");
        //接口地址
        String url = domain + "product/export-delivery-plan-record";
        String requestBody ="{\"warehouseNo\":180,\"startTime\":\"2025-03-14\",\"endTime\":\"2025-03-14\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();

    }

    public String productAgentWarehouseList(String token)throws Exception{
        log.info("仓库维度-查询仓库数据");
        //接口地址
        String url = domain + "product/agent/query/warehouse-data-list";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"onSale\":1,\"inOutRecord\":1,\"pageNum\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();
    }

    public String warehouseAggregationList(String token)throws Exception{
        log.info("货品维度-查询仓库-货品聚合数据");
        //接口地址
        String url = domain + "product/agent/query/warehouse-aggregation-list";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"saleOut\":false,\"onSale\":1,\"inOutRecord\":1,\"pageNum\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();
    }


    public String warehouseOverview(String token)throws Exception{
        log.info("查询仓库库存总览信息");
        //接口地址
        String url = domain + "product/agent/query/warehouse-overview";
        String requestBody ="{\"onSale\":1,\"inOutRecord\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();
    }

    public String shelfLifeQuery(String token)throws Exception{
        log.info("库存保质期查询");
        //接口地址
        String url = domain + "product/agent/shelf-life/query/page";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":5,\"skuId\":124509,\"warehouseNo\":493}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();
    }

    public String stockChangeRecord(String token)throws Exception{
        log.info("查询库存变动记录");
        //接口地址
        String url = domain + "product/agent/query/stock-change-record";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"warehouseNo\":1,\"startTime\":\"2025-06-11 00:00:00\",\"endTime\":\"2025-06-11 23:59:59\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return response.body();
    }

}
