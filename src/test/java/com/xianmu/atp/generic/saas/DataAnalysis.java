package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Assert;

/**
 * <AUTHOR>
 * @date 2025/5/28
 */

@Slf4j
@Component
public class DataAnalysis {

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    public String exportProductSales(String token)throws Exception{
        log.info("导出商品销量概况");
        //接口地址
        String url = domain + "report/export/product-detail-sales";
        String requestBody ="{\"startTime\":\"20250501\",\"endTime\":\"20250515\",\"exportParam\":\"[{\\\"label\\\":\\\"时间\\\",\\\"value\\\":\\\"2025-05-01~2025-05-15\\\"}]\",\"type\":6,\"timeTag\":\"20221201\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportStorePurchase(String token)throws Exception{
        log.info("导出门店采购数据");
        //接口地址
        String url = domain + "report/export/merchant-store-detail-purchase";
        String requestBody ="{\"startTime\":\"20250501\",\"endTime\":\"20250515\",\"exportParam\":\"[{\\\"label\\\":\\\"时间\\\",\\\"value\\\":\\\"2025-05-01~2025-05-15\\\"}]\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportStoreDimension(String token)throws Exception{
        log.info("导出门店订货异常");
        //接口地址
        String url = domain + "merchant/store/audit/center/query/export/store-item-dimension";
        String requestBody ="{\"exportParam\":\"[{\\\"label\\\":\\\"周\\\",\\\"value\\\":\\\"2025-21周\\\"}]\",\"type\":1,\"timeTag\":\"20250519\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String reportQueryList(String token)throws Exception{
        log.info("查询门店采购明细");
        //接口地址
        String url = domain + "report/query/list-all";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"startTime\":\"2025-05-27\",\"endTime\":\"2025-05-27\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportProportion(String token)throws Exception{
        log.info("导出门店订货占比");
        //接口地址
        String url = domain + "merchant/store/audit/center/query/export/proportion";
        String requestBody ="{\"exportParam\":\"[{\\\"label\\\":\\\"周\\\",\\\"value\\\":\\\"2025-21周\\\"}]\",\"type\":1,\"timeTag\":\"20250519\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportCondition(String token)throws Exception{
        log.info("导出门店订货明细");
        //接口地址
        String url = domain + "report/export/export-by-condition";
        String requestBody ="{\"startTime\":\"2025-05-27\",\"endTime\":\"2025-05-27\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportPurchaseDetail(String token)throws Exception{
        log.info("导出采购明细报表");
        //接口地址
        String url = domain + "report/export/purchase/detail";
        String requestBody ="{}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportPurchaseBackDetail(String token)throws Exception{
        log.info("导出采购退货明细");
        //接口地址
        String url = domain + "report/export/purchase/back/detail";
        String requestBody ="{}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportDamageDetail(String token)throws Exception{
        log.info("导出货损明细");
        //接口地址
        String url = domain + "report/export/damage/detail";
        String requestBody ="{}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String damageList(String token)throws Exception{
        log.info("货损明细列表");
        //接口地址
        String url = domain + "report/query/damage/detail";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportDamageSaleDetail(String token)throws Exception{
        log.info("导出货损比");
        //接口地址
        String url = domain + "report/export/damage-sale-ratio/detail";
        String requestBody ="{}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }


    public String damageSaleRatioList(String token)throws Exception{
        log.info("货损比列表");
        //接口地址
        String url = domain + "report/query/damage-sale-ratio/detail";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportSku(String token)throws Exception{
        log.info("导出采购汇总-按货品");
        //接口地址
        String url = domain + "purchase/summary/export/export-sku";
        String requestBody ="{\"params\":\"{}\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String exportSupplier(String token)throws Exception{
        log.info("导出采购汇总-按供应商");
        //接口地址
        String url = domain + "purchase/summary/query/supplier-agg";
        String requestBody ="{}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }


    public String supplierList(String token)throws Exception{
        log.info("供应商列表");
        //接口地址
        String url = domain + "purchase/summary/query/supplier-list-all";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }
}

