package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;

/**
 * AI SaaS接口测试工具类
 * 封装代门店下单、非现金子账户修改、交易流水等接口操作
 * 
 * <AUTHOR> Test Engineer
 * @date 2025/08/11
 */
@Slf4j
@Component
public class AiSaasService {

    @Resource
    private SaasOrder saasOrder;

    @Value("${xm.admin.domain:https://qamanage.cosfo.cn/}")
    private String domain;

    @Value("${xm.mall.domain:https://qamall.cosfo.cn/}")
    private String mallDomain;





    /**
     * 交易流水查询接口
     * 
     * @param token 认证token
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @param recordNo 订单编号
     * @return 响应结果
     */
    public String getBillList(String token, Integer pageNum, Integer pageSize, String recordNo) throws Exception {
        log.info("执行交易流水查询接口，pageNum: {}, pageSize: {}, recordNo: {}", pageNum, pageSize, recordNo);
        
        String url = domain + "bill/list";
        
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("pageNum", pageNum);
        requestBody.put("pageSize", pageSize);
        if (recordNo != null && !recordNo.isEmpty()) {
            requestBody.put("recordNo", recordNo);
        }
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody.toJSONString())
                .timeout(3000)
                .execute();
        
        log.info("交易流水查询响应状态码: {}, 响应内容: {}", response.getStatus(), response.body());
        return response.body();
    }

    /**
     * 线下支付下单流程 - 预下单
     * 
     * @param token H5登录token
     * @param itemId 商品ID
     * @param amount 商品数量
     * @return 预下单响应，包含payablePrice
     */
    public String preOrder(String token, Long itemId, Integer amount) throws Exception {
        log.info("执行预下单接口，itemId: {}, amount: {}", itemId, amount);
        
        String url = mallDomain + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":" + amount + "}]}";
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        
        log.info("预下单响应状态码: {}, 响应内容: {}", response.getStatus(), response.body());
        return response.body();
    }

    /**
     * 线下支付下单流程 - 生成订单
     * 
     * @param token H5登录token
     * @param itemId 商品ID
     * @param payType 支付类型（6-线下支付）
     * @return 订单号
     */
     public String placeOrder(String token, Long itemId, Integer payType) throws Exception {
         log.info("执行生成订单接口，itemId: {}, payType: {}", itemId, payType);
         
         String url = mallDomain + "order/place-order";
         int whType = inferWarehouseType(itemId);
         // 使用完整的请求体结构，参考SaasOrder中placeOrder方法的实现
         String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":" + payType + ",\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":6,\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":null,\"warehouseType\":" + whType + ",\"remark\":\"\",\"orderType\":0,\"combineMarketId\":null,\"combineItemId\":null}]";
         
         HttpResponse response = HttpRequest.post(url)
                 .header(Header.CONTENT_TYPE, "application/json")
                 .header("token", token)
                 .body(requestBody)
                 .timeout(3000)
                 .execute();
         
         log.info("生成订单响应状态码: {}, 响应内容: {}", response.getStatus(), response.body());
         
         // 从响应中提取订单号
         JSONObject jsonResponse = JSON.parseObject(response.body());
         if (jsonResponse != null && jsonResponse.containsKey("data") && jsonResponse.get("data") != null) {
             Object data = jsonResponse.get("data");
             if (data instanceof com.alibaba.fastjson.JSONArray) {
                 com.alibaba.fastjson.JSONArray dataArray = (com.alibaba.fastjson.JSONArray) data;
                 if (dataArray.size() > 0) {
                     return dataArray.getString(0);
                 }
             }
             return jsonResponse.getString("data");
         }
         return null;
     }

    // 重载：支持指定下单数量，与预下单保持一致
    public String placeOrder(String token, Long itemId, Integer payType, Integer amount) throws Exception {
        log.info("执行生成订单接口，itemId: {}, payType: {}, amount: {}", itemId, payType, amount);
        String url = mallDomain + "order/place-order";
        int amt = (amount == null || amount <= 0) ? 1 : amount;
        int whType = inferWarehouseType(itemId);
        String requestBody = "[{\"merchantAddressId\":41346,\"merchantContactId\":41424,\"payType\":" + payType + ",\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":" + amt + ",\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":null,\"warehouseType\":" + whType + ",\"remark\":\"\",\"orderType\":0,\"combineMarketId\":null,\"combineItemId\":null}]";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        log.info("生成订单响应状态码: {}, 响应内容: {}", response.getStatus(), response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        if (jsonResponse == null) return null;
        if (jsonResponse.containsKey("data")) {
            Object data = jsonResponse.get("data");
            if (data instanceof com.alibaba.fastjson.JSONArray) {
                com.alibaba.fastjson.JSONArray arr = (com.alibaba.fastjson.JSONArray) data;
                if (arr.size() > 0) return arr.getString(0);
            }
            return jsonResponse.getString("data");
        }
        return null;
    }

    /**
     * 线下支付下单流程 - 订单支付
     * 
     * @param token H5登录token
     * @param orderNo 订单号
     * @param payType 支付类型（6-线下支付）
     * @return 支付响应，包含transAmt
     */
    public String orderPay(String token, String orderNo, Integer payType) throws Exception {
        log.info("执行订单支付接口，orderNo: {}, payType: {}", orderNo, payType);
        
        String url = mallDomain + "order/pay";
        // orderNos参数应该是数组格式
        String requestBody = "{\"orderNos\":[\"" + orderNo + "\"],\"payType\":" + payType + ",\"paymentReceipt\":\"test/v3fwqz27w5g6xykte\",\"H5Request\":true}";
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        
        log.info("订单支付响应状态码: {}, 响应内容: {}", response.getStatus(), response.body());
        return response.body();
    }

    /**
     * 线下支付下单流程 - 查看订单列表
     * 
     * @param token H5登录token
     * @return 订单列表响应，包含orderNo
     */
    public String getOrderList(String token) throws Exception {
        log.info("执行查看订单列表接口");
        
        String url = mallDomain + "order/list";
        String requestBody = "{\"pageNum\":1,\"pageSize\":10}";
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        
        log.info("订单列表响应状态码: {}, 响应内容: {}", response.getStatus(), response.body());
        return response.body();
    }

    /**
     * 从预下单响应中提取payablePrice
     * 
     * @param preOrderResponse 预下单响应
     * @return payablePrice值
     */
    public Double extractPayablePrice(String preOrderResponse) {
        try {
            log.info("开始提取payablePrice，响应内容: {}", preOrderResponse);
            JSONObject jsonResponse = JSON.parseObject(preOrderResponse);
            if (jsonResponse == null) {
                log.error("响应解析为null");
                return null;
            }
            
            if (jsonResponse.containsKey("data")) {
                Object dataObj = jsonResponse.get("data");
                log.info("data对象类型: {}, 内容: {}", dataObj != null ? dataObj.getClass().getSimpleName() : "null", dataObj);
                
                if (dataObj instanceof com.alibaba.fastjson.JSONArray) {
                    com.alibaba.fastjson.JSONArray dataArray = (com.alibaba.fastjson.JSONArray) dataObj;
                    if (dataArray.size() > 0) {
                        JSONObject firstData = dataArray.getJSONObject(0);
                        if (firstData != null && firstData.containsKey("payablePrice")) {
                            return firstData.getDouble("payablePrice");
                        }
                    }
                } else if (dataObj instanceof JSONObject) {
                    JSONObject dataJsonObj = (JSONObject) dataObj;
                    if (dataJsonObj.containsKey("payablePrice")) {
                        return dataJsonObj.getDouble("payablePrice");
                    }
                }
            }
            log.warn("未找到payablePrice字段");
        } catch (Exception e) {
            log.error("提取payablePrice失败，响应: {}", preOrderResponse, e);
        }
        return null;
    }

    /**
     * 从支付响应中提取transAmt
     * 
     * @param payResponse 支付响应
     * @return transAmt值
     */
    public Double extractTransAmt(String payResponse) {
        try {
            log.info("开始提取transAmt，响应内容: {}", payResponse);
            JSONObject jsonResponse = JSON.parseObject(payResponse);
            if (jsonResponse == null) {
                log.error("支付响应解析为null");
                return null;
            }
            
            if (jsonResponse.containsKey("data")) {
                JSONObject dataObj = jsonResponse.getJSONObject("data");
                if (dataObj != null && dataObj.containsKey("transAmt")) {
                    return dataObj.getDouble("transAmt");
                } else {
                    log.warn("data对象为null或不包含transAmt字段，data: {}", dataObj);
                }
            } else {
                log.warn("响应中未找到data字段");
            }
        } catch (Exception e) {
            log.error("提取transAmt失败，响应: {}", payResponse, e);
        }
        return null;
    }

    /**
     * 从订单列表响应中提取最新订单号
     * 
     * @param orderListResponse 订单列表响应
     * @return 最新订单号
     */
    public String extractLatestOrderNo(String orderListResponse) {
        try {
            log.info("开始提取最新订单号，响应内容: {}", orderListResponse);
            JSONObject jsonResponse = JSON.parseObject(orderListResponse);
            if (jsonResponse == null) {
                log.error("订单列表响应解析为null");
                return null;
            }
            
            if (jsonResponse.containsKey("data")) {
                // data字段直接是数组，不是包含list的对象
                com.alibaba.fastjson.JSONArray dataArray = jsonResponse.getJSONArray("data");
                if (dataArray != null && dataArray.size() > 0) {
                    JSONObject firstOrder = dataArray.getJSONObject(0);
                    if (firstOrder != null && firstOrder.containsKey("orderNo")) {
                        String orderNo = firstOrder.getString("orderNo");
                        log.info("成功提取最新订单号: {}", orderNo);
                        return orderNo;
                    } else {
                        log.warn("第一个订单对象为null或不包含orderNo字段，firstOrder: {}", firstOrder);
                    }
                } else {
                    log.warn("订单列表为空或null，dataArray: {}", dataArray);
                }
            } else {
                log.warn("响应中未找到data字段");
            }
        } catch (Exception e) {
            log.error("提取订单号失败，响应: {}", orderListResponse, e);
        }
        return null;
    }

    /**
     * 从交易流水响应中提取billPrice
     * 
     * @param billListResponse 交易流水响应
     * @param orderNo 订单号
     * @return billPrice值
     */
    public Double extractBillPrice(String billListResponse, String orderNo) {
        try {
            JSONObject jsonResponse = JSON.parseObject(billListResponse);
            if (jsonResponse.containsKey("data")) {
                JSONObject dataObj = jsonResponse.getJSONObject("data");
                if (dataObj.containsKey("list")) {
                    com.alibaba.fastjson.JSONArray listArray = dataObj.getJSONArray("list");
                    for (int i = 0; i < listArray.size(); i++) {
                        JSONObject bill = listArray.getJSONObject(i);
                        if (bill.containsKey("recordNo") && orderNo.equals(bill.getString("recordNo"))) {
                            if (bill.containsKey("billPrice")) {
                                return bill.getDouble("billPrice");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("提取billPrice失败", e);
        }
        return null;
    }
    
    // 根据商品推断仓库类型，避免因仓库参数不匹配导致下单失败
    private int inferWarehouseType(Long itemId) {
        try {
            if (itemId != null && itemId.longValue() == 51156L) {
                return 1; // 该商品在SaasOrder中示例为warehouseType=1
            }
        } catch (Exception ignore) {}
        return 0;
    }
}