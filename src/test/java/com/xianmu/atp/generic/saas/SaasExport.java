package com.xianmu.atp.generic.saas;

import cn.hutool.core.lang.Console;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 * saas的通用方法导出
 * <AUTHOR>
 * @ClassName SaasExport
 * @date 2025-03-30
 */
@Slf4j
@Component
public class SaasExport {

    private static final String LOGIN_URL = "https://qamanage.cosfo.cn/tenant/user/query/login";

    private static final String VERIFY_CODE_URL = "https://qamall.cosfo.cn/merchant/store/sendCode?phone=13900099909&tenantId=24514";

    private static final String H5_LOGIN_URL = "https://qamall.cosfo.cn/merchant/store/smsCodeLogin";

    private static final String GET_CODE_URL = "https://qamall.cosfo.cn/merchant/store/sendCode";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    public String bomExport(String token) throws Exception{
        //半成品导出
        String url = domain1 + "pos/pos-bom/exportSemiFinished";
        String requestBody = "{\"outMenuName\":\"测试\",\"params\":\"{\\\"半成品名称\\\":\\\"测试\\\"}\",\"bomType\":[2]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String orderExport(String token) throws Exception{
        //订单导出
        String url = domain1 + "order/export";
        String requestBody = "{\"title\":\"导出测试\",\"timeQueryType\":1,\"startTime\":\"2025-03-27 00:00:00\",\"endTime\":\"2025-03-27 23:59:59\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

    public String afterExport(String token) throws Exception{
        //售后导出
        String url = domain1 + "order/after/sale/export";
        String requestBody = "{\"afterSaleOrderNo\":\"AS1904011571882373120\"}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        String body = response.body();
        System.out.println(body);
        int code = response.getStatus();
        Assert.assertEquals(code, 200);
        return body;
    }

}
