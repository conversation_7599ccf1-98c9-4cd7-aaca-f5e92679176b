package com.xianmu.atp.generic.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/12
 */

@Slf4j
@Component
public class TenantAccount {

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    public String preLoginV2()throws Exception{
        log.info("账号登录");
        //接口地址
        String url = domain + "tenant/user/query/pre-loginV2";
        String requestBody ="{\"phone\":\"***********\",\"password\":\"hello1234\"}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", null)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String list()throws Exception{
        log.info("查询绑定品牌方");
        //接口地址
        String url = domain + "tenant/user/query/tenant/list";
        String requestBody ="{\"phone\":\"***********\",\"name\":\"\",\"pageSize\":10,\"pageIndex\":1}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", null)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String login()throws Exception{
        log.info("登录工作台");
        //接口地址
        String url = domain + "tenant/user/query/login";
        String requestBody ="{\"phone\":\"***********\",\"password\":\"hello1234\",\"tenantId\":24514}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", null)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String loginUserInfo(String token)throws Exception{
        log.info("登录工作台-查看用户信息");
        //接口地址
        String url = domain + "tenant/user/query/login/user-info";
        String requestBody ="{\"phone\":\"***********\",\"password\":\"hello1234\",\"tenantId\":24514}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String accountList(String token)throws Exception{
        log.info("账号列表");
        //接口地址
        String url = domain + "tenant/user/query/account/list";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10,\"roleId\":10681}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String addAccount(String token)throws Exception{
        log.info("新增账号");
        //接口地址
        String url = domain + "tenant/user/upsert/add";
        String requestBody ="{\"phone\":\""+ NumberUtil.getPhoneNumber("134", 8) +"\",\"nickname\":\""+NumberUtil.getNameRandom("测试账号")+"\",\"roleIds\":10681,\"supplierIds\":[]}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String userInfo(String token, String accountId)throws Exception{
        log.info("查询详细信息");
        //接口地址
        String url = domain + "tenant/user/query/user-info?accountId="+accountId;
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String update(String token, String requestBody)throws Exception{
        log.info("编辑账号信息");
        //接口地址
        String url = domain + "tenant/user/upsert/user/info";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String delete(String token, String id)throws Exception{
        log.info("删除账号信息");
        //接口地址
        String url = domain + "tenant/user/upsert/delete?id=" + id;
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        System.out.println(response);
        return response.body();
    }

    public String fundAccountPage(String token)throws Exception{
        log.info("调整账户类型列表");
        //接口地址
        String url = domain + "tenant-fund-account/page";
        String requestBody ="{\"pageIndex\":1,\"pageSize\":10}";
        System.out.println(requestBody);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        return response.body();
    }

}
