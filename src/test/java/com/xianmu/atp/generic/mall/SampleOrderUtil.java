package com.xianmu.atp.generic.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class SampleOrderUtil extends BaseTest {
    @Value("${app.manage-domain}")
    private String domain;
    @Resource
    private LoginHttp login;
    public String SampleOrder(String env, String phone, int contactId, String sku1, int quantity1,Integer areaNo){
        String token = login.login();
        log.info("token:{}", token);
        //
        String samplePoolUrl = domain+
                "/inventory/skus/samplePool/"+areaNo+"queryStr="+sku1;
        log.info("获取SKU信息详情");
        HttpResponse response = HttpRequest.get(samplePoolUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("")
                .execute();
        log.info("获取SKu data:{}"+response.body());
        String skuData = JSON.parseObject(response.body()).getJSONObject("data").toString();
        JSONArray skuDateArray = new JSONArray();
        return "";
    }
}
