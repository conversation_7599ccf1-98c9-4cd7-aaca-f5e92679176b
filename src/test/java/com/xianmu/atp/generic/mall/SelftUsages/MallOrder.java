package com.xianmu.atp.generic.mall.SelftUsages;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.MerchantCouponDao;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

public class MallOrder extends BaseTest {

    public List<Map<String, Object>> getOrderItemList() {
        return orderItemList;
    }

    public void setOrderItemList(List<Map<String, Object>> orderItemList) {
        this.orderItemList = orderItemList;
    }

    public int getContactId() {
        return contactId;
    }

    public List<Map<String, Object>> getUsedCoupons() {
        return usedCoupons;
    }

    public void setUsedCoupons(List<Map<String, Object>> usedCoupons) {
        this.usedCoupons = usedCoupons;
    }

    public int getPayment() {
        return payment;
    }

    public void setPayment(int payment) {
        this.payment = payment;
    }

    List<Map<String, Object>> orderItemList;

    int contactId;

    List<Map<String, Object>> usedCoupons;

    int payment;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    String phone;

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    String env;

    private String lastResult;

    public MallOrder() {
        this.payment = 0;
        this.contactId = 0;
        this.phone="";
        this.usedCoupons= new ArrayList<>();
        this.orderItemList = new ArrayList<>();
    }
    public void addSku(SkuEnum skuEnum) {
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put("sku", skuEnum.getSku());
        skuMap.put("quantity", skuEnum.getDefaultBuyNum());
        this.orderItemList.add(skuMap);
    }

    public void addSku(SkuEnum skuEnum,int quantity) {
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put("sku", skuEnum.getSku());
        skuMap.put("quantity",quantity);
        this.orderItemList.add(skuMap);
    }

    public void addAddress(AddressEnum addressEnum) {
        this.contactId = addressEnum.getContactId();
        for(MallUserEnum mallUserEnum: MallUserEnum.values()){
            if(mallUserEnum.getMid()==addressEnum.getMid()){
                this.phone=mallUserEnum.getPhone();
            }
        }
    }

    public void addCoupun(int couponId) {
        List<Map<String, Object>> usedCouponsList = this.usedCoupons;
        Map<String, Object> usedCouponMap = new HashMap<>();
        usedCouponMap.put("usedCouponId", couponId);
        usedCouponMap.put("couponType", 1);
        usedCouponsList.add(usedCouponMap);
        this.usedCoupons= usedCouponsList;
    }

    public void addUser(MallUserEnum mallUserEnum) {
        this.phone= mallUserEnum.getPhone();
    }

    public void addEnv(EnvEnum envEnum){
        this.env=envEnum.getName();
    }

    @Retry(attempts =2, delay = 2000, onExceptions = {Exception.class})
    public void send(boolean needPay) throws InterruptedException {
        String thisOrderUrl = "https://" + this.env+"h5.summerfarm.net/order/upsert/place-order/v3";
        String token = getXianmuMallUserToken(this.phone, this.env);
        int retryCount=0;
        boolean success = false;
        HttpResponse response;
        while(retryCount<5 && !success) {
            Map<String, Object> body = new HashMap<>();
            body.put("contactId", this.contactId);
            body.put("orderItemList", this.orderItemList);
            body.put("usedCoupons", this.usedCoupons);
            body.put("payment", this.payment);
            response = HttpRequest.post(thisOrderUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(JSONObject.toJSONString(body))
                    .execute();
            this.lastResult = response.body();
            if(!response.body().contains("重新")){
                success=true;
            }
            Thread.sleep(2000);
            retryCount++;
        }
        if(needPay){
            String masterOrderNo= JSONObject.parseObject(this.lastResult).getJSONObject("data").get("masterOrderNo").toString();
            String paymentUrl="https://" + env+"h5.summerfarm.net/payment/pay";
            HttpResponse payResponse =HttpRequest.post(paymentUrl)
                    //.header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body("masterOrderNo="+masterOrderNo+"&payChannel=1")
                    .execute();
        }
    }

    public void sendWithToken(boolean needPay,String token) throws InterruptedException {
        String thisOrderUrl = "https://" + this.env+"h5.summerfarm.net/order/upsert/place-order/v3";
        int retryCount=0;
        boolean success = false;
        HttpResponse response;
        while(retryCount<5 && !success){
            Map<String, Object> body = new HashMap<>();
            body.put("contactId", this.contactId);
            body.put("orderItemList", this.orderItemList);
            body.put("usedCoupons",this.usedCoupons);
            body.put("payment",this.payment);
            response = HttpRequest.post(thisOrderUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(JSONObject.toJSONString(body))
                    .execute();
            this.lastResult=response.body();
            if(!response.body().contains("重新")){
                success=true;
            }
            retryCount++;
            Thread.sleep(2000);
        }
        if(needPay){
            String masterOrderNo= JSONObject.parseObject(this.lastResult).getJSONObject("data").get("masterOrderNo").toString();
            String paymentUrl="https://" + env+"h5.summerfarm.net/payment/pay";
            HttpResponse payResponse =HttpRequest.post(paymentUrl)
                    //.header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body("masterOrderNo="+masterOrderNo+"&payChannel=1")
                    .execute();
        }
    }

    public void payByBill(){
        String token=getXianmuMallUserToken(this.phone,this.env);
        String masterOrderNo= JSONObject.parseObject(this.lastResult).getJSONObject("data").get("masterOrderNo").toString();
        String paymentUrl="https://" + env+"h5.summerfarm.net/payment/pay";
        HttpResponse payResponse =HttpRequest.post(paymentUrl)
                //.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("masterOrderNo="+masterOrderNo+"&payChannel=3")
                .execute();
    }




    public String getOrderdetail(){
        String orderdetailUrl="https://"+env+"h5.summerfarm.net/order/query/detail/orderNo?orderNo="+getOrderNo();
        String token=getXianmuMallUserToken(this.phone,this.env);
        HttpResponse response = HttpRequest.post(orderdetailUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        return response.body();
    }

    public String getOrderStatus(){
        String orderdetailUrl="https://"+env+"h5.summerfarm.net/order/query/detail/orderNo?orderNo="+getOrderNo();
        String token=getXianmuMallUserToken(this.phone,this.env);
        HttpResponse response = HttpRequest.post(orderdetailUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        return JSONObject.parseObject(response.body()).getJSONObject("data").getJSONObject("orderVO").getString("status");
    }

    public String getStringResult(){
        return this.lastResult;
    }

    public JSONObject getJsonResult(){return JSONObject.parseObject(this.lastResult);}

    public String getOrderNo(){
        String result=this.lastResult;
        String orderNo="";

        try{
            orderNo=JSONObject.parseObject(result).getJSONObject("data")
                    .getJSONArray("subOrderNos").getString(0);
        }catch (Exception e){
            e.printStackTrace();
        }
        return orderNo;
    }

    public String getMasterOrderNo(){
        String result=this.lastResult;
        String orderNo="";

        try{
            orderNo=JSONObject.parseObject(result).getJSONObject("data").getString("masterOrderNo");
        }catch (Exception e){
            e.printStackTrace();
        }
        return orderNo;
    }

    public String getSecondOrderNo(){
        String result=this.lastResult;
        String orderNo="";
        try{
            orderNo=JSONObject.parseObject(result).getJSONObject("data")
                    .getJSONArray("subOrderNos").getString(1);
        }catch (Exception e){
            e.printStackTrace();
        }
        return orderNo;
    }
    public static String getXianmuMallUserToken(String phone, String env){
        String user=phone;
        String currentDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String original = user+currentDate+"login";
        String sign= calculateMD5Hash(original);

        String url="https://"+env+"h5.summerfarm.net"+"/openid?";
        String result="";
        String finalResult="";
        try {
            result = HttpRequest.get(url + "phone="+user+"&sign="+sign)
                    .execute().body();
            //System.out.println(result);
            finalResult= JSONObject.parseObject(result).getJSONObject("data").getString("token");
        }catch (Exception e){
            return "";
        }
        return finalResult;
    }

    public static String calculateMD5Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());

            StringBuilder sb = new StringBuilder();
            for (byte b : messageDigest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

}

