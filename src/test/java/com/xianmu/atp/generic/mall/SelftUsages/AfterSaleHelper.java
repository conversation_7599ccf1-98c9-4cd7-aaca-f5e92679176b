package com.xianmu.atp.generic.mall.SelftUsages;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AfterSaleHelper {

    public static void setOrderReceivable(JdbcTemplate jdbcTemplate, String orderNo){
        jdbcTemplate.update("update orders set order_time=date_add(order_time,interval -2 day) where order_no=?",orderNo);
        jdbcTemplate.update("update delivery_plan set status=6,delivery_time=date_add(delivery_time,interval -1 day) where order_no=?",orderNo);
        jdbcTemplate.update("update tms_dist_order set state=40,expect_begin_time=date_add(expect_begin_time,interval -1 day) where outer_order_id=?",orderNo);
    }

    public static void accessRefundAfterSale(JdbcTemplate jdbcTemplate,String adminToken,String orderNo,int handleType,int quantity){
        Map<String,Object> params = new HashMap<>();
        List<Map<String, Object>> afterSaleList = jdbcTemplate.queryForList("select A.after_sale_order_no,B.handle_num from after_sale_order A left join after_sale_proof B on A.after_sale_order_no=B.after_sale_order_no where A.order_no=?",orderNo);
        String afterSaleOrderNo = afterSaleList.get(0).get("after_sale_order_no").toString();
        BigDecimal handleNum = new BigDecimal(afterSaleList.get(0).get("handle_num").toString());

        String handleUrl= "https://qaadmin.summerfarm.net/after-sale/order/handle";
//        Map<String,Object> handleParams = new HashMap<>();
//        handleParams.put("afterSaleOrderNo",afterSaleOrderNo);
//        handleParams.put("handleType",handleType);
//        handleParams.put("quantity",quantity);
//        handleParams.put("extraRemark", "test");
//        handleParams.put("handleNum",handleNum);
//        handleParams.put("handleSecondaryRemark", "质量,异物/有虫");

        String handleStr="afterSaleOrderNo="+afterSaleOrderNo+
                "&handleNum=53.20&quantity=1&extraRemark=&handleType="+ handleType+
                "&handleSecondaryRemark=质量,异物/有虫";

        HttpResponse response = HttpRequest.post(handleUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", adminToken)
                .body(handleStr)
                .execute();

        String auditUrl= "https://qaadmin.summerfarm.net/after-sale/order/audit";
        String auditStr ="afterSaleOrderNo="+afterSaleOrderNo+"&status=1&auditeRemark=testaudit";
        HttpResponse response2 = HttpRequest.post(auditUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", adminToken)
                .body(auditStr)
                .execute();

    }
}
