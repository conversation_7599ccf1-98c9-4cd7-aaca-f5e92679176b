package com.xianmu.atp.generic.mall.SelftUsages;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

public class ProductInfo {

    private String pdId;
    private String phone;

    private String env;

    public String getStringResult() {
        return this.result;
    }

    public JSONObject getJsonResult(){
        return JSONObject.parseObject(this.result);
    }

    private String result;
    public ProductInfo(String pdId, String phone){
        this.pdId = pdId;
        this.phone = phone;
        this.env=env;
    }

    public ProductInfo(){
        this.pdId = "";
        this.phone = "";
        this.env="qa";
    }


    public void addPdId(SkuEnum skuEnum){
        this.pdId = skuEnum.getSpuId();
    }

    public void addEnv(EnvEnum envEnum){
        this.env=envEnum.getName();
    }

    public void addUser(MallUserEnum mallUserEnum){
        this.phone=mallUserEnum.getPhone();
    }

    public void send(){
        String thisProductUrl = "https://" + this.env+"h5.summerfarm.net/product-info/"+this.pdId;
        String token=getXianmuMallUserToken(this.phone,this.env);
        HttpResponse response = HttpRequest.get(thisProductUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        this.result=response.body();
        System.out.println(thisProductUrl);
        System.out.println("result:"+response.body());
    }
}
