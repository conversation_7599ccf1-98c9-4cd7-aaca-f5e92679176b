package com.xianmu.atp.generic.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.dal.dao.AfterSaleOrderDao;

import javax.annotation.Resource;
import java.util.*;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

public class MallOrderUtil {
    @Resource
    private static AfterSaleOrderDao afterSaleOrderDao;

    /*
    * 鲜沐与pop共通
    * @param env:qa/prod
    * @param phone:手机号
    * @param contactId 地址ID
    * @param sku1:sku
    * @param quantity1:数量
    * 返回值：订单号
     */

    public static String send(String env, String phone, int contactId, String sku1, int quantity1){
        try{
            String thisOrderUrl = "https://" + env+"h5.summerfarm.net/order/upsert/place-order/v3";
            Map<String, Object> body = new HashMap<>();
            body.put("contactId", contactId);
            body.put("payment",0);
            body.put("deliveryRulesType",1);
            List<Map<String, Object>> orderItemList=new ArrayList<>();
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("sku", sku1);
            skuMap.put("quantity",quantity1);
            orderItemList.add(skuMap);
//        Map<String, Object> skuMap2 = new HashMap<>();
//        skuMap.put("sku", sku2);
//        skuMap.put("quantity",quantity2);
//        orderItemList.add(skuMap2);
            body.put("orderItemList", orderItemList);
            List<Map<String, Object>> usedCouponsList = new ArrayList<>();
            Map<String, Object> usedCouponMap = new HashMap<>();
//        if(couponId>0){
//            usedCouponMap.put("usedCouponId", couponId);
//            usedCouponMap.put("couponType", 1);
//            usedCouponsList.add(usedCouponMap);
//        }
            body.put("usedCouponMap",usedCouponMap);
            String token;
            if(phone.length()>12){
                token=phone;
            }else{
                token= getXianmuMallUserToken(phone,env);
            }
            HttpResponse response = HttpRequest.post(thisOrderUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(JSONObject.toJSONString(body))
                    .execute();
            String result =response.body();
            System.out.println(result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String orderNo=jsonObject.getJSONObject("data").getJSONArray("subOrderNos").get(0).toString();
            String masterOrderNo= jsonObject.getJSONObject("data").get("masterOrderNo").toString();
            String paymentUrl="https://" + env+"h5.summerfarm.net/payment/pay";
            HttpResponse payResponse =HttpRequest.post(paymentUrl)
                    //.header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body("masterOrderNo="+masterOrderNo+"&payChannel=1")
                    .execute();
            System.out.println(payResponse.body());
            return orderNo;
        }catch (Exception e){

        }
        return "";
    }


    public static String getOrderResult(String env, String phone, int contactId, String sku1, int quantity1){
        try{
            String thisOrderUrl = "https://" + env+"h5.summerfarm.net/order/upsert/place-order/v3";
            Map<String, Object> body = new HashMap<>();
            body.put("contactId", contactId);
            body.put("payment",0);
            body.put("deliveryRulesType",1);
            List<Map<String, Object>> orderItemList=new ArrayList<>();
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("sku", sku1);
            skuMap.put("quantity",quantity1);
            orderItemList.add(skuMap);
//        Map<String, Object> skuMap2 = new HashMap<>();
//        skuMap.put("sku", sku2);
//        skuMap.put("quantity",quantity2);
//        orderItemList.add(skuMap2);
            body.put("orderItemList", orderItemList);
            List<Map<String, Object>> usedCouponsList = new ArrayList<>();
            Map<String, Object> usedCouponMap = new HashMap<>();
//        if(couponId>0){
//            usedCouponMap.put("usedCouponId", couponId);
//            usedCouponMap.put("couponType", 1);
//            usedCouponsList.add(usedCouponMap);
//        }
            body.put("usedCouponMap",usedCouponMap);
            String token;
            if(phone.length()>12){
                token=phone;
            }else{
                token= getXianmuMallUserToken(phone,env);
            }
            HttpResponse response = HttpRequest.post(thisOrderUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(JSONObject.toJSONString(body))
                    .execute();
            String result =response.body();
            System.out.println(result);
            return result;


        }catch (Exception e){

        }
        return "";
    }

    public String SampleOrder(String env, String phone, int contactId, String sku1, int quantity1){

        return "";
    }

    public static void main(String[] args) {
        //String orderNo =MallOrderUtil.send("qa", MallUserEnum.mallAutoUser.getPhone(), AddressEnum.mallAutoAddress.getContactId(), SkuEnum.skuForSpuStartNum.getSku(), 6);
        String orderNo = MallOrderUtil.send("qa","13734659494",350649,"2185235215881",5);
        System.out.println(orderNo);
    }

    public static String preOrder(String env, String phone, int contactId, String sku1, int quantity1){
        String thisOrderUrl = "https://" + env+"h5.summerfarm.net/order/query/pre-order/v3";
        Map<String, Object> body = new HashMap<>();
        body.put("contactId", contactId);
        List<Map<String, Object>> orderItemList=new ArrayList<>();
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put("sku", sku1);
        skuMap.put("quantity",quantity1);
        skuMap.put("productType",0);
        orderItemList.add(skuMap);
        body.put("orderItemList", orderItemList);
        body.put("isTakePrice", 0);
        body.put("outTimes",0);
        String token= getXianmuMallUserToken(phone,env);
        HttpResponse response = HttpRequest.post(thisOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(body))
                .execute();
        return response.body();
    }


    public static String preOrderWithToken(String env, String token, int contactId, String sku1, int quantity1){

        String thisOrderUrl = "https://" + env+"h5.summerfarm.net/order/query/pre-order/v3";
        Map<String, Object> body = new HashMap<>();
        body.put("contactId", contactId);
        List<Map<String, Object>> orderItemList=new ArrayList<>();
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put("sku", sku1);
        skuMap.put("quantity",quantity1);
        skuMap.put("productType",0);
        orderItemList.add(skuMap);
        body.put("orderItemList", orderItemList);
        body.put("isTakePrice", 0);
        body.put("outTimes",0);
        body.put("deliveryRulesType",1);

        HttpResponse response = HttpRequest.post(thisOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(body))
                .execute();
        return response.body();
    }

    public static String payOrder(String env, String token,String orderNo,int payType){
        String thisOrderUrl = "https://" + env+"h5.summerfarm.net/payment/union/pay";
        Map<String, Object> body = new HashMap<>();
        body.put("bizOrderNo", orderNo);
        body.put("bizType", 0);
        body.put("paymentMethod", payType);


        HttpResponse response = HttpRequest.post(thisOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(body))
                .execute();
        return response.body();
    }

    public static String preTimingWithToken(String env, String token, String mphone, int timingRuleId, int quantity){

        String thisOrderUrl = "https://" + env+"h5.summerfarm.net/order/checkTimingOrder";
        Map<String, Object> body = new HashMap<>();
        body.put("merchantCouponId", "");
        body.put("mphone", mphone);
        body.put("quantity", quantity);
        body.put("rpCouponId","");
        body.put("timingRuleId", timingRuleId);

        HttpResponse response = HttpRequest.post(thisOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(body))
                .execute();
        return response.body();
    }
}
