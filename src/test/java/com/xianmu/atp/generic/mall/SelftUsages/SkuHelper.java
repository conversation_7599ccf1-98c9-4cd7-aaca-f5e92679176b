package com.xianmu.atp.generic.mall.SelftUsages;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.enums.SkuEnum;


import static com.xianmu.atp.generic.common.JsonDealer.findKeyInJson;

public class SkuHelper {


    //@return 1:存在且匹配，2:存在但不匹配，3:不存在
    public static int checkSkuExtraInfo(SkuEnum skuEnum, JSONObject jsonObject) {
        String extraKey = skuEnum.getExtraKey();
        String extraValue = skuEnum.getExtraValue();
        int judge = findKeyInJson(jsonObject, extraKey, extraValue);
        Assert.isFalse(judge==2,skuEnum.getSku()+"的"+extraKey+"预期为"+extraValue+",与结果不同");
        Assert.isFalse(judge==3,skuEnum.getSku()+"的"+extraKey+"在结果中未发现");
        return findKeyInJson(jsonObject, extraKey, extraValue);
    }


}
