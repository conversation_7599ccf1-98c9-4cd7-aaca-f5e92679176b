package com.xianmu.atp.generic.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AfterSaleOrderDao;
import com.xianmu.atp.dal.model.AfterSaleOrder;
import com.xianmu.atp.util.http.LoginHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Reporter;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class AfterOrderUtil extends BaseTest {
    @Resource
    private AfterSaleOrderDao afterSaleOrderDao;
    @Value("${app.manage-domain}")
    private String domain;
    @Resource
    private LoginHttp login;
    public String AfterSaleResendOrder(String env, String phone, int contactId, String sku1, int quantity1) {
        String orderNo = MallOrderUtil.send(env, phone, contactId, sku1, quantity1);
        String afterSaleUrl = domain+
                "/after-sale/order/save";
        String token = login.login();
        log.info("token:{}", token);
        String afterSaleBody = "{\n" +
                "    \"orderNo\": \"" + orderNo + "\",\n" +
                "    \"sku\": \"" + sku1 + "\",\n" +
                "    \"suitId\": 0,\n" +
                "    \"afterSaleUnit\": \"件\",\n" +
                "    \"handleRemark\": \"\",\n" +
                "    \"handleType\": 7,\n" +
                "    \"proofPic\": \"\",\n" +
                "    \"quantity\": 1,\n" +
                "    \"handleNum\": 0,\n" +
                "    \"deliveryed\": 1,\n" +
                "    \"afterSaleRemarkType\": 9,\n" +
                "    \"afterSaleRemark\": \"\",\n" +
                "    \"recoveryType\": 0,\n" +
                "    \"recoveryNum\": 0,\n" +
                "    \"deliveryId\": \"\",\n" +
                "    \"afterSaleType\": \"商品品质问题\",\n" +
                "    \"carryingGoods\": 0\n" +
                "}";
        HttpResponse response = HttpRequest.post(afterSaleUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(afterSaleBody)
                .execute();
        Reporter.log("log-test");
        log.info("response:{}", response.body());
        List<AfterSaleOrder> afterSaleOrderList = afterSaleOrderDao.getAfterSaleOrder(orderNo);
        return afterSaleOrderList.get(0).getAfterSaleOrderNo();
    }

    public String AfterSaleRecycle(String env, String phone, int contactId, String sku1, int quantity1) {
        String orderNo = MallOrderUtil.send(env, phone, contactId, sku1, quantity1);
        String afterSaleUrl = domain+
                "/after-sale/order/save";
        String token = login.login();
        log.info("token:{}", token);
        String afterSaleBody = "{\n" +
                "    \"orderNo\": \""+orderNo+"\",\n" +
                "    \"sku\": \""+sku1+"\",\n" +
                "    \"suitId\": 0,\n" +
                "    \"isManage\": true,\n" +
                "    \"afterSaleUnit\": \"件\",\n" +
                "    \"handleRemark\": \"\",\n" +
                "    \"handleType\": 6,\n" +
                "    \"proofPic\": \"\",\n" +
                "    \"proofVideo\": \"\",\n" +
                "    \"quantity\": 1,\n" +
                "    \"handleNum\": 0,\n" +
                "    \"deliveryed\": 1,\n" +
                "    \"afterSaleRemarkType\": \"\",\n" +
                "    \"afterSaleRemark\": \"\",\n" +
                "    \"recoveryType\": 0,\n" +
                "    \"recoveryNum\": 0,\n" +
                "    \"exchangeGoodList\": [\n" +
                "        {\n" +
                "            \"sku\": \""+sku1+"\",\n" +
                "            \"quantity\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"deliveryId\": \"\",\n" +
                "    \"afterSaleType\": \"商品数量不符\",\n" +
                "    \"applySecondaryRemark\": \"错拍\"\n" +
                "}";
        HttpResponse response = HttpRequest.post(afterSaleUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(afterSaleBody)
                .execute();
        Reporter.log("log-test");
        log.info("response:{}", response.body());
        List<AfterSaleOrder> afterSaleOrderList = afterSaleOrderDao.getAfterSaleOrder(orderNo);
        return afterSaleOrderList.get(0).getAfterSaleOrderNo();
    }
}
