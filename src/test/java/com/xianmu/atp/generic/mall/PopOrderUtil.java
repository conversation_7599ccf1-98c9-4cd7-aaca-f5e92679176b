package com.xianmu.atp.generic.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.MerchantDao;
import com.xianmu.atp.dal.dao.SkuInfoDao;
import com.xianmu.atp.dal.dao.pms.SupplyListDao;
import com.xianmu.atp.dal.model.PmsSupplyList;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.util.http.LoginHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Reporter;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

@Component
@Slf4j
public class PopOrderUtil extends BaseTest {
    @Resource
    private SkuInfoDao skuInfoDao;
    @Resource
    private SupplyListDao supplyListDao;
    @Resource
    private MerchantDao merchantDao;

    @Value("${app.manage-domain}")
    private String domain;
    @Resource
    private LoginHttp login;
    public String PopOrder(String env, String phone, int contactId, String sku1, int quantity1,Integer warehouse){
        log.info("sku去查spu");
        SkuInfo productInfo = skuInfoDao.getProduct(sku1);
        String spu = productInfo.getPdNo();
        log.info("查询对应的供应商库");
        List<PmsSupplyList> supplyList = supplyListDao.getSupplierList(spu);
        long supplier = supplyList.get(0).getSupplierId();
        String stockUrl = domain+
                "/pms-service/srm/pop/upsert/sku/stock-change/stock";
        String token = login.login();
        log.info("token:{}", token);
        String stockBody = "{\n" +
                "\t\"sku\": \""+sku1+"\",\n" +
                "\t\"supplierId\": "+supplier+",\n" +
                "\t\"warehouseNo\": "+warehouse+",\n" +
                "\t\"originalTotalSaleStock\": 999,\n" +
                "\t\"adjustTotalSaleStock\": \"999\"\n" +
                "}";
        log.info("更新供应商库存");
        HttpResponse response = HttpRequest.post(stockUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(stockBody)
                .execute();
        Reporter.log("log-test");
        log.info("更新库存返回结果：response:{}", response.body());
        log.info("更新余额");
        merchantDao.updateAmountByMerchantIds(phone,1000000);
        log.info("下单");
        log.info("微信登錄");
        String popToken= getXianmuMallUserToken(phone,env);
        String thisOrderUrl = "https://" + env+"shunluda.cosfo.cn/order/upsert/place-order/v3";
        Map<String, Object> body = new HashMap<>();
        body.put("contactId", contactId);
        body.put("payment",0);
        body.put("deliveryRulesType",1);
        List<Map<String, Object>> orderItemList=new ArrayList<>();
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put("sku", sku1);
        skuMap.put("quantity",quantity1);
        orderItemList.add(skuMap);
//        Map<String, Object> skuMap2 = new HashMap<>();
//        skuMap.put("sku", sku2);
//        skuMap.put("quantity",quantity2);
//        orderItemList.add(skuMap2);
        body.put("orderItemList", orderItemList);
        List<Map<String, Object>> usedCouponsList = new ArrayList<>();
        Map<String, Object> usedCouponMap = new HashMap<>();
//        if(couponId>0){
//            usedCouponMap.put("usedCouponId", couponId);
//            usedCouponMap.put("couponType", 1);
//            usedCouponsList.add(usedCouponMap);
//        }
        body.put("usedCouponMap",usedCouponMap);
        HttpResponse popResponse = HttpRequest.post(thisOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", popToken)
                .body(JSONObject.toJSONString(body))
                .execute();
        String result =popResponse.body();
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        String orderNo=jsonObject.getJSONObject("data").getJSONArray("subOrderNos").get(0).toString();
        String masterOrderNo= jsonObject.getJSONObject("data").get("masterOrderNo").toString();
        String paymentUrl="https://" + env+"shunluda.cosfo.cn/payment/pay";
        HttpResponse payResponse =HttpRequest.post(paymentUrl)
                //.header(Header.CONTENT_TYPE, "application/json")
                .header("token", popToken)
                .body("masterOrderNo="+masterOrderNo+"&payChannel=1")
                .execute();
        System.out.println(payResponse.body());
        return orderNo;
    }
}
