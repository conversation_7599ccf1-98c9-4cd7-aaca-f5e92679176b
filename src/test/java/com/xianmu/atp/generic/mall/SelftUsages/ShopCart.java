package com.xianmu.atp.generic.mall.SelftUsages;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;

import java.util.HashMap;
import java.util.Map;

public class ShopCart {

    private String phone;
    private String env;

    public void addEnv(EnvEnum envEnum){
        this.env=envEnum.getName();
    }

    public void addUser(MallUserEnum mallUserEnum){
        this.phone= mallUserEnum.getPhone();
    }

    public void addSku(SkuEnum skuEnum,int num){
        Map<String,Object> map=new HashMap<>();
        map.put("sku",skuEnum.getSku());
        map.put("quantity",num);
        map.put("productTYpe",0);
        String token = MallOrder.getXianmuMallUserToken(phone, env);
        HttpResponse response = HttpRequest.post("https://" + this.env+"h5.summerfarm.net/shopping/cart/upsert/insert")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(map))
                .execute();
        System.out.println(response.body());
    }

    public JSONObject getCartInfo(){
        String token = MallOrder.getXianmuMallUserToken(phone, env);
        try{
            HttpResponse response = HttpRequest.post("https://" + this.env+"h5.summerfarm.net/shopping/cart/query/get-all")
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body("{\"deliveryRulesType\":1}")
                    .execute();
            return JSONObject.parseObject(response.body());
        }catch (Exception e) {
        }
        return null;
    }

    public void clearCart(){
        JSONObject cart=getCartInfo();
        JSONArray cartItems=cart.getJSONArray("data");
        int[] cartItemIds=new int[cartItems.size()];
        for(int i=0;i<cartItems.size();i++){
            cartItemIds[i] = cartItems.getJSONObject(i).getIntValue("id");
        }
        Map<String,Object> map=new HashMap<>();
        map.put("ids",cartItemIds);
        String token = MallOrder.getXianmuMallUserToken(phone, env);
        HttpResponse response = HttpRequest.post("https://" + this.env+"h5.summerfarm.net/shopping/cart/upsert/delete")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(map))
                .execute();
    }
}
