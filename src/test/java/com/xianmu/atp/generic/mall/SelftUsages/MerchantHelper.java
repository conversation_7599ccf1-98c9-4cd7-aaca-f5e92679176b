package com.xianmu.atp.generic.mall.SelftUsages;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.xianmu.atp.enums.EnvEnum;

import java.util.HashMap;

public class MerchantHelper {

    public static long createMerchantToCancel() {
        String env= EnvEnum.qa.getName();
        String mname="自动化注销用用户";
        String contact="自动化注销用联系人";
        String phone="13765432100";
        String invitecode="seeegj";
        String addressRemark="{\"customRemark\":\"\",\"baseRemark\":[]}";
        String province="浙江";
        String city="杭州市";
        String area="西湖区";
        String address="自动化注销用地址";
        String houseNumber="";
        String areaPhone = "";
        String poiNote="120.058426,30.279688";
        String size="";

        String body = "mname=" + mname +
                "&contact=" + contact +
                "&phone=" + phone +
                "&invitecode=" + invitecode +
                "&addressRemark=" + addressRemark +
                "&province=" + province +
                "&city=" + city +
                "&area=" + area +
                "&address=" + address +
                "&houseNumber=" + houseNumber +
                "&areaPhone=" + areaPhone +
                "&poiNote=" + poiNote +
                "&size=" + size;

        String adminToken= getAdminToken(env);

        String url = "https://" + env + "admin.summerfarm.net" + "/merchant/addMerchant";
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type","application/x-www-form-urlencoded")
                .header("token",adminToken)
                .body(body)
                .execute();
        System.out.println(response.body());

        String queryListUrl="https://" + env + "admin.summerfarm.net" + "/merchant/1/10?islock=1";
        HttpResponse queryListResponse = HttpRequest.get(queryListUrl)
                .header("token",adminToken)
                .execute();

        JSONObject listBody=JSONObject.parseObject(queryListResponse.body());
        String newMid=listBody.getJSONObject("data").getJSONArray("list").getJSONObject(0).getString("mId");

        String auditUrl="https://" + env + "admin.summerfarm.net" + "/merchant/review/"+newMid;
        String auditJson="{\n" +
                "    \"state\":0,\n" +
                "    \"areaNo\":1001,\n" +
                "    \"size\":\"大客户\",\n" +
                "    \"direct\":2,\n" +
                "    \"type\":\"茶饮\",\n" +
                "    \"adminId\":11494,\n" +
                "    \"server\":1,\n" +
                "    \"mname\":\""+mname+"\",\n" +
                "    \"mcontact\":\"自动化注销用联系人\",\n" +
                "    \"phone\":\""+phone+"\",\n" +
                "    \"skuShow\":1,\n" +
                "    \"poiNote\":\""+poiNote+"\",\n" +
                "    \"address\":\"自动化注销用地址\",\n" +
                "    \"showPrice\":true,\n" +
                "    \"cluePool\":1,\n" +
                "    \"merchantType\":\"普通\",\n" +
                "    \"outerMappings\":[\n" +
                "\n" +
                "    ],\n" +
                "    \"displayButton\":0,\n" +
                "    \"groupHeadFlag\":0,\n" +
                "    \"enterpriseScale\":\"加盟店\",\n" +
                "}";

        HttpResponse auditResponse =  HttpRequest.post(auditUrl)
                .header("Content-Type","application/x-www-form-urlencoded")
                .header("token",adminToken)
                .body(auditJson)
                .execute();
        return Long.parseLong(newMid);
    }


    public static String getAdminToken(String env) {

        String loginUrl = "https://" + env + "admin.summerfarm.net" + "/authentication/auth/username/login";
        String token = "";
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("username", "<EMAIL>");
            paramMap.put("password", "hello1234");
            HttpResponse response = HttpRequest.post(loginUrl)
                    .form(paramMap)
                    .timeout(2000)
                    .execute();
            token = JSON.parseObject(response.body()).getJSONObject("data").getString("token");
        } catch (Exception e){
            e.printStackTrace();
        }
        return token;
    }


    public static long createMerchant(String phoneNum) {
        String env= EnvEnum.qa.getName();
        String mname="自动化注销用用户";
        String contact="自动化注销用联系人";
        String phone=phoneNum;
        String invitecode="seeegj";
        String addressRemark="{\"customRemark\":\"\",\"baseRemark\":[]}";
        String province="浙江";
        String city="杭州市";
        String area="西湖区";
        String address="自动化注销用地址";
        String houseNumber="";
        String areaPhone = "";
        String poiNote="120.058426,30.279688";
        String size="";

        String body = "mname=" + mname +
                "&contact=" + contact +
                "&phone=" + phone +
                "&invitecode=" + invitecode +
                "&addressRemark=" + addressRemark +
                "&province=" + province +
                "&city=" + city +
                "&area=" + area +
                "&address=" + address +
                "&houseNumber=" + houseNumber +
                "&areaPhone=" + areaPhone +
                "&poiNote=" + poiNote +
                "&size=" + size;

        String adminToken= getAdminToken(env);

        String url = "https://" + env + "admin.summerfarm.net" + "/merchant/addMerchant";
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type","application/x-www-form-urlencoded")
                .header("token",adminToken)
                .body(body)
                .execute();
        System.out.println(response.body());

        String queryListUrl="https://" + env + "admin.summerfarm.net" + "/merchant/1/10?islock=1";
        HttpResponse queryListResponse = HttpRequest.get(queryListUrl)
                .header("token",adminToken)
                .execute();

        JSONObject listBody=JSONObject.parseObject(queryListResponse.body());
        String newMid=listBody.getJSONObject("data").getJSONArray("list").getJSONObject(0).getString("mId");

        String auditUrl="https://" + env + "admin.summerfarm.net" + "/merchant/review/"+newMid;
        String auditJson="{\n" +
                "    \"state\":0,\n" +
                "    \"areaNo\":1001,\n" +
                "    \"size\":\"大客户\",\n" +
                "    \"direct\":2,\n" +
                "    \"type\":\"茶饮\",\n" +
                "    \"adminId\":11494,\n" +
                "    \"server\":1,\n" +
                "    \"mname\":\""+mname+"\",\n" +
                "    \"mcontact\":\"自动化注销用联系人\",\n" +
                "    \"phone\":\""+phone+"\",\n" +
                "    \"skuShow\":1,\n" +
                "    \"poiNote\":\""+poiNote+"\",\n" +
                "    \"address\":\"自动化注销用地址\",\n" +
                "    \"showPrice\":true,\n" +
                "    \"cluePool\":1,\n" +
                "    \"merchantType\":\"普通\",\n" +
                "    \"outerMappings\":[\n" +
                "\n" +
                "    ],\n" +
                "    \"displayButton\":0,\n" +
                "    \"groupHeadFlag\":0,\n" +
                "    \"enterpriseScale\":\"加盟店\",\n" +
                "}";

        HttpResponse auditResponse =  HttpRequest.post(auditUrl)
                .header("Content-Type","application/x-www-form-urlencoded")
                .header("token",adminToken)
                .body(auditJson)
                .execute();
        return Long.parseLong(newMid);
    }
}
