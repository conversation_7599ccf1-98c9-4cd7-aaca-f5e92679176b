package com.xianmu.atp.generic.mall.SelftUsages;

import com.xianmu.atp.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

public class OfcHelper extends BaseTest {

    @Autowired
    @Qualifier("saasJdbcTemplate")
    private JdbcTemplate saasJdbcTemplate;

    @Autowired
    @Qualifier("xianmuJdbcTemplate")
    private JdbcTemplate xianmuJdbcTemplate;

    public void checkOrderInfo(String orderNo){
        String sql = "select * from ofcdb.fulfillment_order where out_order_no='"+orderNo+"'";
        String result = saasJdbcTemplate.queryForObject(sql, String.class);
        System.out.println(result);
    }
}
