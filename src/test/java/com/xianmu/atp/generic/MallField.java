package com.xianmu.atp.generic;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.ContactDao;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Allure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 * 商城对外公共方法
 * <AUTHOR>
 * @ClassName MallField
 * @date 2024-11-27
 */

@Component
@Slf4j
public class MallField extends BaseTest {

    @Resource
    private ContactDao contactDao;

    @Resource
    private LoginHttp login;

    @Value("https://devh5.summerfarm.net")
    private String devDomain;


    public String order(String phone){
        System.out.println("order");
        String orderUrl = devDomain + "/order/upsert/place-order/v3";
        String payUrl = devDomain + "/payment/pay";
        String token = login.mockLogin(phone);
        // 日志输出
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        /* TODO：
         * 1. 确认商品库存
         * 2. 确认鲜沐卡余额
         */

        // 构建请求体
        String requestBody = "{\"orderItemList\":[{\"sku\":\"849713171106\",\"productType\":0,\"quantity\":5}],\"contactId\":350315,\"outTimes\":0,\"usedCoupons\":[],\"payment\":0,\"isTakePrice\":1,\"subOrderItemList\":[{\"deliveryDate\":\"2024-11-28\",\"orderItemList\":[{\"sku\":\"849713171106\",\"productType\":0}]}],\"deliveryRulesType\":1}";
        JSONObject jsonObject = JSONObject.parseObject(requestBody);
        String today= DateUtil.tomorrow().toString("yyyy-MM-dd");
        // 替换日期和contactId
        JSONPath.set(jsonObject, "$.subOrderItemList[0].deliveryDate", today);
        String contactId = contactDao.getContactId(phone);////
        JSONPath.set(jsonObject, "$.contactId", contactId);
        requestBody = jsonObject.toJSONString();
        HttpResponse response = HttpRequest.post(orderUrl)
               .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("xm-biz", "xm-mall")
                .header("xm-uid", "350736")
                .body(requestBody)
                .execute();
        log.info("response:{}", response.body());
        // 获取主单号并付款
        if (JSONObject.parseObject(response.body()).getJSONObject("data").containsKey("orderItemErrorList")){
            throw new RuntimeException("下单失败");
        }
        String masterOrder = JSONObject.parseObject(response.body()).getJSONObject("data").getString("masterOrderNo");
        Map<String, Object> payBody = new HashMap<>();
        payBody.put("masterOrderNo", masterOrder);
        payBody.put("payChannel", 1);
        HttpResponse payResponse = HttpRequest.post(payUrl)
               .header("token", token)
               .header("xm-biz", "xm-mall")
               .header("xm-uid", "350736")
                .form(payBody)
                .execute();
        log.info("payResponse:{}", payResponse.body());
        if (!JSONObject.parseObject(response.body()).getString("msg").equals("请求成功")){
            throw new RuntimeException("下单失败");
        }
        return masterOrder;
    }
}
