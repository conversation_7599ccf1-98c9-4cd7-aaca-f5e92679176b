package com.xianmu.atp.generic.common;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;

import java.util.HashMap;
import java.util.Map;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

public class zylTest {
    public static void main(String[] args) {
        String curlCommand="curl 'https://qah5.summerfarm.net/price/query/take-actual-price' \\\n" +
                "  -H 'authority: qah5.summerfarm.net' \\\n" +
                "  -H 'xm-platform: web' \\\n" +
                "  -H 'xm-uid: 350935' \\\n" +
                "  -H 'xm-ab-exp: [{\"experimentId\":\"new-home\",\"experimentPlace\":\"place-of:new-home\",\"variantId\":\"V3\"},{\"experimentId\":\"product_search_rerank\",\"experimentPlace\":\"place-of:product_search_rerank\",\"variantId\":\"V5\"},{\"experimentId\":\"product_search_disperse\",\"experimentPlace\":\"place-of:product_search_disperse\",\"variantId\":\"V3\"}]' \\\n" +
                "  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.06.2402040 MicroMessenger/8.0.5 Language/zh_CN webview/17422681532782848 webdebugger port/29331 token/70ec0818b9f1c658edc4bb51ce19514a' \\\n" +
                "  -H 'content-type: application/json' \\\n" +
                "  -H 'accept: application/json, text/plain, */*' \\\n" +
                "  -H 'xm-rqid: 1742290654960-57997757' \\\n" +
                "  -H 'xm-page: %2Fpublic-goods-details' \\\n" +
                "  -H 'xm-phone: 13732254809' \\\n" +
                "  -H 'token: mall__775d4293-52c5-45cd-a9fe-a8a025a87998' \\\n" +
                "  -H 'xm-biz: xm-mall' \\\n" +
                "  -H 'origin: https://qah5.summerfarm.net' \\\n" +
                "  -H 'sec-fetch-site: same-origin' \\\n" +
                "  -H 'sec-fetch-mode: cors' \\\n" +
                "  -H 'sec-fetch-dest: empty' \\\n" +
                "  -H 'referer: https://qah5.summerfarm.net/home.html?code=0612Ce0w3DTtz437U01w3x3WQI12Ce0-&state=STATE' \\\n" +
                "  -H 'accept-language: zh-CN,zh;q=0.9' \\\n" +
                "  --data-binary '{\"orderNow\":[{\"sku\":\"596146708263\",\"suitId\":0,\"quantity\":1}],\"timingSkuPrice\":1,\"timingRuleId\":3540,\"minLadderPrice\":0}' \\\n" +
                "  --compressed";
        try{
            RequestConverter.convertCurlToJava(curlCommand);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
