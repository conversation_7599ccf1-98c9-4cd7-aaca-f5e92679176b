package com.xianmu.atp.generic.common;

import cn.hutool.core.lang.Console;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.common.DingdingProcessFlowService;
import com.xianmu.atp.dal.model.DingdingProcessFlow;
import com.xianmu.atp.enums.api.common.CommonEnumsInterface;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.Assert;


import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class FlowRequest {
    @Resource
    private DingdingProcessFlowService dingdingProcessFlowService;

    @Resource
    private Request request;

    @Value("${app.xm_admin_domain}")// 使用正确的占位符语法
    public String xmAdminDomain;
    /**
     * 审批流程请求
     * 此方法用于向特定的业务流程管理系统发送审批请求，根据isPass参数决定是否通过审批
     * 它构造了一个请求体，包含了业务ID、业务类型和审批结果（同意或拒绝），然后发送请求
     * 并处理响应，以确定审批操作是否成功
     *
     * @param bizId 业务ID，用于标识特定的业务流程
     * @param bizType 业务类型，指明业务的种类
     * @param isPass 布尔值，决定是否通过审批
     * @return 如果审批成功且状态为"200"，返回true；否则返回false
     */
    public boolean  approveFlowRequest(String bizId, Integer bizType,boolean isPass) {

        String url = request.urlBuild(xmAdminDomain,CommonEnumsInterface.FlowEnums.flow.getUrl());
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("bizId", bizId);
        requestBody.put("processType", String.valueOf(bizType));
        requestBody.put("channelType", "TEST_PLATFORM_SYSTEM");
        if (isPass){
            requestBody.put("result", "AGREE");
        }else {
            requestBody.put("result", "REFUSE");
        }
        HttpResponseWrapper res = request.sendRequest(url,
                                CommonEnumsInterface.FlowEnums.flow.getMethod(),
                                CommonEnumsInterface.FlowEnums.flow.getContentType(),
                                requestBody);
        if (res != null && res.getStatusCode() == 200) {
            JSONObject result = JSON.parseObject(res.getBody());
            return result.getString("status").equals("200");
        } else {
            return false;
        }
    }

    /**
     * 审批流处理
     * 此方法根据参数决定是否通过飞书审批请求
     * @param bizId 业务ID，用于标识特定的业务流程
     * @param bizType 业务类型，指明业务的种类
     * @param isPass 布尔值，决定是否通过审批
     */
    @Retry(attempts = 5,delay = 2000, onExceptions = {AssertionError.class})
    public void approveFlow(String bizId, Integer bizType, boolean isPass) {
        //查询审批流信息
        DingdingProcessFlow flowInfo = dingdingProcessFlowService.selectOneByBizIdAndBizType(Long.parseLong(bizId) ,
                bizType);
        Assert.assertNotNull(flowInfo,"审批流查询为空--Id:"+bizId);
        //根据isPass决定通过还是拒绝审批流
        boolean flowRes= approveFlowRequest(bizId,
                bizType,
                isPass);
        Assert.assertTrue(flowRes,String.format("审批流处理失败--业务Id:%s，业务类型ID：%s",bizId,bizType.toString()));
    }

}
