package com.xianmu.atp.generic.common;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.enums.DataSourceEnums;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

public class SqlExcutorUtil {
    public static JdbcTemplate createJdbcTemplate(DynamicDataSourceProperties properties, DataSourceEnums dataSourceEnums){
        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        dataSource.setPrimary(properties.getPrimary());
        dataSource.setStrict(false);

        Map<String, DataSourceProperty> dataSourcePropertyMap = properties.getDatasource();
        HikariDataSource ds = new HikariDataSource();
        for (String key : dataSourcePropertyMap.keySet()) {
            if(key.equals(dataSourceEnums.getValue())){
                DataSourceProperty value = dataSourcePropertyMap.get(key);
                ds.setDriverClassName("com.mysql.cj.jdbc.Driver");
                ds.setJdbcUrl(value.getUrl());
                ds.setUsername(value.getUsername());
                ds.setPassword(value.getPassword());

            }
        }
        JdbcTemplate JdbcTemplate=new JdbcTemplate(ds);
        return JdbcTemplate;
    };


    public static HikariDataSource getDs(DynamicDataSourceProperties properties, DataSourceEnums dataSourceEnums){
        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        dataSource.setPrimary(properties.getPrimary());
        dataSource.setStrict(false);

        Map<String, DataSourceProperty> dataSourcePropertyMap = properties.getDatasource();
        HikariDataSource ds = new HikariDataSource();
        for (String key : dataSourcePropertyMap.keySet()) {
            if(key.equals(dataSourceEnums.getValue())){
                DataSourceProperty value = dataSourcePropertyMap.get(key);
                ds.setDriverClassName("com.mysql.cj.jdbc.Driver");
                ds.setJdbcUrl(value.getUrl());
                ds.setUsername(value.getUsername());
                ds.setPassword(value.getPassword());

            }
        }
        return ds;
    };

    /*
    * 循环执行sql，直到返回结果符合要求，或者超过重试次数
    * jdbcTemplate:jdbc模板,在外面初始化后传进来
    * sql:sql语句
    * key:结果中需要校验的key
    * value:结果中需要校验的value
    * retryCount:重试次数
    */

    public static boolean checkSqlValue(JdbcTemplate jdbcTemplate,String sql,String key,String value,int retryCount) throws InterruptedException {
        int count=0;
        while (count<retryCount){
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
            if(results.size() == 0||results.get(0).get(key)==null||!results.get(0).get(key).toString().equals(value)){
                count++;
                System.out.println("重试次数："+count);
                Thread.sleep(1000);
                System.out.println("sqlExcutor:"+results.get(0).get(key));
                continue;
            }
            System.out.println("sqlExcutor:"+results.get(0).get(key));
            return true;
        }
        return false;
    }


    public static String getOnlyValue(JdbcTemplate jdbcTemplate, String sql) {
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);

        if (results != null && !results.isEmpty()) {
            // 获取第一行
            Map<String, Object> firstRow = results.get(0);

            // 获取第一个字段的值
            if (!firstRow.isEmpty()) {
                // 假设我们只需要第一个字段的值
                String key = firstRow.keySet().iterator().next(); // 获取第一个字段名
                Object value = firstRow.get(key); // 获取第一个字段的值

                return value != null ? value.toString() : null; // 返回值的字符串表示
            }
        }

        return null; // 如果没有结果，返回null
    }
}
