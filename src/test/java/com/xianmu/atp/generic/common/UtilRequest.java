package com.xianmu.atp.generic.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.enums.api.common.CommonEnumsInterface;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.enums.api.testTool.TestTool;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class UtilRequest {

    @Resource
    private Request request;

    @Resource
    private EnvVariable envVar;


    // 入库任务类型和入库单类型的映射关系 - 设置为类级别的静态变量
    private static final Map<String, Integer> TYPE_MAPPING = new HashMap<>();

    static {
        TYPE_MAPPING.put("11", 0); //采购入库
        TYPE_MAPPING.put("10", 1); //调拨入库
        TYPE_MAPPING.put("13", 2); //拒收入库
        TYPE_MAPPING.put("19", 4); //退货入库
        TYPE_MAPPING.put("20", 5); //缺货入库
        TYPE_MAPPING.put("22", 7); //拦截入库
        TYPE_MAPPING.put("23", 8); //多出入库
    }

    /**
     * 生成Excel文件并上传到OSS
     * 该方法通过发送HTTP请求来生成Excel文件，并将其上传到OSS（对象存储服务）
     * 它首先发送请求到指定的URL，然后验证响应状态码和消息体中的代码，
     * 以确保Excel文件成功生成并上传
     *
     * @param requestBody 包含生成Excel所需信息的请求体
     * @return 返回一个包含响应数据的JSONObject对象
     */
    public JSONObject excelCreate(Map<String, Object> requestBody) {

        HttpResponseWrapper res = request.sendRequest(CommonEnumsInterface.UtilEnums.excelCreate.getUrl(),
                CommonEnumsInterface.UtilEnums.excelCreate.getMethod(),
                CommonEnumsInterface.UtilEnums.excelCreate.getContentType(),
                requestBody);
        Assert.assertTrue(res != null && res.getStatusCode() == 200,"excel生成或上传失败");
        JSONObject resBody=JSON.parseObject(res.getBody());
        Assert.assertEquals(resBody.getString("code"),"200","excel生成或上传失败: %s".replace("%s",resBody.getString("msg")));
        return JSON.parseObject(res.getBody());
    }

    /**
     * 获取上传结果&导出结果(非通用，视业务而定)
     * 它首先发送请求到指定的URL，然后验证响应状态码和消息体中的代码，
     * @param requestBody 包含生成Excel所需信息的请求体
     */
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public void getResult(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.srmDomain, CommonEnumsInterface.DownloadEnums.importResult.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                CommonEnumsInterface.DownloadEnums.importResult.getMethod(),
                CommonEnumsInterface.DownloadEnums.importResult.getContentType(),
                requestBody);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        Assert.assertNotSame(result.getJSONObject("data").getString("result"),"2", "失败");
        Assert.assertNotSame(result.getJSONObject("data").getString("result"),"0",  "上传、导出中");
        Assert.assertEquals(result.getJSONObject("data").getString("result"),"1" , "上传&导出失败");
    }

    /**
     * common-service上传excel接口封装工具
     * 它首先发送请求到指定的URL，然后验证响应状态码和消息体中的代码，
     * @param requestBody 包含生成Excel所需信息的请求体
     * @return 返回上传结果
     */
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public String importExcel(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.srmDomain, CommonEnumsInterface.DownloadEnums.importOssUrl.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                CommonEnumsInterface.DownloadEnums.importOssUrl.getMethod(),
                CommonEnumsInterface.DownloadEnums.importOssUrl.getContentType(),
                requestBody);
        Assert.assertEquals(res.getStatusCode(), 200, "上传失败");

        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("status"),"200");
        String FileId=result.getString("data");
        Assert.assertNotNull(FileId, "上传失败,上传结果为空");
        return FileId;

    }

    /**
     * 创建采购单（支持自定义请求体）
     * @param requestBody 自定义请求体参数
     */
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public String createPurchaseOrder(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.xmAdminDomain, TestTool.TestToolEnum.xmPurchaseCreate.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL, TestTool.TestToolEnum.xmPurchaseCreate.getMethod(),
                TestTool.TestToolEnum.xmPurchaseCreate.getContentType(), requestBody);

        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");

        // 验证采购单号
        String purchaseNo = result.getString("data");
        Assert.assertNotNull(purchaseNo, "采购单号不能为空");
        Assert.assertFalse(purchaseNo.trim().isEmpty(), "采购单号不能为空字符串");

        return purchaseNo;

    }
    //查询当前登录用户信息
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public Map<String, Object> getPersonalInfo() {
        String URL = request.urlBuild(envVar.xmAdminDomain, CommonEnumsInterface.CommonEnum.personalInfo.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                CommonEnumsInterface.CommonEnum.personalInfo.getMethod(),
                CommonEnumsInterface.CommonEnum.personalInfo.getContentType(),
                null);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        return result.getJSONObject("data");
    }
    //查询门店详情
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public Map<String, Object> getMerchantDetail(String mID) {
        String URL = request.urlBuild(envVar.xmAdminDomain, CommonEnumsInterface.CommonEnum.merchantDetail.getUrl()+mID);
        HttpResponseWrapper res = request.sendRequest(URL,
                CommonEnumsInterface.CommonEnum.merchantDetail.getMethod(),
                CommonEnumsInterface.CommonEnum.merchantDetail.getContentType(),
                null);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        return result.getJSONObject("data");
    }

    //查询货品列表
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public JSONArray selectLikeBySkuOrName(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, CommonEnumsInterface.CommonEnum.selectLikeBySkuOrName.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                CommonEnumsInterface.CommonEnum.selectLikeBySkuOrName.getMethod(),
                CommonEnumsInterface.CommonEnum.selectLikeBySkuOrName.getContentType(),
                params);
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONObject data = result.getJSONObject("data");
        JSONArray skuList = data.getJSONArray("list");
        Assert.assertNotNull(skuList, "货品查询结果为空");
        return skuList;
    }

    /**
     * 构造入库任务参数
     * @param taskInfo 任务详情信息（包含taskId、sourceId等信息）
     * @return 入库任务参数
     */
    public Map<String, Object> buildStockTaskParamsFromTaskInfo(JSONObject taskInfo) {
        Map<String, Object> stockTaskParams = new HashMap<>();
        JSONArray resDTOList = taskInfo.getJSONArray("resDTOList");

        String taskType = taskInfo.getString("type");
        if (taskType == null) {
            throw new IllegalArgumentException("任务类型不能为空");
        }
        Integer mappedType = TYPE_MAPPING.get(taskType);
        if (mappedType == null) {
            throw new IllegalArgumentException("暂不支持该入库任务类型: " + taskType);
        }
        // 基本任务信息
        stockTaskParams.put("stockStorageTaskId", taskInfo.get("taskId"));
        stockTaskParams.put("bizId", taskInfo.get("sourceId"));
        stockTaskParams.put("type", mappedType);
        stockTaskParams.put("warehouseNo", taskInfo.get("inWarehouseNo"));

        // 构建订单详情列表
        JSONArray orderDetails = new JSONArray();

        for (int i = 0; i < resDTOList.size(); i++) {
            JSONObject item = resDTOList.getJSONObject(i);
            JSONArray stockStorageItemDetailResDTOList= item.getJSONArray("stockStorageItemDetailResDTOList");
            int actualQuantity = 0;
            if (stockStorageItemDetailResDTOList != null) {
                actualQuantity = stockStorageItemDetailResDTOList.stream()
                        .mapToInt(obj -> ((JSONObject) obj).getInteger("quantity"))
                        .filter(Objects::nonNull)
                        .sum();
            }
            Map<String, Object> orderDetail = new HashMap<>();

            // 基本信息
            orderDetail.put("belongType", 0);
            orderDetail.put("supplierId", item.get("supplierId"));
            orderDetail.put("shouldIn", item.get("quantity"));
            orderDetail.put("qualityTimeType", item.get("qualityTimeType") != null ? item.get("qualityTimeType") : 1);
            orderDetail.put("specification", item.get("weight"));
            orderDetail.put("packaging", item.get("packaging"));
            orderDetail.put("cargoInspection", item.get("canInspect"));
            orderDetail.put("categoryType", item.get("categoryType"));
            orderDetail.put("supplier", item.get("supplierName"));
            orderDetail.put("temperature", item.get("storageLocationId"));
            orderDetail.put("stockNum", item.getInteger("quantity")-actualQuantity);
            orderDetail.put("purchaseNo", taskInfo.get("sourceId"));
            orderDetail.put("pdName", item.get("pdName"));
            orderDetail.put("sku", item.get("sku"));

            Long produceAt= DateUtil.getStartOfDayTimestamp();
            if (mappedType.equals(0)){
                // 时间相关字段（使用固定值，与示例保持一致）
                orderDetail.put("shelfLife", produceAt);
                orderDetail.put("produceAt", produceAt);

                // prove信息
                Map<String, Object> proveInfo = new HashMap<>();
                proveInfo.put("sku", item.get("sku"));
                proveInfo.put("purchaseNo", taskInfo.get("sourceId"));
                proveInfo.put("shelfLife", produceAt);
                proveInfo.put("produceAt", produceAt);
                orderDetail.put("prove", proveInfo);
            }

            orderDetails.add(orderDetail);
        }

        // 将订单详情列表放入参数中
        stockTaskParams.put("orderDetails", orderDetails);

        return stockTaskParams;
    }

    //创建入库单
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public void createInBound(Long taskId, String taskType) {
        String url = request.urlBuild(envVar.saasManageDomain, PmsEnumsInterface.WmsInStockEnum.queryTaskDetail.getUrl());
        Map<String, Object> queryTaskDetailParams = new HashMap<>();
        Integer mappedType = TYPE_MAPPING.get(taskType);
        if (mappedType == null) {
            throw new IllegalArgumentException("暂不支持该入库任务类型: " + taskType);
        }
        queryTaskDetailParams.put("taskId", taskId);
        queryTaskDetailParams.put("queryType", mappedType);
        HttpResponseWrapper queryTaskDetailRes = request.sendRequest(url,
                PmsEnumsInterface.WmsInStockEnum.queryTaskDetail.getMethod(),
                PmsEnumsInterface.WmsInStockEnum.queryTaskDetail.getContentType(),
                queryTaskDetailParams);
        // HTTP响应断言
        Assert.assertEquals(queryTaskDetailRes.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject queryTaskDetailResult = JSON.parseObject(queryTaskDetailRes.getBody());
        Assert.assertEquals(queryTaskDetailResult.getIntValue("status"), 200, "业务状态码验证失败: " +
                queryTaskDetailResult.getString("msg"));
        JSONObject taskInfo = queryTaskDetailResult.getJSONObject("data");
        Assert.assertNotNull(taskInfo, "入库任务详情数据不能为空");
        Map<String, Object> createInBoundParams = buildStockTaskParamsFromTaskInfo(taskInfo);//构造入库单参数
        String url1 = request.urlBuild(envVar.saasManageDomain, PmsEnumsInterface.WmsInStockEnum.createInBound.getUrl());
        HttpResponseWrapper createInBoundRes = request.sendRequest(url1,
                PmsEnumsInterface.WmsInStockEnum.createInBound.getMethod(),
                PmsEnumsInterface.WmsInStockEnum.createInBound.getContentType(),
                createInBoundParams);

        // HTTP响应断言
        Assert.assertEquals(createInBoundRes.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject createInBoundResult = JSON.parseObject(createInBoundRes.getBody());
        Assert.assertEquals(createInBoundResult.getIntValue("status"), 200, "业务状态码验证失败: " +
                createInBoundResult.getString("msg"));


    }


}
