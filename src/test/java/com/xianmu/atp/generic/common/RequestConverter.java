package com.xianmu.atp.generic.common;

import com.alibaba.fastjson.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RequestConverter {

    public static void main(String[] args) throws Exception{
        convertCurlToJava(getRequestJsonString());
        System.out.println();
        convertCurlToJava(getWWstring());
        System.out.println();
        convertCurlToJava(getGETString());
    }

    /*
    * curl 转换为 java 代码
    * @param curlCommand 从浏览器上复制的curl bash命令
     */
    public static void convertCurlToJava(String curlCommand) throws Exception{
        // 提取 URL
        String url = extractUrl(curlCommand);
        if (url == null) {
            System.out.println("无法解析 URL");
            return;
        }

        // 提取请求方法
        String method = determineHttpMethod(curlCommand);

        // 提取请求体
        String body = extractBody(curlCommand, method);
        if(!method.equals("GET")&&!method.equals("NONEPOST")){
            body = URLDecoder.decode(body, "UTF-8");
        }


        // 生成 Java 代码
        generateJavaCode(url, method, body);
    }

    private static String extractUrl(String curlCommand) {
        Pattern urlPattern = Pattern.compile("curl '([^']+)'");
        Matcher urlMatcher = urlPattern.matcher(curlCommand);
        if (urlMatcher.find()) {
            return urlMatcher.group(1);
        }
        return null;
    }

    private static String determineHttpMethod(String curlCommand) {
        // 检查 content-type
        Pattern contentTypePattern = Pattern.compile("-H 'content-type: ([^']+)'");
        Matcher contentTypeMatcher = contentTypePattern.matcher(curlCommand);
        if (contentTypeMatcher.find()) {
            String contentType = contentTypeMatcher.group(1).toLowerCase();
            if (contentType.equals("application/x-www-form-urlencoded")) {
                return "wwPOST";
            }
            if (contentType.contains("json")) {
                return "jsonPOST";
            }
        }
        if(curlCommand.contains("-X 'POST'")){
            return "NONEPOST";
        }
        return "GET"; // 默认为 GET
    }

    private static String extractBody(String curlCommand, String method) {
        if (method.equals("wwPOST")) {
            // 提取请求体
            Pattern bodyPattern = Pattern.compile("--data-raw '([^']+)'|--data-binary '([^']+)'|--data '([^']+)'");
            Matcher bodyMatcher = bodyPattern.matcher(curlCommand);
            if (bodyMatcher.find()) {
                String body = bodyMatcher.group(1);
                if (body == null) {
                    body = bodyMatcher.group(2);
                    if (body == null) {
                        body = bodyMatcher.group(3);
                    }
                }
                return body;
            }
        } else if (method.equals("jsonPOST")) {
            // 提取 JSON 请求体
            Pattern bodyPattern = Pattern.compile("--data-binary '([^']+)'|--data '([^']+)'");
            Matcher bodyMatcher = bodyPattern.matcher(curlCommand);
            if (bodyMatcher.find()) {
                return bodyMatcher.group(1) != null ? bodyMatcher.group(1) : bodyMatcher.group(2);
            }
        }
        return null;
    }

    private static void generateJavaCode (String url, String method, String body) throws Exception{

        StringBuilder javaCode = new StringBuilder();

        // URL
        javaCode.append("String url = \"").append(url).append("\";\n");

        // 请求体处理
        String bodyStr = null;

        if (method.equals("wwPOST")) {
            // 处理 x-www-form-urlencoded 数据
            if (body != null) {
                javaCode.append(parsePrintWwStr(body));
                javaCode.append("\n");
            }
        } else if (method.equals("jsonPOST")) {
            // 处理 JSON 请求体
            if (body != null) {
                javaCode.append(parseJsonBody(body));
            }
        }

        if(method.contains("json")){
            javaCode.append("HttpResponse response = HttpRequest.")
                    .append("post(url)\n")
                    .append("    .header(\"Content-Type\", \"application/json\")\n")
                    .append("    .header(\"token\", token)\n");
        }else if(method.equals("wwPOST")){
            javaCode.append("HttpResponse response = HttpRequest.")
                    .append("post(url)\n")
                    .append("    .header(\"Content-Type\", \"application/x-www-form-urlencoded\")\n")
                    .append("    .header(\"token\", token)\n");
        }
        else if(method.equals("GET")){
            javaCode.append("HttpResponse response = HttpRequest.")
                    .append("get(url)\n")
                    .append("    .header(\"token\", token)\n");
        }else {
            javaCode.append("HttpResponse response = HttpRequest.")
                    .append("post(url)\n")
                    .append("    .header(\"token\", token)\n");
        }
        // 生成请求代码


        if (body != null) {
            javaCode.append("    .body(").append("bodyStr").append(")\n");
        }


        // 执行请求
        javaCode.append("    .execute();");

        // 打印生成的 Java 代码
        System.out.println(javaCode.toString());
    }

    private static String parseUrlEncodedBody(String body) {
        StringBuilder result = new StringBuilder();
        String[] pairs = body.split("&");
        Map<String, String> params = new HashMap<>();

        for (String pair : pairs) {
            try {
                String[] keyValue = pair.split("=", 2);
                String key = URLDecoder.decode(keyValue[0], "UTF-8");
                String value = URLDecoder.decode(keyValue.length > 1 ? keyValue[1] : "", "UTF-8");
                params.put(key, value); // 所有值都默认为 String
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        int i = 0;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key.isEmpty()) {
                continue; // 跳过空键
            }
            if (i == 0) {
                result.append(key).append("=\"").append(escapeQuotes(value)).append("\"");
            } else {
                result.append(", ").append(key).append("=\"").append(escapeQuotes(value)).append("\"");
            }
            i++;
        }
        System.out.println("rrr:"+result.toString());
        return "{" + result.toString() + "}";

    }


    private static String parsePrintWwStr(String body) throws UnsupportedEncodingException {
        StringBuilder javaCode = new StringBuilder();
        if (body != null) {
            javaCode.append("String bodyStr = ");
            String[] bodyLines = body.split("&");
            for (int i = 0; i < bodyLines.length; i++) {
                if (i == 0) {
                    javaCode.append("\"").append(bodyLines[i].replace("=", "=\"+\"").concat("\"+\n"));
                } else if(i<bodyLines.length-1){
                    javaCode.append("                  \"&").append("").append(bodyLines[i].replace("=", "=\"+\"").concat("\"+\n"));
                } else {
                    javaCode.append("                  \"&").append("").append(bodyLines[i].replace("=", "=\"+\"").concat("\""));
                }
            }
            javaCode.append(";\n");
        }
        return javaCode.toString();
    }

    private static String parseJsonBody(String bodyStr) {
        Map<String, Object> body = convertJsonToMap(bodyStr);
        // 生成代码
        StringBuilder codeBuilder = new StringBuilder();
        codeBuilder.append("Map<String, Object> body = new HashMap<>();\n");
        for (Map.Entry<String, Object> entry : body.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof List) {
                codeBuilder.append("body.put(\"").append(key).append("\", ").append("\""+value+"\"").append(");\n");
            } else {
                codeBuilder.append("body.put(\"").append(key).append("\", ").append("\""+value+"\"").append(");\n");
            }
        }
        codeBuilder.append("String bodyStr = JSONObject.toJSONString(body);\n");
        return codeBuilder.toString(); // 直接返回 JSON 字符串
    }

    public static Map<String, Object> convertJsonToMap(String jsonString) {
        // 将 JSON 字符串解析为 JSONObject
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        Map<String, Object> map = new HashMap<>();

        // 遍历 JSON 对象的每个键值对
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            // 如果值是列表，处理为 List<Object>
            if (value instanceof List) {
                map.put(key, ((List<?>) value));
            } else {
                map.put(key, value);
            }
        }

        return map;
    }

    private static String escapeQuotes(String value) {
        return value.replace("\"", "\\\""); // 转义引号
    }



    public static String getWWstring() {
        return "curl 'https://qah5.summerfarm.net/order/InsertContact' \\\n" +
                "  -H 'authority: qah5.summerfarm.net' \\\n" +
                "  -H 'xm-platform: web' \\\n" +
                "  -H 'xm-uid: 350935' \\\n" +
                "  -H 'xm-ab-exp: [{\"experimentId\":\"new-home\",\"experimentPlace\":\"place-of:new-home\",\"variantId\":\"V3\"},{\"experimentId\":\"product_search_rerank\",\"experimentPlace\":\"place-of:product_search_rerank\",\"variantId\":\"V5\"},{\"experimentId\":\"product_search_disperse\",\"experimentPlace\":\"place-of:product_search_disperse\",\"variantId\":\"V3\"}]' \\\n" +
                "  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.06.2402040 MicroMessenger/8.0.5 Language/zh_CN webview/17407087198063336 webdebugger port/38704 token/c23dcfbbee3e29b991a5336bce7df818' \\\n" +
                "  -H 'content-type: application/x-www-form-urlencoded' \\\n" +
                "  -H 'accept: application/json, text/plain, */*' \\\n" +
                "  -H 'xm-rqid: 1740712507866-66277534' \\\n" +
                "  -H 'xm-page: %2Faddress%2Fedit' \\\n" +
                "  -H 'xm-phone: 13732254809' \\\n" +
                "  -H 'token: mall__54dc3c73-f261-495f-9026-13bd5f52df20' \\\n" +
                "  -H 'xm-biz: xm-mall' \\\n" +
                "  -H 'origin: https://qah5.summerfarm.net' \\\n" +
                "  -H 'sec-fetch-site: same-origin' \\\n" +
                "  -H 'sec-fetch-mode: cors' \\\n" +
                "  -H 'sec-fetch-dest: empty' \\\n" +
                "  -H 'referer: https://qah5.summerfarm.net/home.html?code=041TlXll2I8K9f4FaPml2p9Afb2TlXly&state=STATE' \\\n" +
                "  -H 'accept-language: zh-CN,zh;q=0.9' \\\n" +
                "  --data-raw 'address=%E6%B5%99%E5%A4%A7%E8%B7%AF1%E5%8F%B7%20%E4%B8%AD%E5%85%B1%E6%9D%AD%E5%B7%9E%E5%B8%82%E8%A5%BF%E6%B9%96%E5%8C%BA%E7%BA%AA%E5%BE%8B%E6%A3%80%E6%9F%A5%E5%A7%94%E5%91%98%E4%BC%9A&addressRemark=%7B%22customRemark%22%3A%22%22%2C%22baseRemark%22%3A%5B%5D%7D&area=%E8%A5%BF%E6%B9%96%E5%8C%BA&areaPhone=&city=%E6%9D%AD%E5%B7%9E%E5%B8%82&contact=222&fixedPhone=&houseNumber=111&isChecked=0&phone=13124215214&poiNote=120.130188%2C30.259608&province=%E6%B5%99%E6%B1%9F' \\\n" +
                "  --compressed";
    }

    public static String getRequestJsonString() {
        return "curl 'https://qah5.summerfarm.net/price/query/take-actual-price' \\\n" +
                "  -H 'authority: qah5.summerfarm.net' \\\n" +
                "  -H 'xm-platform: web' \\\n" +
                "  -H 'xm-uid: 350935' \\\n" +
                "  -H 'xm-ab-exp: [{\"experimentId\":\"new-home\",\"experimentPlace\":\"place-of:new-home\",\"variantId\":\"V3\"},{\"experimentId\":\"product_search_rerank\",\"experimentPlace\":\"place-of:product_search_rerank\",\"variantId\":\"V5\"},{\"experimentId\":\"product_search_disperse\",\"experimentPlace\":\"place-of:product_search_disperse\",\"variantId\":\"V3\"}]' \\\n" +
                "  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.06.2402040 MicroMessenger/8.0.5 Language/zh_CN webview/17407087198063336 webdebugger port/38704 token/c23dcfbbee3e29b991a5336bce7df818' \\\n" +
                "  -H 'content-type: application/json' \\\n" +
                "  -H 'accept: application/json, text/plain, */*' \\\n" +
                "  -H 'xm-rqid: 1740714150287-53915200' \\\n" +
                "  -H 'xm-page: %2Fgoods%2Fcategory' \\\n" +
                "  -H 'xm-phone: 13732254809' \\\n" +
                "  -H 'token: mall__c9c8394d-3a35-4e04-b0fe-eebb18424c5c' \\\n" +
                "  -H 'xm-biz: xm-mall' \\\n" +
                "  -H 'origin: https://qah5.summerfarm.net' \\\n" +
                "  -H 'sec-fetch-site: same-origin' \\\n" +
                "  -H 'sec-fetch-mode: cors' \\\n" +
                "  -H 'sec-fetch-dest: empty' \\\n" +
                "  -H 'referer: https://qah5.summerfarm.net/home.html?code=041Xvw0w37RGt43e3s0w3a21xT2Xvw0j&state=STATE' \\\n" +
                "  -H 'accept-language: zh-CN,zh;q=0.9' \\\n" +
                "  --data-binary '{\"orderNow\":[{\"sku\":\"308366655042\",\"suitId\":0,\"quantity\":3},{\"sku\":\"308354112240\",\"suitId\":0,\"quantity\":1},{\"sku\":\"308803141827\",\"suitId\":0,\"quantity\":1},{\"sku\":\"308022074423\",\"suitId\":0,\"quantity\":1},{\"sku\":\"308354112237\",\"suitId\":0,\"quantity\":1},{\"sku\":\"308354112281\",\"suitId\":0,\"quantity\":1}],\"minLadderPrice\":0}' \\\n" +
                "  --compressed";
    }

    public static String getGETString(){
        return "curl 'https://qah5.summerfarm.net/timing-delivery/sku/1/2?type=0' \\\n" +
                "  -H 'authority: qah5.summerfarm.net' \\\n" +
                "  -H 'xm-platform: web' \\\n" +
                "  -H 'xm-ab-exp: [{\"experimentId\":\"new-home\",\"experimentPlace\":\"place-of:new-home\",\"variantId\":\"V3\"},{\"experimentId\":\"product_search_rerank\",\"experimentPlace\":\"place-of:product_search_rerank\",\"variantId\":\"V5\"},{\"experimentId\":\"product_search_disperse\",\"experimentPlace\":\"place-of:product_search_disperse\",\"variantId\":\"V3\"}]' \\\n" +
                "  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.06.2402040 MicroMessenger/8.0.5 Language/zh_CN webview/17407087198063336 webdebugger port/38704 token/c23dcfbbee3e29b991a5336bce7df818' \\\n" +
                "  -H 'xm-uid: 350935' \\\n" +
                "  -H 'accept: application/json, text/plain, */*' \\\n" +
                "  -H 'xm-rqid: 1740727109550-84039648' \\\n" +
                "  -H 'xm-page: %2Fhome' \\\n" +
                "  -H 'xm-phone: 13732254809' \\\n" +
                "  -H 'token: mall__2e91162f-4f57-4734-aef9-85b3f5ffca37' \\\n" +
                "  -H 'xm-biz: xm-mall' \\\n" +
                "  -H 'sec-fetch-site: same-origin' \\\n" +
                "  -H 'sec-fetch-mode: cors' \\\n" +
                "  -H 'sec-fetch-dest: empty' \\\n" +
                "  -H 'referer: https://qah5.summerfarm.net/home.html?code=081brHll2cNQ8f4k73ll2MnDP21brHlC&state=STATE' \\\n" +
                "  -H 'accept-language: zh-CN,zh;q=0.9' \\\n" +
                "  --compressed";
    }
}
