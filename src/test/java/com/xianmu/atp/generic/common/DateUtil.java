package com.xianmu.atp.generic.common;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public class DateUtil {

    /**
     * 获取当天日期，格式=yyyy-MM-dd
     * @return 当天日期字符串
     */
    public static String getCurrentDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 获取当前时间精确到秒，格式=yyyyMMddHHmmss
     * @return 当前时间字符串
     */
    public static String getCurrentDateTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    /**
     * 获取当天开始时间，格式=yyyy-MM-dd HH:mm:ss
     * @return 当天开始时间字符串
     */
    public static String getStartOfDay() {
        LocalDateTime startOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        return startOfDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取当天结束时间，格式=yyyy-MM-dd HH:mm:ss
     * @return 当天结束时间字符串
     */
    public static String getEndOfDay() {
        LocalDateTime endOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        return endOfDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取相对于当前日期的偏移日期，格式=yyyy-MM-dd
     * @param days 天数偏移量（正数表示未来，负数表示过去）
     * @return 偏移后的日期字符串
     */
    public static String getOffsetDate(int days) {
        LocalDate date = LocalDate.now().plusDays(days);
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 获取当天开始时间的时间戳（毫秒）
     * @return 当天开始时间的时间戳
     */
    public static long getStartOfDayTimestamp() {
        LocalDateTime startOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        return startOfDay.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取相对于当前日期的偏移日期的时间戳（毫秒）
     * @param days 天数偏移量（正数表示未来，负数表示过去）
     * @return 偏移日期的时间戳
     */
    public static long getOffsetDateTimestamp(int days) {
        LocalDateTime offsetDate = LocalDateTime.of(LocalDate.now().plusDays(days), LocalTime.MIN);
        return offsetDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

}
