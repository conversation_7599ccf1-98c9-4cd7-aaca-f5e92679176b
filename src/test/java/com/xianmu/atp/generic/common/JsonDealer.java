package com.xianmu.atp.generic.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Iterator;

public class JsonDealer {
    /*
     * 递归查找JSONObject中是否存在指定key和value
     * @param jsonObject JSONObject对象
     * @param extraKey 要查找的key
     * @param extraValue 要查找的value
     * @return 1:存在且匹配，2:存在但不匹配，3:不存在
     */
    public static int findKeyInJson(JSONObject jsonObject, String extraKey, String extraValue) {
        Iterator<String> keys = jsonObject.keySet().iterator();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = jsonObject.get(key);

            if (key.equals(extraKey)) {
                if (value.toString().equals(extraValue)) {
                    return 1;
                } else {
                    return 2;
                }
            }

            if (value instanceof JSONObject) {
                int result = findKeyInJson((JSONObject) value, extraKey, extraValue);
                if (result != 3) {
                    return result;
                }
            }

            if (value instanceof JSONArray) {
                for (int i = 0; i < ((JSONArray) value).size(); i++) {
                    Object arrayItem = ((JSONArray) value).get(i);
                    if (arrayItem instanceof JSONObject) {
                        int result = findKeyInJson((JSONObject) arrayItem, extraKey, extraValue);
                        if (result != 3) {
                            return result;
                        }
                    }
                }
            }
        }
        return 3;
    }


    public static String getKeyStringINJson(JSONObject jsonObject, String expectedkey) {
        Iterator<String> keys = jsonObject.keySet().iterator();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = jsonObject.get(key);

            if (key.equals(expectedkey)) {
                return value.toString();
            }

            if (value instanceof JSONObject) {
                String result = getKeyStringINJson((JSONObject) value, expectedkey);
                return result;
            }

            if (value instanceof JSONArray) {
                for (int i = 0; i < ((JSONArray) value).size(); i++) {
                    Object arrayItem = ((JSONArray) value).get(i);
                    if (arrayItem instanceof JSONObject) {
                        String result = getKeyStringINJson((JSONObject) value, expectedkey);
                        return result;
                    }
                }
            }
        }
        return "";
    }
}
