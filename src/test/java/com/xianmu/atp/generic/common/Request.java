package com.xianmu.atp.generic.common;

import cn.hutool.core.lang.Console;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.*;

import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;
import org.testng.Assert;

import javax.annotation.Resource;

@Slf4j
@Data
@Component
public class Request {

    @Resource
    private EnvVariable envVar;

    private ThreadLocal<String> token = new ThreadLocal<>();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public void setToken(String token) {
        this.token.set(token);
    }

    public String getToken() {
        return this.token.get();
    }

    /**
     * 发送HTTP请求并接收响应
     *
     * @param url 请求的URL地址
     * @param method HTTP方法，如GET、POST等
     * @param contentType 请求内容类型，当前兼容application/json、x-www-form-urlencoded
     * @param requestBody 请求体内容，具体类型视contentType而定
     * @return HttpResponseWrapper对象，包含响应状态码、响应体和响应头
     * 此方法用于构建HTTP请求，发送到指定的URL，并返回响应结果它处理了请求的构建、发送以及响应的接收和封装
     * 在请求失败时，会记录日志并返回相应的错误信息
     */
    public  HttpResponseWrapper sendRequest(String url, String method, String contentType, Object requestBody) {
        // 构建HttpRequest对象并设置基本属性
        if(Objects.isNull(contentType)){
            contentType= "application/x-www-form-urlencoded";
        }
        method = method.toUpperCase();
        HttpRequest request = HttpUtil.createRequest(Method.valueOf(method.toUpperCase()), url);

            // 根据method和contentType设置请求体
        if (Objects.nonNull(requestBody)) {
            try{
                if ("POST".equalsIgnoreCase(method) || "PUT".equalsIgnoreCase(method)) {
                    switch (contentType) {
                        case "application/json": {
                            Map<String, Object> paramsMap = convertToMap(requestBody);
                            String serializedBody = objectMapper.writeValueAsString(paramsMap);
                            request.body(serializedBody);
                            break;
                        }
                        case "application/x-www-form-urlencoded": {
                            Map<String, Object> paramsMap = convertToMap(requestBody);
                            request.form(paramsMap);
                            break;
                        }
                        case "multipart/form-data": {
                            Map<String, Object> paramsMap = convertToMap(requestBody);
                            request.form(paramsMap);
//                            for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
//                                if (entry.getValue() instanceof File) {
//                                    request.form(entry.getKey(), (File) entry.getValue());
//                                } else {
//                                    request.form(entry.getKey(), entry.getValue().toString());
//                                }
//                            }
                            break;
                        }
                        default:
                            throw new RuntimeException("不支持的contentType:" + contentType);
                    }

                }else if ("GET".equalsIgnoreCase(method)) {
                    Map<String, Object> params = convertToMap(requestBody);
                    url = addQueryParams(url, params);
                    request.setUrl(url);
                }else {
                    throw new RuntimeException("不支持的method:"+method);
                }
            }catch (Exception e){
                throw new RuntimeException("请求参数构造异常:body"+requestBody, e);
            }
        }
        if (Objects.nonNull(token)){
            request.header("token", getToken());
            request.header("Content-Type", contentType);
            request.header("xm-platform", "xm-atp");
        }
            // 发送请求并获取响应
        try (HttpResponse response = request.execute()) {
            return new HttpResponseWrapper(response.getStatus(), response.body(), response.headers()); // 返回响应体
        } catch (Exception e) {
            throw new RuntimeException("请求异常:", e);
        }
    }

    public static Map<String, Object> convertToMap(Object requestBody) {
        if (requestBody instanceof Map) {
            // 如果已经是Map类型，则直接返回
            return new HashMap<>((Map<? extends String, ?>) requestBody);
        } else if (requestBody instanceof String) {
            String str = (String) requestBody;
            try {
                // 尝试解析JSON对象
                Gson gson = new Gson();
                Map<String, Object> mapFromJson = gson.fromJson(str, Map.class);
                if (mapFromJson != null && !mapFromJson.isEmpty()) {
                    return mapFromJson;
                }
            } catch (Exception ignored) {
                // JSON解析失败，尝试解析为表单URL编码字符串
            }

            // 解析表单URL编码字符串
            return parseFormUrlEncoded(str);
        } else {
            throw new IllegalArgumentException("Unsupported request body type.");
        }
    }


    private static Map<String, Object> parseFormUrlEncoded(String formUrlEncoded) {
        Map<String, Object> params = new HashMap<>();
        if (formUrlEncoded == null || formUrlEncoded.trim().isEmpty()) {
            return params;
        }

        // Split the string by "&" to get each key-value pair
        for (String param : formUrlEncoded.split("&")) {
            // Trim spaces and ignore empty parameters
            param = param.trim();
            if (param.isEmpty()) continue;

            // Split each key-value pair by "="
            String[] pair = param.split("=", 2);
            if (pair.length >= 1) {
                String key;
                String value = "";

                try {
                    key = java.net.URLDecoder.decode(pair[0], "UTF-8");
                    if (pair.length > 1) {
                        value = java.net.URLDecoder.decode(pair[1], "UTF-8");
                    }
                } catch (Exception e) {
                    throw new IllegalArgumentException("Invalid form-url-encoded string.", e);
                }

                // Handle multiple values for the same key
                if (params.containsKey(key)) {
                    Object existingValue = params.get(key);
                    if (existingValue instanceof List) {
                        ((List<String>) existingValue).add(value);
                    } else {
                        List<String> values = new ArrayList<>();
                        values.add((String) existingValue);
                        values.add(value);
                        params.put(key, values);
                    }
                } else {
                    params.put(key, value);
                }
            }
        }
        return params;
    }
    /**
     * 添加查询参数到URL
     *
     * @param url 原始URL
     * @param params 查询参数
     * @return 包含查询参数的URL
     */
    private String addQueryParams(String url, Object params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (params instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) params;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getValue() != null) {
                    builder.queryParam(entry.getKey(), entry.getValue());
                }
            }
        }
        // 如果 contentType 不是 application/x-www-form-urlencoded 或 params 不是 Map 或 String，返回原始 URL
        return builder.build().toUriString();
    }

    public String urlBuild(String baseDomain,String url){

        if (baseDomain == null || baseDomain.isEmpty()  || url == null || url.isEmpty()) {
            throw new IllegalArgumentException("Input parameters cannot be null or empty");
        }
        // Normalize baseDomain
        if (baseDomain.endsWith("/")) {
            baseDomain = baseDomain.substring(0, baseDomain.length() - 1);
        }
        if (!baseDomain.matches("^https?://.*")) {
            baseDomain = "https://" + baseDomain;
        }

        // Normalize url
        if (url.startsWith("/")) {
            url = url.substring(1);
        }

        return UriComponentsBuilder.newInstance()
                .scheme("https") // Ensure the scheme is https
                .host(baseDomain.replaceFirst("^https?://", "")) // Remove scheme from baseDomain
                .path(url)
                .build()
                .toUriString();
    }

    /**
     * 鲜沐后台登录
     * @return token
     */
    public  String xmAdminLogin() {
        String URL = urlBuild(envVar.xmAdminDomain, LoginEnumsInterface.xianmuLogin.getUrl());
        HashMap<String, String> loginBody = new HashMap<>();
        loginBody.put("username", envVar.getAdminUserName());
        loginBody.put("password", envVar.getAdminpassword());
        HttpResponseWrapper loginRes= sendRequest(URL,
                LoginEnumsInterface.xianmuLogin.getMethod(),
                LoginEnumsInterface.xianmuLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        Assert.assertNotNull( token);
        return token;
    }

    /**
     * saas商家后台登录
     * @return token
     */
    public String saasLogin(String userName, String password) {
        String URL = urlBuild(envVar.saasManageDomain, LoginEnumsInterface.saasManageLogin.getUrl());

        HashMap<String, Object> loginBody = new HashMap<>();
        loginBody.put("phone", userName);
        loginBody.put("password", password);
        loginBody.put("origin", 6);
        HttpResponseWrapper response = sendRequest(URL,
                LoginEnumsInterface.saasManageLogin.getMethod(),
                LoginEnumsInterface.saasManageLogin.getContentType(),
                loginBody);
        Assert.assertEquals(response.getStatusCode(),200,"登录失败");
        JSONObject result = JSON.parseObject(response.getBody());
        Assert.assertEquals(result.getString("status"),"200","登录失败");
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "登录返回数据为空");
        Assert.assertNotNull(data.getString("token"), "登录返回数据中不存在token");
        return result.getJSONObject("data").getString("token");
    }

}



