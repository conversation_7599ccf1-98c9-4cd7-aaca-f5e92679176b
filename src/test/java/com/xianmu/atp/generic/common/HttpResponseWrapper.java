package com.xianmu.atp.generic.common;

import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
public class HttpResponseWrapper {
    private final int statusCode;
    private final String body;
    private final Map<String, List<String>> headers;

    public HttpResponseWrapper(int statusCode, String body, Map<String, List<String>> headers) {
        this.statusCode = statusCode;
        this.body = body;
        this.headers = headers;
    }

    @Override
    public String toString() {
        return "{" +
                "statusCode=" + statusCode +
                ", body='" + body + '\'' +
                ", headers=" + headers +
                '}';
    }
}