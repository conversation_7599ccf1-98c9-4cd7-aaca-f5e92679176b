package com.xianmu.atp.generic.common;

import com.alibaba.fastjson.JSON;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.schedulerx220190430.AsyncClient;
import com.aliyun.sdk.service.schedulerx220190430.models.ExecuteJobRequest;
import com.aliyun.sdk.service.schedulerx220190430.models.ExecuteJobResponse;
import com.google.gson.Gson;
import com.xianmu.atp.BaseTest;
import darabonba.core.client.ClientOverrideConfiguration;
import io.qameta.allure.Allure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import io.qameta.allure.Allure;


@Component
@Slf4j
public class ExecuteJob extends BaseTest {
    public String RunExecuteJob(String namespace,String groupId,Long jobId){
        if(namespace.equals("dev"))
            namespace = "0fba89cd-351e-4e9f-86dc-4b4fbc06170e";
        else if (namespace.equals("qa")) {
            namespace = "a40f0ca3-5bb6-417f-9df0-fb12c4c464a7";
        }
        String accessKeyId = "LTAI5t8PTbn9cEHupgySKbzB";
        String accessKeySecret = "******************************";
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKeyId)
                .accessKeySecret(accessKeySecret)
                .build());

        AsyncClient client = AsyncClient.builder()
                .region("cn-hangzhou") // Region ID
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride("schedulerx.aliyuncs.com")
                )
                .build();

        ExecuteJobRequest executeJobRequest = ExecuteJobRequest.builder()
                .regionId("public")
                .jobId(jobId)
                .namespace(namespace)
                .groupId(groupId)
                .build();
        try {
            CompletableFuture<ExecuteJobResponse> response = client.executeJob(executeJobRequest);
            ExecuteJobResponse resp = response.get();
            String runResponse = new Gson().toJson(resp);
            log.info("定时任务执行返回：{}",runResponse);
            Allure.addAttachment("定时任务执行返回",runResponse);
            client.close();
            return JSON.parseObject(runResponse).get("statusCode").toString();
        }catch (Exception e){
            return e.getMessage();
        }

    }

}
