package com.xianmu.atp.generic.common;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class BaseInput {

    /**
     * 获取所有非空的参数
     */
    public Map<String, Object> getNonEmptyParams() {
        Map<String, Object> nonEmptyParams = new HashMap<>();
        Field[] fields = this.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(this);
                if (value != null) {
                    nonEmptyParams.put(field.getName(), value);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return nonEmptyParams;
    }
}
