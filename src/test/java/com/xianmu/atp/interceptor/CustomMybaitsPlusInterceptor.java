package com.xianmu.atp.interceptor;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.xianmu.atp.annotation.Remark;
import com.xianmu.atp.annotation.RemarkAspect;
import io.qameta.allure.Allure;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.*;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName CustomMybaitsPlusInterceptor
 * @date 2025-01-10
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = Executor.class, method = "query",
                args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query",
                args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})
})

public class CustomMybaitsPlusInterceptor implements Interceptor, InnerInterceptor {
    @Override
    public void setProperties(Properties properties) {
        Interceptor.super.setProperties(properties);
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        log.info("进入sql分析");
        try {
            Object target = invocation.getTarget();
            Object[] args = invocation.getArgs();
            if (target instanceof Executor) {
                final Executor executor = (Executor) target;
                Object parameter = args[1];
                log.info("参数,executor={},parameter={}", executor, parameter);
//                boolean isUpdate = args.length == 2;
                MappedStatement ms = (MappedStatement) args[0];
                BoundSql boundSql;
                if (args.length == 4) {
                    boundSql = ms.getBoundSql(parameter);
                } else {
                    boundSql = (BoundSql) args[5];
                }
                String sql = getSql(boundSql, ms);
                // 获取 SQL 标题
                Object result = invocation.proceed();
                // 获取当前线程中的上下文
                Map<String, Object> context = RemarkAspect.getCurrentContext();
                String sqlTitle = context != null ? (String) context.get("remark") : null;;
                String attachmentTitle = StringUtils.isNotBlank(sqlTitle) ?  sqlTitle :  ms.getId();
                Allure.addAttachment("SQL标题:" +attachmentTitle, sql);
                if (result != null) {
                    log.info("返回结果为 {}", result.toString());
                    Allure.addAttachment("SQL--返回结果", result.toString());
                }
                log.info("原来 sql 为 {}", sql);
//                this.handleExplain(executor, sql);
                return result;

            }
            return invocation.proceed();
        }catch (Exception e){
            log.info("异常信息：{}",e.getMessage());
            return invocation.proceed();
        }
    }

    private String getSql(BoundSql boundSql, MappedStatement ms) {
        String sql = boundSql.getSql();
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        if (!CollectionUtils.isEmpty(parameterMappings) && parameterObject != null) {
            for (ParameterMapping parameterMapping : parameterMappings) {
                if (parameterMapping.getMode() != ParameterMode.OUT) {
                    Object value;
                    String propertyName = parameterMapping.getProperty();
                    if (boundSql.hasAdditionalParameter(propertyName)) {
                        value = boundSql.getAdditionalParameter(propertyName);
                    } else if (ms.getConfiguration().getTypeHandlerRegistry().hasTypeHandler(parameterObject.getClass())) {
                        value = parameterObject;
                    } else {
                        MetaObject metaObject = ms.getConfiguration().newMetaObject(parameterObject);
                        value = metaObject.getValue(propertyName);
                    }
                    sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(getParameter(value)));
                }
            }
        }
        return sql;
    }

    public String getParameter(Object parameter) {
        if (parameter instanceof String) {
            return "'" + parameter + "'";
        }
        return parameter.toString();
    }

    private void handleExplain(Executor executor, String sql) throws SQLException {
        Statement stmt = executor.getTransaction().getConnection().createStatement();
        stmt.execute("EXPLAIN " + sql + " ;");
        ResultSet rs = stmt.getResultSet();
        while (rs.next()) {
            ExplainResultVo explainResultVo = new ExplainResultVo();
            explainResultVo.setId(rs.getString("id"));
            explainResultVo.setSelectType(rs.getString("select_type"));
            explainResultVo.setTable(rs.getString("table"));
            explainResultVo.setPartitions(rs.getString("partitions"));
            explainResultVo.setType(rs.getString("type"));
            explainResultVo.setPossibleKeys(rs.getString("possible_keys"));
            explainResultVo.setKey(rs.getString("key"));
            explainResultVo.setKeyLen(rs.getString("key_len"));
            explainResultVo.setRef(rs.getString("ref"));
            String rows = rs.getString("rows");
            explainResultVo.setRows(rows);
            explainResultVo.setFiltered(rs.getString("filtered"));
            explainResultVo.setExtra(rs.getString("Extra"));
            boolean isSimple = "SIMPLE".equals(rs.getString("select_type"));
            int rowsInt = 0;
            if (StringUtils.isNotBlank(rows)) {
                try {
                    rowsInt = Integer.parseInt(rows);
                } catch (Exception e) {
                    rowsInt = 0;
                }
            }
            log.info("explain语句: {}", explainResultVo);
            Allure.addAttachment("sql结果{}", String.valueOf(explainResultVo));
        }
    }
}
