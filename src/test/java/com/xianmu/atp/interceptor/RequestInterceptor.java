package com.xianmu.atp.interceptor;

import cn.hutool.cache.CacheUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.http.HttpInterceptor;
import cn.hutool.http.HttpRequest;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.xianmu.atp.util.cache.CafflineUtils;
import io.qameta.allure.Allure;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 * 请求拦截器
 * <AUTHOR>
 * @ClassName RequestInterceptor
 * @date 2024-11-07
 */
@Component
public class RequestInterceptor implements HttpInterceptor<HttpRequest> {

    @Value("${app.environment}")
    private String env;

    public LoadingCache<String, Object> cache = CafflineUtils.getInstance();

    @Override
    public void process(HttpRequest httpRequest) {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        String methodName = stackTraceElements[7].getMethodName();

//        Console.log("当前执行的方法名: " + methodName);
//        Console.log("前置探测器");
        //TODO: 遍历枚举的url进行匹配
        //TODO:request的签名作为key，然后body和header先存入缓存，在response中取出
        String url = httpRequest.getUrl();
//        if (env.equals("dev")){
//            url = url.replace("qa", "dev");
//            httpRequest.setUrl(url);
//        }
        Map<String, Object> formData = httpRequest.form();
        if (formData != null && !formData.isEmpty() && "GET".equals(httpRequest.getMethod().name())) {
            url = url + "?";
            // 表单数据不为空的处理逻辑
            for (Map.Entry<String, Object> entry: formData.entrySet()){
                url = url  + entry.getKey() + "=" + entry.getValue() + "&";
            }
            url = url.substring(0, url.length() - 1);
            Console.log("表单数据非空：{}", url);
        } else {
            Console.log("表单数据为空");
        }
        cache.put(url, httpRequest.toString());
        Console.log(httpRequest);
    }
}
