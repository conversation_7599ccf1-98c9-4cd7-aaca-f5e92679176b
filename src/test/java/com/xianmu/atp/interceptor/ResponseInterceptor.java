package com.xianmu.atp.interceptor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.http.HttpConnection;
import cn.hutool.http.HttpInterceptor;
import cn.hutool.http.HttpResponse;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.xianmu.atp.util.cache.CafflineUtils;
import io.qameta.allure.Allure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.net.URI;

/**
 * 类<code>Doc</code>用于：TODO
 * 响应拦截器
 * <AUTHOR>
 * @ClassName ResponseInterceptor
 * @date 2024-11-07
 */
@Slf4j
@Component
public class ResponseInterceptor implements HttpInterceptor<HttpResponse> {

    public LoadingCache<String, Object> cache = CafflineUtils.getInstance();



    @Override
    public void process(HttpResponse httpResponse) {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        String methodName = stackTraceElements[7].getMethodName();
//        Console.log("当前执行的方法名: " + methodName);
        String url = null;
        String uri = "";
        try {
            Class<?> clazz = httpResponse.getClass();
            Field field = clazz.getDeclaredField("httpConnection");
            field.setAccessible(true);
            url = String.valueOf(((HttpConnection) field.get(httpResponse)).getUrl());
            uri = new URI(url).getPath();
        } catch (Exception e) {
            e.printStackTrace();
        }
        String title = methodName + " -【" + uri + "】";
//        Console.log("【http请求】：{}\n 【http响应】：{}\n========================\n",cache.get(url), httpResponse.body());
        log.info("【http请求】：{}\n 【http响应】：{}\n========================\n",cache.get(url), httpResponse);
        Allure.addAttachment(title, "【TIME】" + DateUtil.now() + "\n【http请求】：" + cache.get(url) + "\n分割线========================\n" + "\n 【http响应】：" + url + "\n" + httpResponse );
        try {
            Thread.sleep(500); // 捕获 InterruptedException
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            log.error("线程被中断", e);
        }
    }
}
