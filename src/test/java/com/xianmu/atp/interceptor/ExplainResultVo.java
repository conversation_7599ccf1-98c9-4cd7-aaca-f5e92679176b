package com.xianmu.atp.interceptor;

import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName ExplainResultVo
 * @date 2025-01-13
 */
@Data
public class ExplainResultVo {
    private String id;
    private String selectType;
    private String table;
    private String partitions;
    private String type;
    private String possibleKeys;
    private String key;
    private String keyLen;
    private String ref;
    private String rows;
    private String filtered;
    private String extra;

}
