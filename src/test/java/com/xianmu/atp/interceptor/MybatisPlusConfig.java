package com.xianmu.atp.interceptor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import io.qameta.allure.Allure;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName StdoutLogger
 * @date 2025-01-10
 */
@Configuration
public class MybatisPlusConfig {

    @Autowired
    private CustomMybaitsPlusInterceptor customMybaitsPlusInterceptor;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        InnerInterceptor innerInterceptor = new InnerInterceptor() {
            @Override
            public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
                InnerInterceptor.super.beforeQuery(executor, ms, parameter, rowBounds, resultHandler, boundSql);
                Console.log("sql执行：bs:{}", boundSql.getSql());
                Allure.addAttachment("sql执行：bs:{}", boundSql.getSql());
            }

            @Override
            public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) throws SQLException {
                InnerInterceptor.super.beforeUpdate(executor, ms, parameter);
            }

            @Override
            public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
                InnerInterceptor.super.beforePrepare(sh, connection, transactionTimeout);
                Console.log("sql执行：bp:{}", sh);
            }

            @Override
            public void beforeGetBoundSql(StatementHandler sh) {
                InnerInterceptor.super.beforeGetBoundSql(sh);
                Console.log("sql执行：s:{}", sh);
            }
        };
        interceptor.addInnerInterceptor(customMybaitsPlusInterceptor);
        return interceptor;
    }
}
