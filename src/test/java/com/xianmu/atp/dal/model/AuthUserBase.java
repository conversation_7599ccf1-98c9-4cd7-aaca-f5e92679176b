package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("auth_user_base") // 数据库表名
public class AuthUserBase {
    private Long id; // 主键
    private String username; // 用户名
    private String password; // 密码
    private Integer status; // 状态
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
    private String phone; // 手机号
    private String email; // 邮箱
    private String nickname; // 昵称
    private String logo; // logo
}
