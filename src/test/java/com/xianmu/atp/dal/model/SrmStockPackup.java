package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 供应商库存提报
 * @TableName srm_stock_packup
 */
@TableName(value ="srm_stock_packup")
@Data
public class SrmStockPackup implements Serializable {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * SPU编码
     */
    @TableField(value = "spu")
    private String spu;

    /**
     * SKU编码
     */
    @TableField(value = "sku")
    private String sku;

    /**
     * 仓库编号
     */
    @TableField(value = "warehouse_no")
    private Integer warehouseNo;

    /**
     * 供应商ID
     */
    @TableField(value = "supplier_id")
    private Integer supplierId;

    /**
     * 生产日期
     */
    @TableField(value = "production_date")
    private LocalDate productionDate;

    /**
     * 昨日库存提报
     */
    @TableField(value = "yesterday_packup_stock")
    private Integer yesterdayPackupStock;

    /**
     * 今日提报状态,0未提报；1已提报
     */
    @TableField(value = "packup_status")
    private Integer packupStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;

    /**
     * 今日库存提报
     */
    @TableField(value = "today_packup_stock")
    private Integer todayPackupStock;

    /**
     * 今日清理库存，0否；1是
     */
    @TableField(value = "today_clear_stock")
    private Integer todayClearStock;

    /**
     * 商品属性
     */
    @TableField(value = "sub_type")
    private Integer subType;

    /**
     * 供应商单件售价（斤）
     */
    @TableField(value = "supplier_price")
    private BigDecimal supplierPrice;

    /**
     * 供应商毛重成本(斤)
     */
    @TableField(value = "supplier_weight_price")
    private BigDecimal supplierWeightPrice;

    /**
     * 毛重（kg）
     */
    @TableField(value = "sku_weight")
    private BigDecimal skuWeight;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    @TableField(value = "quote_type")
    private Integer quoteType;

    /**
     * 供应商整件成本
     */
    @TableField(value = "supplier_cost")
    private BigDecimal supplierCost;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SrmStockPackup other = (SrmStockPackup) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpu() == null ? other.getSpu() == null : this.getSpu().equals(other.getSpu()))
            && (this.getSku() == null ? other.getSku() == null : this.getSku().equals(other.getSku()))
            && (this.getWarehouseNo() == null ? other.getWarehouseNo() == null : this.getWarehouseNo().equals(other.getWarehouseNo()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getProductionDate() == null ? other.getProductionDate() == null : this.getProductionDate().equals(other.getProductionDate()))
            && (this.getYesterdayPackupStock() == null ? other.getYesterdayPackupStock() == null : this.getYesterdayPackupStock().equals(other.getYesterdayPackupStock()))
            && (this.getPackupStatus() == null ? other.getPackupStatus() == null : this.getPackupStatus().equals(other.getPackupStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getTodayPackupStock() == null ? other.getTodayPackupStock() == null : this.getTodayPackupStock().equals(other.getTodayPackupStock()))
            && (this.getTodayClearStock() == null ? other.getTodayClearStock() == null : this.getTodayClearStock().equals(other.getTodayClearStock()))
            && (this.getSubType() == null ? other.getSubType() == null : this.getSubType().equals(other.getSubType()))
            && (this.getSupplierPrice() == null ? other.getSupplierPrice() == null : this.getSupplierPrice().equals(other.getSupplierPrice()))
            && (this.getSupplierWeightPrice() == null ? other.getSupplierWeightPrice() == null : this.getSupplierWeightPrice().equals(other.getSupplierWeightPrice()))
            && (this.getSkuWeight() == null ? other.getSkuWeight() == null : this.getSkuWeight().equals(other.getSkuWeight()))
            && (this.getQuoteType() == null ? other.getQuoteType() == null : this.getQuoteType().equals(other.getQuoteType()))
            && (this.getSupplierCost() == null ? other.getSupplierCost() == null : this.getSupplierCost().equals(other.getSupplierCost()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpu() == null) ? 0 : getSpu().hashCode());
        result = prime * result + ((getSku() == null) ? 0 : getSku().hashCode());
        result = prime * result + ((getWarehouseNo() == null) ? 0 : getWarehouseNo().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getProductionDate() == null) ? 0 : getProductionDate().hashCode());
        result = prime * result + ((getYesterdayPackupStock() == null) ? 0 : getYesterdayPackupStock().hashCode());
        result = prime * result + ((getPackupStatus() == null) ? 0 : getPackupStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getTodayPackupStock() == null) ? 0 : getTodayPackupStock().hashCode());
        result = prime * result + ((getTodayClearStock() == null) ? 0 : getTodayClearStock().hashCode());
        result = prime * result + ((getSubType() == null) ? 0 : getSubType().hashCode());
        result = prime * result + ((getSupplierPrice() == null) ? 0 : getSupplierPrice().hashCode());
        result = prime * result + ((getSupplierWeightPrice() == null) ? 0 : getSupplierWeightPrice().hashCode());
        result = prime * result + ((getSkuWeight() == null) ? 0 : getSkuWeight().hashCode());
        result = prime * result + ((getQuoteType() == null) ? 0 : getQuoteType().hashCode());
        result = prime * result + ((getSupplierCost() == null) ? 0 : getSupplierCost().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spu=").append(spu);
        sb.append(", sku=").append(sku);
        sb.append(", warehouseNo=").append(warehouseNo);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", productionDate=").append(productionDate);
        sb.append(", yesterdayPackupStock=").append(yesterdayPackupStock);
        sb.append(", packupStatus=").append(packupStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", creator=").append(creator);
        sb.append(", updater=").append(updater);
        sb.append(", todayPackupStock=").append(todayPackupStock);
        sb.append(", todayClearStock=").append(todayClearStock);
        sb.append(", subType=").append(subType);
        sb.append(", supplierPrice=").append(supplierPrice);
        sb.append(", supplierWeightPrice=").append(supplierWeightPrice);
        sb.append(", skuWeight=").append(skuWeight);
        sb.append(", quoteType=").append(quoteType);
        sb.append(", supplierCost=").append(supplierCost);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}