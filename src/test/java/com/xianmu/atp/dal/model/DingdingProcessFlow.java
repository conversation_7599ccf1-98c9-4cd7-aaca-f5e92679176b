package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 钉钉审批表
 * @TableName dingding_process_flow
 */
@TableName(value ="dingding_process_flow")
@Data
public class DingdingProcessFlow implements Serializable {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 钉钉审批实例id
     */
    @TableField(value = "process_instance_id")
    private String processInstanceId;

    /**
     * 钉钉审批模版code
     */
    @TableField(value = "process_code")
    private String processCode;

    /**
     * 当前审批所属业务 1-CRM-客户倒闭申请
     */
    @TableField(value = "biz_type")
    private Integer bizType;

    /**
     * 业务数据id
     */
    @TableField(value = "biz_id")
    private Long bizId;

    /**
     * 审批状态 1-审批中 2-审批通过 3-审批拒绝 4-审批终止 5-审批撤销中
     */
    @TableField(value = "process_status")
    private Integer processStatus;

    /**
     * 钉钉审批详情url
     */
    @TableField(value = "process_url")
    private String processUrl;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人id(admin.admin_id)
     */
    @TableField(value = "creator")
    private Integer creator;

    /**
     * 修改人id(admin.admin_id或钉钉用户id)
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 审批表单数据
     */
    @TableField(value = "form_data")
    private String formData;

    /**
     * 平台类型
     */
    @TableField(value = "platform_type")
    private String platformType;

    /**
     * 环境
     */
    @TableField(value = "env")
    private String env;

    /**
     * 幂等键
     */
    @TableField(value = "uuid")
    private String uuid;

    /**
     * 自动撤销时间
     */
    @TableField(value = "auto_terminate_time")
    private LocalDateTime autoTerminateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DingdingProcessFlow other = (DingdingProcessFlow) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProcessInstanceId() == null ? other.getProcessInstanceId() == null : this.getProcessInstanceId().equals(other.getProcessInstanceId()))
            && (this.getProcessCode() == null ? other.getProcessCode() == null : this.getProcessCode().equals(other.getProcessCode()))
            && (this.getBizType() == null ? other.getBizType() == null : this.getBizType().equals(other.getBizType()))
            && (this.getBizId() == null ? other.getBizId() == null : this.getBizId().equals(other.getBizId()))
            && (this.getProcessStatus() == null ? other.getProcessStatus() == null : this.getProcessStatus().equals(other.getProcessStatus()))
            && (this.getProcessUrl() == null ? other.getProcessUrl() == null : this.getProcessUrl().equals(other.getProcessUrl()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getFormData() == null ? other.getFormData() == null : this.getFormData().equals(other.getFormData()))
            && (this.getPlatformType() == null ? other.getPlatformType() == null : this.getPlatformType().equals(other.getPlatformType()))
            && (this.getEnv() == null ? other.getEnv() == null : this.getEnv().equals(other.getEnv()))
            && (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getAutoTerminateTime() == null ? other.getAutoTerminateTime() == null : this.getAutoTerminateTime().equals(other.getAutoTerminateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProcessInstanceId() == null) ? 0 : getProcessInstanceId().hashCode());
        result = prime * result + ((getProcessCode() == null) ? 0 : getProcessCode().hashCode());
        result = prime * result + ((getBizType() == null) ? 0 : getBizType().hashCode());
        result = prime * result + ((getBizId() == null) ? 0 : getBizId().hashCode());
        result = prime * result + ((getProcessStatus() == null) ? 0 : getProcessStatus().hashCode());
        result = prime * result + ((getProcessUrl() == null) ? 0 : getProcessUrl().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getFormData() == null) ? 0 : getFormData().hashCode());
        result = prime * result + ((getPlatformType() == null) ? 0 : getPlatformType().hashCode());
        result = prime * result + ((getEnv() == null) ? 0 : getEnv().hashCode());
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getAutoTerminateTime() == null) ? 0 : getAutoTerminateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", processInstanceId=").append(processInstanceId);
        sb.append(", processCode=").append(processCode);
        sb.append(", bizType=").append(bizType);
        sb.append(", bizId=").append(bizId);
        sb.append(", processStatus=").append(processStatus);
        sb.append(", processUrl=").append(processUrl);
        sb.append(", remark=").append(remark);
        sb.append(", creator=").append(creator);
        sb.append(", updater=").append(updater);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", formData=").append(formData);
        sb.append(", platformType=").append(platformType);
        sb.append(", env=").append(env);
        sb.append(", uuid=").append(uuid);
        sb.append(", autoTerminateTime=").append(autoTerminateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}