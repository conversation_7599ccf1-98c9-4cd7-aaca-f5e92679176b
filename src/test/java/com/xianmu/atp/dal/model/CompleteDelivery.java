package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

import java.util.Date;

@Data
@TableName("complete_delivery")
public class CompleteDelivery {

    /**
     * 主键、自增
     */

    private Integer id;

    /**
     * 物流中心编号（配送仓编号）
     */
    private Integer storeNo;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 配送完成时间
     */
    private String completeDeliveryTime;

    /**
     * 状态 0 正常 1 暂停
     */
    private Integer status;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 区域
     */
    private String region;

    /**
     * 城市名称
     */
    private String city;


}
