package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName AreaSku
 * @date 2024-11-05
 */

@Data
@TableName("area_sku")
public class AreaSku {

    /**
     * 区域存储的唯一标识
     */
    private long id;
    /**
     * 商品SKU
     */
    private String sku;
    /**
     * 区域编号
     */
    private long areaNo;
    /**
     * 商品数量
     */
    private long quantity;
    /**
     * 共享数量
     */
    private long share;
    /**
     * 原始价格
     */
    private double originalPrice;
    /**
     * 价格
     */
    private double price;
    /**
     * 更新时间
     */
    private java.sql.Timestamp updateTime;
    /**
     * 销售状态
     */
    private long onSale;
    /**
     * 添加时间
     */
    private java.sql.Timestamp addTime;
    /**
     * 优先级
     */
    private long priority;
    /**
     * 产品优先级
     */
    private long pdPriority;
    /**
     * 阶梯价格
     */
    private String ladderPrice;
    /**
     * 限制数量
     */
    private long limitedQuantity;
    /**
     * 销售模式
     */
    private long salesMode;
    /**
     * 展示状态
     */
    private long show;
    /**
     * 信息
     */
    private String info;
    /**
     * 类型
     */
    private long mType;
    /**
     * 展示提前期
     */
    private long showAdvance;
    /**
     * 提前期
     */
    private String advance;
    /**
     * 角落状态
     */
    private long cornerStatus;
    /**
     * 角落开放时间
     */
    private java.sql.Timestamp cornerOpenTime;
    /**
     * 开放销售状态
     */
    private long openSale;
    /**
     * 开放销售时间
     */
    private java.sql.Timestamp openSaleTime;
    /**
     * 关闭销售状态
     */
    private long closeSale;
    /**
     * 关闭销售时间
     */
    private java.sql.Timestamp closeSaleTime;
    /**
     * 固定标志
     */
    private long fixFlag;
    /**
     * 固定数量
     */
    private long fixNum;
    /**
     * 更新者
     */
    private String updater;

}
