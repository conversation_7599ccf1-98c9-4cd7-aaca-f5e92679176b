package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

import java.util.Date;

/**
 * 类<code>TmsOutDistanceConfig</code>用于：运输管理系统的签收距离配置
 *
 * <AUTHOR>
 * @ClassName TmsOutDistanceConfig
 * @date 2024-11-05
 */
@Data
@TableName("tms_out_distance_config")
public class OutDistanceConfig {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 签收距离（公里）
     */
    private Float outDistance;

    /**
     * 状态，0正常，1暂停
     */
    private Byte state;

    /**
     * 操作人ID
     */
    private Integer adminId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
