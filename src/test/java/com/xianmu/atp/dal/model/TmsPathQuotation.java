package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;


@Data
@TableName("tms_path_quotation")
public class TmsPathQuotation {
    // 数据库主键
    private Long id;

    // 路由 ID
    private Long pathId;

    // 承运商 ID
    private Long carrierId;

    // 车型 (0: 小面包车, 1: 中面包车, 2: 依维柯, 3: 小型货车, 4: 4米2, 5: 6米8, 6: 7米6, 7: 7米9, 8: 13米5, 9: 17米5)
    private Integer carType;

    // 存储条件 (0: 常温, 1: 冷藏)
    private Integer storage;

    // 报价费用 (单位: 元)
    private BigDecimal quotationFee;
}
