package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("tms_path")
public class TmsPath {
    // 数据库主键
    private Long id;

    // 数据创建时间
    private LocalDateTime createTime;

    // 数据最后更新时间
    private LocalDateTime updateTime;

    // 路径编码
    private String pathCode;

    // 路径名称
    private String pathName;

    // 驾驶员 ID
    private Long driverId;

    // 车辆 ID
    private Long carId;

    // 承运人 ID
    private Long carrierId;

    // 是否自动切换 (0: 否, 1: 是)
    private Integer autoSwitch;

    // 运输周期周
    private Integer periodWeek;

    // 运输周期星期几
    private String periodWeekday;

    // 交付天数
    private Integer deliveryDay;

    // 交付时间
    private String deliveryDayTime;

    // 状态 (0: 无效, 1: 有效)
    private Integer status;

    // 起始站点 ID
    private Integer beginSiteId;

    // 结束站点 ID
    private Integer endSiteId;

    // 创建人
    private String creator;

    // 更新人
    private String updater;

    // 预计运费
    private BigDecimal estimateFare;

    // 创建人 ID
    private String creatorId;

    // 更新人 ID
    private String updaterId;

    // 类型 (例如：0: 普通, 1: 优先)
    private Integer type;

    // 距离
    private BigDecimal distance;

    // 区域
    private String region;
}
