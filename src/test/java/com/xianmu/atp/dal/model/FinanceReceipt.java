package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 收款单表
 * @TableName finance_receipt
 */
@TableName(value ="finance_receipt")
@Data
public class FinanceReceipt implements Serializable {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id/门店id
     */
    @TableField(value = "admin_id")
    private Long adminId;

    /**
     * 企业工商名称id
     */
    @TableField(value = "invoice_id")
    private Long invoiceId;

    /**
     * 企业工商名称
     */
    @TableField(value = "invoice_title")
    private String invoiceTitle;

    /**
     * 所属销售
     */
    @TableField(value = "saler_id")
    private Long salerId;

    /**
     * 本次核销金额
     */
    @TableField(value = "receipt_amount")
    private BigDecimal receiptAmount;

    /**
     * 匹配账单数量
     */
    @TableField(value = "bill_number")
    private Integer billNumber;

    /**
     * 收款凭证
     */
    @TableField(value = "receipt_voucher")
    private String receiptVoucher;

    /**
     * 核销状态；0-待核销；1-已核销；2-已撤销; 3驳回
     */
    @TableField(value = "write_off_status")
    private Integer writeOffStatus;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 所属销售名称
     */
    @TableField(value = "saler_name")
    private String salerName;

    /**
     * 创建人id
     */
    @TableField(value = "creator_id")
    private Long creatorId;

    /**
     * 品牌名称/门店名称/供应商名称
     */
    @TableField(value = "name_remakes")
    private String nameRemakes;

    /**
     * 客户类型：0-大客户；1-单店
     */
    @TableField(value = "customer_type")
    private Integer customerType;

    /**
     * 招银流水表id
     */
    @TableField(value = "finance_bank_flowing_water_id")
    private Long financeBankFlowingWaterId;

    /**
     * 收款类型 0 账期账单 1 鲜沐卡 2 供应商退款
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 核销单号
     */
    @TableField(value = "receipt_no")
    private String receiptNo;

    /**
     * 审核人
     */
    @TableField(value = "auditor")
    private String auditor;

    /**
     * 审核时间
     */
    @TableField(value = "audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核人id
     */
    @TableField(value = "auditor_id")
    private Long auditorId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        FinanceReceipt other = (FinanceReceipt) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAdminId() == null ? other.getAdminId() == null : this.getAdminId().equals(other.getAdminId()))
            && (this.getInvoiceId() == null ? other.getInvoiceId() == null : this.getInvoiceId().equals(other.getInvoiceId()))
            && (this.getInvoiceTitle() == null ? other.getInvoiceTitle() == null : this.getInvoiceTitle().equals(other.getInvoiceTitle()))
            && (this.getSalerId() == null ? other.getSalerId() == null : this.getSalerId().equals(other.getSalerId()))
            && (this.getReceiptAmount() == null ? other.getReceiptAmount() == null : this.getReceiptAmount().equals(other.getReceiptAmount()))
            && (this.getBillNumber() == null ? other.getBillNumber() == null : this.getBillNumber().equals(other.getBillNumber()))
            && (this.getReceiptVoucher() == null ? other.getReceiptVoucher() == null : this.getReceiptVoucher().equals(other.getReceiptVoucher()))
            && (this.getWriteOffStatus() == null ? other.getWriteOffStatus() == null : this.getWriteOffStatus().equals(other.getWriteOffStatus()))
            && (this.getRemarks() == null ? other.getRemarks() == null : this.getRemarks().equals(other.getRemarks()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getSalerName() == null ? other.getSalerName() == null : this.getSalerName().equals(other.getSalerName()))
            && (this.getCreatorId() == null ? other.getCreatorId() == null : this.getCreatorId().equals(other.getCreatorId()))
            && (this.getNameRemakes() == null ? other.getNameRemakes() == null : this.getNameRemakes().equals(other.getNameRemakes()))
            && (this.getCustomerType() == null ? other.getCustomerType() == null : this.getCustomerType().equals(other.getCustomerType()))
            && (this.getFinanceBankFlowingWaterId() == null ? other.getFinanceBankFlowingWaterId() == null : this.getFinanceBankFlowingWaterId().equals(other.getFinanceBankFlowingWaterId()))
            && (this.getPayType() == null ? other.getPayType() == null : this.getPayType().equals(other.getPayType()))
            && (this.getReceiptNo() == null ? other.getReceiptNo() == null : this.getReceiptNo().equals(other.getReceiptNo()))
            && (this.getAuditor() == null ? other.getAuditor() == null : this.getAuditor().equals(other.getAuditor()))
            && (this.getAuditTime() == null ? other.getAuditTime() == null : this.getAuditTime().equals(other.getAuditTime()))
            && (this.getAuditorId() == null ? other.getAuditorId() == null : this.getAuditorId().equals(other.getAuditorId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAdminId() == null) ? 0 : getAdminId().hashCode());
        result = prime * result + ((getInvoiceId() == null) ? 0 : getInvoiceId().hashCode());
        result = prime * result + ((getInvoiceTitle() == null) ? 0 : getInvoiceTitle().hashCode());
        result = prime * result + ((getSalerId() == null) ? 0 : getSalerId().hashCode());
        result = prime * result + ((getReceiptAmount() == null) ? 0 : getReceiptAmount().hashCode());
        result = prime * result + ((getBillNumber() == null) ? 0 : getBillNumber().hashCode());
        result = prime * result + ((getReceiptVoucher() == null) ? 0 : getReceiptVoucher().hashCode());
        result = prime * result + ((getWriteOffStatus() == null) ? 0 : getWriteOffStatus().hashCode());
        result = prime * result + ((getRemarks() == null) ? 0 : getRemarks().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getSalerName() == null) ? 0 : getSalerName().hashCode());
        result = prime * result + ((getCreatorId() == null) ? 0 : getCreatorId().hashCode());
        result = prime * result + ((getNameRemakes() == null) ? 0 : getNameRemakes().hashCode());
        result = prime * result + ((getCustomerType() == null) ? 0 : getCustomerType().hashCode());
        result = prime * result + ((getFinanceBankFlowingWaterId() == null) ? 0 : getFinanceBankFlowingWaterId().hashCode());
        result = prime * result + ((getPayType() == null) ? 0 : getPayType().hashCode());
        result = prime * result + ((getReceiptNo() == null) ? 0 : getReceiptNo().hashCode());
        result = prime * result + ((getAuditor() == null) ? 0 : getAuditor().hashCode());
        result = prime * result + ((getAuditTime() == null) ? 0 : getAuditTime().hashCode());
        result = prime * result + ((getAuditorId() == null) ? 0 : getAuditorId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", adminId=").append(adminId);
        sb.append(", invoiceId=").append(invoiceId);
        sb.append(", invoiceTitle=").append(invoiceTitle);
        sb.append(", salerId=").append(salerId);
        sb.append(", receiptAmount=").append(receiptAmount);
        sb.append(", billNumber=").append(billNumber);
        sb.append(", receiptVoucher=").append(receiptVoucher);
        sb.append(", writeOffStatus=").append(writeOffStatus);
        sb.append(", remarks=").append(remarks);
        sb.append(", createTime=").append(createTime);
        sb.append(", creator=").append(creator);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updater=").append(updater);
        sb.append(", salerName=").append(salerName);
        sb.append(", creatorId=").append(creatorId);
        sb.append(", nameRemakes=").append(nameRemakes);
        sb.append(", customerType=").append(customerType);
        sb.append(", financeBankFlowingWaterId=").append(financeBankFlowingWaterId);
        sb.append(", payType=").append(payType);
        sb.append(", receiptNo=").append(receiptNo);
        sb.append(", auditor=").append(auditor);
        sb.append(", auditTime=").append(auditTime);
        sb.append(", auditorId=").append(auditorId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}