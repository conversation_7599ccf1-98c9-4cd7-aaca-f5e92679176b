package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("wnc_precise_delivery_area_detail")
public class PreciseDeliveryAreaDetail {
    private Long id;              // 主键
    private Long configId;        // 配置ID
    private String adCode;        // 区域编码
    private String city;          // 市
    private String area;          // 区
    private LocalDateTime createTime; // 创建时间
}
