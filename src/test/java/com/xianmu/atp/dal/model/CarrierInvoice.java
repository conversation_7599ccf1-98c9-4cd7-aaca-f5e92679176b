package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("carrier_invoice")

public class CarrierInvoice {
    private Long id; // 主键 ID
    private Long carrierId; // 承运商 ID
    private String invoiceHead; // 发票抬头
    private String taxNo; // 税号
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime;
}
