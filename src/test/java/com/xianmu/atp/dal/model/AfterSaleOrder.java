package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 售后订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-49 11:42:42
 */
@Getter
@Setter
@TableName("after_sale_order")
public class AfterSaleOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 售后编号
     */
    @TableField("after_sale_order_no")
    private String afterSaleOrderNo;

    /**
     * 售后用户
     */
    @TableField("m_id")
    private Long mId;

    /**
     * 售后订单
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 售后sku，为空则为整单退
     */
    @TableField("sku")
    private String sku;

    /**
     * 售后数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 售后原因
     */
    @TableField("after_sale_type")
    private String afterSaleType;

    /**
     * 照片凭证
     */
    @TableField("proof_pic")
    private String proofPic;

    /**
     * 售后处理方式,0返券，1未知，2退款，3录入账单，4退货退款，5退货录入账单，6换货，7补发，8人工退款，9拒收退款，10拒收账单，11拦截退款，12拦截录入账单 13退运费 14 退运费录入账单
     */
    @TableField("handle_type")
    private Integer handleType;

    /**
     * 补偿数量
     */
    @TableField("handle_num")
    private BigDecimal handleNum;

    /**
     * 处理人
     */
    @TableField("`handler`")
    private String handler;

    /**
     * 审核人
     */
    @TableField("auditer")
    private String auditer;

    /**
     * 补充说明
     */
    @TableField("apply_remark")
    private String applyRemark;

    /**
     * 售后状态：0、审核中
1、处理中
2、成功
3、失败
4、补充凭证
11、取消
12、退款中
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 处理结果
     */
    @TableField("handle_remark")
    private String handleRemark;

    /**
     * 售后单位
     */
    @TableField("after_sale_unit")
    private String afterSaleUnit;

    /**
     * 更新时间
     */
    @TableField("updatetime")
    private LocalDateTime updatetime;

    /**
     * 添加时间
     */
    @TableField("add_time")
    private LocalDateTime addTime;

    /**
     * 0、普通售后 1、极速售后
     */
    @TableField("`type`")
    private Integer type;

    @TableField("grade")
    private Integer grade;

    /**
     * 0 未到货售后 1 已到货售后
     */
    @TableField("deliveryed")
    private Integer deliveryed;

    @TableField("times")
    private Integer times;

    @TableField("suit_id")
    private Integer suitId;

    @TableField("`view`")
    private Integer view;

    /**
     * 是否全额退款
     */
    @TableField("is_full")
    private Integer isFull;

    /**
     * 售后发起子账号id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 配送计划id
     */
    @TableField("delivery_id")
    private Integer deliveryId;

    /**
     * 是否需要回收费 0 不需要 1 需要
     */
    @TableField("recovery_type")
    private Integer recoveryType;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 售后类型
     */
    @TableField("after_sale_remark_type")
    private Integer afterSaleRemarkType;

    /**
     * 售后类型备注
     */
    @TableField("after_sale_remark")
    private String afterSaleRemark;

    /**
     * 售后单类型：0 普通售后单，1 拦截售后单
     */
    @TableField("after_sale_order_status")
    private Integer afterSaleOrderStatus;

    /**
     * 0为没退运费，1为退了运费
     */
    @TableField("refund_freight")
    private Integer refundFreight;

    /**
     * 关闭售后单操作者
     */
    @TableField("closer")
    private String closer;

    /**
     * 关单时间
     */
    @TableField("close_time")
    private LocalDateTime closeTime;

    /**
     * 商品类型：0、普通商品 1、赠品 2、换购商品
     */
    @TableField("product_type")
    private Integer productType;

    /**
     * 补发是否带货回来：0没带，1带了
     */
    @TableField("carrying_goods")
    private Integer carryingGoods;

    /**
     * 售后快照信息（平台、供应商定责比例）
     */
    @TableField("`snapshot`")
    private String snapshot;

    /**
     * 自动售后标记，1-自动补差售后
     */
    @TableField("auto_after_sale_flag")
    private Integer autoAfterSaleFlag;


}
