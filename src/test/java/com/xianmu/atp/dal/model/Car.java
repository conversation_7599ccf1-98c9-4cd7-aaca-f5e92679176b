package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("tms_car")
public class Car {
    private Long id;                        // primary key
    private String carNumber;               // 车牌号
    private Integer type;                   // 车型
    private BigDecimal volume;              // 体积
    private BigDecimal weight;              // 重量
    private String carPhotos;               // 车辆照片
    private String driverPhotos;            // 行驶证照片
    private Byte status;                    // 状态
    private String adminId;                 // 操作人id
    private Date createTime;                // 创建时间
    private Date updateTime;                // 更新时间
    private Integer storage;                 // 存储条件
    private String trafficInsurancePolicy;  // 交强险保额保单
    private Date commercialInsuranceExpireTime; // 商业险到期时间
    private BigDecimal commercialInsuranceAmount; // 商业险保额额度
    private String commercialInsurancePolicy; // 商业险保额保单
    private Date trafficInsuranceExpireTime; // 交强险到期时间
    private BigDecimal trafficInsuranceAmount; // 交强险保额额度
    private Integer quantity;               // 装载件数
}
