package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("tms_driver")
public class Driver {
    private Long id;                         // 主键 ID
    private String name;                     // 司机姓名
    private String phone;                    // 手机号（登录账号）
    private Byte cooperationCycle;           // 合作周期 0: 临时, 1: 长期
    private Byte businessType;               // 业务类型 0: 干线, 1: 城配, 2: 干线城配
    private Byte status;                     // 状态 0: 有效, 1: 无效
    private String idCard;                   // 身份证号
    private String idCardFrontPic;           // 身份证正面照片路径
    private String idCardBehindPic;          // 身份证背面照片路径
    private String driverPics;                // 驾驶证照片路径
    private Integer adminId;                 // 操作人 ID
    private Date createTime;                 // 创建时间
    private Date updateTime;                 // 更新时间
    private Long citySiteId;                 // 城配仓点位 ID
    private Integer cityCarrierId;           // 承运商 ID
    private Long baseUserId;                 // 用户中心表 user_base_id
}
