package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 入库任务主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-348 11:18:07
 */
@Data
@TableName("stock_task_storage")
public class StockTaskStorage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 操作人id
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     * 来源单号
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 入库任务类型
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 状态入库进度
     */
    @TableField("state")
    private Integer state;

    /**
     * 预约入库时间
     */
    @TableField("expect_time")
    private LocalDateTime expectTime;

    /**
     * 入库库存仓编号
     */
    @TableField("in_warehouse_no")
    private Integer inWarehouseNo;

    /**
     * 城配仓(货品来源仓)
     */
    @TableField("out_warehouse_no")
    private Integer outWarehouseNo;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 入库仓名称
     */
    @TableField("in_warehouse_name")
    private String inWarehouseName;

    /**
     * 出库仓名称
     */
    @TableField("out_warehouse_name")
    private String outWarehouseName;

    /**
     * 入库进度
     */
    @TableField("process_state")
    private Integer processState;

    /**
     * 鲜果,非鲜果
     */
    @TableField("category")
    private Integer category;

    /**
     * 关闭原因
     */
    @TableField("close_reason")
    private String closeReason;

    /**
     * 应入不符合原因
     */
    @TableField("mismatch_reason")
    private String mismatchReason;

    /**
     * 旧任务id
     */
    @TableField("stock_task_id")
    private Long stockTaskId;

    /**
     * 操作人名称
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 任务归属人
     */
    @TableField("ownership")
    private String ownership;

    /**
     * 租户id(saas品牌方)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 售后单号
     */
    @TableField("after_sale_order_no")
    private String afterSaleOrderNo;

    /**
     * 文件上传地址
     */
    @TableField("file_address")
    private String fileAddress;

    /**
     * 关闭时间
     */
    @TableField("close_time")
    private LocalDateTime closeTime;

    /**
     * 履约单号
     */
    @TableField("fulfillment_no")
    private String fulfillmentNo;

    /**
     * 货主
     */
    @TableField("cargo_owner")
    private String cargoOwner;

    /**
     * 到货时间
     */
    @TableField("arrival_time")
    private LocalDateTime arrivalTime;

    /**
     * 收货状态（默认未确认收货）
     */
    @TableField("receiving_state")
    private Integer receivingState;

    /**
     * 任务票据
     */
    @TableField("receipt_url")
    private String receiptUrl;

    /**
     * 系统来源 0-内部 1-外部
     */
    @TableField("system_source")
    private Integer systemSource;

    /**
     * OFC货品供应单批次号
     */
    @TableField("pso_no")
    private String psoNo;

    /**
     * 幂等键，不同type类型不同规则
     */
    @TableField("unique_key")
    private String uniqueKey;

    /**
     * 三方订单号
     */
    @TableField("third_order_no")
    private String thirdOrderNo;

    /**
     * 采购模式 1: 代销不入仓越库采购 2: 备货采购单 3: 预提采购单 4:POP采购
     */
    @TableField("purchase_mode")
    private Integer purchaseMode;


}
