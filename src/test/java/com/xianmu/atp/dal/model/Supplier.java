package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 供应商
 * @TableName supplier
 */
@TableName(value ="supplier")
@Data
public class Supplier implements Serializable {
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 供应商名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 经营范围
     */
    @TableField(value = "category_array")
    private String categoryArray;

    /**
     * 供应品类
     */
    @TableField(value = "product_array")
    private String productArray;

    /**
     * 
     */
    @TableField(value = "pay_type")
    private String payType;

    /**
     * 
     */
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 
     */
    @TableField(value = "account_bank")
    private String accountBank;

    /**
     * 
     */
    @TableField(value = "account")
    private String account;

    /**
     * 供应商管理人
     */
    @TableField(value = "manager")
    private String manager;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 是否开发票
     */
    @TableField(value = "invoice")
    private Integer invoice;

    /**
     * 合同
     */
    @TableField(value = "contract")
    private String contract;

    /**
     * 发货频率
     */
    @TableField(value = "delivery_frequent")
    private String deliveryFrequent;

    /**
     * 0、账期结算 1、备用金结算 2、公对公现结 3、私对私现结
     */
    @TableField(value = "settle_form")
    private Integer settleForm;

    /**
     * 结算类型 0、月结 1、自定义
     */
    @TableField(value = "settle_type")
    private Integer settleType;

    /**
     * 自定义开启日期
     */
    @TableField(value = "custom_start_date")
    private LocalDate customStartDate;

    /**
     * 自定义周期天数
     */
    @TableField(value = "custom_cycle")
    private Integer customCycle;

    /**
     * 账期天数
     */
    @TableField(value = "credit_days")
    private Integer creditDays;

    /**
     * 0企业（生产商），1个人，2企业（经销商）
     */
    @TableField(value = "supplier_type")
    private Integer supplierType;

    /**
     * 供应商工商信息（税号字段/身份证号）
     */
    @TableField(value = "tax_number")
    private String taxNumber;

    /**
     * 企业（生产商），1个人，2企业（经销商）【无用字段】
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 0启用，1停用,2审核中，3已关闭
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 审核通过日期（用于计算剩余天数）
     */
    @TableField(value = "audit_pass_date")
    private LocalDate auditPassDate;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 最后修改人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 二维码是否展示供应商信息 0不展示1展示
     */
    @TableField(value = "qr_code_show_supplier_switch")
    private Integer qrCodeShowSupplierSwitch;

    /**
     * 来源:xianmu,saas
     */
    @TableField(value = "source")
    private String source;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 业务类型，1代销
     */
    @TableField(value = "business_type")
    private Integer businessType;

    /**
     * 外部供应商id
     */
    @TableField(value = "customer_supplier_id")
    private String customerSupplierId;

    /**
     * 提货日配置（json）
     */
    @TableField(value = "packup_frequent")
    private String packupFrequent;

    /**
     * 不可提货日期配置(json)
     */
    @TableField(value = "no_packup_frequent_date")
    private String noPackupFrequentDate;

    /**
     * 代销采购负责人
     */
    @TableField(value = "consignment_purchaser")
    private String consignmentPurchaser;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Supplier other = (Supplier) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getCategoryArray() == null ? other.getCategoryArray() == null : this.getCategoryArray().equals(other.getCategoryArray()))
            && (this.getProductArray() == null ? other.getProductArray() == null : this.getProductArray().equals(other.getProductArray()))
            && (this.getPayType() == null ? other.getPayType() == null : this.getPayType().equals(other.getPayType()))
            && (this.getAccountName() == null ? other.getAccountName() == null : this.getAccountName().equals(other.getAccountName()))
            && (this.getAccountBank() == null ? other.getAccountBank() == null : this.getAccountBank().equals(other.getAccountBank()))
            && (this.getAccount() == null ? other.getAccount() == null : this.getAccount().equals(other.getAccount()))
            && (this.getManager() == null ? other.getManager() == null : this.getManager().equals(other.getManager()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getInvoice() == null ? other.getInvoice() == null : this.getInvoice().equals(other.getInvoice()))
            && (this.getContract() == null ? other.getContract() == null : this.getContract().equals(other.getContract()))
            && (this.getDeliveryFrequent() == null ? other.getDeliveryFrequent() == null : this.getDeliveryFrequent().equals(other.getDeliveryFrequent()))
            && (this.getSettleForm() == null ? other.getSettleForm() == null : this.getSettleForm().equals(other.getSettleForm()))
            && (this.getSettleType() == null ? other.getSettleType() == null : this.getSettleType().equals(other.getSettleType()))
            && (this.getCustomStartDate() == null ? other.getCustomStartDate() == null : this.getCustomStartDate().equals(other.getCustomStartDate()))
            && (this.getCustomCycle() == null ? other.getCustomCycle() == null : this.getCustomCycle().equals(other.getCustomCycle()))
            && (this.getCreditDays() == null ? other.getCreditDays() == null : this.getCreditDays().equals(other.getCreditDays()))
            && (this.getSupplierType() == null ? other.getSupplierType() == null : this.getSupplierType().equals(other.getSupplierType()))
            && (this.getTaxNumber() == null ? other.getTaxNumber() == null : this.getTaxNumber().equals(other.getTaxNumber()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAuditPassDate() == null ? other.getAuditPassDate() == null : this.getAuditPassDate().equals(other.getAuditPassDate()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getQrCodeShowSupplierSwitch() == null ? other.getQrCodeShowSupplierSwitch() == null : this.getQrCodeShowSupplierSwitch().equals(other.getQrCodeShowSupplierSwitch()))
            && (this.getSource() == null ? other.getSource() == null : this.getSource().equals(other.getSource()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getBusinessType() == null ? other.getBusinessType() == null : this.getBusinessType().equals(other.getBusinessType()))
            && (this.getCustomerSupplierId() == null ? other.getCustomerSupplierId() == null : this.getCustomerSupplierId().equals(other.getCustomerSupplierId()))
            && (this.getPackupFrequent() == null ? other.getPackupFrequent() == null : this.getPackupFrequent().equals(other.getPackupFrequent()))
            && (this.getNoPackupFrequentDate() == null ? other.getNoPackupFrequentDate() == null : this.getNoPackupFrequentDate().equals(other.getNoPackupFrequentDate()))
            && (this.getConsignmentPurchaser() == null ? other.getConsignmentPurchaser() == null : this.getConsignmentPurchaser().equals(other.getConsignmentPurchaser()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getCategoryArray() == null) ? 0 : getCategoryArray().hashCode());
        result = prime * result + ((getProductArray() == null) ? 0 : getProductArray().hashCode());
        result = prime * result + ((getPayType() == null) ? 0 : getPayType().hashCode());
        result = prime * result + ((getAccountName() == null) ? 0 : getAccountName().hashCode());
        result = prime * result + ((getAccountBank() == null) ? 0 : getAccountBank().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        result = prime * result + ((getManager() == null) ? 0 : getManager().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getInvoice() == null) ? 0 : getInvoice().hashCode());
        result = prime * result + ((getContract() == null) ? 0 : getContract().hashCode());
        result = prime * result + ((getDeliveryFrequent() == null) ? 0 : getDeliveryFrequent().hashCode());
        result = prime * result + ((getSettleForm() == null) ? 0 : getSettleForm().hashCode());
        result = prime * result + ((getSettleType() == null) ? 0 : getSettleType().hashCode());
        result = prime * result + ((getCustomStartDate() == null) ? 0 : getCustomStartDate().hashCode());
        result = prime * result + ((getCustomCycle() == null) ? 0 : getCustomCycle().hashCode());
        result = prime * result + ((getCreditDays() == null) ? 0 : getCreditDays().hashCode());
        result = prime * result + ((getSupplierType() == null) ? 0 : getSupplierType().hashCode());
        result = prime * result + ((getTaxNumber() == null) ? 0 : getTaxNumber().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAuditPassDate() == null) ? 0 : getAuditPassDate().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getQrCodeShowSupplierSwitch() == null) ? 0 : getQrCodeShowSupplierSwitch().hashCode());
        result = prime * result + ((getSource() == null) ? 0 : getSource().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getBusinessType() == null) ? 0 : getBusinessType().hashCode());
        result = prime * result + ((getCustomerSupplierId() == null) ? 0 : getCustomerSupplierId().hashCode());
        result = prime * result + ((getPackupFrequent() == null) ? 0 : getPackupFrequent().hashCode());
        result = prime * result + ((getNoPackupFrequentDate() == null) ? 0 : getNoPackupFrequentDate().hashCode());
        result = prime * result + ((getConsignmentPurchaser() == null) ? 0 : getConsignmentPurchaser().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", address=").append(address);
        sb.append(", categoryArray=").append(categoryArray);
        sb.append(", productArray=").append(productArray);
        sb.append(", payType=").append(payType);
        sb.append(", accountName=").append(accountName);
        sb.append(", accountBank=").append(accountBank);
        sb.append(", account=").append(account);
        sb.append(", manager=").append(manager);
        sb.append(", remark=").append(remark);
        sb.append(", invoice=").append(invoice);
        sb.append(", contract=").append(contract);
        sb.append(", deliveryFrequent=").append(deliveryFrequent);
        sb.append(", settleForm=").append(settleForm);
        sb.append(", settleType=").append(settleType);
        sb.append(", customStartDate=").append(customStartDate);
        sb.append(", customCycle=").append(customCycle);
        sb.append(", creditDays=").append(creditDays);
        sb.append(", supplierType=").append(supplierType);
        sb.append(", taxNumber=").append(taxNumber);
        sb.append(", type=").append(type);
        sb.append(", status=").append(status);
        sb.append(", auditPassDate=").append(auditPassDate);
        sb.append(", creator=").append(creator);
        sb.append(", createTime=").append(createTime);
        sb.append(", updater=").append(updater);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", qrCodeShowSupplierSwitch=").append(qrCodeShowSupplierSwitch);
        sb.append(", source=").append(source);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", businessType=").append(businessType);
        sb.append(", customerSupplierId=").append(customerSupplierId);
        sb.append(", packupFrequent=").append(packupFrequent);
        sb.append(", noPackupFrequentDate=").append(noPackupFrequentDate);
        sb.append(", consignmentPurchaser=").append(consignmentPurchaser);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}