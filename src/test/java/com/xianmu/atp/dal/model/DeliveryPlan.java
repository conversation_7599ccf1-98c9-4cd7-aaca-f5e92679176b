package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22 11:20:57
 */
@Getter
@Setter
@TableName("delivery_plan")
public class DeliveryPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 配送状态: 1待支付，2待配送，3待收货，6已收货，8已退款，10支付中断超时关闭订单，11已撤销，14手动关闭订单
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 配送时间
     */
    @TableField("delivery_time")
    private LocalDate deliveryTime;

    /**
     * 配送数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 随单配送时对应的普通订单订单号
     */
    @TableField("master_order_no")
    private String masterOrderNo;

    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 联系人
     */
    @TableField("contact_id")
    private Integer contactId;

    /**
     * 配送方式：0 配送，1自提
     */
    @TableField("deliverytype")
    private Integer deliverytype;

    /**
     * 配送时间区间
     */
    @TableField("time_frame")
    private String timeFrame;

    /**
     * 子帐号id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 管理员id
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     * 下单仓编号
     */
    @TableField("order_store_no")
    private Integer orderStoreNo;

    /**
     * 省心送推迟订单时间
     */
    @TableField("put_off_time")
    private LocalDate putOffTime;

    @TableField("add_time")
    private LocalDateTime addTime;

    /**
     * 旧配送时间
     */
    @TableField("ord_delivery_time")
    private LocalDate ordDeliveryTime;

    /**
     * 旧配送时间
     */
    @TableField("old_delivery_time")
    private LocalDate oldDeliveryTime;

    /**
     * 拦截状态 0 正常 1被拦截
     */
    @TableField("intercept_flag")
    private Integer interceptFlag;

    /**
     * 拦截时间
     */
    @TableField("intercept_time")
    private LocalDateTime interceptTime;

    /**
     * 完成排线-展示标识 0 展示 1不展示
     */
    @TableField("show_flag")
    private Integer showFlag;

    /**
     * 0:未评价，1:已评价
     */
    @TableField("delivery_evaluation_status")
    private Integer deliveryEvaluationStatus;


}
