package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 报价审核记录表
 * @TableName srm_supplier_offer_detail_audit_record
 */
@TableName(value ="srm_supplier_offer_detail_audit_record")
@Data
public class SrmSupplierOfferDetailAuditRecord implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 生效中的报价单详情id
     */
    @TableField(value = "offer_detail_id")
    private Long offerDetailId;

    /**
     * 新价格json
     */
    @TableField(value = "edit_price")
    private String editPrice;

    /**
     * 结果0审核中1审核通过2审核拒绝3撤销 4自动通过
     */
    @TableField(value = "result")
    private Integer result;

    /**
     * 审核时间
     */
    @TableField(value = "audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核账号
     */
    @TableField(value = "audit_account")
    private String auditAccount;

    /**
     * 创建人id
     */
    @TableField(value = "creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * sku
     */
    @TableField(value = "sku")
    private String sku;

    /**
     * 仓库id
     */
    @TableField(value = "warehouse_no")
    private Long warehouseNo;

    /**
     * 供应商id
     */
    @TableField(value = "supplier_id")
    private Long supplierId;

    /**
     * 供货价内容json
     */
    @TableField(value = "price_content")
    private String priceContent;

    /**
     * 商品属性
     */
    @TableField(value = "sku_sub_type")
    private Integer skuSubType;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SrmSupplierOfferDetailAuditRecord other = (SrmSupplierOfferDetailAuditRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOfferDetailId() == null ? other.getOfferDetailId() == null : this.getOfferDetailId().equals(other.getOfferDetailId()))
            && (this.getEditPrice() == null ? other.getEditPrice() == null : this.getEditPrice().equals(other.getEditPrice()))
            && (this.getResult() == null ? other.getResult() == null : this.getResult().equals(other.getResult()))
            && (this.getAuditTime() == null ? other.getAuditTime() == null : this.getAuditTime().equals(other.getAuditTime()))
            && (this.getAuditAccount() == null ? other.getAuditAccount() == null : this.getAuditAccount().equals(other.getAuditAccount()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getSku() == null ? other.getSku() == null : this.getSku().equals(other.getSku()))
            && (this.getWarehouseNo() == null ? other.getWarehouseNo() == null : this.getWarehouseNo().equals(other.getWarehouseNo()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getPriceContent() == null ? other.getPriceContent() == null : this.getPriceContent().equals(other.getPriceContent()))
            && (this.getSkuSubType() == null ? other.getSkuSubType() == null : this.getSkuSubType().equals(other.getSkuSubType()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOfferDetailId() == null) ? 0 : getOfferDetailId().hashCode());
        result = prime * result + ((getEditPrice() == null) ? 0 : getEditPrice().hashCode());
        result = prime * result + ((getResult() == null) ? 0 : getResult().hashCode());
        result = prime * result + ((getAuditTime() == null) ? 0 : getAuditTime().hashCode());
        result = prime * result + ((getAuditAccount() == null) ? 0 : getAuditAccount().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getSku() == null) ? 0 : getSku().hashCode());
        result = prime * result + ((getWarehouseNo() == null) ? 0 : getWarehouseNo().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getPriceContent() == null) ? 0 : getPriceContent().hashCode());
        result = prime * result + ((getSkuSubType() == null) ? 0 : getSkuSubType().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", offerDetailId=").append(offerDetailId);
        sb.append(", editPrice=").append(editPrice);
        sb.append(", result=").append(result);
        sb.append(", auditTime=").append(auditTime);
        sb.append(", auditAccount=").append(auditAccount);
        sb.append(", creator=").append(creator);
        sb.append(", createTime=").append(createTime);
        sb.append(", sku=").append(sku);
        sb.append(", warehouseNo=").append(warehouseNo);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", priceContent=").append(priceContent);
        sb.append(", skuSubType=").append(skuSubType);
        sb.append(", remark=").append(remark);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}