package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 入库通知单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-37 14:15:53
 */
@Getter
@Setter
@TableName("wms_stock_task_storage_notice_order")
public class WmsStockTaskStorageNoticeOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 货品回收单号
     */
    @TableField("goods_recycle_order_no")
    private String goodsRecycleOrderNo;

    /**
     * 订单号
     */
    @TableField("out_order_no")
    private String outOrderNo;

    /**
     * 售后单号
     */
    @TableField("after_sale_order_no")
    private String afterSaleOrderNo;

    /**
     * 库存仓名称
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 库存仓
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 城配仓
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 售后单类型（1:拦截，2:拦截延迟配送，3:退货，4:拒收，5:缺货）
     */
    @TableField("after_sale_order_type")
    private Integer afterSaleOrderType;

    /**
     * 预计时间
     */
    @TableField("except_time")
    private LocalDateTime exceptTime;

    /**
     * 通知单状态（1:待处理，2:已处理，10:入库任务生成中，20:入库任务生成失败，30:通知单冻结中，40:已取消）
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 入库任务id
     */
    @TableField("stock_task_storage_id")
    private Long stockTaskStorageId;

    /**
     * 入库任务生成时间
     */
    @TableField("stock_task_storage_create_time")
    private LocalDateTime stockTaskStorageCreateTime;

    /**
     * 收货人姓名
     */
    @TableField("receiver")
    private String receiver;

    /**
     * 联系方式
     */
    @TableField("phone")
    private String phone;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 详细地址
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否软删
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    @TableField("last_ver")
    private Integer lastVer;

    /**
     * 仓库租户id
     */
    @TableField("warehouse_tenant_id")
    private Long warehouseTenantId;

    /**
     * 通知批次
     */
    @TableField("notice_batch")
    private String noticeBatch;


}
