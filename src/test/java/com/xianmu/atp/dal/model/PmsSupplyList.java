package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 供货目录（品仓供应商信息）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-62 18:28:33
 */
@Getter
@Setter
@TableName("pms_supply_list")
public class PmsSupplyList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * SPUID
     */
    @TableField("spu")
    private String spu;

    /**
     * SPU名称
     */
    @TableField("pd_name")
    private String pdName;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 渠道类型,1源头直采；2分销市场
     */
    @TableField("channel_type")
    private Integer channelType;

    /**
     * 是否为默认供应商，0否；1是
     */
    @TableField("default_supplier")
    private Boolean defaultSupplier;

    /**
     * 订货模式，1不定期不定量；2不定期定量；3定期不定量；4定期定量
     */
    @TableField("order_model")
    private Integer orderModel;

    /**
     * 定期时间，多个用逗号隔开
     */
    @TableField("fixed_time")
    private String fixedTime;

    /**
     * 提前期（非负数，最多不超过30天）
     */
    @TableField("advance_day")
    private Integer advanceDay;

    /**
     * 来源,xianmu,saas
     */
    @TableField("`source`")
    private String source;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 后台三级类目
     */
    @TableField("category_id")
    private Long categoryId;


}
