package com.xianmu.atp.dal.model;

import lombok.Data;

@Data
public class SkuInfo {
    private String pdId;
    private String pdNo;
    private String pdName;
    private int qualityTime;
    private String qualityTimeUnit;
    private String categoryId;
    private String storageLocation;

    // Inventory fields
    private String weight;
    private String skuPic;
    private String unit;
    private String inventoryType;

    // Category fields
    private String categoryType;

    // Getters and Setters
    public String getPdNo() {
        return pdNo;
    }

    public void setPdNo(String pdNo) {
        this.pdNo = pdNo;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public int getQualityTime() {
        return qualityTime;
    }

    public void setQualityTime(int qualityTime) {
        this.qualityTime = qualityTime;
    }

    public String getQualityTimeUnit() {
        return qualityTimeUnit;
    }

    public void setQualityTimeUnit(String qualityTimeUnit) {
        this.qualityTimeUnit = qualityTimeUnit;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(String storageLocation) {
        this.storageLocation = storageLocation;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getSkuPic() {
        return skuPic;
    }

    public void setSkuPic(String skuPic) {
        this.skuPic = skuPic;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getInventoryType() {
        return inventoryType;
    }

    public void setInventoryType(String inventoryType) {
        this.inventoryType = inventoryType;
    }

    public String getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(String categoryType) {
        this.categoryType = categoryType;
    }
}
