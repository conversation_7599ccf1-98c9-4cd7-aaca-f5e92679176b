package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@TableName("wnc_precise_delivery_time_detail")
public class PreciseDeliveryTimeDetail {
    private Long id;                // 主键
    private Long configId;          // 配置ID
    private LocalTime beginTime;    // 开始时间
    private LocalTime endTime;      // 结束时间
    private LocalDateTime createTime; // 创建时间
}
