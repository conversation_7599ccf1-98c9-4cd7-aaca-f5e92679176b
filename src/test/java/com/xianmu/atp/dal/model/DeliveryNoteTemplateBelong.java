package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("tms_delivery_note_template_belong")
public class DeliveryNoteTemplateBelong {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 配送单模板ID
     */
    private Long deliveryNoteTemplateId;

    /**
     * 租户ID (Saas默认配送单该值为0)
     */
    private Long tenantId;

    /**
     * 作用域下的业务ID (默认值为0)
     */
    private String scopeBusinessId;

    /**
     * 业务方名称 (默认值为“默认”)
     */
    private String scopeBusinessName;

    /**
     * 应用来源 (1 - 顺路达, 2 - 鲜沐, 3 - SaaS, 4 - 外单)
     */
    private Integer appSource;

    /**
     * 作用域类型 (0 - 默认, 1 - 租户, 2 - 大客户)
     */
    private Integer scopeType;
}
