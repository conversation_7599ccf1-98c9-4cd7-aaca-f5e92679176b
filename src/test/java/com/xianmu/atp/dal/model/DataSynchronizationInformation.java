package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName DataSynchronizationInformation
 * @date 2025-03-12
 */
@Data
@TableName("data_synchronization_information")
public class DataSynchronizationInformation {

    private long id;
    private String tableName;
    private long dateFlag;
    private java.sql.Timestamp updateTime;
    private java.sql.Timestamp createTime;
}
