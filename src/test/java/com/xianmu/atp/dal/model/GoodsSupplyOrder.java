package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货品供应单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22 15:48:47
 */
@Getter
@Setter
@TableName("goods_supply_order")
public class GoodsSupplyOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货品供应单号
     */
    @TableField("supply_order_no")
    private String supplyOrderNo;

    /**
     * 履约单号
     */
    @TableField("fulfillment_no")
    private Long fulfillmentNo;

    /**
     * 原订单号
     */
    @TableField("source_order_no")
    private String sourceOrderNo;

    /**
     * 售后单号(如果是售后补发)
     */
    @TableField("source_after_sale_order_no")
    private String sourceAfterSaleOrderNo;

    /**
     * 货品供应单状态 0:待下发，1:已下发
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 类型 0: 正向订单, 1: 补发, 2: 自提
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 库存仓号
     */
    @TableField("warehouse_no")
    private Long warehouseNo;

    /**
     * 租户号, 鲜沐默认1
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 仓库所属租户id
     */
    @TableField("warehouse_tenant_id")
    private Long warehouseTenantId;

    /**
     * 供应模式（0：默认，1：销转采）
     */
    @TableField("supply_mode")
    private Integer supplyMode;

    /**
     * 推单模式（0手动推单，1实时推单，2全品类推单）
     */
    @TableField("push_mode")
    private Integer pushMode;

    /**
     * 履约时间
     */
    @TableField("schedule_date")
    private LocalDateTime scheduleDate;

    /**
     * 城配仓号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 记录标记（-1逻辑删除，0已归档/非最新记录，1当前/最新记录）
     */
    @TableField("data_status")
    private Integer dataStatus;


}
