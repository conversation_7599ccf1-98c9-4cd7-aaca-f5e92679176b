package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货品回收单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-37 14:29:25
 */
@Getter
@Setter
@TableName("goods_recycle_order")
public class GoodsRecycleOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 货品回收单号
     */
    @TableField("goods_recycle_order_no")
    private String goodsRecycleOrderNo;

    /**
     * 单据租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 履约单号
     */
    @TableField("fulfillment_no")
    private Long fulfillmentNo;

    /**
     * 来源订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 来源售后单号
     */
    @TableField("after_sale_order_no")
    private String afterSaleOrderNo;

    /**
     * 库存仓编号
     */
    @TableField("warehouse_no")
    private Long warehouseNo;

    /**
     * 库存仓租户id
     */
    @TableField("warehouse_tenant_id")
    private Long warehouseTenantId;

    /**
     * 类型（1. 拦截入库 2. 拦截入库-延配  3. 退货入库  4. 拒收入库 5. 缺货入库）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 状态（-1. 已取消 0. 未下发 1. 已下发 2. 已回告 3. 已入库）
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 预计入库时间
     */
    @TableField("inbound_time")
    private LocalDateTime inboundTime;

    /**
     * 城配仓号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;


}
