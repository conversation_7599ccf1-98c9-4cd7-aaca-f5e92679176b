package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("fulfillment_order")
@Data
public class OfcOrder {
    private long fulfillmentNo;
    private String outOrderNo;
    private long orderSource;
    private java.sql.Date fulfillmentTime;
    private long fulfillmentWay;
    private long fulfillmentStatus;
    private long storeNo;
    private long fulfillmentType;
    private String remark;
    private String outClientId;
    private String outClientName;
    private long outClientType;
    private String outContactId;
    private String province;
    private String city;
    private String area;
    private String address;
    private String outClientPhone;
    private String poi;
    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
    private long tenantId;
    private long saasOrderWarehouseType;
    private long status;
    private long afterSaleStatus;
    private double afterSalePrice;
    private java.sql.Timestamp payTime;
    private java.sql.Timestamp expectArriveTime;
    private String addressRemark;
    private String orderSubNo;
    private String fulfillmentRemark;
}
