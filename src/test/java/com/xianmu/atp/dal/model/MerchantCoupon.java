package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName MerchantCoupon
 * @date 2024-11-05
 */
@TableName("merchant_coupon")
@Data
public class MerchantCoupon {
    /**
     * 优惠券的唯一标识
     */
    private long id;
    /**
     * 商户ID
     */
    private long mId;
    /**
     * 优惠券ID
     */
    private long couponId;
    /**
     * 优惠券的有效日期
     */
    private java.sql.Timestamp vaildDate;
    /**
     * 优惠券的发送者
     */
    private String sender;
    /**
     * 优惠券的使用状态，0 表示未使用，1 表示已使用
     */
    private long used;
    /**
     * 优惠券的添加时间
     */
    private java.sql.Timestamp addTime;
    /**
     * 优惠券的查看次数
     */
    private long view;
    /**
     * 优惠券关联的订单号
     */
    private String orderNo;
    /**
     * 优惠券的领取类型
     */
    private long receiveType;
    /**
     * 优惠券的开始时间
     */
    private java.sql.Timestamp startTime;
    /**
     * 优惠券的更新时间
     */
    private java.sql.Timestamp updateTime;
    /**
     * 优惠券的发送者ID
     */
    private long sendId;
    /**
     * 优惠券的关联ID
     */
    private long relatedId;
}
