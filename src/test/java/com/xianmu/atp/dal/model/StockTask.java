package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 出入库任务表
 * @TableName stock_task
 */
@TableName(value ="stock_task")
@Data
public class StockTask {
    /**
     * 任务编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务订单编号
     */
    @TableField(value = "task_no")
    private String taskNo;

    /**
     * 库存仓
     */
    @TableField(value = "area_no")
    private Integer areaNo;

    /**
     * 出入库类型：10-调拨入库、11-采购入库、12-退货入库、13-拒收回库、14-调拨未收入库（实收+拒收小于实发的情况）、15-盘盈入库、16-转换入库、17-终止调拨回库、19、新退货入库、20、缺货入库、30-批次调整、31-安全库存、50-调拨出库、51-销售出库、52-出样出库、53-货损出库、54-盘亏出库、55-转换出库、56-采购退货出库、57-补货出库、58-销售自提、59-调拨货损出库
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 预期出(入)库时间
     */
    @TableField(value = "expect_time")
    private LocalDateTime expectTime;

    /**
     * 出入库进展: 0待入(出)库 1部分入(出)库 2已入(出)库
采购类型 01状态为正常 2 已完成 3 已取消 4已关闭
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 添加时间
     */
    @TableField(value = "addtime")
    private LocalDateTime addtime;

    /**
     * 发起人
     */
    @TableField(value = "admin_id")
    private Integer adminId;

    /**
     * 
     */
    @TableField(value = "updatetime")
    private LocalDateTime updatetime;

    /**
     * 城配仓
     */
    @TableField(value = "out_store_no")
    private Integer outStoreNo;

    /**
     * 出库性质，0普通 1越库
     */
    @TableField(value = "out_type")
    private Integer outType;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 盘点维度：0、SKU 1、类目 2、货位 3、批次,转换维度：0、库存转换 1、降级转换
     */
    @TableField(value = "dimension")
    private Integer dimension;

    /**
     * 入库进度:0、全未入库1、部分入库2、完全入库
     */
    @TableField(value = "process_state")
    private Integer processState;

    /**
     * 应入不符原因
     */
    @TableField(value = "mismatch_reason")
    private String mismatchReason;

    /**
     * 库存编号初始化过渡字段
     */
    @TableField(value = "transition_field")
    private Integer transitionField;

    /**
     * 任务类型 1、退货 2、拒收 3、拦截
     */
    @TableField(value = "task_type")
    private Integer taskType;

    /**
     * 类目名称
     */
    @TableField(value = "category")
    private String category;

    /**
     * 关闭原因
     */
    @TableField(value = "close_reason")
    private String closeReason;

    /**
     * 最后修改人admin_id
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 扩展标志位
     */
    @TableField(value = "option_flag")
    private Long optionFlag;

    /**
     * 租户ID 1-鲜沐
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 库存冻结状态（0：未冻结，1：已冻结）
     */
    @TableField(value = "inventory_locked")
    private Integer inventoryLocked;

    /**
     * 系统来源 0-内部 1-外部
     */
    @TableField(value = "system_source")
    private Integer systemSource;

    /**
     * 外部单号
     */
    @TableField(value = "out_order_no")
    private String outOrderNo;

    /**
     * 出库分类 0-默认，1-POP出库
     */
    @TableField(value = "outbound_category")
    private Integer outboundCategory;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StockTask other = (StockTask) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskNo() == null ? other.getTaskNo() == null : this.getTaskNo().equals(other.getTaskNo()))
            && (this.getAreaNo() == null ? other.getAreaNo() == null : this.getAreaNo().equals(other.getAreaNo()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getExpectTime() == null ? other.getExpectTime() == null : this.getExpectTime().equals(other.getExpectTime()))
            && (this.getState() == null ? other.getState() == null : this.getState().equals(other.getState()))
            && (this.getAddtime() == null ? other.getAddtime() == null : this.getAddtime().equals(other.getAddtime()))
            && (this.getAdminId() == null ? other.getAdminId() == null : this.getAdminId().equals(other.getAdminId()))
            && (this.getUpdatetime() == null ? other.getUpdatetime() == null : this.getUpdatetime().equals(other.getUpdatetime()))
            && (this.getOutStoreNo() == null ? other.getOutStoreNo() == null : this.getOutStoreNo().equals(other.getOutStoreNo()))
            && (this.getOutType() == null ? other.getOutType() == null : this.getOutType().equals(other.getOutType()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getDimension() == null ? other.getDimension() == null : this.getDimension().equals(other.getDimension()))
            && (this.getProcessState() == null ? other.getProcessState() == null : this.getProcessState().equals(other.getProcessState()))
            && (this.getMismatchReason() == null ? other.getMismatchReason() == null : this.getMismatchReason().equals(other.getMismatchReason()))
            && (this.getTransitionField() == null ? other.getTransitionField() == null : this.getTransitionField().equals(other.getTransitionField()))
            && (this.getTaskType() == null ? other.getTaskType() == null : this.getTaskType().equals(other.getTaskType()))
            && (this.getCategory() == null ? other.getCategory() == null : this.getCategory().equals(other.getCategory()))
            && (this.getCloseReason() == null ? other.getCloseReason() == null : this.getCloseReason().equals(other.getCloseReason()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getOptionFlag() == null ? other.getOptionFlag() == null : this.getOptionFlag().equals(other.getOptionFlag()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getInventoryLocked() == null ? other.getInventoryLocked() == null : this.getInventoryLocked().equals(other.getInventoryLocked()))
            && (this.getSystemSource() == null ? other.getSystemSource() == null : this.getSystemSource().equals(other.getSystemSource()))
            && (this.getOutOrderNo() == null ? other.getOutOrderNo() == null : this.getOutOrderNo().equals(other.getOutOrderNo()))
            && (this.getOutboundCategory() == null ? other.getOutboundCategory() == null : this.getOutboundCategory().equals(other.getOutboundCategory()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskNo() == null) ? 0 : getTaskNo().hashCode());
        result = prime * result + ((getAreaNo() == null) ? 0 : getAreaNo().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getExpectTime() == null) ? 0 : getExpectTime().hashCode());
        result = prime * result + ((getState() == null) ? 0 : getState().hashCode());
        result = prime * result + ((getAddtime() == null) ? 0 : getAddtime().hashCode());
        result = prime * result + ((getAdminId() == null) ? 0 : getAdminId().hashCode());
        result = prime * result + ((getUpdatetime() == null) ? 0 : getUpdatetime().hashCode());
        result = prime * result + ((getOutStoreNo() == null) ? 0 : getOutStoreNo().hashCode());
        result = prime * result + ((getOutType() == null) ? 0 : getOutType().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getDimension() == null) ? 0 : getDimension().hashCode());
        result = prime * result + ((getProcessState() == null) ? 0 : getProcessState().hashCode());
        result = prime * result + ((getMismatchReason() == null) ? 0 : getMismatchReason().hashCode());
        result = prime * result + ((getTransitionField() == null) ? 0 : getTransitionField().hashCode());
        result = prime * result + ((getTaskType() == null) ? 0 : getTaskType().hashCode());
        result = prime * result + ((getCategory() == null) ? 0 : getCategory().hashCode());
        result = prime * result + ((getCloseReason() == null) ? 0 : getCloseReason().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getOptionFlag() == null) ? 0 : getOptionFlag().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getInventoryLocked() == null) ? 0 : getInventoryLocked().hashCode());
        result = prime * result + ((getSystemSource() == null) ? 0 : getSystemSource().hashCode());
        result = prime * result + ((getOutOrderNo() == null) ? 0 : getOutOrderNo().hashCode());
        result = prime * result + ((getOutboundCategory() == null) ? 0 : getOutboundCategory().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", areaNo=").append(areaNo);
        sb.append(", type=").append(type);
        sb.append(", expectTime=").append(expectTime);
        sb.append(", state=").append(state);
        sb.append(", addtime=").append(addtime);
        sb.append(", adminId=").append(adminId);
        sb.append(", updatetime=").append(updatetime);
        sb.append(", outStoreNo=").append(outStoreNo);
        sb.append(", outType=").append(outType);
        sb.append(", remark=").append(remark);
        sb.append(", dimension=").append(dimension);
        sb.append(", processState=").append(processState);
        sb.append(", mismatchReason=").append(mismatchReason);
        sb.append(", transitionField=").append(transitionField);
        sb.append(", taskType=").append(taskType);
        sb.append(", category=").append(category);
        sb.append(", closeReason=").append(closeReason);
        sb.append(", updater=").append(updater);
        sb.append(", optionFlag=").append(optionFlag);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", inventoryLocked=").append(inventoryLocked);
        sb.append(", systemSource=").append(systemSource);
        sb.append(", outOrderNo=").append(outOrderNo);
        sb.append(", outboundCategory=").append(outboundCategory);
        sb.append("]");
        return sb.toString();
    }
}