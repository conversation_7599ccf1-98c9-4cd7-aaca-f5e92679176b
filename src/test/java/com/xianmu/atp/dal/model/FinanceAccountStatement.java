package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 财务对账单表
 * @TableName finance_account_statement
 */
@TableName(value ="finance_account_statement")
@Data
public class FinanceAccountStatement {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 暂估总成本
     */
    @TableField(value = "estimate_amount")
    private BigDecimal estimateAmount;

    /**
     * 账单总额（调整后总额）
     */
    @TableField(value = "total_bill_amount")
    private BigDecimal totalBillAmount;

    /**
     * 票夹id/为空则没被匹配
     */
    @TableField(value = "wallets_id")
    private Long walletsId;

    /**
     * 核销总额
     */
    @TableField(value = "write_off_amount")
    private BigDecimal writeOffAmount;

    /**
     * 供应商id
     */
    @TableField(value = "supplier_id")
    private Integer supplierId;

    /**
     * 供应商名称
     */
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 税号
     */
    @TableField(value = "tax_number")
    private String taxNumber;

    /**
     * 支付方式
     */
    @TableField(value = "supplier_account_id")
    private Integer supplierAccountId;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 1 生鲜 2 品牌
     */
    @TableField(value = "pd_type")
    private Integer pdType;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 对账单状态 0、付款审核中 1、待审核（账单审核） 2、待供应商确认  5、待付款 6、待开票 7、待复核发票 8、已完成 9、作废
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 预付单id
     */
    @TableField(value = "advanced_order_id")
    private String advancedOrderId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 作废原因：0：审核失败 1：供应商驳回 2：账单审核失败 3：撤回申请 4：付款审核失败
     */
    @TableField(value = "delete_reason")
    private Integer deleteReason;

    /**
     * 当前状态处理人
     */
    @TableField(value = "current_processor")
    private String currentProcessor;

    /**
     * 发起人adminId
     */
    @TableField(value = "creator_admin_id")
    private Integer creatorAdminId;

    /**
     * 供应商确认状态 1、未确认 2、已通过 3、已拒绝
     */
    @TableField(value = "supplier_confirm_status")
    private Integer supplierConfirmStatus;

    /**
     * 确认人 0：采购代确认 1：供应商确认
     */
    @TableField(value = "confirm_user")
    private Integer confirmUser;

    /**
     * 支付方式 1、银行卡 2、现金
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 账户名称
     */
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 开户银行
     */
    @TableField(value = "account_bank")
    private String accountBank;

    /**
     * 银行卡归属地
     */
    @TableField(value = "account_ascription")
    private String accountAscription;

    /**
     * 账号
     */
    @TableField(value = "account")
    private String account;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        FinanceAccountStatement other = (FinanceAccountStatement) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getEstimateAmount() == null ? other.getEstimateAmount() == null : this.getEstimateAmount().equals(other.getEstimateAmount()))
            && (this.getTotalBillAmount() == null ? other.getTotalBillAmount() == null : this.getTotalBillAmount().equals(other.getTotalBillAmount()))
            && (this.getWalletsId() == null ? other.getWalletsId() == null : this.getWalletsId().equals(other.getWalletsId()))
            && (this.getWriteOffAmount() == null ? other.getWriteOffAmount() == null : this.getWriteOffAmount().equals(other.getWriteOffAmount()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
            && (this.getTaxNumber() == null ? other.getTaxNumber() == null : this.getTaxNumber().equals(other.getTaxNumber()))
            && (this.getSupplierAccountId() == null ? other.getSupplierAccountId() == null : this.getSupplierAccountId().equals(other.getSupplierAccountId()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getPdType() == null ? other.getPdType() == null : this.getPdType().equals(other.getPdType()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAdvancedOrderId() == null ? other.getAdvancedOrderId() == null : this.getAdvancedOrderId().equals(other.getAdvancedOrderId()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleteReason() == null ? other.getDeleteReason() == null : this.getDeleteReason().equals(other.getDeleteReason()))
            && (this.getCurrentProcessor() == null ? other.getCurrentProcessor() == null : this.getCurrentProcessor().equals(other.getCurrentProcessor()))
            && (this.getCreatorAdminId() == null ? other.getCreatorAdminId() == null : this.getCreatorAdminId().equals(other.getCreatorAdminId()))
            && (this.getSupplierConfirmStatus() == null ? other.getSupplierConfirmStatus() == null : this.getSupplierConfirmStatus().equals(other.getSupplierConfirmStatus()))
            && (this.getConfirmUser() == null ? other.getConfirmUser() == null : this.getConfirmUser().equals(other.getConfirmUser()))
            && (this.getPayType() == null ? other.getPayType() == null : this.getPayType().equals(other.getPayType()))
            && (this.getAccountName() == null ? other.getAccountName() == null : this.getAccountName().equals(other.getAccountName()))
            && (this.getAccountBank() == null ? other.getAccountBank() == null : this.getAccountBank().equals(other.getAccountBank()))
            && (this.getAccountAscription() == null ? other.getAccountAscription() == null : this.getAccountAscription().equals(other.getAccountAscription()))
            && (this.getAccount() == null ? other.getAccount() == null : this.getAccount().equals(other.getAccount()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getEstimateAmount() == null) ? 0 : getEstimateAmount().hashCode());
        result = prime * result + ((getTotalBillAmount() == null) ? 0 : getTotalBillAmount().hashCode());
        result = prime * result + ((getWalletsId() == null) ? 0 : getWalletsId().hashCode());
        result = prime * result + ((getWriteOffAmount() == null) ? 0 : getWriteOffAmount().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getTaxNumber() == null) ? 0 : getTaxNumber().hashCode());
        result = prime * result + ((getSupplierAccountId() == null) ? 0 : getSupplierAccountId().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getPdType() == null) ? 0 : getPdType().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAdvancedOrderId() == null) ? 0 : getAdvancedOrderId().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleteReason() == null) ? 0 : getDeleteReason().hashCode());
        result = prime * result + ((getCurrentProcessor() == null) ? 0 : getCurrentProcessor().hashCode());
        result = prime * result + ((getCreatorAdminId() == null) ? 0 : getCreatorAdminId().hashCode());
        result = prime * result + ((getSupplierConfirmStatus() == null) ? 0 : getSupplierConfirmStatus().hashCode());
        result = prime * result + ((getConfirmUser() == null) ? 0 : getConfirmUser().hashCode());
        result = prime * result + ((getPayType() == null) ? 0 : getPayType().hashCode());
        result = prime * result + ((getAccountName() == null) ? 0 : getAccountName().hashCode());
        result = prime * result + ((getAccountBank() == null) ? 0 : getAccountBank().hashCode());
        result = prime * result + ((getAccountAscription() == null) ? 0 : getAccountAscription().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", estimateAmount=").append(estimateAmount);
        sb.append(", totalBillAmount=").append(totalBillAmount);
        sb.append(", walletsId=").append(walletsId);
        sb.append(", writeOffAmount=").append(writeOffAmount);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", taxNumber=").append(taxNumber);
        sb.append(", supplierAccountId=").append(supplierAccountId);
        sb.append(", creator=").append(creator);
        sb.append(", createTime=").append(createTime);
        sb.append(", pdType=").append(pdType);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", advancedOrderId=").append(advancedOrderId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleteReason=").append(deleteReason);
        sb.append(", currentProcessor=").append(currentProcessor);
        sb.append(", creatorAdminId=").append(creatorAdminId);
        sb.append(", supplierConfirmStatus=").append(supplierConfirmStatus);
        sb.append(", confirmUser=").append(confirmUser);
        sb.append(", payType=").append(payType);
        sb.append(", accountName=").append(accountName);
        sb.append(", accountBank=").append(accountBank);
        sb.append(", accountAscription=").append(accountAscription);
        sb.append(", account=").append(account);
        sb.append("]");
        return sb.toString();
    }
}