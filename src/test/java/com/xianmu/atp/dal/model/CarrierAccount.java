package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("carrier_account")

public class CarrierAccount {
    private Integer id; // 主键 ID
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
    private Long carrierId; // 承运商 ID
    private Integer payType; // 支付方式
    private String accountName; // 账户名称
    private String accountBank; // 开户银行
    private String accountAscription; // 银行卡归属地
    private String account; // 账号
}
