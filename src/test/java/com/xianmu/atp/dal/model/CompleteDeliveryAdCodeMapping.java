package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("complete_delivery_ad_code_mapping")
public class CompleteDeliveryAdCodeMapping {

    /**
     * 主键
     */

    private Long id;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 完成配送提醒id
     */
    private Integer completeDeliveryId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
