package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @ClassName Merchant
 * @date 2024-11-05
 */
@TableName("merchant")
@Data
public class Merchant {
    // 商户ID
    private long mId;
    // 角色ID
    private long roleId;
    // 商户名称
    private String mname;
    // 商户联系人
    private String mcontact;
    // 商户的OpenID
    private String openid;
    // 商户电话
    private String phone;
    // 商户是否被锁定
    private long islock;
    // 商户等级ID
    private long rankId;
    // 商户注册时间
    private java.sql.Timestamp registerTime;
    // 商户登录时间
    private java.sql.Timestamp loginTime;
    // 商户邀请码
    private String invitecode;
    // 商户渠道代码
    private String channelCode;
    // 商户邀请人渠道代码
    private String inviterChannelCode;
    // 商户审核时间
    private java.sql.Timestamp auditTime;
    // 商户审核用户
    private long auditUser;
    // 商户营业执照
    private String businessLicense;
    // 商户所在省份
    private String province;
    // 商户所在城市
    private String city;
    // 商户所在地区
    private String area;
    // 商户地址
    private String address;
    // 商户POI备注
    private String poiNote;
    // 商户备注
    private String remark;
    // 商户店铺标识
    private String shopSign;
    // 商户其他证明
    private String otherProof;
    // 商户最后订单时间
    private java.sql.Timestamp lastOrderTime;
    // 商户区域编号
    private long areaNo;
    // 商户规模
    private String size;
    // 商户类型
    private String type;
    // 商户贸易区域
    private String tradeArea;
    // 商户贸易组
    private String tradeGroup;
    // 商户UnionID
    private String unionid;
    // 商户公众号OpenID
    private String mpOpenid;
    // 商户管理员ID
    private long adminId;
    // 商户直接服务
    private long direct;
    // 商户服务器
    private long server;
    // 商户流行视图
    private long popView;
    // 会员积分
    private double memberIntegral;
    // 商户等级
    private long grade;
    // SKU展示
    private long skuShow;
    // 充值金额
    private double rechargeAmount;
    // 现金金额
    private double cashAmount;
    // 现金更新时间
    private java.sql.Timestamp cashUpdateTime;
    // 展示价格
    private long showPrice;
    // 合并管理员
    private String mergeAdmin;
    // 合并时间
    private java.sql.Timestamp mergeTime;
    // 首次登录弹出
    private long firstLoginPop;
    // 更改弹出
    private long changePop;
    // 拉黑备注
    private String pullBlackRemark;
    // 拉黑操作人
    private String pullBlackOperator;
    // 门牌号
    private String houseNumber;
    // 公司品牌
    private String companyBrand;
    // 线索池
    private long cluePool;
    // 商户类型
    private String merchantType;
    // 企业规模
    private String enterpriseScale;
    // 更新时间
    private java.sql.Timestamp updateTime;
    // 审核类型
    private long examineType;
    // 展示按钮
    private long displayButton;
    // 操作状态
    private long operateStatus;
    // 更新者
    private long updater;
    // 门头照片
    private String doorPic;
    // 预注册标志
    private long preRegisterFlag;
    // 业务线
    private long businessLine;
    // 提交审核时间
    private java.sql.Timestamp submitReviewTime;
    // 下一级别
    private long nextGrade;
}
