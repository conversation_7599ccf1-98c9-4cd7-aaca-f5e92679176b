package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName Contact
 * @date 2024-11-29
 */
@Data
@TableName("contact")
public class Contact {

    private long contactId;
    private long mId;
    private String contact;
    private String position;
    private long gender;
    private String phone;
    private String email;
    private String weixincode;
    private String province;
    private String city;
    private String area;
    private String address;
    private String deliveryCar;
    private long status;
    private String remark;
    private long isDefault;
    private String poiNote;
    private double distance;
    private String path;
    private String houseNumber;
    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
    private long storeNo;
    private long acmId;
    private long backStoreNo;
    private String deliveryFrequent;
    private String deliveryRule;
    private double deliveryFee;
    private String addressRemark;
    private long addressCompletionFlag;
    private long appointStoreNoFlag;
}
