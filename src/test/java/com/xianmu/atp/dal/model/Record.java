package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 * 自动化结果
 * <AUTHOR>
 * @ClassName CountResult
 * @date 2024-11-15
 */
@Data
@TableName("record")
public class Record {
    /**
     * 唯一标识符
     */
    private long id;

    /**
     * 创建时间
     */
    private java.sql.Timestamp gmtCreate;

    /**
     * 记录时间
     */
    private java.sql.Timestamp gmtRecord;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 环境
     */
    private String environment;

    /**
     * 主机
     */
    private String host;

    /**
     * 跟踪ID
     */
    private String traceId;

    /**
     * 入口描述
     */
    private String entranceDesc;

    /**
     * 包装器记录
     */
    private String wrapperRecord;

    /**
     * 请求
     */
    private String request;

    /**
     * 响应
     */
    private String response;


}
