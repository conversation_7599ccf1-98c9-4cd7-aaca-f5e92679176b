package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDate;

@Data
@TableName("tms_path_car")
public class TmsPathCar {
    // 数据库主键
    private Long id;

    // 路由 ID
    private Long pathId;

    // 调度类型 (1-干线，2-城配)
    private Integer dispatchType;

    // 班次类型 (0-正班，1-加班)
    private Integer shiftType;

    // 开始计算时间
    private LocalDate startTime;

    // 周期类型 (0-表示每天，1表示每周)
    private Integer periodType;

    // 每周选项下的类型 (0-每天，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六，7-周日)
    private String everyWeekType;

    // 每天选项下，间隔天数
    private Integer intervalDays;

    // 承运类型 (-1前一天，0-当日)
    private Integer carryType;

    // 承运时间 (HH:mm)
    private String carryTime;

    // 承运商 ID
    private Long carrierId;

    // 司机 ID
    private Long driverId;

    // 车辆 ID
    private Long carId;

    // 备注
    private String remark;

    // 配送批次类型 (-1: 干线城配用车, 0: 干线用车, 1: 调拨用车, 2: 采购用车, 3: 大客户用车, 4: 仓库用车, 5: 城配用车, 6: 提货用车)
    private Integer deliveryBatchType;
}
