package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 出库通知单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08 17:33:03
 */
@Getter
@Setter
@TableName("wms_stock_task_notice_order")
public class WmsStockTaskNoticeOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 门店id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 货品供应单号
     */
    @TableField("goods_supply_no")
    private String goodsSupplyNo;

    /**
     * 外部订单号
     */
    @TableField("out_order_no")
    private String outOrderNo;

    /**
     * 库存仓
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 城配仓
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 订单类型（0:销售，1:补发，2:销售自提，3:样品，4:样品自提）
     */
    @TableField("out_order_type")
    private Integer outOrderType;

    /**
     * 预计送达时间
     */
    @TableField("except_time")
    private LocalDateTime exceptTime;

    /**
     * 通知单状态（1:待处理，2:已处理，10:出库任务生成中，20:出库任务生成失败，30:通知单冻结中，40:已取消）
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 收货人姓名
     */
    @TableField("receiver")
    private String receiver;

    /**
     * 联系方式
     */
    @TableField("phone")
    private String phone;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 详细地址
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否软删
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    @TableField("last_ver")
    private Integer lastVer;

    /**
     * 门店名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 出库任务生成时间
     */
    @TableField("stock_task_create_time")
    private LocalDateTime stockTaskCreateTime;

    /**
     * 库存仓名称
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 出库任务编码
     */
    @TableField("stock_task_id")
    private Long stockTaskId;

    /**
     * 仓库租户id
     */
    @TableField("warehouse_tenant_id")
    private Long warehouseTenantId;

    /**
     * 货品供应单方式（0:代销入仓，1:代销不入仓）
     */
    @TableField("supply_mode")
    private Integer supplyMode;

    /**
     * 通知单品标记 1-非代销不入仓品 2-代销不入仓品 3-混合品 4-POP品
     */
    @TableField("notice_sku_flag")
    private Integer noticeSkuFlag;

    /**
     * 外部标识（1:内部，2:外部）
     */
    @TableField("external_option")
    private Integer externalOption;

    /**
     * 外部状态（1:初始，2:已下发）
     */
    @TableField("external_status")
    private Integer externalStatus;


}
