package com.xianmu.atp.dal.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("tms_delivery_note_template")
public class DeliveryNoteTemplate {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private java.sql.Timestamp createTime;

    /**
     * update time
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 配送单名称
     */
    private String deliveryNoteName;

    /**
     * 应用来源 1顺路达 2鲜沐 3saas 4外单
     */
    private Integer appSource;

    /**
     * 使用状态 0使用中 1停用
     */
    private Integer useState;

    /**
     * 前端页面字符串
     */
    private String frontPageStr;

    /**
     * 最近操作人
     */
    private String lastOperatorName;

    /**
     * 展示价格标识 0展示价格 1不展示价格
     */
    private Integer showPriceFlag;

    /**
     * 配送单归属业务方名称
     */
    private String belongBusinessName;

    /**
     * 展示价格的oss url
     */
    private String showPriceTemplateOssUrl;

    /**
     * 不展示价格的oss url
     */
    private String noShowPriceTemplateOssUrl;


}
