package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 供应商仓库库存
 * @TableName inventory_supplier_warehouse_store
 */
@TableName(value ="inventory_supplier_warehouse_store")
@Data
public class InventorySupplierWarehouseStore implements Serializable {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 仓库编码
     */
    @TableField(value = "warehouse_no")
    private Integer warehouseNo;

    /**
     * 供应商id
     */
    @TableField(value = "supplier_id")
    private Integer supplierId;

    /**
     * 货品编码
     */
    @TableField(value = "sku_code")
    private String skuCode;

    /**
     * 库存数量
     */
    @TableField(value = "quantity")
    private Long quantity;

    /**
     * 安全库存数量
     */
    @TableField(value = "safe_quantity")
    private Long safeQuantity;

    /**
     * 剩余可售库存数量
     */
    @TableField(value = "saleable_quantity")
    private Long saleableQuantity;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        InventorySupplierWarehouseStore other = (InventorySupplierWarehouseStore) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getWarehouseNo() == null ? other.getWarehouseNo() == null : this.getWarehouseNo().equals(other.getWarehouseNo()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getSafeQuantity() == null ? other.getSafeQuantity() == null : this.getSafeQuantity().equals(other.getSafeQuantity()))
            && (this.getSaleableQuantity() == null ? other.getSaleableQuantity() == null : this.getSaleableQuantity().equals(other.getSaleableQuantity()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getWarehouseNo() == null) ? 0 : getWarehouseNo().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getSafeQuantity() == null) ? 0 : getSafeQuantity().hashCode());
        result = prime * result + ((getSaleableQuantity() == null) ? 0 : getSaleableQuantity().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", creator=").append(creator);
        sb.append(", updater=").append(updater);
        sb.append(", warehouseNo=").append(warehouseNo);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", quantity=").append(quantity);
        sb.append(", safeQuantity=").append(safeQuantity);
        sb.append(", saleableQuantity=").append(saleableQuantity);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}