package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 收款单-账单表
 * @TableName finance_receipt_bill
 */
@TableName(value ="finance_receipt_bill")
@Data
public class FinanceReceiptBill implements Serializable {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * finance_receipt收款单表id
     */
    @TableField(value = "finance_receipt_id")
    private Long financeReceiptId;

    /**
     * 业务表id:账单id/充值id/退款id
     */
    @TableField(value = "finance_order_id")
    private Long financeOrderId;

    /**
     * 收款金额
     */
    @TableField(value = "receipt_amount")
    private BigDecimal receiptAmount;

    /**
     * 其他金额
     */
    @TableField(value = "other_amount")
    private BigDecimal otherAmount;

    /**
     * 收款凭证
     */
    @TableField(value = "receipt_voucher")
    private String receiptVoucher;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * finance_accounting_period_order对账单表应收总额
     */
    @TableField(value = "receivable_amount")
    private BigDecimal receivableAmount;

    /**
     * 来源单据号:账单编号/充值单号/退款单号
     */
    @TableField(value = "source_no")
    private String sourceNo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        FinanceReceiptBill other = (FinanceReceiptBill) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFinanceReceiptId() == null ? other.getFinanceReceiptId() == null : this.getFinanceReceiptId().equals(other.getFinanceReceiptId()))
            && (this.getFinanceOrderId() == null ? other.getFinanceOrderId() == null : this.getFinanceOrderId().equals(other.getFinanceOrderId()))
            && (this.getReceiptAmount() == null ? other.getReceiptAmount() == null : this.getReceiptAmount().equals(other.getReceiptAmount()))
            && (this.getOtherAmount() == null ? other.getOtherAmount() == null : this.getOtherAmount().equals(other.getOtherAmount()))
            && (this.getReceiptVoucher() == null ? other.getReceiptVoucher() == null : this.getReceiptVoucher().equals(other.getReceiptVoucher()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getReceivableAmount() == null ? other.getReceivableAmount() == null : this.getReceivableAmount().equals(other.getReceivableAmount()))
            && (this.getSourceNo() == null ? other.getSourceNo() == null : this.getSourceNo().equals(other.getSourceNo()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFinanceReceiptId() == null) ? 0 : getFinanceReceiptId().hashCode());
        result = prime * result + ((getFinanceOrderId() == null) ? 0 : getFinanceOrderId().hashCode());
        result = prime * result + ((getReceiptAmount() == null) ? 0 : getReceiptAmount().hashCode());
        result = prime * result + ((getOtherAmount() == null) ? 0 : getOtherAmount().hashCode());
        result = prime * result + ((getReceiptVoucher() == null) ? 0 : getReceiptVoucher().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getReceivableAmount() == null) ? 0 : getReceivableAmount().hashCode());
        result = prime * result + ((getSourceNo() == null) ? 0 : getSourceNo().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", financeReceiptId=").append(financeReceiptId);
        sb.append(", financeOrderId=").append(financeOrderId);
        sb.append(", receiptAmount=").append(receiptAmount);
        sb.append(", otherAmount=").append(otherAmount);
        sb.append(", receiptVoucher=").append(receiptVoucher);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", receivableAmount=").append(receivableAmount);
        sb.append(", sourceNo=").append(sourceNo);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}