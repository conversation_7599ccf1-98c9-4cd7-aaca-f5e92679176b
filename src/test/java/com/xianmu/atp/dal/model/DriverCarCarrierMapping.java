package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
@Data
@TableName("tms_driver_car_carrier_mapping")
public class DriverCarCarrierMapping {
    private Long id;                         // 主键 ID
    private Long tmsCarId;                   // 车辆 ID
    private Long tmsDriverId;                // 司机 ID
    private Integer carrierId;                // 承运商 ID
    private Byte state;                       // 状态，0=正常，1=取消
    private Date createTime;                 // 创建时间
    private Date updateTime;                 // 更新时间
    private Byte businessType;                // 业务类型，0=干线，1=城配
    private Long warehouseSiteId;            // 城配仓点位 ID

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTmsCarId() {
        return tmsCarId;
    }

    public void setTmsCarId(Long tmsCarId) {
        this.tmsCarId = tmsCarId;
    }

    public Long getTmsDriverId() {
        return tmsDriverId;
    }

    public void setTmsDriverId(Long tmsDriverId) {
        this.tmsDriverId = tmsDriverId;
    }

    public Integer getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Integer carrierId) {
        this.carrierId = carrierId;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Long getWarehouseSiteId() {
        return warehouseSiteId;
    }

    public void setWarehouseSiteId(Long warehouseSiteId) {
        this.warehouseSiteId = warehouseSiteId;
    }
}
