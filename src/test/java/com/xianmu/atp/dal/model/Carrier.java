package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("carrier")

public class Carrier {
    private Long id; // 主键 ID
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
    private String carrierName; // 承运商名称
    private String director; // 负责人
    private String directorPhone; // 负责人电话
    private String address; // 地址
    private String cooperationAgreement; // 合作协议地址
    private Integer businessType; // 业务类型
    private String socialCreditCode; // 社会信用代码
    private String subBusinessType; // 二级类型
}
