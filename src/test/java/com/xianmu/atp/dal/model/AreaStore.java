package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 * 区域库存
 * <AUTHOR>
 * @version 1.0
 * @ClassName AreaStore
 * @date 2024-11-05
 */
@Data
@TableName("area_store")
public class AreaStore {

    /**
     * 区域存储的唯一标识
     */
    private long id;
    /**
     * 区域编号
     */
    private long areaNo;
    /**
     * 商品SKU
     */
    private String sku;
    /**
     * 商品数量
     */
    private long quantity;
    /**
     * 更新时间
     */
    private java.sql.Timestamp updateTime;
    /**
     * 管理员ID
     */
    private long adminId;
    /**
     * 提前期
     */
    private long leadTime;
    /**
     * 市场价格
     */
    private double marketPrice;
    /**
     * 成本价格
     */
    private double costPrice;
    /**
     * 价格状态
     */
    private long priceStatus;
    /**
     * 在线数量
     */
    private long onlineQuantity;
    /**
     * 销售锁定数量
     */
    private long saleLockQuantity;
    /**
     * 锁定数量
     */
    private long lockQuantity;
    /**
     * 道路数量
     */
    private long roadQuantity;
    /**
     * 同步状态
     */
    private long sync;
    /**
     * 安全数量
     */
    private long safeQuantity;
    /**
     * 变更数量
     */
    private long change;
    /**
     * 状态
     */
    private long status;
    /**
     * 自动转移状态
     */
    private long autoTransfer;
    /**
     * 支持保留状态
     */
    private long supportReserved;
    /**
     * 最大保留数量
     */
    private long reserveMaxQuantity;
    /**
     * 最小保留数量
     */
    private long reserveMinQuantity;
    /**
     * 已使用保留数量
     */
    private long reserveUseQuantity;
    /**
     * 警告数量
     */
    private long warningQuantity;
    /**
     * 发送警告标志
     */
    private long sendWarningFlag;
    /**
     * 提前数量
     */
    private long advanceQuantity;
    /**
     * 租户ID
     */
    private long tenantId;
    /**
     * 仓库租户ID
     */
    private long warehouseTenantId;
    /**
     * 所有者代码
     */
    private String ownerCode;
    /**
     * 所有者名称
     */
    private String ownerName;
}
