package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName CrmBdVisitCust
 * @date 2025-03-12
 */
@Data
@TableName("crm_bd_visit_cust")
public class CrmBdVisitCust {


    private long id;
    private long dateTag;
    private long custId;
    private String custName;
    private String custType;
    private String custGroup;
    private String registerProvince;
    private String registerCity;
    private long registerType;
    private long visitBdId;
    private String visitBdName;
    private String visitType;
    private java.sql.Timestamp visitTime;
    private java.sql.Timestamp visitAfterLoadTime;
    private java.sql.Timestamp visitAfterClickTime;
    private java.sql.Timestamp visitAfterAddTime;
    private String visitAfterIsBuy;
    private String visitAfterFirstorderTime;
    private double visitAfterOriginAmt;
    private String visitAfterSkuName;
    private String visitRemarks;
    private String isEffectiveVisit;
}
