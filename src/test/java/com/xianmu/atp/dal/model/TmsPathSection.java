package com.xianmu.atp.dal.model;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("tms_path_section")
public class TmsPathSection {
    // 数据库主键
    private Long id;

    // 创建时间
    private LocalDateTime createTime;

    // 更新时间
    private LocalDateTime updateTime;

    // 路段起点 ID
    private Long beginSiteId;

    // 路段起点名称
    private String beginSiteName;

    // 路段终点 ID
    private Long endSiteId;

    // 路段终点名称
    private String endSiteName;

    // 预计花费时间（单位：小时）
    private BigDecimal planSpan;

    // 路段在路径中的次序
    private Integer sequence;

    // 所属路线 ID
    private Long pathId;

    // 路段类型 (0: 城配, 1: 干线)
    private Integer type;

    // 装货时长（单位：小时）
    private BigDecimal loadHour;
}
