package com.xianmu.atp.dal.model.saas;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Getter
@Setter
@TableName("`order`")
public class Order implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 主键Id
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 租户Id
   */
  private Long tenantId;

  /**
   * 店铺Id
   */
  private Long storeId;

  /**
   * 下单账号Id
   */
  private Long accountId;

  /**
   * 供应商租户Id
   */
  private Long supplierTenantId;

  /**
   * 订单编号
   */
  private String orderNo;

  /**
   * 配送仓类型 0,无仓1三方仓 2自营仓
   */
  private Integer warehouseType;

  /**
   * 应付价格
   */
  private BigDecimal payablePrice;

  /**
   * 配送费
   */
  private BigDecimal deliveryFee;

  /**
   * 总金额
   */
  private BigDecimal totalPrice;

  /**
   * 状态 1下单中 2待支付 3待配送 4待收货 5已完成 6已取消 7已退款
   */
  private Integer status;

  /**
   * 支付方式 1,线上支付 2,账期 3、余额支付
   */
  private Integer payType;

  /**
   * 支付渠道 0、微信 1、汇付
   */
  private Integer onlinePayChannel;

  /**
   * 支付时间
   */
  private LocalDateTime payTime;

  /**
   * 配送时间
   */
  private LocalDateTime deliveryTime;

  /**
   * 完成时间
   */
  private LocalDateTime finishedTime;

  /**
   * 创建时间
   */
  private LocalDateTime createTime;

  /**
   * 修改时间
   */
  private LocalDateTime updateTime;

  /**
   * 备注
   */
  private String remark;

  /**
   * 可申请售后时间(小时)
   */
  private Integer applyEndTime;

  /**
   * 自动完成时间(天)
   */
  private Integer autoFinishedTime;

  /**
   * 仓库编号
   */
  private String warehouseNo;

  /**
   * 组合订单id
   */
  private Long combineOrderId;

  /**
   * 0-普通订单,1=组合订单, 2=预售订单
   */
  private Integer orderType;

  /**
   * 开始配送时间
   */
  private LocalDateTime beginDeliveryTime;

  /**
   * 分账完成时间
   */
  private LocalDateTime profitSharingFinishTime;


  /**
   * 订单记录版本，用来区分旧下单链路，旧链路为空，新链路为1
   */
  private Integer orderVersion;

  /**
   * 订单来源：0：内部系统;1：openapi调用; 2:总部代下单
   */
  private Integer orderSource;

  /**
   * 外部系统订单号
   */
  private String customerOrderId;

  /**
   * ofc履约单号
   */
  private Long fulfillmentNo;

  /**
   * 计划单编号
   */
  private String planOrderNo;

  /**
   * 履约类型，0：城配履约，1：快递履约
   */
  private Integer fulfillmentType;
}
