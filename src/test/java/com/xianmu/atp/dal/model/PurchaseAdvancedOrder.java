package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 预付单表
 * @TableName purchase_advanced_order
 */
@TableName(value ="purchase_advanced_order")
@Data
public class PurchaseAdvancedOrder implements Serializable {
    /**
     * 主键id，也为预付单号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 是否关联采购单 1、关联 2、不关联
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 付款金额
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 供应商id
     */
    @TableField(value = "supplier_id")
    private Integer supplierId;

    /**
     * 供应商名称
     */
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 支付方式 1、银行卡 2、现金
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 1 生鲜 2 品牌
     */
    @TableField(value = "pd_type")
    private Integer pdType;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 预付单状态 1、待审核（账单审核） 4、付款审核中 5、待付款 6、已付款 7、作废
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 供应商账户id
     */
    @TableField(value = "supplier_account_id")
    private Integer supplierAccountId;

    /**
     * 临时备注
     */
    @TableField(value = "temporary_remark")
    private String temporaryRemark;

    /**
     * 是否有关联的对账单 0 是 1 否
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 作废原因：0：预付审核失败 1：撤回申请 2：：付款审核失败 3:财务付款取消
     */
    @TableField(value = "delete_reason")
    private Integer deleteReason;

    /**
     * 当前状态处理人
     */
    @TableField(value = "current_processor")
    private String currentProcessor;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PurchaseAdvancedOrder other = (PurchaseAdvancedOrder) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getTotalAmount() == null ? other.getTotalAmount() == null : this.getTotalAmount().equals(other.getTotalAmount()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
            && (this.getPayType() == null ? other.getPayType() == null : this.getPayType().equals(other.getPayType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getPdType() == null ? other.getPdType() == null : this.getPdType().equals(other.getPdType()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getSupplierAccountId() == null ? other.getSupplierAccountId() == null : this.getSupplierAccountId().equals(other.getSupplierAccountId()))
            && (this.getTemporaryRemark() == null ? other.getTemporaryRemark() == null : this.getTemporaryRemark().equals(other.getTemporaryRemark()))
            && (this.getState() == null ? other.getState() == null : this.getState().equals(other.getState()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleteReason() == null ? other.getDeleteReason() == null : this.getDeleteReason().equals(other.getDeleteReason()))
            && (this.getCurrentProcessor() == null ? other.getCurrentProcessor() == null : this.getCurrentProcessor().equals(other.getCurrentProcessor()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getTotalAmount() == null) ? 0 : getTotalAmount().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getPayType() == null) ? 0 : getPayType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getPdType() == null) ? 0 : getPdType().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getSupplierAccountId() == null) ? 0 : getSupplierAccountId().hashCode());
        result = prime * result + ((getTemporaryRemark() == null) ? 0 : getTemporaryRemark().hashCode());
        result = prime * result + ((getState() == null) ? 0 : getState().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleteReason() == null) ? 0 : getDeleteReason().hashCode());
        result = prime * result + ((getCurrentProcessor() == null) ? 0 : getCurrentProcessor().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", type=").append(type);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", payType=").append(payType);
        sb.append(", createTime=").append(createTime);
        sb.append(", pdType=").append(pdType);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", supplierAccountId=").append(supplierAccountId);
        sb.append(", temporaryRemark=").append(temporaryRemark);
        sb.append(", state=").append(state);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleteReason=").append(deleteReason);
        sb.append(", currentProcessor=").append(currentProcessor);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}