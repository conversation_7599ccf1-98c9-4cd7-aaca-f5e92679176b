package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓储物流中心
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-36 14:24:15
 */
@Getter
@Setter
@TableName("warehouse_logistics_center")
public class WarehouseLogisticsCenter implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物流中心编号（配送仓编号）
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 配送中心状态：0、失效 1、有效
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 物流中心负责人
     */
    @TableField("manage_admin_id")
    private Integer manageAdminId;

    /**
     * 高德poi
     */
    @TableField("poi_note")
    private String poiNote;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 是否支持提前截单：0、false 1、true
     */
    @TableField("close_order_type")
    private Integer closeOrderType;

    /**
     * 同步库存使用仓编号
     */
    @TableField("origin_store_no")
    private Integer originStoreNo;

    /**
     * 销售出库完成时间
     */
    @TableField("sot_finish_time")
    private LocalDateTime sotFinishTime;

    /**
     * 创建人adminId
     */
    @TableField("creator")
    private Integer creator;

    /**
     * 修改人adminId
     */
    @TableField("updater")
    private Integer updater;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 截单时间
     */
    @TableField("close_time")
    private String closeTime;

    /**
     * 更新截单时间
     */
    @TableField("update_close_time")
    private String updateCloseTime;

    /**
     * 联系人
     */
    @TableField("person_contact")
    private String personContact;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 区域
     */
    @TableField("region")
    private String region;

    /**
     * 城配仓照片
     */
    @TableField("store_pic")
    private String storePic;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    @TableField("fulfillment_type")
    private Integer fulfillmentType;


}
