package com.xianmu.atp.dal.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName ShoppingCart
 * @date 2024-11-05
 */

@TableName("shopping_cart")
@Data
public class ShoppingCart {

    /**
     * 购物车记录的唯一标识
     */
    private long id;
    /**
     * 商品ID
     */
    private long mId;
    /**
     * 账户ID
     */
    private long accountId;
    /**
     * 业务ID
     */
    private long bizId;
    /**
     * 商品SKU
     */
    private String sku;
    /**
     * 父SKU，如果有
     */
    private String parentSku;
    /**
     * 产品类型
     */
    private long productType;
    /**
     * 商品数量
     */
    private long quantity;
    /**
     * 创建时间
     */
    private java.sql.Timestamp createTime;
    /**
     * 更新时间
     */
    private java.sql.Timestamp updateTime;
}
