package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.TmsPathCarMapper;
import com.xianmu.atp.dal.model.TmsPathCar;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("TmsPathCarDao")
public class TmsPathCarDao {
    @Resource
    private TmsPathCarMapper tmsPathCarMapper;

    public Long getTmsPathCarId(Long pathId) {
        LambdaUpdateWrapper<TmsPathCar>wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsPathCar::getPathId,pathId);
        TmsPathCar mapping = tmsPathCarMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetTmsPathCar(Long pathId) {
        LambdaQueryWrapper<TmsPathCar>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPathCar::getPathId,pathId);
        return tmsPathCarMapper.delete(wrapper);
    }
}
