package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.AfterSaleOrderMapper;
import com.xianmu.atp.dal.model.AfterSaleOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("AfterSaleOrderDao")
public class AfterSaleOrderDao {
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    public List<AfterSaleOrder> getAfterSaleOrder(String orderNo){
        LambdaQueryWrapper<AfterSaleOrder> afterSaleWrapper = new LambdaQueryWrapper<>();
        afterSaleWrapper
                .eq(AfterSaleOrder::getOrderNo, orderNo);;
        return  afterSaleOrderMapper.selectList(afterSaleWrapper);
    }
}
