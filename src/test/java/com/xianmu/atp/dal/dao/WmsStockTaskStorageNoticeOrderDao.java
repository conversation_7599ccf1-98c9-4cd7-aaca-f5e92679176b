package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.WmsStockTaskStorageNoticeOrderMapper;
import com.xianmu.atp.dal.model.WmsStockTaskNoticeOrder;
import com.xianmu.atp.dal.model.WmsStockTaskStorageNoticeOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("WmsStockTaskStorageNoticeOrderDao")
public class WmsStockTaskStorageNoticeOrderDao {
    @Resource
    private WmsStockTaskStorageNoticeOrderMapper wmsStockTaskStorageNoticeOrderMapper;
    public List<WmsStockTaskStorageNoticeOrder> getStorageNoticeInfo(String order){
        LambdaQueryWrapper<WmsStockTaskStorageNoticeOrder> storageNoticeWrapper = new LambdaQueryWrapper<>();
        storageNoticeWrapper
                .eq(WmsStockTaskStorageNoticeOrder::getOutOrderNo, order)
                .orderByDesc(WmsStockTaskStorageNoticeOrder::getId);;
        return  wmsStockTaskStorageNoticeOrderMapper.selectList(storageNoticeWrapper);
    }
}
