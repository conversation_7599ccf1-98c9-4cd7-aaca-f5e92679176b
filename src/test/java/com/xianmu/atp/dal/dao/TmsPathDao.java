package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.TmsPathMapper;
import com.xianmu.atp.dal.model.TmsPath;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("TmsPathDao")
public class TmsPathDao {
    @Resource
    private TmsPathMapper tmsPathMapper;

    public Long getTmsPathId(String pathCode) {
        LambdaUpdateWrapper<TmsPath>wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsPath::getPathCode,pathCode);
        TmsPath mapping = tmsPathMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetTmsPath(String pathCode) {
        LambdaQueryWrapper<TmsPath>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPath::getPathCode,pathCode);
        return tmsPathMapper.delete(wrapper);
    }
}
