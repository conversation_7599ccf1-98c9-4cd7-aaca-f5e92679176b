package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.WmsStockTaskNoticeOrderMapper;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import com.xianmu.atp.dal.model.WmsStockTaskNoticeOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Component("WmsStockTaskNoticeOrderDao")
public class WmsStockTaskNoticeOrderDao {
    @Resource
    private WmsStockTaskNoticeOrderMapper wmsStockTaskNoticeOrderMapper;

    /**
     * 更新出库通知单的时间
     * @param order
     */
    public void updateNoticeTime(String order){
        LambdaQueryWrapper<WmsStockTaskNoticeOrder> stockTaskNoticeOrderWrapper = new LambdaQueryWrapper<>();
        stockTaskNoticeOrderWrapper
                .eq(WmsStockTaskNoticeOrder::getOutOrderNo, order)
                .orderByDesc(WmsStockTaskNoticeOrder::getId);
        WmsStockTaskNoticeOrder entityNoticeOrder = new WmsStockTaskNoticeOrder();
        // 获取明天的日期
        LocalDate tomorrow = LocalDate.now().plusDays(1);

        // 将 LocalDate 转换为 LocalDateTime，并设置时间为 00:00:00
        LocalDateTime formattedTomorrow = tomorrow.atStartOfDay();
        // 定义日期格式
        entityNoticeOrder.setExceptTime(formattedTomorrow);
        wmsStockTaskNoticeOrderMapper.update(entityNoticeOrder, stockTaskNoticeOrderWrapper);
    }

    /**
     * 获取出库通知单的相关信息
     * @param order
     * @return
     */
    public  List<WmsStockTaskNoticeOrder> getNoticeInfo(String order){
        LambdaQueryWrapper<WmsStockTaskNoticeOrder> stockTaskNoticeOrderWrapper = new LambdaQueryWrapper<>();
        stockTaskNoticeOrderWrapper
                .eq(WmsStockTaskNoticeOrder::getOutOrderNo, order)
                .orderByDesc(WmsStockTaskNoticeOrder::getId);;
        List<WmsStockTaskNoticeOrder> taskNoticeInfoDbs = wmsStockTaskNoticeOrderMapper.selectList(stockTaskNoticeOrderWrapper);
        return  taskNoticeInfoDbs;
    }
}
