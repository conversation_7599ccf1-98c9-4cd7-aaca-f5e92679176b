package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.AuthUserBaseMapper;
import com.xianmu.atp.dal.model.AuthUserBase;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("AuthUserBaseDao")
public class AuthUserBaseDao {
    @Resource
    private AuthUserBaseMapper authUserBaseMapper;

    public int delcetAuthUserBase(String username) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<AuthUserBase> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(AuthUserBase::getUsername, username);
        // 执行删除操作并返回删除的行数
        return authUserBaseMapper.delete(wrapper);
    }
}
