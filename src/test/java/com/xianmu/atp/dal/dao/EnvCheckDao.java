package com.xianmu.atp.dal.dao;

import cn.hutool.core.lang.Console;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.EnvCheckMapper;
import com.xianmu.atp.dal.model.EnvCheck;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName EnvCheckDao
 * @date 2024-11-19
 */
@Component("EnvCheckDao")
public class EnvCheckDao {

    @Resource
    private EnvCheckMapper envCheckMapper;

    public String getCheckUrl(int id) {
        LambdaUpdateWrapper<EnvCheck> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(EnvCheck::getId, id);
        EnvCheck check = envCheckMapper.selectOne(lambdaUpdateWrapper);
        String checkUrl = "dev" + check.getBaseUrl()+check.getCheckUrl();
        return checkUrl;
    }

    public String getAppName(int id) {
        LambdaUpdateWrapper<EnvCheck> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(EnvCheck::getId, id);
        EnvCheck check = envCheckMapper.selectOne(lambdaUpdateWrapper);
        return check.getServiceName();
    }
}
