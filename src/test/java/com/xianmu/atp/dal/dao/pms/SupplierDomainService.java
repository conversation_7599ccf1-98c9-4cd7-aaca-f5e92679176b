package com.xianmu.atp.dal.dao.pms;

import cn.hutool.core.lang.Console;
import com.xianmu.atp.dal.mapper.SupplierAccountMapper;
import com.xianmu.atp.dal.mapper.SupplierMapper;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.SupplierAccount;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class SupplierDomainService {

    @Resource
    private SupplierMapper supplierMapper;
    @Resource
    private SupplierAccountMapper supplierAccountMapper;

    /**
     * 根据供应商ID获取供应商账户信息
     * 此方法旨在查询与特定供应商相关的账户信息，返回第一个匹配的账户对象
     * 如果没有找到匹配的账户信息，则记录错误日志并返回null
     *
     * @param supplierId 供应商ID，用于查询供应商账户信息
     * @return 如果找到匹配的账户信息，则返回SupplierAccount对象；否则返回null
     */
    @Retry(attempts = 3, delay = 2000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public SupplierAccount getSupplierAccountList(Integer supplierId) {
        List<SupplierAccount> supplierAccountList = supplierAccountMapper.selectBySupplierId(supplierId);

        if (supplierAccountList != null && !supplierAccountList.isEmpty()) {
            return supplierAccountList.get(0);
        } else {
            throw new NullPointerException("供应商账户信息查询为空supplierId: "+ supplierId);
        }
    }

    /**
     * 根据供应商ID获取供应商基本信息
     * 此方法用于查询供应商的基本信息，返回第一个匹配的供应商对象
     * 如果没有找到匹配的供应商信息，则记录错误日志并返回null
     *
     * @param supplierId 供应商ID，用于查询供应商信息
     * @return 如果找到匹配的供应商信息，则返回Supplier对象；否则返回null
     */
    @Retry(attempts = 3, delay = 2000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public Supplier selectById(Integer supplierId) {
        Supplier supplier = supplierMapper.selectById(supplierId);

        if (supplier != null) {
            return supplier;
        } else {
            Console.log("供应商信息查询为空supplierId: {}", supplierId);
            throw new NullPointerException("供应商信息查询为空supplierId: " + supplierId);// 或者抛出异常，根据业务需求决定
//            return null;
        }
    }
}
