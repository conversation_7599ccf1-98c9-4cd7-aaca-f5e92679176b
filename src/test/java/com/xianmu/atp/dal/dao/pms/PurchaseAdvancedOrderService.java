package com.xianmu.atp.dal.dao.pms;

import cn.hutool.core.lang.Console;
import com.xianmu.atp.dal.mapper.PurchaseAdvancedOrderMapper;
import com.xianmu.atp.dal.model.PurchaseAdvancedOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【purchase_advanced_order(预付单表)】的数据库操作Service
* @createDate 2025-01-13 16:34:55
*/

@Component("PurchaseAdvancedOrderService")
public class PurchaseAdvancedOrderService{
    @Resource
    private PurchaseAdvancedOrderMapper purchaseAdvancedOrderMapper;
    

    @Retry(attempts = 3, delay = 2000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public PurchaseAdvancedOrder selectById(Integer supplierId) {
        PurchaseAdvancedOrder purchaseAdvancedOrderInfo = purchaseAdvancedOrderMapper.selectById(supplierId);
        Console.log("预付单Id: {}", supplierId);
        if (purchaseAdvancedOrderInfo != null) {
            return purchaseAdvancedOrderInfo;
        } else {
            throw new NullPointerException("预付单信息查询为空Id: " + supplierId);// 或者抛出异常，根据业务需求决定
//            return null;
        }
    }
}
