package com.xianmu.atp.dal.dao.pms;

import com.xianmu.atp.dal.mapper.pms.SrmSupplierOfferDetailStepPriceMapper;
import com.xianmu.atp.dal.model.SrmSupplierOfferDetailStepPrice;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【srm_supplier_offer_detail_step_price(报价详情阶梯价表)】的数据库操作Service实现
 * @createDate 2025-04-23 18:00:19
 */
@Component()
public class SrmSupplierOfferDetailStepPriceService {

    @Resource
    private SrmSupplierOfferDetailStepPriceMapper srmSupplierOfferDetailStepPriceMapper;


    //查询最近供应商报价
    public SrmSupplierOfferDetailStepPrice selectStepDetailPrice( Integer supplierId, Integer warehouseNo, String skuCode, Integer status) {
        // 查询最近的报价数据
        return srmSupplierOfferDetailStepPriceMapper.selectStepDetailPrice(supplierId,
                warehouseNo,skuCode,status);
    }
}
