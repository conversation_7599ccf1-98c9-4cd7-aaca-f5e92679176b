package com.xianmu.atp.dal.dao.pms;


import cn.hutool.core.lang.Console;
import com.xianmu.atp.dal.mapper.pms.SrmStockChangeRecordMapper;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component()
public class SrmStockChangeRecordService {

    @Resource
    private SrmStockChangeRecordMapper srmStockChangeRecordMapper;

    @Retry(attempts = 3, delay = 2000, onExceptions = {Exception.class})
    public void deleteById(Integer recordId) {
        Console.log("预执行删除动作--未提报库存变更记录");
        try {
            srmStockChangeRecordMapper.deleteById(recordId);
        }catch (Exception e){
            throw new RuntimeException("删除库存变更记录失败，recordId: " + recordId, e);
        }
    }
}
