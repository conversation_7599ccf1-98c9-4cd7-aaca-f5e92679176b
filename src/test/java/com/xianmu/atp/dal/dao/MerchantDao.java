package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.MerchantMapper;
import com.xianmu.atp.dal.model.Merchant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 类<code>Doc</code>用于：TODO
 * merchant相关操作
 * <AUTHOR>
 * @ClassName MerchantDao
 * @date 2024-11-05
 */

@Component("MerchantDao")
public class MerchantDao {

    @Resource
    private MerchantMapper merchantMapper;

    @Transactional
    public int updateAmountByMerchantId(long merchantId, double amount) {
        LambdaUpdateWrapper<Merchant> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Merchant::getMId, merchantId);
        lambdaUpdateWrapper.set(Merchant::getRechargeAmount, amount);
        return merchantMapper.update(lambdaUpdateWrapper);
    }
    public int updateAmountByMerchantIds(String phone, double amount) {
        LambdaUpdateWrapper<Merchant> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Merchant::getPhone, phone);
        lambdaUpdateWrapper.set(Merchant::getRechargeAmount, amount);
        return merchantMapper.update(lambdaUpdateWrapper);
    }

    @Transactional
    public void updateRole(long merchantId) {
        LambdaUpdateWrapper<Merchant> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Merchant::getMId, merchantId);
        lambdaUpdateWrapper.set(Merchant::getRoleId, -1);
        merchantMapper.update(lambdaUpdateWrapper);
    }

    public Merchant getMerchantByPhone(String phone) {
        LambdaUpdateWrapper<Merchant> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Merchant::getPhone, phone);
        return merchantMapper.selectOne(lambdaUpdateWrapper);
    }

    @Transactional
    public List<String> getMerchantDoorPicList() {
        LambdaUpdateWrapper<Merchant> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.ne(Merchant::getDoorPic, "");
        lambdaUpdateWrapper.isNotNull(Merchant::getDoorPic);
        lambdaUpdateWrapper.orderByDesc(Merchant::getMId);
        lambdaUpdateWrapper.last(" limit 2000");
        List<Merchant> list = merchantMapper.selectList(lambdaUpdateWrapper);
        List<String> res = new ArrayList<>();
        for (Merchant merchant : list) {
            System.out.println(merchant.getDoorPic());
            res.add(merchant.getDoorPic());
        }
        return res;
    }


}
