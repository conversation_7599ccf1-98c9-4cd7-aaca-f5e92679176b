package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.DriverMapper;
import com.xianmu.atp.dal.model.Driver;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("DriverDao")

public class DriverDao {
    @Resource
    private  DriverMapper driverMapper;

    public Long getDriver(String phone) {
        LambdaUpdateWrapper<Driver> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Driver::getPhone, phone);
        Driver mapping = driverMapper.selectOne(wrapper);
        // 使用 Optional 来处理可能的 null 值
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetDriver(String phone) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<Driver> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(Driver::getPhone, phone);
        // 执行删除操作并返回删除的行数
        return driverMapper.delete(wrapper);
    }
}
