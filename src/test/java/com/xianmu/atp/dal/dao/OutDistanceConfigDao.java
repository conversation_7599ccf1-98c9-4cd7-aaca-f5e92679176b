package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.OutDistanceConfigMapper;
import com.xianmu.atp.dal.model.OutDistanceConfig;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("OutDistanceConfigDao")
public class OutDistanceConfigDao {

    @Resource
    private OutDistanceConfigMapper outDistanceConfigMapper;


    public int deleteOutDistanceConfig(Integer storeNo) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<OutDistanceConfig> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(OutDistanceConfig::getStoreNo, storeNo);
        // 执行删除操作并返回删除的行数
        return outDistanceConfigMapper.delete(wrapper);
    }
}
