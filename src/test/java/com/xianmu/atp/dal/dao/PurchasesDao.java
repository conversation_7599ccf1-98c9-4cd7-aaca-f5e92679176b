package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.PurchasesMapper;
import com.xianmu.atp.dal.model.Purchases;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.List;

@Component("PurchasesDao")
public class PurchasesDao {
    @Resource
    private PurchasesMapper purchasesMapper;
    @Retry(attempts = 3, delay = 2000, onExceptions = {AssertionError.class})
    public String getPurchasesNo(String remark){
        LambdaQueryWrapper<Purchases> purchasesWrapper = new LambdaQueryWrapper<>();
        purchasesWrapper.eq(Purchases::getRemark, remark);
        List<Purchases> purchasesDbs = purchasesMapper.selectList(purchasesWrapper);
        Assert.assertNotNull(purchasesDbs);
        return purchasesDbs.get(0).getPurchaseNo();
    }
}
