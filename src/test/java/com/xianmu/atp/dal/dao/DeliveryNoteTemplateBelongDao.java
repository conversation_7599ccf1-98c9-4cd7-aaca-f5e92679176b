package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.DeliveryNoteTemplateBelongMapper;
import com.xianmu.atp.dal.model.DeliveryNoteTemplateBelong;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("DeliveryNoteTemplateBelongDao")
public class DeliveryNoteTemplateBelongDao {

    @Resource
    private DeliveryNoteTemplateBelongMapper deliveryNoteTemplateBelongMapper;

    /**
     * 根据 ID 删除配送单模板。
     *
     * @param deliveryNoteTemplateId 配送单模板 ID
     * @return 删除的行数
     */
    public int deleteDeliveryNoteTemplateBelong(String deliveryNoteTemplateId) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<DeliveryNoteTemplateBelong> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(DeliveryNoteTemplateBelong::getDeliveryNoteTemplateId, deliveryNoteTemplateId);
        // 执行删除操作并返回删除的行数
        return deliveryNoteTemplateBelongMapper.delete(wrapper);
    }

    public int deleteScopeBusinessName(String scopeBusinessName) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<DeliveryNoteTemplateBelong> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(DeliveryNoteTemplateBelong::getScopeBusinessName, scopeBusinessName);
        // 执行删除操作并返回删除的行数
        return deliveryNoteTemplateBelongMapper.delete(wrapper);
    }
}
