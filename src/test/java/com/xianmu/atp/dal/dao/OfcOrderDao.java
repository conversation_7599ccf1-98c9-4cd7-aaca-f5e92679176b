package com.xianmu.atp.dal.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.OfcOrderMapper;
import com.xianmu.atp.dal.model.OfcOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


@Component("OfcOrderDao")
public class OfcOrderDao {


    @Resource
    private OfcOrderMapper ofcOrderMapper;

    public String getOrderNo(String remark){
        LambdaQueryWrapper<OfcOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(OfcOrder::getOutOrderNo, remark);
        List<OfcOrder> ofcOrders = ofcOrderMapper.selectList(orderWrapper);
        return ofcOrders.get(0).getOutOrderNo();
    }
    public List<OfcOrder> getFulfillmentOrder(String order){
        LambdaQueryWrapper<OfcOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(OfcOrder::getOutOrderNo, order);
        return ofcOrderMapper.selectList(orderWrapper);
    }

}
