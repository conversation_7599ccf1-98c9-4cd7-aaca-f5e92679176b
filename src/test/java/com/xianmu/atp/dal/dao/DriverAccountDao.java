package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.DriverAccountMapper;
import com.xianmu.atp.dal.model.DriverAccount;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("DriverAccountDao")

public class DriverAccountDao {
    @Resource
    private DriverAccountMapper driverAccountMapper;

    public int delcetDriverAccount(long driverId) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<DriverAccount> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(DriverAccount::getDriverId, driverId);
        // 执行删除操作并返回删除的行数
        return driverAccountMapper.delete(wrapper);
    }
}
