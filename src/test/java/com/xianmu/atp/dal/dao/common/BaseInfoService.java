package com.xianmu.atp.dal.dao.common;

import cn.hutool.core.lang.Console;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.dal.mapper.*;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import com.xianmu.atp.enums.api.common.CommonEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 获取商品、供应商、仓库基础数据的数据库操作Service
 * @createDate 2025-01-13 17:06:13
 */
@Component
public class BaseInfoService {

    @Resource
    private SupplierMapper supplierMapper;

    @Resource
    private SkuInfoMapper skuInfoMapper;

    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;


    /**
     * 根据供应商ID获取供应商基本信息
     * 此方法用于查询供应商的基本信息，返回第一个匹配的供应商对象
     * 如果没有找到匹配的供应商信息，则记录错误日志并返回null
     *
     * @param supplierId 供应商ID，用于查询供应商信息
     * @return 如果找到匹配的供应商信息，则返回Supplier对象；否则返回null
     */
    @Retry(attempts = 3, delay = 2000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public Supplier selectById(Integer supplierId) {
        Supplier supplier = supplierMapper.selectById(supplierId);

        if (supplier != null) {
            return supplier;
        } else {
            Console.log("供应商信息查询为空supplierId: {}", supplierId);
            throw new NullPointerException("供应商信息查询为空supplierId: " + supplierId);// 或者抛出异常，根据业务需求决定
//            return null;
        }
    }

    /**
     * 根据供应商ID列表获取供应商基本信息
     * 此方法用于查询供应商的基本信息，返回第一个匹配的供应商对象
     * 如果没有找到匹配的供应商信息，则记录错误日志并返回null
     *
     * @param supplierIds 供应商ID，用于查询供应商信息
     * @return 匹配的供应商信息
     */
    @Retry(attempts = 3, delay = 2000, onExceptions = {AssertionError.class}, retryOnEmptyResult = true)
    public List<Supplier> selectSupplierByIds(List<Integer> supplierIds) {
        LambdaQueryWrapper<Supplier> supplierWrapper = new LambdaQueryWrapper<>();
        supplierWrapper.in(Supplier::getId, supplierIds);
        List<Supplier> supplierList = supplierMapper.selectList(supplierWrapper);
        Assert.assertNotNull(supplierList,"供应商信息查询为空");
        return supplierList;

    }

    //获取商品基础数据
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public SkuInfo getProductInfo(String sku) {
        SkuInfo skuInfo = skuInfoMapper.getSkuInfoBySku(sku);
        Assert.assertNotNull(skuInfo,"商品基础数据查询为空");
        return skuInfo;
    }

    //获取仓库基础数据
    @Retry(attempts = 3, delay = 2000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public WarehouseStorageCenter getWarehouseInfo(String warehouseNo) {
        LambdaQueryWrapper<WarehouseStorageCenter> warehouseWrapper = new LambdaQueryWrapper<>();
        warehouseWrapper.eq(WarehouseStorageCenter::getWarehouseNo, warehouseNo);
        WarehouseStorageCenter warehouseInfo = warehouseStorageCenterMapper.selectOne(warehouseWrapper);

        if (warehouseInfo != null) {
            return warehouseInfo;
        } else {
            Console.log("仓库信息查询为空Id: {}", warehouseNo);
            throw new NullPointerException("仓库信息查询为空Id: " + warehouseNo);// 或者抛出异常，根据业务需求决定
//            return null;
        }
    }

    //获取仓库列表信息
    @Retry(attempts = 3, delay = 2000, onExceptions = {AssertionError.class}, retryOnEmptyResult = true)
    public List<WarehouseStorageCenter> getWarehouseByIds(List<Integer> warehouseNo) {
        LambdaQueryWrapper<WarehouseStorageCenter> warehouseWrapper = new LambdaQueryWrapper<>();
        warehouseWrapper.in(WarehouseStorageCenter::getWarehouseNo, warehouseNo);
        List<WarehouseStorageCenter> warehouseList = warehouseStorageCenterMapper.selectList(warehouseWrapper);
        Assert.assertNotNull(warehouseList,"仓库信息查询为空");
        return warehouseList;
    }

}
