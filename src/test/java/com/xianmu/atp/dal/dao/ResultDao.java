package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.ResultMapper;
import com.xianmu.atp.dal.model.Record;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName CountResultDao
 * @date 2024-11-15
 */

@Component("ResultDao")
public class ResultDao {

    @Resource
    private ResultMapper resultMapper;

    public String select(){
        LambdaUpdateWrapper<Record> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Record::getTraceId,"010244001238173028891250410002ed");
        Record record = resultMapper.selectOne(wrapper);
        return record.toString();
    }
}
