package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.StockTaskStorageMapper;
import com.xianmu.atp.dal.model.StockTaskStorage;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("StockTaskStorageDao")
public class StockTaskStorageDao {
    @Resource
    private StockTaskStorageMapper stockTaskStorageMapper;
    public String getStockTaskStorageId(String purchase){
        LambdaQueryWrapper<StockTaskStorage> StorageWrapper = new LambdaQueryWrapper<>();
        StorageWrapper.eq(StockTaskStorage::getSourceId, purchase);
        List<StockTaskStorage> StorageDbs = stockTaskStorageMapper.selectList(StorageWrapper);
        return StorageDbs.get(0).getId().toString();

    }
}
