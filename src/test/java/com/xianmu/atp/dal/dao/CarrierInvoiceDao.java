package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.CarrierInvoiceMapper;
import com.xianmu.atp.dal.model.CarrierAccount;
import com.xianmu.atp.dal.model.CarrierInvoice;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("CarrierInvoiceDao")
public class CarrierInvoiceDao {
    @Resource
    private CarrierInvoiceMapper carrierInvoiceMapper;

    public int delcetCarrierInvoice(long carrierId) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<CarrierInvoice> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(CarrierInvoice::getCarrierId, carrierId);
        // 执行删除操作并返回删除的行数
        return carrierInvoiceMapper.delete(wrapper);
    }

    public Long getCarrierInvoiceId(long carrierId) {
        LambdaUpdateWrapper<CarrierInvoice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CarrierInvoice::getCarrierId, carrierId);
        CarrierInvoice mapping = carrierInvoiceMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getId() : null;
    }
}
