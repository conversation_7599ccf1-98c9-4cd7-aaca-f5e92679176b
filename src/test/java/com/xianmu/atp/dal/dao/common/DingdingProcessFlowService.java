package com.xianmu.atp.dal.dao.common;

import com.xianmu.atp.dal.mapper.DingdingProcessFlowMapper;
import com.xianmu.atp.dal.model.DingdingProcessFlow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;

/**
* <AUTHOR>
* @description 针对表【dingding_process_flow(钉钉审批表)】的数据库操作Service
* @createDate 2025-01-13 17:06:13
*/
@Component("DingdingProcessFlowService")
public class DingdingProcessFlowService {

    @Resource
    private DingdingProcessFlowMapper dingdingProcessFlowMapper;

    @Retry( delay = 5000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public DingdingProcessFlow selectOneByBizIdAndBizType(Long bizId, Integer bizType) {
        DingdingProcessFlow DingdingProcessFlowInfo = dingdingProcessFlowMapper.selectOneByBizIdAndBizType(bizId, bizType);
        if (DingdingProcessFlowInfo != null) {
            return DingdingProcessFlowInfo;
        } else {
            throw new NullPointerException("审批流查询结果不符合预期--Id: " + bizId);// 或者抛出异常，根据业务需求决定
        }
    }

    //查询审批流状态
    @Retry( delay = 5000, onExceptions = {AssertionError.class}, retryOnEmptyResult = true)
    public void selectOneByBizIdAndBizTypeAndProcessStatus(Long bizId, Integer bizType, Integer processStatus) {
        DingdingProcessFlow DingdingProcessFlowInfo = dingdingProcessFlowMapper.selectOneByBizIdAndBizTypeAndProcessStatus(bizId, bizType,processStatus);
        Assert.assertNotNull(DingdingProcessFlowInfo, "审批流查询结果不符合预期--Id: " + bizId);
    }
}