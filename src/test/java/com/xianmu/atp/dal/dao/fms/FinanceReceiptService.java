package com.xianmu.atp.dal.dao.fms;

import com.xianmu.atp.dal.mapper.FinanceReceiptMapper;
import com.xianmu.atp.dal.model.FinanceReceipt;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.List;

@Component
public class FinanceReceiptService {

    @Resource
    private FinanceReceiptMapper financeReceiptMapper;

    /**
     * 根据业务ID和来源编号查询FinanceReceipt
     * 此方法选择使用重试机制是因为查询操作可能会受到临时性问题的影响，例如数据库连接波动等
     * 通过重试增加查询成功的机会，确保数据的可靠获取
     *
     * @param biz_id 订单的业务ID
     * @return 返回查询到的第一个FinanceReceipt对象
     * @throws AssertionError 如果查询结果为空列表，则抛出断言错误，表明没有找到对应的退款单
     */
    @Retry(attempts = 3, delay = 2000, onExceptions = {AssertionError.class}, retryOnEmptyResult = true)
    public FinanceReceipt selectAllByFinanceOrderId(Integer biz_id) {
        List<FinanceReceipt> financeReceiptList = financeReceiptMapper.selectAllByFinanceOrderIdWithJoin(biz_id,String.valueOf(biz_id));

        Assert.assertFalse(financeReceiptList.isEmpty(), "退款单未查到,业务单号: "+ biz_id);
        return financeReceiptList.get(0);
    }
}
