package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.DataSynchronizationInformationMapper;
import com.xianmu.atp.dal.model.DataSynchronizationInformation;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName DataSynchronizationInformationDao
 * @date 2025-03-12
 */
@Component
public class DataSynchronizationInformationDao {
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    public int updateByTableName(String tableName){
        LambdaUpdateWrapper<DataSynchronizationInformation> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(DataSynchronizationInformation::getTableName, tableName);
        lambdaUpdateWrapper.set(DataSynchronizationInformation::getDateFlag, localDateTimeToStringTwo(LocalDate.now().minusDays(1)));
        return dataSynchronizationInformationMapper.update(lambdaUpdateWrapper);
    }

    public static String localDateTimeToStringTwo(LocalDate localDate) {
        if (localDate == null) {
            return "";
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        return df.format(localDate);
    }
}
