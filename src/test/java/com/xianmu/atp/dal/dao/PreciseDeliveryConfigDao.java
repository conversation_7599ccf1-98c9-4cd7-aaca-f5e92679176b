package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.PreciseDeliveryConfigMapper;
import com.xianmu.atp.dal.model.PreciseDeliveryConfig;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("PreciseDeliveryConfigDao")

public class PreciseDeliveryConfigDao {
    @Resource
    private PreciseDeliveryConfigMapper preciseDeliveryConfigMapper;

    public int delcetPreciseDeliveryConfig(Long id) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<PreciseDeliveryConfig> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(PreciseDeliveryConfig::getId, id);
        // 执行删除操作并返回删除的行数
        return preciseDeliveryConfigMapper.delete(wrapper);
    }
}
