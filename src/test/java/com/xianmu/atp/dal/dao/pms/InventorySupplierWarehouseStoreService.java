package com.xianmu.atp.dal.dao.pms;

import cn.hutool.core.lang.Console;
import com.xianmu.atp.dal.mapper.pms.InventorySupplierWarehouseStoreMapper;
import com.xianmu.atp.dal.model.InventorySupplierWarehouseStore;
import com.xianmu.atp.dal.model.PurchaseAdvancedOrder;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【inventory_supplier_warehouse_store(供应商仓库库存)】的数据库操作Service实现
 * @createDate 2025-03-14 18:00:19
 */
@Component()
public class InventorySupplierWarehouseStoreService {

    @Resource
    private InventorySupplierWarehouseStoreMapper inventorySupplierWarehouseStoreMapper;



    @Retry(attempts = 3, delay = 2000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public InventorySupplierWarehouseStore selectBySupplierIdAndWarehouseNoAndSkuCode(Integer supplierId, Integer warehouseNo, String skuCode) {
        List<InventorySupplierWarehouseStore> stockInfo = inventorySupplierWarehouseStoreMapper.selectBySupplierIdAndWarehouseNoAndSkuCode(supplierId,
                warehouseNo,skuCode);
        if (Objects.nonNull(stockInfo) && !stockInfo.isEmpty()) {
            return stockInfo.get(0);
        } else {
            throw new NullPointerException(
                    "供应商库存查询为空，supplierId: " + supplierId + ", warehouseNo: " + warehouseNo + ", skuCode: " + skuCode
            );// 或者抛出异常，根据业务需求决定
        }
    }
}
