package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.ContactMapper;
import com.xianmu.atp.dal.model.Contact;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("ContactDao")
public class ContactDao {

    @Resource
    private ContactMapper contactMapper;

    public String getContactId(String phone) {
        LambdaUpdateWrapper <Contact> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Contact::getPhone, phone);
        Contact contact = contactMapper.selectOne(wrapper);
        return String.valueOf(contact.getContactId());
    }
}
