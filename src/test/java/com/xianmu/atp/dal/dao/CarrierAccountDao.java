package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.CarrierAccountMapper;
import com.xianmu.atp.dal.model.CarrierAccount;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("CarrierAccountDao")
public class CarrierAccountDao {
    @Resource
    private CarrierAccountMapper carrierAccountMapper;

    public int delcetCarrierAccount(long carrierId) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<CarrierAccount> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(CarrierAccount::getCarrierId, carrierId);
        // 执行删除操作并返回删除的行数
        return carrierAccountMapper.delete(wrapper);
    }

    public int getCarrierAccountId(long carrierId) {
        LambdaUpdateWrapper<CarrierAccount> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CarrierAccount::getCarrierId, carrierId);
        CarrierAccount mapping = carrierAccountMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getId() : null;
    }
}
