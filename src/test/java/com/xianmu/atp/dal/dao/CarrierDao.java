package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.CarrierMapper;
import com.xianmu.atp.dal.model.Carrier;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("CarrierDao")
public class CarrierDao {
    @Resource
    private CarrierMapper carrierMapper;

    public Long getCarrierId(String carrierName) {
        LambdaUpdateWrapper<Carrier>wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Carrier::getCarrierName,carrierName);
        Carrier mapping = carrierMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetCarrier(String carrierName) {
        LambdaQueryWrapper<Carrier>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Carrier::getCarrierName,carrierName);
        return carrierMapper.delete(wrapper);
    }
}
