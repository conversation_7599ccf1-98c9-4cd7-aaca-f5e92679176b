package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.GoodsRecycleOrderMapper;
import com.xianmu.atp.dal.model.GoodsRecycleOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("GoodsRecycleOrderDao")
public class GoodsRecycleOrderDao {
    @Resource
    private GoodsRecycleOrderMapper goodsRecycleOrderMapper;
    public List<GoodsRecycleOrder> getGoodsRecycleOrder(String orderNo){

        LambdaQueryWrapper<GoodsRecycleOrder> goodsRecycleWrapper = new LambdaQueryWrapper<>();
        goodsRecycleWrapper
                .eq(GoodsRecycleOrder::getOrderNo, orderNo)
                .orderByDesc(GoodsRecycleOrder::getId);
        return goodsRecycleOrderMapper.selectList(goodsRecycleWrapper);
    }
}
