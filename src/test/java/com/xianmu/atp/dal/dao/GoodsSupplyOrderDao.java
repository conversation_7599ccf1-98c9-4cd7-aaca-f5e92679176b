package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.GoodsSupplyOrderMapper;
import com.xianmu.atp.dal.model.DeliveryPlan;
import com.xianmu.atp.dal.model.GoodsSupplyOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("GoodsSupplyOrderDao")
public class GoodsSupplyOrderDao {
    @Resource
    GoodsSupplyOrderMapper goodsSupplyOrderMapper;
    public List<GoodsSupplyOrder> getGoodsSupplyOrder(String order){
        LambdaQueryWrapper<GoodsSupplyOrder> GoodsSupplyWrapper = new LambdaQueryWrapper<>();
        GoodsSupplyWrapper
                .eq(GoodsSupplyOrder::getSourceOrderNo, order)
                .orderByDesc(GoodsSupplyOrder::getId);
        return goodsSupplyOrderMapper.selectList(GoodsSupplyWrapper);
    }
}
