package com.xianmu.atp.dal.dao.pms;

import com.xianmu.atp.dal.mapper.pms.InventorySupplierWarehouseStoreMapper;
import com.xianmu.atp.dal.mapper.pms.SrmStockPackupMapper;
import com.xianmu.atp.dal.model.InventorySupplierWarehouseStore;
import com.xianmu.atp.dal.model.SrmStockPackup;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【srm_stock_packup(供应商库存提报)】的数据库操作Service实现
 * @createDate 2025-03-14 18:00:19
 */
@Component()
public class SrmStockPackupService {

    @Resource
    private SrmStockPackupMapper srmStockPackupMapper;



    @Retry(attempts = 3, delay = 2000, onExceptions = {NullPointerException.class}, retryOnEmptyResult = true)
    public SrmStockPackup selectBySupplierIdAndWarehouseNoAndSkuCode(Integer supplierId, Integer warehouseNo, String skuCode) {
        List<SrmStockPackup> stockInfo = srmStockPackupMapper.selectBySupplierIdAndWarehouseNoAndSkuCode(supplierId,
                warehouseNo,skuCode);
        if (Objects.nonNull(stockInfo) && !stockInfo.isEmpty()) {
            return stockInfo.get(0);
        } else {
            throw new NullPointerException(
                    "供应商库存查询为空，supplierId: " + supplierId + ", warehouseNo: " + warehouseNo + ", skuCode: " + skuCode
            );// 或者抛出异常，根据业务需求决定
        }
    }
}
