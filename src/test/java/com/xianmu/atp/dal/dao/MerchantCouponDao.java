package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.model.MerchantCoupon;
import com.xianmu.atp.dal.mapper.MerchantCouponMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 * 优惠券操作
 * <AUTHOR>
 * @ClassName MerchantCouponDao
 * @date 2024-11-05
 */
@Component("MerchantCouponDao")
public class MerchantCouponDao {

    @Resource
    private MerchantCouponMapper merchantCouponMapper;

    /**
     * 删除优惠券
     * @param
     * @return
     */
    @Transactional
    public int deleteByCouponId(long merchanId) {
        LambdaUpdateWrapper<MerchantCoupon> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MerchantCoupon::getMId, merchanId);
        return merchantCouponMapper.delete(wrapper);
    }

    public String select(long id ){
        LambdaUpdateWrapper<MerchantCoupon> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MerchantCoupon::getId, id);
        return merchantCouponMapper.selectOne(wrapper).toString();
    }

    /**
     * 添加优惠券
     * @param merchantCoupon
     * @return
     */
    @Transactional
    public int insertCoupon(MerchantCoupon merchantCoupon) {
        return merchantCouponMapper.insert(merchantCoupon);
    }
}
