package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.AreaSkuMapper;
import com.xianmu.atp.dal.model.AreaSku;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName AreaSkuDao
 * @date 2024-11-05
 */
@Component("AreaSkuDao")
public class AreaSkuDao {

    @Resource
    AreaSkuMapper areaSkuMapper;

    public int updateAreaSku(String sku, double price){
        LambdaUpdateWrapper<AreaSku> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AreaSku::getSku,sku);
        wrapper.set(AreaSku::getPrice,price);
        return areaSkuMapper.update(wrapper);
    }
}
