package com.xianmu.atp.dal.dao.pms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.PmsSupplyListMapper;
import com.xianmu.atp.dal.model.PmsSupplyList;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("SupplyListDao")
public class SupplyListDao {
    @Resource
    private PmsSupplyListMapper pmsSupplyListMapper;
    public List<PmsSupplyList> getSupplierList(String spu){
        LambdaQueryWrapper<PmsSupplyList> SupplierListWrapper = new LambdaQueryWrapper<>();
        SupplierListWrapper.eq(PmsSupplyList::getSpu, spu);
        return pmsSupplyListMapper.selectList(SupplierListWrapper);
    }
}
