package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.DeliveryPlanMapper;
import com.xianmu.atp.dal.model.DeliveryPlan;
import com.xianmu.atp.dal.model.OfcOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("DeliveryPlanDao")
public class DeliveryPlanDao {
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    public List<DeliveryPlan> getDeliveryOrder(String order){
        LambdaQueryWrapper<DeliveryPlan> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(DeliveryPlan::getOrderNo, order);
        return deliveryPlanMapper.selectList(orderWrapper);
    }
}
