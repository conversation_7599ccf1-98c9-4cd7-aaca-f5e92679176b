package com.xianmu.atp.dal.dao;import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.DriverCarCarrierMappingMapper;
import com.xianmu.atp.dal.model.DriverCarCarrierMapping;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("DriverCarCarrierMappingDao")
public class DriverCarCarrierMappingDao {
    @Resource
    private DriverCarCarrierMappingMapper driverCarCarrierMappingMapper;

    public int delcetDriverCarCarrierMapping(Long tmsDriverId) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<DriverCarCarrierMapping> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(DriverCarCarrierMapping::getTmsDriverId, tmsDriverId);
        // 执行删除操作并返回删除的行数
        return driverCarCarrierMappingMapper.delete(wrapper);
    }
}
