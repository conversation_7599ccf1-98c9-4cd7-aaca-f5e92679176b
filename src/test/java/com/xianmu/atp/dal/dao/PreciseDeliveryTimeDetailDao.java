package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.PreciseDeliveryTimeDetailMapper;
import com.xianmu.atp.dal.model.PreciseDeliveryTimeDetail;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("PreciseDeliveryTimeDetailDao")

public class PreciseDeliveryTimeDetailDao {
    @Resource
    private PreciseDeliveryTimeDetailMapper preciseDeliveryTimeDetailMapper;

    public Long getPreciseDeliveryTimeDetailId(Long configId) {
        LambdaUpdateWrapper<PreciseDeliveryTimeDetail> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PreciseDeliveryTimeDetail::getConfigId, configId);
        PreciseDeliveryTimeDetail mapping = preciseDeliveryTimeDetailMapper.selectOne(wrapper);
        // 使用 Optional 来处理可能的 null 值
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetPreciseDeliveryTimeDetail(Long configId) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<PreciseDeliveryTimeDetail> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(PreciseDeliveryTimeDetail::getConfigId, configId);
        // 执行删除操作并返回删除的行数
        return preciseDeliveryTimeDetailMapper.delete(wrapper);
    }
}
