package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.ShoppingCartMapper;
import com.xianmu.atp.dal.model.ShoppingCart;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 * 购物车的sql操作
 * <AUTHOR>
 * @ClassName ShoppingCartDao
 * @date 2024-11-05
 */
@Component("ShoppingCartDao")
public class ShoppingCartDao {

    @Resource
    private ShoppingCartMapper shoppingCartMapper;

    /**
     * 方法<code>deleteByAccountId</code>作用为：
     * 根据账户ID删除购物车记录
     * <AUTHOR> Wu
     * [accountId]
     */
    @Transactional
    public int deleteByAccountId(long merchantId){
        LambdaUpdateWrapper<ShoppingCart> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ShoppingCart::getMId, merchantId);
        return shoppingCartMapper.delete(wrapper);
    }
}
