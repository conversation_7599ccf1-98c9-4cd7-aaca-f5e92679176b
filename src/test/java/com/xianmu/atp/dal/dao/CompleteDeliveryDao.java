package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.CompleteDeliveryMapper;
import com.xianmu.atp.dal.model.CompleteDelivery;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;


@Component("CompleteDeliveryDao")
public class CompleteDeliveryDao {
    @Resource
    private CompleteDeliveryMapper completeDeliveryMapper;

    public int deleteCompleteDelivery(Integer id) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<CompleteDelivery> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(CompleteDelivery::getId, id);
        // 执行删除操作并返回删除的行数
        return completeDeliveryMapper.delete(wrapper);
    }
}
