package com.xianmu.atp.dal.dao.pms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.SupplierMapper;
import com.xianmu.atp.dal.model.OfcOrder;
import com.xianmu.atp.dal.model.Supplier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("SupplierDao")
public class SupplierDao {
    @Resource
    private SupplierMapper supplierMapper;
    public List<Supplier> getSupplierInfo(String supplierId){
        LambdaQueryWrapper<Supplier> supplierWrapper = new LambdaQueryWrapper<>();
        supplierWrapper.eq(Supplier::getId, supplierId);
        return supplierMapper.selectList(supplierWrapper);
    }
}
