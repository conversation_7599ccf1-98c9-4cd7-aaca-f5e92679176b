package com.xianmu.atp.dal.dao;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.DeliveryNoteTemplateMapper;
import com.xianmu.atp.dal.model.DeliveryNoteTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("DeliveryNoteTemplateDao")
public class DeliveryNoteTemplateDao {

    @Resource
    private DeliveryNoteTemplateMapper deliveryNoteTemplateMapper;

    /**
     * 根据 ID 删除配送单模板。
     *
     * @param id 配送单模板 ID
     * @return 删除的行数
     */
    public int deleteDeliveryNoteTemplate(String id) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<DeliveryNoteTemplate> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(DeliveryNoteTemplate::getId, id);
        // 执行删除操作并返回删除的行数
        return deliveryNoteTemplateMapper.delete(wrapper);
    }

    public int deletebelongBusinessName(String belongBusinessName) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<DeliveryNoteTemplate> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(DeliveryNoteTemplate::getBelongBusinessName, belongBusinessName);
        // 执行删除操作并返回删除的行数
        return deliveryNoteTemplateMapper.delete(wrapper);
    }
}
