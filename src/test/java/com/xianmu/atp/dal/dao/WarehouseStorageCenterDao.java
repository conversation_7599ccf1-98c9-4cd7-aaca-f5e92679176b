package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.WarehouseStorageCenterMapper;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("WarehouseStorageCenterDao")
public class WarehouseStorageCenterDao {
    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;

    /**
     * 获取仓库名称
     * @param warehouseNo
     * @return
     */
    public String getWarehouseName(String warehouseNo){
       try {
           LambdaQueryWrapper<WarehouseStorageCenter> warehouseWrapper = new LambdaQueryWrapper<>();
           warehouseWrapper.eq(WarehouseStorageCenter::getWarehouseNo, warehouseNo);
           List<WarehouseStorageCenter> WarehouseDbs = warehouseStorageCenterMapper.selectList(warehouseWrapper);
           return WarehouseDbs.get(0).getWarehouseName();
       }catch (Exception e){
            e.printStackTrace();
            return null;
       }

    }

}
