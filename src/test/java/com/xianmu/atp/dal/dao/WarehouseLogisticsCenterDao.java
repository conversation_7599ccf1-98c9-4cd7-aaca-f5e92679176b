package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.WarehouseLogisticsCenterMapper;
import com.xianmu.atp.dal.model.GoodsSupplyOrder;
import com.xianmu.atp.dal.model.WarehouseLogisticsCenter;
import com.xianmu.atp.dal.model.WmsStockTaskNoticeOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("WarehouseLogisticsCenterDao")
public class WarehouseLogisticsCenterDao {
    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;
    public void updateCloseTime(String closeTime,Integer storeNo){
        LambdaQueryWrapper<WarehouseLogisticsCenter> logisticsCenterWrapper = new LambdaQueryWrapper<>();
        logisticsCenterWrapper
                .eq(WarehouseLogisticsCenter::getStoreNo,storeNo);
        WarehouseLogisticsCenter entityLogisticsCenter = new WarehouseLogisticsCenter();
        entityLogisticsCenter.setCloseTime(closeTime);
        warehouseLogisticsCenterMapper.update(entityLogisticsCenter, logisticsCenterWrapper);

    }

    public List<WarehouseLogisticsCenter> getCloseTime(Integer storeNo){
        LambdaQueryWrapper<WarehouseLogisticsCenter> logisticsCenterWrapper = new LambdaQueryWrapper<>();
        logisticsCenterWrapper
                .eq(WarehouseLogisticsCenter::getStoreNo,storeNo);
        return warehouseLogisticsCenterMapper.selectList(logisticsCenterWrapper);
    }

}
