package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.CompleteDeliveryAdCodeMappingMapper;
import com.xianmu.atp.dal.model.CompleteDeliveryAdCodeMapping;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("CompleteDeliveryAdCodeMappingDao")
public class CompleteDeliveryAdCodeMappingDao {

    @Resource
    private CompleteDeliveryAdCodeMappingMapper completeDeliveryAdCodeMappingMapper;

    public Integer getCompleteDeliveryId(String adCode ){
        LambdaUpdateWrapper<CompleteDeliveryAdCodeMapping> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CompleteDeliveryAdCodeMapping::getAdCode, adCode);
        CompleteDeliveryAdCodeMapping mapping = completeDeliveryAdCodeMappingMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getCompleteDeliveryId() : null;
    }

    public int deleteCompleteDeliveryAdCodeMapping(Integer completeDeliveryId) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<CompleteDeliveryAdCodeMapping> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(CompleteDeliveryAdCodeMapping::getCompleteDeliveryId, completeDeliveryId);
        // 执行删除操作并返回删除的行数
        return completeDeliveryAdCodeMappingMapper.delete(wrapper);
    }




}
