package com.xianmu.atp.dal.dao.pms;

import com.xianmu.atp.dal.mapper.pms.SrmSupplierOfferDetailAuditRecordMapper;
import com.xianmu.atp.dal.model.SrmSupplierOfferDetailAuditRecord;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.testng.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【srm_supplier_offer_detail_audit_record(报价审核记录表)】的数据库操作Service实现
 * @createDate 2025-03-20 11:38:01
 */
@Component
public class SrmSupplierOfferDetailAuditRecordService{

    @Resource
    private SrmSupplierOfferDetailAuditRecordMapper srmSupplierOfferDetailAuditRecordMapper;

    //查询供货价申请记录表
    @Retry(attempts = 3, delay = 2000, onExceptions = {AssertionError.class})
    public SrmSupplierOfferDetailAuditRecord selectBySupplierIdAndWarehouseNoAndSkuAndCreateTime(
            Integer supplierId, Integer warehouseNo, String sku, String createTime) {
        List<SrmSupplierOfferDetailAuditRecord> res = srmSupplierOfferDetailAuditRecordMapper.selectBySupplierIdAndWarehouseNoAndSkuAndCreateTime(
                Long.valueOf(supplierId),
                Long.valueOf(warehouseNo),
                sku,
                createTime);
        Assert.assertFalse(res.isEmpty(), "查询供货价申请记录表失败");
        return  res.get(0);
    }

    public SrmSupplierOfferDetailAuditRecord selectBySupplierIdAndWarehouseNoAndSkuAndStatus(
            Integer supplierId, Integer warehouseNo, String sku, Integer result) {
        List<SrmSupplierOfferDetailAuditRecord> res = srmSupplierOfferDetailAuditRecordMapper.selectBySupplierIdAndWarehouseNoAndSkuAndResult(
                Long.valueOf(supplierId),
                Long.valueOf(warehouseNo),
                sku,
                result);
        if (res.isEmpty()){
            return null;
        }else {
            return   res.get(0);
        }
    }


    @Retry(attempts = 3, delay = 2000, onExceptions = {AssertionError.class})
    public SrmSupplierOfferDetailAuditRecord selectByRecordId(Integer recordId) {
        List<SrmSupplierOfferDetailAuditRecord> res = srmSupplierOfferDetailAuditRecordMapper.selectById(
                Long.valueOf(recordId));
        Assert.assertFalse(res.isEmpty(), "查询供货价申请记录表失败");
        return  res.get(0);
    }
}
