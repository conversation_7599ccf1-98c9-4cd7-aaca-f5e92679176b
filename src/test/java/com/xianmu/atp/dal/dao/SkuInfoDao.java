package com.xianmu.atp.dal.dao;

import com.xianmu.atp.dal.mapper.SkuInfoMapper;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.util.retry.Retry;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.List;

@Component("SkuInfoDao")
public class SkuInfoDao {
    @Resource
    private SkuInfoMapper skuInfoMapper;
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public SkuInfo getProduct(String sku) {
        try {
            SkuInfo skuInfo = skuInfoMapper.getSkuInfoBySku(sku);
            Assert.assertNotNull(skuInfo,"商品基础数据查询为空");
            return skuInfo;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
