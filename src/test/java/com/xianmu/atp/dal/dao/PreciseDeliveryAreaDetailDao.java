package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.PreciseDeliveryAreaDetailMapper;
import com.xianmu.atp.dal.model.PreciseDeliveryAreaDetail;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("PreciseDeliveryAreaDetailDao")
public class PreciseDeliveryAreaDetailDao {
    @Resource
    private PreciseDeliveryAreaDetailMapper preciseDeliveryAreaDetailMapper;

    public Long getPreciseDeliveryAreaDetailId(String adCode) {
        LambdaUpdateWrapper<PreciseDeliveryAreaDetail> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PreciseDeliveryAreaDetail::getAdCode, adCode);
        PreciseDeliveryAreaDetail mapping = preciseDeliveryAreaDetailMapper.selectOne(wrapper);
        // 使用 Optional 来处理可能的 null 值
        return (mapping != null) ? mapping.getId() : null;
    }

    public Long getPreciseDeliveryAreaDetailConfigId(String adCode) {
        LambdaUpdateWrapper<PreciseDeliveryAreaDetail> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PreciseDeliveryAreaDetail::getAdCode, adCode);
        PreciseDeliveryAreaDetail mapping = preciseDeliveryAreaDetailMapper.selectOne(wrapper);
        // 使用 Optional 来处理可能的 null 值
        return (mapping != null) ? mapping.getConfigId() : null;
    }


    public int delcetPreciseDeliveryAreaDetail(String adCode) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<PreciseDeliveryAreaDetail> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(PreciseDeliveryAreaDetail::getAdCode, adCode);
        // 执行删除操作并返回删除的行数
        return preciseDeliveryAreaDetailMapper.delete(wrapper);
    }
}
