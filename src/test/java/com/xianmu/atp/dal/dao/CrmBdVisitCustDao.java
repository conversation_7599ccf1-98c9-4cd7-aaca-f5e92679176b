package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.CrmBdVisitCustMapper;
import com.xianmu.atp.dal.model.CrmBdVisitCust;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName CrmBdVisitCustDao
 * @date 2025-03-12
 */
@Component
public class CrmBdVisitCustDao {

    @Resource
    private CrmBdVisitCustMapper crmBdVisitCustMapper;

    public int updateByType(){
        LambdaUpdateWrapper<CrmBdVisitCust> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CrmBdVisitCust::getVisitAfterIsBuy, "是");
        lambdaUpdateWrapper.set(CrmBdVisitCust::getDateTag, localDateTimeToStringTwo(LocalDate.now().minusDays(0)));
        return crmBdVisitCustMapper.update(lambdaUpdateWrapper);
    }

    public static String localDateTimeToStringTwo(LocalDate localDate) {
        if (localDate == null) {
            return "";
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        return df.format(localDate);
    }
}
