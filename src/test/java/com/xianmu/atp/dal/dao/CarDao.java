package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.CarMapper;
import com.xianmu.atp.dal.model.Car;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("CarDao")
public class CarDao {

    @Resource
    private CarMapper carMapper;

    public Long getCarId(String carNumber) {
        LambdaUpdateWrapper<Car> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Car::getCarNumber, carNumber);
        Car mapping = carMapper.selectOne(wrapper);
        // 使用 Optional 来处理可能的 null 值
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetCar(String carNumber) {
        // 使用 LambdaQueryWrapper 构建删除条件
        LambdaQueryWrapper<Car> wrapper = new LambdaQueryWrapper<>();
        // 设置删除条件
        wrapper.eq(Car::getCarNumber, carNumber);
        // 执行删除操作并返回删除的行数
        return carMapper.delete(wrapper);
    }


}
