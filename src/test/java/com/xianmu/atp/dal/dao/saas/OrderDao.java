package com.xianmu.atp.dal.dao.saas;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.dal.mapper.saas.OrderMapper;
import com.xianmu.atp.dal.model.OfcOrder;
import com.xianmu.atp.dal.model.saas.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


@Component
public class OrderDao {


    @Resource
    private OrderMapper orderMapper;

    public String getOrderNo(String orderNos){
        // ["ORxxx"] json字符串转数组
        List<String> orderNoList = JSONArray.parseArray(orderNos, String.class);
        LambdaQueryWrapper<Order> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(Order::getOrderNo, orderNoList.get(0));
        List<Order> orders = orderMapper.selectList(orderWrapper);
        if(CollectionUtils.isEmpty(orders)){
            return null;
        }
        return orders.get(0).getOrderNo();
    }

}
