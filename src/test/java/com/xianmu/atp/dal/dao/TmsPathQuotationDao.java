package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.TmsPathQuotationMapper;
import com.xianmu.atp.dal.model.TmsPathQuotation;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("TmsPathQuotationDao")
public class TmsPathQuotationDao {
    @Resource
    private TmsPathQuotationMapper tmsPathQuotationMapper;

    public Long getTmsPathQuotationId(Long pathId) {
        LambdaUpdateWrapper<TmsPathQuotation>wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsPathQuotation::getPathId,pathId);
        TmsPathQuotation mapping = tmsPathQuotationMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetTmsPathQuotation(Long pathId) {
        LambdaQueryWrapper<TmsPathQuotation>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPathQuotation::getPathId,pathId);
        return tmsPathQuotationMapper.delete(wrapper);
    }
}
