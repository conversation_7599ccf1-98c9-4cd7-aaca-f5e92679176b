package com.xianmu.atp.dal.dao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.TmsPathSectionMapper;
import com.xianmu.atp.dal.model.TmsPathSection;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component("TmsPathSectionDao")
public class TmsPathSectionDao {
    @Resource
    private TmsPathSectionMapper tmsPathSectionMapper;

    public Long getTmsPathSectionId(Long pathId) {
        LambdaUpdateWrapper<TmsPathSection>wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsPathSection::getPathId,pathId);
        TmsPathSection mapping = tmsPathSectionMapper.selectOne(wrapper);
        return (mapping != null) ? mapping.getId() : null;
    }

    public int delcetTmsPathSection(Long pathId) {
        LambdaQueryWrapper<TmsPathSection>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPathSection::getPathId,pathId);
        return tmsPathSectionMapper.delete(wrapper);
    }
}
