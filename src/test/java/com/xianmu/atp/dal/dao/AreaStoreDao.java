package com.xianmu.atp.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xianmu.atp.dal.mapper.AreaStoreMapper;
import com.xianmu.atp.dal.model.AreaStore;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 类<code>Doc</code>用于：TODO
 * 区域库存相关操作
 * <AUTHOR>
 * @ClassName AreaStoreDao
 * @date 2024-11-05
 */

@Component("AreaStoreDao")
public class AreaStoreDao {

    @Resource
    private AreaStoreMapper areaStoreMapper;

    @Transactional
    public int updateOnlineQuantity(String sku){
        LambdaUpdateWrapper<AreaStore> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AreaStore::getSku,sku);
        wrapper.eq(AreaStore::getAreaNo,20);
        wrapper.set(AreaStore::getOnlineQuantity,10000);
        return areaStoreMapper.update(wrapper);
    }
    public void updateOnlineQuantity(String sku,Integer AreaNo) {
        LambdaQueryWrapper<AreaStore> updateAreaStore = new LambdaQueryWrapper<>();
        updateAreaStore.eq(AreaStore::getAreaNo, AreaNo).eq(AreaStore::getSku, sku);
        List<AreaStore> storesNumDb = areaStoreMapper.selectList(updateAreaStore);
        AreaStore entityAreaStore = new AreaStore();
        entityAreaStore.setOnlineQuantity(999);
        entityAreaStore.setChange(0);
        areaStoreMapper.update(entityAreaStore, updateAreaStore);

    }
    @Transactional
    public int setOnlineQuantity(String sku,int storeNo){
        LambdaUpdateWrapper<AreaStore> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AreaStore::getSku,sku);
        wrapper.eq(AreaStore::getAreaNo,storeNo);
        wrapper.set(AreaStore::getOnlineQuantity,10000);
        return areaStoreMapper.update(wrapper);
    }
}
