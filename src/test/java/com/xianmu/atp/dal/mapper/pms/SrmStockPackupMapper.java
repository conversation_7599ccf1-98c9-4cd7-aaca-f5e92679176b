package com.xianmu.atp.dal.mapper.pms;
import com.xianmu.atp.annotation.Remark;
import org.apache.ibatis.annotations.Param;

import com.xianmu.atp.dal.model.InventorySupplierWarehouseStore;
import com.xianmu.atp.dal.model.SrmStockPackup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_stock_packup(供应商库存提报)】的数据库操作Mapper
* @createDate 2025-03-14 18:55:15
* @Entity com.xianmu.atp.dal.model.SrmStockPackup
*/
public interface SrmStockPackupMapper extends BaseMapper<SrmStockPackup> {

    @Remark("查询供应商提报价格")
    List<SrmStockPackup> selectBySupplierIdAndWarehouseNoAndSkuCode(@Param("supplierId") Integer supplierId, @Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku);


}




