package com.xianmu.atp.dal.mapper;
import com.xianmu.atp.annotation.Remark;
import org.apache.ibatis.annotations.Param;

import com.xianmu.atp.dal.model.DingdingProcessFlow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【dingding_process_flow(钉钉审批表)】的数据库操作Mapper
* @createDate 2025-01-13 17:06:13
* @Entity com.xianmu.atp.dal.model.DingdingProcessFlow
*/
public interface DingdingProcessFlowMapper extends BaseMapper<DingdingProcessFlow> {

    @Remark("根据业务id和业务类型查询飞书审批流记录")
    DingdingProcessFlow selectOneByBizIdAndBizType(@Param("bizId") Long bizId, @Param("bizType") Integer bizType);
    @Remark("查询飞书审批流指定ID,类型,状态的记录")
    DingdingProcessFlow selectOneByBizIdAndBizTypeAndProcessStatus(@Param("bizId") Long bizId, @Param("bizType") Integer bizType, @Param("processStatus") Integer processStatus);

}




