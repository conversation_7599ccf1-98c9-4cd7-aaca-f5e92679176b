package com.xianmu.atp.dal.mapper.pms;
import com.xianmu.atp.annotation.Remark;
import org.apache.ibatis.annotations.Param;

import com.xianmu.atp.dal.model.SrmStockChangeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【srm_stock_change_record(供应商库存变更记录)】的数据库操作Mapper
* @createDate 2025-04-01 17:23:42
* @Entity com.xianmu.atp.dal.model.SrmStockChangeRecord
*/
public interface SrmStockChangeRecordMapper extends BaseMapper<SrmStockChangeRecord> {

    @Remark("根据id删除库存变更记录")
    int deleteById(@Param("id") Integer id);

}




