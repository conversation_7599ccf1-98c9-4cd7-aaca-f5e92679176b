package com.xianmu.atp.dal.mapper.pms;
import java.util.List;

import com.xianmu.atp.annotation.Remark;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDateTime;

import com.xianmu.atp.dal.model.SrmSupplierOfferDetailAuditRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【srm_supplier_offer_detail_audit_record(报价审核记录表)】的数据库操作Mapper
* @createDate 2025-03-20 11:38:01
* @Entity com.xianmu.atp.dal.model.SrmSupplierOfferDetailAuditRecord
*/
public interface SrmSupplierOfferDetailAuditRecordMapper extends BaseMapper<SrmSupplierOfferDetailAuditRecord> {

    @Remark("查询供货价申请记录表")
    List<SrmSupplierOfferDetailAuditRecord> selectBySupplierIdAndWarehouseNoAndSkuAndCreateTime(@Param("supplierId") Long supplierId, @Param("warehouseNo") Long warehouseNo, @Param("sku") String sku, @Param("createTime") String createTime);
    @Remark("根据ID查询供货价申请记录表")
    List<SrmSupplierOfferDetailAuditRecord> selectById(@Param("id") Long id);

    @Remark("根据状态查询供货价申请记录表")
    List<SrmSupplierOfferDetailAuditRecord> selectBySupplierIdAndWarehouseNoAndSkuAndResult(
            @Param("supplierId") Long supplierId, @Param("warehouseNo") Long warehouseNo, @Param("sku") String sku, @Param("result") Integer result);
}




