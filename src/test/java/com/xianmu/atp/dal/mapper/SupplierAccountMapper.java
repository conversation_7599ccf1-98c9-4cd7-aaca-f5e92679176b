package com.xianmu.atp.dal.mapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.xianmu.atp.dal.model.SupplierAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【supplier_account(供应商账户表)】的数据库操作Mapper
* @createDate 2024-12-31 11:06:51
* @Entity com.xianmu.atp.dal.model.SupplierAccount
*/
public interface SupplierAccountMapper extends BaseMapper<SupplierAccount> {

    List<SupplierAccount> selectBySupplierId(@Param("supplierId") Integer supplierId);
}




