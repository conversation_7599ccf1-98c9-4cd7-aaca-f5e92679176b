package com.xianmu.atp.dal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xianmu.atp.dal.model.CompleteDeliveryAdCodeMapping;
import com.xianmu.atp.dal.model.Contact;
import com.xianmu.atp.dal.model.MerchantCoupon;

public interface CompleteDeliveryAdCodeMappingMapper extends BaseMapper<CompleteDeliveryAdCodeMapping> {


}
