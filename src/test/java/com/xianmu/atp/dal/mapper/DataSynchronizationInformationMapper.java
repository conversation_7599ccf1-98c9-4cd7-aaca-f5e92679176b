package com.xianmu.atp.dal.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xianmu.atp.dal.model.DataSynchronizationInformation;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @date 2025-03-12
 */
@DS("offline")
public interface DataSynchronizationInformationMapper extends BaseMapper<DataSynchronizationInformation> {
}
