package com.xianmu.atp.dal.mapper;

import com.xianmu.atp.annotation.Remark;
import com.xianmu.atp.dal.model.FinanceReceipt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【finance_receipt(收款单表)】的数据库操作Mapper
* @createDate 2025-03-11 14:54:22
* @Entity com.xianmu.atp.dal.model.FinanceReceipt
*/
public interface FinanceReceiptMapper extends BaseMapper<FinanceReceipt> {

    @Remark("根据业务单号查询供应商收款核销单")
    List<FinanceReceipt> selectAllByFinanceOrderIdWithJoin(@Param("financeOrderId") Integer financeOrderId, @Param("sourceNo") String sourceNo);
}




