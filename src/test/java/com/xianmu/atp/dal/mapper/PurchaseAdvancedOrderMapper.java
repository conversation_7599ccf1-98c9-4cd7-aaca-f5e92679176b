package com.xianmu.atp.dal.mapper;

import com.xianmu.atp.dal.model.PurchaseAdvancedOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
* <AUTHOR>
* @description 针对表【purchase_advanced_order(预付单表)】的数据库操作Mapper
* @createDate 2025-01-13 16:34:55
* @Entity com.xianmu.atp.dal.model.PurchaseAdvancedOrder
*/
public interface PurchaseAdvancedOrderMapper extends BaseMapper<PurchaseAdvancedOrder> {
    @Override
    PurchaseAdvancedOrder selectById(@Param("id") Serializable id);
}




