package com.xianmu.atp.dal.mapper.pms;

import com.xianmu.atp.annotation.Remark;
import com.xianmu.atp.dal.model.SrmSupplierOfferDetailStepPrice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【srm_supplier_offer_detail_step_price(报价详情阶梯价表)】的数据库操作Mapper
* @createDate 2025-04-23 16:18:43
* @Entity com.xianmu.atp.dal.model.SrmSupplierOfferDetailStepPrice
*/
public interface SrmSupplierOfferDetailStepPriceMapper extends BaseMapper<SrmSupplierOfferDetailStepPrice> {

    @Remark("查询供应商报价详情")
    SrmSupplierOfferDetailStepPrice selectStepDetailPrice(@Param("supplierId") Integer supplierId, @Param("warehouseNo") Integer warehouseNo, @Param("skuCode") String skuCode, @Param("status") Integer status);

}




