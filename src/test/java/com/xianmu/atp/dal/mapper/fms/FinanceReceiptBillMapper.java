package com.xianmu.atp.dal.mapper.fms;
import java.util.List;

import com.xianmu.atp.annotation.Remark;
import org.apache.ibatis.annotations.Param;

import com.xianmu.atp.dal.model.FinanceReceiptBill;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【finance_receipt_bill(收款单-账单表)】的数据库操作Mapper
* @createDate 2025-03-10 18:12:12
* @Entity com.xianmu.atp.dal.model.FinanceReceiptBill
*/
public interface FinanceReceiptBillMapper extends BaseMapper<FinanceReceiptBill> {

    @Remark("根据业务单号查询供应商收款单")
    List<FinanceReceiptBill> selectAllByFinanceOrderIdAndSourceNo(@Param("financeOrderId") Integer financeOrderId, @Param("sourceNo") String sourceNo);

}




