package com.xianmu.atp.dal.mapper.pms;
import java.util.List;

import com.xianmu.atp.annotation.Remark;
import org.apache.ibatis.annotations.Param;

import com.xianmu.atp.dal.model.InventorySupplierWarehouseStore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【inventory_supplier_warehouse_store(供应商仓库库存)】的数据库操作Mapper
* @createDate 2025-03-14 18:00:19
* @Entity com.xianmu.atp.dal.model.InventorySupplierWarehouseStore
*/
public interface InventorySupplierWarehouseStoreMapper extends BaseMapper<InventorySupplierWarehouseStore> {
    @Remark("查询供应商库存")
    List<InventorySupplierWarehouseStore> selectBySupplierIdAndWarehouseNoAndSkuCode(@Param("supplierId") Integer supplierId, @Param("warehouseNo") Integer warehouseNo, @Param("skuCode") String skuCode);
}




