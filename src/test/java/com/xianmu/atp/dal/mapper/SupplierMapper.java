package com.xianmu.atp.dal.mapper;
import java.util.List;

import com.xianmu.atp.annotation.Remark;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.xianmu.atp.dal.model.Supplier;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【supplier(供应商)】的数据库操作Mapper
* @createDate 2024-12-30 15:54:30
* @Entity com.xianmu.atp.dal.model.Supplier
*/

public interface SupplierMapper extends BaseMapper<Supplier> {

    @Remark("根据ID查询供应商信息")
    Supplier selectById(@Param("id") Integer id );

    List<Supplier> selectByStatus(@Param("status") Integer status);

    int updateStatusById(@Param("status") Integer status, @Param("id") Integer id);
}




