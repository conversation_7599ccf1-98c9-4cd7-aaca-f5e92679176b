package com.xianmu.atp.DTO;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR> Wu
 * @version 1.0
 * @date 2025-03-18
 */

@Data
public class SalesDataQuery extends BasePageInput {
    /**
     * 区域
     */
    private List<Integer> areaNo;
    /**
     * 销售团队类型:1:平台销售，2:大客户销售
     */
    private Integer type;
    /**
     * 销售姓名
     */
    private String bdName;
    /**
     * 查询时间 格式:yyyyMM/yyyyMMdd
     */
    private Integer queryTime;

    /**
     * 大客户团队:销售ids
     */
    private List<Integer> vipTeamAdminIds;

    /**
     * 私海0,公海1
     */
    private Integer reassign;
    /**
     * 是否是本月标记
     */
    private Boolean thisMonth;
    /**
     * 销售id
     */
    private Integer adminId;
    /**
     * 销售ids
     */
    private List<Integer> adminIds;
    /**
     * 销售orgid
     */
    private List<Integer> bdOrgId;
    /**
     * 不计算月活的大客户id：目前仅有书亦及茶百道
     */
    private List<Integer> bigMerchantAdminIds;
    /**
     * 行政城市集合
     */
    private List<String> administrativeCityList;

    /**
     * 销售区域id
     */
    private Integer salesAreaId;
    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private List<String> district;
}