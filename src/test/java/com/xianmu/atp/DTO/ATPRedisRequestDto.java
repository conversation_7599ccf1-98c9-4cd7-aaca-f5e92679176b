package com.xianmu.atp.DTO;

import lombok.Builder;
import lombok.Data;

import java.util.concurrent.TimeUnit;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName ATPRedisRequestDto
 * @date 2025-03-14
 */
@Data
@Builder
public class ATPRedisRequestDto {
    private String operation;
    private Object queryContent;
    private String queryKey;
    private long timeout;
    private TimeUnit timeUnit;

    @Builder.Default
    private String dbIndex = "0";
}
