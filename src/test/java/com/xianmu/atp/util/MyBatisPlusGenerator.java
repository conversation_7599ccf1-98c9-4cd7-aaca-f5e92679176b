package com.xianmu.atp.util;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.querys.MySqlQuery;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;

import java.util.Arrays;
import java.util.Collections;

public class MyBatisPlusGenerator {

    public static void main(String[] args) {
        String projectPath = System.getProperty("user.dir");
        // 数据源配置
        DataSourceConfig.Builder dataSourceConfig = new DataSourceConfig
                .Builder(
                //"**************************************************************************************",
                "******************************************************************************************",
                "test",
                "xianmu619")
                .dbQuery(new MySqlQuery())
                .typeConvert(new MySqlTypeConvert())
                .keyWordsHandler(new MySqlKeyWordsHandler());

        FastAutoGenerator.create(dataSourceConfig)
                .globalConfig(builder -> {
                    builder.author("cmq") //设置作者
                            .commentDate("YYYY-MM-DD HH:mm:ss")//注释日期
                            .disableOpenDir()  //禁止打开目录
                            .outputDir(projectPath + "/src/test/java"); //指定输出目录

                })
                .packageConfig(builder -> {
                    builder.parent("com.xianmu.atp.dal"); // 设置父包名
                })
                .strategyConfig(builder -> {
                    builder.addInclude(Arrays.asList("pms_supply_list")).addTablePrefix("");
                   /* builder.addInclude("area_sku,delivery_plan") // 设置需要生成的表名
                            .addTablePrefix(""); // 设置过滤表前缀*/
                    builder.entityBuilder().enableLombok();//开启 lombok 模型
                    builder.entityBuilder().enableTableFieldAnnotation();//开启生成实体时生成字段注解
                    builder.controllerBuilder().enableRestStyle();//开启生成@RestController 控制器

                }).templateConfig(builder -> {
                    builder.controller("");
                    builder.serviceImpl("");
                    builder.service("");
                }).packageConfig(builder -> {
                    builder.entity("model");
                    builder.mapper("mapper");
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }

}