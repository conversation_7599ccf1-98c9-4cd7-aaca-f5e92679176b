package com.xianmu.atp.util.dubbo;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 类<code>Doc</code>用于：
 * dubbo泛化调用配置
 * <AUTHOR>
 * @ClassName DefaultDubboConfig
 * @date 2025-02-05
 */

@Component
public class DefaultDubboConfig {

    private static final String INVOKE_URL = "https://qaquantity.summerfarm.net/quantity-service/dubboInvoke/referenceInvoke";

    public ATPDto getDubboResult(ATPRequestDto atpRequestDto){
        String requestBody = JSON.toJSONString(atpRequestDto);
        HttpResponse response = HttpRequest.post(INVOKE_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestBody)
                .execute();

        return ATPDto.builder()
                .dubboResponse(JSONObject.parseObject(response.body()).getJSONObject("dubboResponse"))
                .message(JSONObject.parseObject(response.body()).getString("message"))
                .build();
    }

}
