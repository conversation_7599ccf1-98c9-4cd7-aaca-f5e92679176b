package com.xianmu.atp.util.testng;

import lombok.extern.slf4j.Slf4j;
import org.testng.IInvokedMethod;
import org.testng.IInvokedMethodListener;
import org.testng.ITestContext;
import org.testng.ITestResult;

import java.lang.reflect.Field;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName MethodVariableListener
 * @date 2025-03-28
 */
@Slf4j
public class MethodVariableListener implements IInvokedMethodListener {
    @Override
    public void afterInvocation(IInvokedMethod method, ITestResult testResult) {
        IInvokedMethodListener.super.afterInvocation(method, testResult);
        Object[] par = testResult.getParameters();

        Field[] fields = testResult.getInstance().getClass().getDeclaredFields();

        for (Field field : fields)
        {
            // 对于每个属性，获取属性名
            String varName = field.getName();
            try
            {
                boolean access = field.isAccessible();
                if(!access) field.setAccessible(true);

                //从obj中获取field变量
                Object o = field.get(testResult.getInstance());
                System.out.println("变量： " + varName + " = " + o);

                if(!access) field.setAccessible(false);
            }
            catch (Exception ex)
            {
                ex.printStackTrace();
            }
        }

        System.out.println( "afterInvocation");
    }

    @Override
    public void beforeInvocation(IInvokedMethod method, ITestResult testResult)
    {
        Object[] par = testResult.getParameters();
        System.out.println("beforeInvocation");
        Object testInstance = testResult.getInstance();
    }

}
