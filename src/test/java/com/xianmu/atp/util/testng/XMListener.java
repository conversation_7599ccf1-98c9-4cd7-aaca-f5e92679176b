package com.xianmu.atp.util.testng;

import cn.hutool.core.lang.Console;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.xianmu.atp.annotation.DeployConfig;
import lombok.extern.slf4j.Slf4j;
import org.testng.ITestContext;
import org.testng.ITestListener;
import org.testng.ITestResult;

/**
 * 类<code>Doc</code>用于：TODO
 * 定制化listener 用于测试各个阶段的自定义处理
 * <AUTHOR>
 * @version 1.0
 * @ClassName XMListener
 * @date 2024-11-01
 */
@Slf4j
public class XMListener implements ITestListener {

    private long startTime;


    @Override
    public void onTestStart(org.testng.ITestResult result) {

        log.info("test will start:{}", result);
        log.info(String.valueOf(result.getInstance().getClass().getAnnotation(DeployConfig.class)));
        Console.log(result.getTestClass());
//        try {
//            Field field = mqProducer.getClass().getDeclaredField("producer");//TODO:通过反射修改producer的权限获取defaultProducer
//            field.setAccessible(true);
//            DefaultMQProducer defaultMQProducer = (DefaultMQProducer) field.get(mqProducer);
//            defaultMQProducer.shutdown();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
    }

    @Override
    public void onTestSuccess(org.testng.ITestResult result) {
        Console.log("onTestSuccess:{}", result);
    }

    @Override
    public void onTestFailure(org.testng.ITestResult result) {
        Console.log("onTestFailure:{}", result);
    }

    @Override
    public void onTestFailedWithTimeout(ITestResult result) {
        ITestListener.super.onTestFailedWithTimeout(result);
    }

    @Override
    public void onStart(ITestContext context) {
        log.info("测试_________________________________________开始");
        log.info("tt:{}",context);
        ITestListener.super.onStart(context);
        startTime = System.currentTimeMillis();
    }

    @Override
    public void onFinish(ITestContext context) {
        ITestListener.super.onFinish(context);
        log.info("测试_________________________________________完成");
    }
}
