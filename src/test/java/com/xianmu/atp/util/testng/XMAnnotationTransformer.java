package com.xianmu.atp.util.testng;


import cn.hutool.core.lang.Console;
import com.xianmu.atp.annotation.DeployConfig;
import org.testng.IAnnotationTransformer;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.ITestAnnotation;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName XMAnnotationTransformer
 * @date 2024-11-18
 */
public class XMAnnotationTransformer implements IAnnotationTransformer {

    /**
     * 方法<code>transform</code>作用为：
     * 获取重试次数与间隔
     * <AUTHOR> Wu
     */
    @Override
    public void transform(ITestAnnotation annotation, Class testClass, Constructor testConstructor, Method testMethod) {
        if (annotation.getRetryAnalyzerClass().getName().equals("com.xianmu.atp.util.testng.XMRetryAnalyzer")){
            CustomAttribute[] customAttributes = annotation.getAttributes();
            int loop = 0;
            int interval = 0;
            for (CustomAttribute customAttribute : customAttributes) {
                if (customAttribute.name().equals("loop")){
                    loop = Integer.parseInt(customAttribute.values()[0]);
                }
                if (customAttribute.name().equals("interval")){
                    interval = Integer.parseInt(customAttribute.values()[0]);
                }
            }
            XMRetryAnalyzer.setInterval(interval);
            XMRetryAnalyzer.setMaxRetryCount(loop);
            annotation.setRetryAnalyzer(XMRetryAnalyzer.class);
        }
    }
}
