package com.xianmu.atp.util.testng;

import lombok.extern.slf4j.Slf4j;
import org.testng.ISuite;
import org.testng.ISuiteListener;

import java.util.HashMap;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName XMSuiteListener
 * @date 2024-11-20
 */
@Slf4j
public class XMSuiteListener implements ISuiteListener {

    private static final Map<String, String> map = new HashMap<>();

    static {
        map.put("1", "summerfarm-wnc");
        map.put("2", "common-service");
        map.put("3", "item-center");
        map.put("4", "xianmu-authentication");
        map.put("5", "goods-center");
        map.put("6", "summerfarm-ofc");
        map.put("7", "xianmu-usercenter");
        map.put("8", "summerfarm-manage");
        map.put("9", "summerfarm-wms");
        map.put("10", "tms");
        map.put("11", "mall");
        map.put("12", "crm");
        map.put("13", "cosfo-mall");
        map.put("14", "cosfo-manage");
        map.put("15", "cosfo-oms");
        map.put("16", "cosfo-erp");
        map.put("17", "message-center");
        map.put("18", "summerfarm-wnc");
        map.put("19", "pms-service");
        map.put("20", "bms-service");
        map.put("21", "order-center");
        map.put("22", "scp-service");
        map.put("23", "marketing-center");
        map.put("24", "sf-mall-engine");
        map.put("25", "open-platform");
        map.put("26", "sf-mall-manage");
        map.put("27", "sumerfarm-inventory-center");
    }

    @Override
    public void onStart(ISuite suite) {
        ISuiteListener.super.onStart(suite);
        log.info("suit的值:{}",suite.getParameter("dependence"));
        //执行心跳检测，检查服务是否正常，重试完成
        String[] dependence = suite.getParameter("dependence").split(",");
        for (String s : dependence) {
            log.info("依赖应用的值:{}",s);
            if ( HeartbeatHandler.checkHeartbeat("ID"+s)){
                continue;
            }else {
                throw new RuntimeException("依赖应用启动失败，服务："+map.get(s));
            }
        }
    }

    @Override
    public void onFinish(ISuite suite) {
        ISuiteListener.super.onFinish(suite);
    }
}
