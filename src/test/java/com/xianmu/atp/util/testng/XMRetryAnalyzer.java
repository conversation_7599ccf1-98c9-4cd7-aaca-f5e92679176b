package com.xianmu.atp.util.testng;


import cn.hutool.core.lang.Console;
import cn.hutool.log.Log;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.testng.IRetryAnalyzer;
import org.testng.ITestResult;
import org.testng.Reporter;

/**
 * 类<code>Doc</code>用于：TODO
 * 自定义重试机制
 * <AUTHOR>
 * @ClassName XMRetryAnalyzer
 * @date 2024-11-12
 */

@Slf4j
public class XMRetryAnalyzer implements IRetryAnalyzer {


    private int retryCount = 0;

    @Setter
    public static int maxRetryCount = 2;

    @Setter
    public static int interval = 1;
    @Override
    public boolean retry(ITestResult iTestResult) {
        log.info("需要重试的次数:{}", maxRetryCount);
        // 记录重试次数
        Console.log("已重试次数：" + retryCount);
        // 重试次数小于最大重试次数时，重试
        if (retryCount < maxRetryCount) {
            retryCount++;
            Console.log("状态：{}",iTestResult.getStatus());
            try {
                Thread.sleep(1000L * interval); // 等待5秒
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return true;
        }
        return false;
    }
}
