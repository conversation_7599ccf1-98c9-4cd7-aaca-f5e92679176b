package com.xianmu.atp.util.testng;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.xianmu.atp.dal.dao.EnvCheckDao;
import com.xianmu.atp.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 * 依赖应用心跳检测
 * <AUTHOR>
 * @ClassName HeartbeatHandler
 * @date 2024-11-19
 */
@Slf4j
@Component
public class HeartbeatHandler {

    private static final Map<String, String> map = new java.util.HashMap<>();
    static {
        map.put("ID1", Constants.ID1);
        map.put("ID2", Constants.ID2);
        map.put("ID3", Constants.ID3);
        map.put("ID4", Constants.ID4);
        map.put("ID5", Constants.ID5);
        map.put("ID6", Constants.ID6);
        map.put("ID7", Constants.ID7);
        map.put("ID8", Constants.ID8);
        map.put("ID9", Constants.ID9);
        map.put("ID10", Constants.ID10);
        map.put("ID11", Constants.ID11);
        map.put("ID12", Constants.ID12);
        map.put("ID13", Constants.ID13);
        map.put("ID14", Constants.ID14);
        map.put("ID15", Constants.ID15);
        map.put("ID16", Constants.ID16);
        map.put("ID17", Constants.ID17);
        map.put("ID18", Constants.ID18);
        map.put("ID19", Constants.ID19);
        map.put("ID20", Constants.ID20);
        map.put("ID21", Constants.ID21);
        map.put("ID22", Constants.ID22);
        map.put("ID23", Constants.ID23);
        map.put("ID24", Constants.ID24);
        map.put("ID25", Constants.ID25);
        map.put("ID26", Constants.ID26);
        map.put("ID27", Constants.ID27);
    }

    public static boolean checkHeartbeat(String num){
        log.info("调用 checkHeartbeat 方法，url：{}", map.get(num));
        if (!map.containsKey(num)){
           return false;
        }
        HttpResponse response = HttpRequest.get(map.get(num))
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        log.info("心跳报文结果：{}", response);
        if (!response.body().contains("success")){
            log.info("心跳检测失败，服务{}", num);
            return false;
        }else {
            log.info("心跳检测成功，服务{}", num);
        }
        return true;
    }
}
