package com.xianmu.atp.util.retry;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.testng.IRetryAnalyzer;

import java.util.Arrays;
import java.util.Collection;
import java.util.Map;

@Aspect
@Component
@Slf4j
public class RetryAspect {

    @Around("@annotation(retry)")
    public Object retry(ProceedingJoinPoint joinPoint, Retry retry) throws Throwable {
        // 获取重试次数、异常类型和是否在结果为空时重试的配置
        int attempts = retry.attempts();
        Class<? extends Throwable>[] exceptionTypes = retry.onExceptions();
        boolean retryOnEmptyResult = retry.retryOnEmptyResult();
        for (int i = 0; i < attempts; i++) {
            try {
                Object result = joinPoint.proceed(); // 执行目标方法并获取返回结果
                if (retryOnEmptyResult && isEmptyResult(result)) {
                    if (i == attempts - 1) {
                        return result; // 返回最后一次尝试的结果
                    }
                    log.warn("失败重试当前/目标次数(-{}/{}) ", i + 1, attempts);
                    Thread.sleep(calculateDelay(i, retry.delay())); // 使用指数退避算法计算延迟时间
                    continue;
                }
                return result; // 如果不是空结果，则直接返回结果

            } catch (Throwable e) {
                boolean shouldRetry = false;
                if (exceptionTypes.length == 0 || Arrays.stream(exceptionTypes).anyMatch(t -> t.isAssignableFrom(e.getClass()))) {
                    shouldRetry = true;
                }
                if (!shouldRetry || i == attempts - 1) { // 如果不应该重试或者已经是最后一次尝试
                    throw e; // 抛出异常给上层处理
                }
                log.warn("失败重试当前/目标次数({}/{}) because of: {}", i + 1, attempts, e.getMessage());
                Thread.sleep(calculateDelay(i, retry.delay())); // 使用指数退避算法计算延迟时间
            }
        }
        return null; // 不应该达到这里，但为了编译正确而提供返回值
    }

    private boolean isEmptyResult(Object result) {
        if (result == null) return true;
        if (result instanceof String && ((String) result).trim().isEmpty()) return true;
        if (result instanceof Collection && ((Collection<?>) result).isEmpty()) return true;
        if (result instanceof Map && ((Map<?, ?>) result).isEmpty()) return true; // 添加对 Map 的空检查
        // 可以继续添加其他类型的空检查...
        return false;
    }

    private long calculateDelay(int attempt, long baseDelay) {
        return baseDelay * (1L << attempt); // 使用位移操作实现指数增长
    }
}