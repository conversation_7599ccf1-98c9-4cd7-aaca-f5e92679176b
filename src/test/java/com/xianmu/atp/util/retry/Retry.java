package com.xianmu.atp.util.retry;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Retry {
    int attempts() default 3; // 默认重试次数
    long delay() default 1000; // 每次重试之间的延迟时间，默认为1秒
    Class<? extends Throwable>[] onExceptions() default {}; // 触发重试的异常类型
    boolean retryOnEmptyResult() default false; // 是否基于返回值为空进行重试
}

