package com.xianmu.atp.util.http;


import cn.hutool.http.*;
import com.xianmu.atp.interceptor.RequestInterceptor;
import com.xianmu.atp.interceptor.ResponseInterceptor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 * 自定义http请求，使用拦截器实现请求输出至allure报告中
 * <AUTHOR>
 * @ClassName CustomRequest
 * @date 2024-11-07
 */
@Component
public class CustomRequest {

    @Resource
    private RequestInterceptor requestInterceptor;

    @Resource
    private ResponseInterceptor responseInterceptor;

    @PostConstruct
    public void init() {
        // 初始化代码
        System.out.println("Application is starting up!");
        // 注册拦截器
        GlobalInterceptor.INSTANCE.addRequestInterceptor(requestInterceptor);
        GlobalInterceptor.INSTANCE.addResponseInterceptor(responseInterceptor);
//        GlobalInterceptor.INSTANCE.addRequestInterceptor((httpobj -> Console.log(httpobj)));
    }

}
