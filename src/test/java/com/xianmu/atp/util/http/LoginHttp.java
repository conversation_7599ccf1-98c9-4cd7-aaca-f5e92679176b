package com.xianmu.atp.util.http;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.xianmu.atp.BaseTest;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.MD5Util;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 *  通用登录接口
 * <AUTHOR>
 * @version 1.0
 * @ClassName LoginHttp
 * @date 2024-11-04
 */
@Component
@Slf4j
public class LoginHttp extends BaseTest {

    @Value("${app.manage-domain}")
    private String domain;

    @Value("${app.username}")
    private String username;

    @Value("${app.password}")
    private String password;

    @Value("${app.dev-domain}")
    private String wechatUrl;

    @Value("${app.wechat-phone}")
    private String wechatPhone;

    @Value("${app.wechat-token}")
    private String wechatToken;

    @Value("${app.environment}")
    private String env;


    /**
     * 方法<code>login</code>作用为：
     *  manage登录
     * <AUTHOR> Wu
     */
    public String login() {

        String loginUrl = "https://" + env + "admin.summerfarm.net" + "/authentication/auth/username/login";
        String token = "";
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("username", username);
            paramMap.put("password", password);
            HttpResponse response = HttpRequest.post(loginUrl)
                    .form(paramMap)
                    .timeout(2000)
                    .execute();
            token = JSON.parseObject(response.body()).getJSONObject("data").getString("token");
        } catch (Exception e){
            e.printStackTrace();
        }
        return token;
    }

    /**
     * 方法<code>wechatLogin</code>作用为：
     * 微信登录,为空返回空
     * <AUTHOR> Wu
     */
    public String wechatLogin() {
        String loginUrl = wechatUrl + "/openid";
        Date date = DateUtil.date();
        String timeStamp = DateUtil.format(date, "yyyyMMdd");
        String sign = SecureUtil.md5(wechatPhone + timeStamp + "login");
        Console.log(sign);
        Map<String, Object> map = new HashMap<>();
        map.put("phone", wechatPhone);
        map.put("sign", sign);
        HttpResponse response = HttpRequest.get(loginUrl)
                .header("token", wechatToken)
                .form(map)
                .timeout(2000)
                .execute();
        if (JSON.parseObject(response.body()).getJSONObject("data").get("token") != null){
            return JSON.parseObject(response.body()).getJSONObject("data").getString("token");
        } else {return null;}
    }

    public String mockLogin(String phone){
        String url = "https://devh5.summerfarm.net/loginV2";
        String deStr = phone + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "login";
        String sign = MD5Util.string2MD5(deStr);
        String json = "{\n" +
                "\"type\":0,\n" +
                "\"code\":\"\",\n" +
                "\"popMerchant\": false,\n" +
                "\"phone\":\"" + phone + "\",\n" +
                "\"sign\":\"" + sign + "\"}";
        HttpResponse response = HttpRequest.post(url)
              .body(json)
              .execute();
        return JSON.parseObject(response.body()).getJSONObject("data").getString("token");
    }

    public String adminLogin(){
        String loginUrl = "https://" + env + "admin.summerfarm.net" + "/authentication/auth/username/login";
        String token = "";
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("username", "<EMAIL>");
            paramMap.put("password", "hello1234");
            HttpResponse response = HttpRequest.post(loginUrl)
                    .form(paramMap)
                    .timeout(2000)
                    .execute();
            token = JSON.parseObject(response.body()).getJSONObject("data").getString("token");
        } catch (Exception e){
            e.printStackTrace();
        }
        return token;
    }

}
