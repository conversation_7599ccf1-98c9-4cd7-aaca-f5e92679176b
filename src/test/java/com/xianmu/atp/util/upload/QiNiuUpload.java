package com.xianmu.atp.util.upload;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.enums.api.common.CommonEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class QiNiuUpload{
    @Resource
    private Request request;

    @Resource
    private EnvVariable envVariable;

    /**
     * 上传文件到七牛云
     * 本方法实现将本地文件上传到七牛云存储的流程，包括获取上传凭证和执行上传操作
     * 首先，方法通过发送请求获取七牛云的上传凭证（token），然后使用该凭证进行文件上传
     * @return boolean 上传成功返回true，上传失败返回false
     */
    public boolean  uploadQiNiuYun() {
        // 使用 ClassLoader 加载资源文件
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("data/test_upload.jpg");
        if (inputStream == null) {
            log.error("资源文件未找到: {}", envVariable.picPath);
            return false;
        }
        try {
            File tempFile = File.createTempFile("test", ".jpg");
            Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            String filepath = tempFile.getAbsolutePath();

            Map<String, Object> filePathMap = new HashMap<>();
            filePathMap.put("fileName", envVariable.picPath);
            String url = request.urlBuild(envVariable.xmAdminDomain, CommonEnumsInterface.FlowEnums.get_qi_niu_token.getUrl());
            HttpResponseWrapper res = request.sendRequest(url,
                    CommonEnumsInterface.FlowEnums.get_qi_niu_token.getMethod(),
                    CommonEnumsInterface.FlowEnums.get_qi_niu_token.getContentType(),
                    filePathMap);
            if (res != null && res.getStatusCode() == 200){
                JSONObject result = JSON.parseObject(res.getBody());
                HashMap<String, Object> map = new HashMap<>();
                File filedata = new File(filepath);
                map.put("file",filedata);
                map.put("token",result.getJSONObject("data").getString("token"));
                map.put("key",result.getJSONObject("data").getString("key"));
                HttpResponseWrapper uploadRes = request.sendRequest(CommonEnumsInterface.FlowEnums.qi_niu_upload.getUrl(),
                        CommonEnumsInterface.FlowEnums.qi_niu_upload.getMethod(),
                        CommonEnumsInterface.FlowEnums.qi_niu_upload.getContentType(),
                        map);
                if (uploadRes.getStatusCode() != 200){
                    log.error("上传失败"+ uploadRes.toString());
                    return false;
                }
            }
        } catch (IOException e) {
            log.error("文件处理异常: {}", e.getMessage(), e);
            return false;
        }
        return true;

    }

}
