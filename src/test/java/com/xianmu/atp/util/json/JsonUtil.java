package com.xianmu.atp.util.json;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 * dubbo泛化调用支持json字符转转map
 * <AUTHOR>
 * @ClassName JsonUtil
 * @date 2025-03-07
 */
public class JsonUtil {

    public static Map<String, Object> toMap(String jsonString) {
        try {
            JSONObject jsonObject = JSON.parseObject(jsonString);
            String jsonStr = jsonObject.toJSONString();
            Map<String, Object> paramMap = JSON.parseObject(jsonStr, Map.class);
            return paramMap;
        } catch (Exception e){
            return null;
        }

    }
}
