package com.xianmu.atp.util.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName CafflineUtils
 * @date 2025-03-24
 */

public class CafflineUtils {

    private CafflineUtils() {
    }

    private static class CacheHolder {
        private static final LoadingCache<String, Object> cache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(100, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Object>() {
                    @Override
                    public Object load(String key) throws Exception {
                        // 根据 key 加载缓存值的逻辑
                        return null;
                    }
                });
    }

    public static LoadingCache<String, Object> getInstance() {
        return CacheHolder.cache;
    }
}
