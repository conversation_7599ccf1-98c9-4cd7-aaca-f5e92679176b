package com.xianmu.atp.util.redis;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRedisRequestDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import org.springframework.stereotype.Component;

@Component
public class DefaultRedisConfig {
    private static final String INVOKE_URL = "https://qaquantity.summerfarm.net/quantity-service/middleWare/referenceInvoke";

    public String getRedisResult(ATPRedisRequestDto atpRedisRequestDto){
        HttpResponse response = HttpRequest.post(INVOKE_URL)
                .body(JSONObject.toJSONString(atpRedisRequestDto))
                .execute();

        return response.body();
    }

}
