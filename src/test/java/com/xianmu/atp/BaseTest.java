package com.xianmu.atp;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;

@ContextConfiguration
@SpringBootTest(classes = AtpApplication.class)
public class BaseTest extends AbstractTestNGSpringContextTests {


    @Value("${app.xm_admin_domain}")// 使用正确的占位符语法
    public String xmAdminDomain;

    @Value("${app.srm_domain}")// 使用正确的占位符语法
    public String srmDomain;

    @Value("${app.pic-path}")
    public String picPath;

    @Value("${app.environment}")
    public String env;



}
