package com.xianmu.atp.cases.crm;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import java.util.Collections;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

@Epic("mall")
@Feature("常购商品")
@Owner("zyl") // 请替换为实际的Owner
@Slf4j
public class FrequentSkuPoolTest extends BaseTest {

    private final String BASE_URL = "https://" + EnvEnum.qa.getName() + "h5.summerfarm.net"; // 请根据实际环境配置基础URL
    private final String ADD_SKU_URL = BASE_URL + "/frequent-sku-pool/update/add";
    private final String TOKEN = getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), EnvEnum.qa.getName()); // 获取用户token

    // TC_API_FrequentSkuPool_Add_001
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_001: 添加常购商品 - 有效等价类测试"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_001_ValidSkuListAndSource() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_001
         * **用例名称**: 添加常购商品 - 正常业务流程验证
         * **优先级**: P0
         * **前置条件**: 用户已登录，存在有效的SKU
         * **测试步骤**:
         * 1. 准备有效的skuList和source参数。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":["561568850421"],"source":3}
         * **预期结果**:
         * - 返回状态码200。
         * - 响应体status为0，errCode为空，msg为"成功"。
         * - 响应体data为null或空字符串。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_001_ValidSkuListAndSource");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", Collections.singletonList("561568850421")); //
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isTrue(response.isOk(), "响应状态码非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") == 0, "业务状态码不为0"); //
        Assert.isTrue(responseJson.getString("errCode") == null || responseJson.getString("errCode").isEmpty(), "errCode不为空"); //
        Assert.isTrue("成功".equals(responseJson.getString("msg")), "msg不为成功"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_001_ValidSkuListAndSource 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_002
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_002: 添加常购商品 - 缺少skuList参数"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_002_MissingSkuList() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_002
         * **用例名称**: 添加常购商品 - 缺少必填参数skuList
         * **优先级**: P1
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备只包含source参数的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"source":3}
         * **预期结果**:
         * - 返回状态码400 (Bad Request) 或 200 (取决于后端对缺失参数的处理逻辑)。
         * - 响应体包含错误提示信息，指示skuList缺失。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_002_MissingSkuList");
        JSONObject requestBody = new JSONObject();
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        // 预期后端返回400 Bad Request或业务错误码
        Assert.isFalse(response.isOk(), "响应状态码不符合预期，应为非200");
        // 根据实际接口报错信息进行断言，这里假设会返回业务错误信息
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") != 0, "业务状态码不为非0"); //
        Assert.isTrue(responseJson.getString("msg") != null && !responseJson.getString("msg").isEmpty(), "msg为空"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_002_MissingSkuList 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_003
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_003: 添加常购商品 - skuList为空数组"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_003_EmptySkuList() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_003
         * **用例名称**: 添加常购商品 - skuList为空数组
         * **优先级**: P1
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备skuList为空数组的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":[],"source":3}
         * **预期结果**:
         * - 返回状态码200。
         * - 响应体status为0，errCode为空，msg为"成功" (或者根据业务规则有特定的提示)。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_003_EmptySkuList");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", Collections.emptyList());
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isTrue(response.isOk(), "响应状态码非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") == 0, "业务状态码不为0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_003_EmptySkuList 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_004
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_004: 添加常购商品 - 无效的source值"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_004_InvalidSourceValue() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_004
         * **用例名称**: 添加常购商品 - source参数值超出允许范围
         * **优先级**: P2
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备包含无效source值的请求体 (例如：0 或 6)。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":["123456789012"],"source":0}
         * **预期结果**:
         * - 返回状态码400 (Bad Request) 或 200 (业务错误码)。
         * - 响应体包含错误提示信息，指示source值无效。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_004_InvalidSourceValue");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", Collections.singletonList("123456789012"));
        requestBody.put("source", 0); // 无效的source值，预期会报错

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isFalse(response.isOk(), "响应状态码不符合预期，应为非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") != 0, "业务状态码不为非0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_004_InvalidSourceValue 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_005
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_005: 添加常购商品 - masterOrderNo参数验证"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_005_MasterOrderNo() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_005
         * **用例名称**: 添加常购商品 - masterOrderNo参数验证 (可选参数)
         * **优先级**: P1
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备包含masterOrderNo参数的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"masterOrderNo":"TEST_ORDER_123456","source":3} (skuList可省略或为空)
         * **预期结果**:
         * - 返回状态码200。
         * - 响应体status为0，errCode为空，msg为"成功"。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_005_MasterOrderNo");
        JSONObject requestBody = new JSONObject();
        requestBody.put("masterOrderNo", "TEST_ORDER_123456"); // 示例订单号
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isTrue(response.isOk(), "响应状态码非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") == 0, "业务状态码不为0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_005_MasterOrderNo 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_006
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_006: 添加常购商品 - skuList和masterOrderNo同时存在"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_006_SkuListAndMasterOrderNoTogether() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_006
         * **用例名称**: 添加常购商品 - skuList和masterOrderNo同时存在
         * **优先级**: P1
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备同时包含skuList和masterOrderNo的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":["SKU1","SKU2"],"masterOrderNo":"ORDER_XYZ","source":3}
         * **预期结果**:
         * - 返回状态码200。
         * - 响应体status为0，errCode为空，msg为"成功"。
         * - 验证系统是否优先处理skuList或masterOrderNo，或合并处理。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_006_SkuListAndMasterOrderNoTogether");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", Collections.singletonList("561568850421")); //
        requestBody.put("masterOrderNo", "TEST_ORDER_XYZ_789"); //
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isTrue(response.isOk(), "响应状态码非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") == 0, "业务状态码不为0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_006_SkuListAndMasterOrderNoTogether 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_007
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_007: 添加常购商品 - skuList中包含重复SKU"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_007_DuplicateSkuInList() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_007
         * **用例名称**: 添加常购商品 - skuList中包含重复SKU
         * **优先级**: P2
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备skuList包含重复SKU的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":["SKU_A","SKU_B","SKU_A"],"source":3}
         * **预期结果**:
         * - 返回状态码200。
         * - 响应体status为0，errCode为空，msg为"成功"。
         * - 验证系统是否去重处理。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_007_DuplicateSkuInList");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", new String[]{"561568850421", "SKU_B", "561568850421"}); //
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isTrue(response.isOk(), "响应状态码非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") == 0, "业务状态码不为0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_007_DuplicateSkuInList 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_008
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_008: 添加常购商品 - skuList中包含不存在的SKU"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_008_NonExistentSku() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_008
         * **用例名称**: 添加常购商品 - skuList中包含不存在的SKU
         * **优先级**: P1
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备skuList包含一个或多个不存在SKU的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":["NON_EXISTENT_SKU_123"],"source":3}
         * **预期结果**:
         * - 返回状态码200。
         * - 响应体status为0，errCode为空，msg为"成功" (或者根据业务规则有特定的提示)。
         * - 验证系统是否能正确处理不存在的SKU（忽略或返回错误）。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_008_NonExistentSku");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", Collections.singletonList("NON_EXISTENT_SKU_12345")); // 不存在的SKU
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isTrue(response.isOk(), "响应状态码非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") == 0, "业务状态码不为0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_008_NonExistentSku 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_009
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_009: 添加常购商品 - 未授权访问"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_009_UnauthorizedAccess() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_009
         * **用例名称**: 添加常购商品 - 未授权访问 (token缺失或无效)
         * **优先级**: P0
         * **前置条件**: 无
         * **测试步骤**:
         * 1. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 2. 请求头中不带token或带无效token。
         * 3. 传入请求体：{"skuList":["561568850421"],"source":3}
         * **预期结果**:
         * - 返回状态码401 (Unauthorized) 或 403 (Forbidden)。
         * - 响应体包含权限错误提示信息。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_009_UnauthorizedAccess");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", Collections.singletonList("561568850421")); //
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestBody.toJSONString())
                .execute();

        Assert.isFalse(response.isOk(), "响应状态码不符合预期，应为非200");
        // 预期返回401或403，此处根据实际接口返回进行更精确的断言
        Assert.isTrue(response.getStatus() == 401 || response.getStatus() == 403, "响应状态码非401或403");
        log.info("用例 TC_API_FrequentSkuPool_Add_009_UnauthorizedAccess 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_010
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_010: 添加常购商品 - skuList中包含非字符串类型SKU"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_010_SkuListNonString() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_010
         * **用例名称**: 添加常购商品 - skuList中包含非字符串类型SKU
         * **优先级**: P2
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备skuList中包含非字符串类型SKU的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":[12345],"source":3}
         * **预期结果**:
         * - 返回状态码400 (Bad Request) 或 200 (业务错误码)。
         * - 响应体包含错误提示信息，指示SKU类型不匹配。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_010_SkuListNonString");
        JSONObject requestBody = new JSONObject();
        // 尝试传入非字符串的SKU，预期会报错
        requestBody.put("skuList", Collections.singletonList(12345));
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isFalse(response.isOk(), "响应状态码不符合预期，应为非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") != 0, "业务状态码不为非0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_010_SkuListNonString 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_011
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_011: 添加常购商品 - source参数类型错误"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_011_SourceTypeError() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_011
         * **用例名称**: 添加常购商品 - source参数类型错误 (非整数)
         * **优先级**: P2
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备source参数为非整数类型的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"skuList":["561568850421"],"source":"abc"}
         * **预期结果**:
         * - 返回状态码400 (Bad Request) 或 200 (业务错误码)。
         * - 响应体包含错误提示信息，指示source类型不匹配。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_011_SourceTypeError");
        JSONObject requestBody = new JSONObject();
        requestBody.put("skuList", Collections.singletonList("561568850421")); //
        requestBody.put("source", "abc"); // 传入非整数类型，预期会报错

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isFalse(response.isOk(), "响应状态码不符合预期，应为非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") != 0, "业务状态码不为非0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_011_SourceTypeError 执行成功");
    }

    // TC_API_FrequentSkuPool_Add_012
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_FrequentSkuPool_Add_012: 添加常购商品 - masterOrderNo为空字符串"
    )
    @Story("添加常购商品")
    public void TC_API_FrequentSkuPool_Add_012_EmptyMasterOrderNo() {
        /**
         * **用例ID**: TC_API_FrequentSkuPool_Add_012
         * **用例名称**: 添加常购商品 - masterOrderNo为空字符串
         * **优先级**: P2
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 准备masterOrderNo为空字符串的请求体。
         * 2. 发送POST请求到/frequent-sku-pool/update/add接口。
         * 3. 传入请求体：{"masterOrderNo":"","source":3}
         * **预期结果**:
         * - 返回状态码200。
         * - 响应体status为0，errCode为空，msg为"成功"。
         * - 验证系统是否正确处理空字符串的masterOrderNo。
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_FrequentSkuPool_Add_012_EmptyMasterOrderNo");
        JSONObject requestBody = new JSONObject();
        requestBody.put("masterOrderNo", ""); //
        requestBody.put("source", 3); //

        HttpResponse response = HttpRequest.post(ADD_SKU_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(requestBody.toJSONString())
                .execute();

        Assert.isTrue(response.isOk(), "响应状态码非200");
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.isTrue(responseJson.getInteger("status") == 0, "业务状态码不为0"); //
        log.info("用例 TC_API_FrequentSkuPool_Add_012_EmptyMasterOrderNo 执行成功");
    }
}