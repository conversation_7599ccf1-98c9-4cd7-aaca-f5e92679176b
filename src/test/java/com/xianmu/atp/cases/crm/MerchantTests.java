package com.xianmu.atp.cases.crm;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPRedisRequestDto;
import com.xianmu.atp.DTO.SalesDataQuery;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.testng.Assert;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName MerchantTests
 * @date 2025-02-27
 */
@Epic("crm测试")
@Feature("merchant")
@Owner("Wufz")
@Slf4j
public class MerchantTests extends BaseTest {

    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private LoginHttp login;

    @Story("今日配送")
    @Owner("Wufz")
    @Test(description = "配送", retryAnalyzer = XMRetryAnalyzer.class)
    public void testOrder() {
        String token = login.login();
        String url = domain + "/crm-service/merchant/query/order-delivery-list";
        log.info("配送");
        JSONObject json = JSONObject.parseObject("{\"areaNo\":1001}");
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        Allure.addAttachment("response：", response.body());
        Assert.assertEquals(response.getStatus(), 200);
    }


    @Story("客户详情")
    @Owner("Wufz")
    @Test(description = "客户详情")
    /*TODO: 校验问题*/
    public void testDetail() {
        String token = login.login();
        String url = domain + "/crm-service/merchant/detail";
        log.info("配送");
        JSONObject json = JSONObject.parseObject("{\"mId\":\"350785\"}");
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        Allure.addAttachment("response：", response.body());
        Assert.assertEquals(response.getStatus(), 200);
    }

    @Story("线索池")
    @Owner("Wufz")
    @Test(description = "线索池")
    /*TODO: 校验问题*/
    public void testQueryCluePool() {
        String token = login.login();
        String url = domain + "/crm-service/merchant/queryCluePool";
        JSONObject json = JSONObject.parseObject("{\"mId\":\"350785\"}");
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        Assert.assertEquals(response.getStatus(), 200);
    }

    @Story("快速校验")
    @Owner("Wufz")
    @Test
    /*TODO: 校验问题*/
    public void testQuickCheck() {
        String token = login.login();
        String url = domain + "/crm-service/merchant";
        String detailUrlToday = url + "/query/order-delivery-detail";
        String reviewUrl = url + "/query/pending-review";
        String clientUrl = url + "/key-customers/count";
        String dailyUrl = url + "/crm-service/salesdata/daily-data";
        String salesUrl = url + "/crm-service/salesdata/selectGmvByZoneName";
        JSONObject json = JSONObject.parseObject("{\"mId\":\"350785\"}");
        JSONObject json2 = JSONObject.parseObject("{\"city\":\"杭州市\",\"province\":\"浙江\"}");
        HttpResponse response = HttpRequest.post(detailUrlToday)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();

        HttpResponse response2 = HttpRequest.post(reviewUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body("pageIndex=1&pageSize=10&isLock=1")
                .execute();
        HttpRequest.post(clientUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body(json2.toJSONString())
                .execute();
        HttpRequest.post(dailyUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("")
                .execute();
        Assert.assertEquals(response.getStatus(), 200);
    }

    @Story("品类券申请")
    @Test(description = "品类券申请")
    public void testCoupon() {
        String token = login.login();
        String url = domain + "/crm-service/merchantSituation/insert";

        log.info("品类券申请");
        JSONObject json = JSONObject.parseObject(
                "{\"status\":0,\"createLocation\":1,\"couponAmount\":1,\"threshold\":2,\"merchantId\":350785,\"monthLivingCoupon\":3,\"adminId\":895,\"adminName\":\"杨路伟\",\"couponBlackAndWhiteDTOS\":[{\"sku\":\"2231662571266\",\"type\":2}],\"sku\":\"{\\\"2231662571266\\\":\\\"大鹏鲜果pop001\\\"}\",\"orderQuantity\":1,\"afterCouponPriceStr\":\"13.00\",\"salePrice\":14,\"activityScope\":2}"
        );

        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.toString());
    }

    @Story("品查询")
    @Test(description = "品查询")
    public void testQuery() {
        String token = login.login();
        String url = domain + "/crm-service/merchantSituation/inventory";
        String url2 = domain + "/crm-service/crm/inventory";
        log.info("品类券申请");
        JSONObject json = JSONObject.parseObject("{\"type\":0,\"outdated\":0,\"areaNoList\":\"29453\",\"pdName\":\"面粉\",\"onSale\":1,\"mType\":0,\"mId\":\"350193\",\"orderType\":0,\"pageIndex\":1,\"pageSize\":10,\"activityScope\":\"2\",\"activityPrice\":true}");
        HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        HttpResponse response = HttpRequest.post(url2)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.toString());
        Assert.assertTrue(response.body().contains("\"status\":200"));
    }

    @Test(description = "bd查询")
    public void testBdQuery() {
        String token = login.login();
        String url = domain + "/crm-service/salesdata/crm/performance-per-bd";
        log.info("bd数据查询");
        JSONObject json = JSONObject.parseObject("{\"salesAreaId\":84,\"timeType\":\"month\"}");
        JSONObject json2 = JSONObject.parseObject("{\"salesAreaId\":84,\"timeType\":\"day\"}");
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.toString());
        HttpResponse response2 = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json2.toJSONString())
                .execute();
        log.info(response2.toString());
        Assert.assertTrue(response.body().contains("\"status\":200"));
    }


    @Test(description = "wechatCall")
    public void testWechatCall() {
        String token = login.login();
        String url = domain + "/crm-service/wechat/callback/customer";
        log.info("bd数据查询");
        Map<String, Object> params = new HashMap<>();
        params.put("msgSignature", "5");
        params.put("timestamp", "20250311");
        params.put("nonce", "nonce");
        params.put("echostr", "test");
        HttpResponse res = HttpRequest.get(url).header("token", token)
                .form(params)
                .execute();
        params.put("reqData","dd");
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .form(params)
                .execute();
        log.info(res.toString());
    }


    @Test(description = "bd区域数据")
    public void testBdAreaQuery() {
        String token = login.login();
        String url = domain + "/crm-service/salesdata/selectBdGmvByZoneName/1/10?queryTime=202503&thisMonth=true";
        JSONObject json = JSONObject.parseObject("{\"type\":1,\"queryTime\":\"202503\",\"thisMonth\":true,\"administrativeCityList\":[\"杭州市\"],\"bdOrgId\":\"895\",\"salesAreaId\":\"895\",\"startTime\":\"2025-01-11T17:00:33.668\",\"endTime\":\"2029-03-18T17:00:33.668\"}");
        HttpResponse response = HttpRequest.get(url).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        String cityUrl = domain + "/crm-service/salesdata/selectBdGmvByCity";
        HttpResponse response2 = HttpRequest.post(cityUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.toString());
    }

    @Test(description = "bdcity数据")
    public void testCityAreaQuery() {
        String token = login.login();
        //TODO:先set crm_city_day_gmv 中的 crm_city_day_gmv 记录 dataFlag为 20250315
        String url = domain + "/crm-service/salesdata/selectCityGmvBeforeDays/1?type=1&queryTime=202503&thisMonth=true&bdOrgId=625";
        JSONObject json = JSONObject.parseObject("{\"type\":1,\"queryTime\":\"202503\",\"thisMonth\":true,\"administrativeCityList\":[\"杭州市\"],\"bdOrgId\":\"895\",\"salesAreaId\":\"895\",\"startTime\":\"2025-01-11T17:00:33.668\",\"endTime\":\"2029-03-18T17:00:33.668\"}");
        HttpResponse response = HttpRequest.get(url).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        log.info(response.toString());
        String zoneUrl = domain + "/crm-service/salesdata/selectGmvByZoneName?type=1&queryTime=202503&thisMonth=true&bdOrgId=625";
        String zoneUrlEarlyMonth = domain + "/crm-service/salesdata/selectGmvByZoneName?type=1&queryTime=202503&thisMonth=false&bdOrgId=625";
        String zoneDataUrl = domain + "/crm-service/salesdata/selectBdDataByZoneName/1/10?type=1&queryTime=202503&thisMonth=true&bdOrgId=625";
        String selectGmvByCityDistrictUrl = domain + "/crm-service/salesdata/query/selectGmvByCityDistrict";
        HttpResponse response2 = HttpRequest.get(zoneUrl).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        HttpResponse response3 = HttpRequest.get(zoneUrlEarlyMonth).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        HttpResponse response4 = HttpRequest.get(zoneDataUrl).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        HttpResponse selectGmvByCityDistrictResponse = HttpRequest.get(selectGmvByCityDistrictUrl).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(json.toJSONString())
                .execute();
        log.info(response2.toString());
    }

    @Test(description = "dashboard数据")
    public void testDashboard() {
        String token = login.login();
        String url = domain + "/crm-service/salesdata/manage/dashboard";
        JSONObject json = JSONObject.parseObject("{\"type\":1,\"queryTime\":\"202503\",\"thisMonth\":true,\"administrativeCityList\":[\"杭州市\"],\"bdOrgId\":\"625\",\"salesAreaId\":\"895\",\"startTime\":\"2025-01-11T17:00:33.668\",\"endTime\":\"2029-03-18T17:00:33.668\"}");
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.toString());
        Assert.assertTrue(true);
    }

    @Test(description = "selectChangeMerchant")
    public void testChangeMerchant() {
        String token = login.login();
        String url = domain + "/crm-service/salesdata/selectChangeMerchant?adminId=625";
        String GmvBeforeDaysUrl = domain + "/crm-service/salesdata/selectGmvBeforeDays/3?type=1&queryTime=202503&thisMonth=true&bdOrgId=625";
        HttpResponse response = HttpRequest.get(url).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        log.info(response.toString());
        HttpResponse response2 = HttpRequest.get(GmvBeforeDaysUrl).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        log.info(response2.toString());
    }
}
