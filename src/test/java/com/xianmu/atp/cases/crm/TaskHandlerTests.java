package com.xianmu.atp.cases.crm;

import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.CrmBdVisitCustDao;
import com.xianmu.atp.dal.dao.DataSynchronizationInformationDao;
import com.xianmu.atp.generic.common.ExecuteJob;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 * 定时任务调用测试
 * <AUTHOR>
 * @ClassName TaskHandlerTests
 * @date 2025-03-11
 */
@Epic("crm定时任务测试")
@Feature("TaskHandlerTests")
@Owner("Wufz")
@Slf4j
public class TaskHandlerTests extends BaseTest {

    @Resource
    private ExecuteJob executeJob;

    @Resource
    DataSynchronizationInformationDao dataSynchronizationInformationDao;

    @Resource
    CrmBdVisitCustDao crmBdVisitCustDao;

    @Story("crm门店任务完成状态")
    @Owner("Wufz")
    @Test(description = "crm门店任务完成状态")
    public void test01() {
        String namespace = "qa";
        String groupId = "crm";
        Long jobId = 29332550L;
        String result = executeJob.RunExecuteJob(namespace, groupId, jobId);
        log.info("定时任务执行结果：{}",result);
    }

    @Story("定时任务批量执行")
    @Owner("Wufz")
    @Test(description = "定时任务批量执行")
    public void test02() {
        // 更新时间
        dataSynchronizationInformationDao.updateByTableName("crm_merchant_increment_label");
        crmBdVisitCustDao.updateByType();
        String namespace = "qa";
        String groupId = "crm";
        Map<String, Long> map = new HashMap<>();
        map.put("自动释放倒计时飞书提醒", 29332549L);
        map.put("释放私海客户V2", 29332548L);
        map.put("crm七鱼企微拜访记录", 29332547L);
        map.put("crm任务中心v2任务状态更新", 29332546L);
        map.put("批量创建样品单", 29332545L);
        map.put("crm余额池定时关闭", 29332544L);
        map.put("统计企微看板数据", 29332543L);
        map.put("CRM 省心送退款任务", 29332542L);
        map.put("新增企微离线标签", 29332541L);
        map.put("线索池新增数据", 29332540L);
        map.put("线索池删除未绑定的数据", 29332539L);
        map.put("crm_拜访记录通知", 29332538L);
        map.put("lable标签同步", 29332537L);
        map.put("发送待审核商户消息", 29332536L);
        map.put("自动释放私海客户", 29332535L);
        map.put("拜访计划消息", 29332534L);
        map.put("样品反馈钉钉消息", 29332533L);
        map.put("更新拜访计划", 29332532L);
        map.put("月活池额度更新", 29332531L);
        map.put("关闭客情申请", 29332530L);
        map.put("关闭样品申请", 29332529L);
        map.put("释放私海客户", 29332528L);
        map.put("销售数据邮件", 29332527L);
        map.put("拜访记录邮件", 29332526L);
        map.put("crm企微对话拉取", 25562993L);
        map.put("CRM - 企微素材上传", 23734139L);
        map.put("crm_品类券奖励返还", 8802853L);
        map.put("门店风控", 8379209L);
        map.put("同步企微标签", 7819900L);
        map.put("BD离职同步客户的企微至新BD的企微", 29333207L);
        map.put("调用企微的离职继承接口", 29333251L);
        map.put("企业微信名字备注初始化", 29333407L);
        for (Map.Entry<String, Long> entry : map.entrySet()){
            String result = executeJob.RunExecuteJob(namespace, groupId, entry.getValue());
            log.info("定时任务:{};执行结果：{}", entry.getKey(), result);
        }
    }

    public void beforeTest() {
        //TODO:DateUtils.localDateTimeToStringTwo(LocalDate.now().minusDays(1));
    }
}
