package com.xianmu.atp.cases.crm;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName CategoryCouponTests
 * @date 2025-03-20
 */
@Slf4j
public class CategoryCouponTests extends BaseTest {

    @Resource
    private LoginHttp login;

    @Value("${app.manage-domain}")
    private String domain;

    @Test(description = "品类券申请")
    public void testCoupon() {
        String token = login.adminLogin();
        String url = domain + "/crm-service/category-coupon/fruit-base-price-rule/upsert/setting";
        String baseUrl = domain + "/crm-service/category-coupon/base-price-rule/setting";
        JSONObject json = JSONObject.parseObject("{\"basePriceProportion\":10}");
        HttpResponse response = HttpRequest.post(url).header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.body());
        HttpResponse response2 = HttpRequest.post(baseUrl).header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response2.body());
    }

    @Test(description = "update底价")
    public void testUpdateBasePrice() {
        String token = login.adminLogin();
        String url = domain + "/crm-service/category-coupon/base-price/update";
        JSONObject json = JSONObject.parseObject("{\"basePrice\":1,\"createTime\":\"2024-11-13 14:52:17\",\"id\":116,\"largeAreaName\":\"测试大区一一\",\"largeAreaNo\":15,\"merchantSituationCategoryBasePrice\":10.07,\"merchantSituationCategoryRedLinePrice\":7,\"pdId\":196,\"pdName\":\"牛奶\",\"sku\":\"25262412323\",\"weight\":\"1L*10盒/常规\"}");
        HttpResponse response = HttpRequest.post(url).header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.body());
        Assert.assertTrue(response.body().contains("\"status\":200"));
    }
}
