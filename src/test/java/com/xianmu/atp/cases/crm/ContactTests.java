package com.xianmu.atp.cases.crm;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName ContactTests
 * @date 2025-03-19
 */
@Slf4j
public class ContactTests extends BaseTest {

    @Resource
    LoginHttp login;

    @Value("${app.manage-domain}")
    private String domain;

    @Test(description = "contact")
    public void testContact() {
        String token = login.login();
        String url = domain + "/crm-service/contact/select-contact?contact=625";
        HttpResponse response = HttpRequest.post(url).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        log.info(response.toString());
        String merchantUrl = domain + "/crm-service/salesdata/merchantLevelDistribution";
        HttpResponse response2 = HttpRequest.get(merchantUrl).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        log.info(response2.toString());
    }

    @Test(description = "/query/export")
    public void testExport() {
        String token = login.login();
        JSONObject json = JSONObject.parseObject("{\"startTime\":\"2024-03-20 14:30:00\",\"endTime\":\"2024-04-20 14:30:00\",\"followUpWay\":\"weixin\",\"fileName\":\"test\"}");
        String exportUrl = domain + "/crm-service/follow-up-record/export";
        String url = domain + "/crm-service/follow-up-record/query/export";
        HttpResponse response = HttpRequest.post(url).header("token", token)
                .body(json.toJSONString())
                .execute();
        HttpResponse response2 = HttpRequest.get(exportUrl).header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response2.body());
    }

    @Test(description = "feedback")
    public void testFeed(){
        String token = login.login();
        String url = domain + "/crm-service/follow-up-record/feedback";
        JSONObject json = JSONObject.parseObject("{\"followRecordId\":2,\"feedBack\":\"testtest\"}");
        HttpResponse response = HttpRequest.post(url).header("token", token)
                .body(json.toJSONString())
                .execute();
        log.info(response.body());
    }

    @Test(description = "gmvQuery")
    public void testGmvQuery(){
        String token = login.login();
        String url = domain + "/crm-service/salesdata/city-gmv-today/bd";
        String url2 = domain + "/crm-service/salesdata/city-gmv-today/city";
        JSONObject json = JSONObject.parseObject("{\"salesAreaId\":19,\"type\":1}");
        HttpResponse response = HttpRequest.post(url).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(json.toJSONString())
                .execute();
        HttpResponse response2 = HttpRequest.post(url2).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(json.toJSONString())
                .execute();
        Assert.assertTrue(response.getStatus() == 200);
    }

    @Test(description = "queryRelationRecord")
    public void testqueryRelationRecord(){
        String token = login.login();
        String url = domain + "/crm-service/crm/relation-record/1/10?mId=352218";
        HttpResponse response = HttpRequest.get(url).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();
        Assert.assertTrue(response.getStatus() == 200);
    }

    @Test(description = "upsert")
    public void upsert(){
        String token = login.login();
        String url = domain + "/crm-service/merchant-label/upsert/school";
        JSONObject json = JSONObject.parseObject("{\"mId\":\"350736\",\"school\":false}");
        HttpResponse response = HttpRequest.post(url).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(json.toJSONString())
                .execute();

        String yearUrl = domain + "/crm-service/merchant-label/upsert/new-year";
        JSONObject yearJson = JSONObject.parseObject("{\"mId\":\"350736\",\"openDuringNewYear\":false,\"openDate\":\"2025-02-23\"}");
        HttpResponse yearResponse = HttpRequest.post(yearUrl).header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(yearJson.toJSONString())
                .execute();
        Assert.assertTrue(response.getStatus() == 200);
    }
}
