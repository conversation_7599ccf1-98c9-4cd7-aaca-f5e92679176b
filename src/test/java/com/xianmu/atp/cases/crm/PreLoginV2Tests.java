package com.xianmu.atp.cases.crm;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Allure;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：预登录V2接口测试用例
 *
 * <AUTHOR>
 * @ClassName PreLoginV2Tests
 * @date 2025-03-18
 */
@Epic("AI测试")
@Feature("伍尔特/登录模块")
@Owner("Wufz")
@Slf4j
public class PreLoginV2Tests extends BaseTest {

    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    @Story("预登录V2-正常登录")
    @Owner("Wufz")
    @Test(description = "预登录V2-正常登录测试", retryAnalyzer = XMRetryAnalyzer.class)
    public void testPreLoginV2Normal() {
        String url = domain + "/tenant/user/query/pre-loginV2";
        log.info("测试预登录V2-正常登录");
        
        // 构建请求体 - 使用正确的手机号和密码
        JSONObject requestJson = new JSONObject();
        requestJson.put("phone", "***********");  // 测试环境有效的手机号
        requestJson.put("password", "123456");    // 测试环境有效的密码
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestJson.toJSONString())
                .execute();
        
        Allure.addAttachment("response：", response.body());
        log.info("响应结果：{}", response.body());
        
        // 验证响应状态码
        Assert.assertEquals(response.getStatus(), 200);
        
        // 验证响应内容
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0));
        
        // 验证返回的数据结构
        Assert.assertTrue(responseJson.containsKey("data"));
        JSONObject dataObject = responseJson.getJSONObject("data");
        Assert.assertNotNull(dataObject);
        
        // 验证关键字段
        Assert.assertTrue(dataObject.containsKey("passwordSuccess"));
        Assert.assertTrue(dataObject.containsKey("errorCount"));
        Assert.assertTrue(dataObject.containsKey("totalCount"));
        Assert.assertTrue(dataObject.containsKey("remainCount"));
    }
    
    @Story("预登录V2-密码错误")
    @Owner("Wufz")
    @Test(description = "预登录V2-密码错误测试")
    public void testPreLoginV2WrongPassword() {
        String url = domain + "/tenant/user/query/pre-loginV2";
        log.info("测试预登录V2-密码错误");
        
        // 构建请求体 - 使用正确的手机号和错误的密码
        JSONObject requestJson = new JSONObject();
        requestJson.put("phone", "***********");  // 测试环境有效的手机号
        requestJson.put("password", "wrong_password");  // 错误的密码
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestJson.toJSONString())
                .execute();
        
        Allure.addAttachment("response：", response.body());
        log.info("响应结果：{}", response.body());
        
        // 验证响应状态码
        Assert.assertEquals(response.getStatus(), 200);
        
        // 验证响应内容
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0));
        
        // 验证返回的数据结构
        Assert.assertTrue(responseJson.containsKey("data"));
        JSONObject dataObject = responseJson.getJSONObject("data");
        Assert.assertNotNull(dataObject);
        
        // 验证密码错误的情况
        Assert.assertFalse(dataObject.getBooleanValue("passwordSuccess"));
        Assert.assertTrue(dataObject.getIntValue("errorCount") > 0);
        Assert.assertTrue(dataObject.getIntValue("remainCount") < dataObject.getIntValue("totalCount"));
    }
    
    @Story("预登录V2-手机号不存在")
    @Owner("Wufz")
    @Test(description = "预登录V2-手机号不存在测试")
    public void testPreLoginV2NonExistentPhone() {
        String url = domain + "/tenant/user/query/pre-loginV2";
        log.info("测试预登录V2-手机号不存在");
        
        // 构建请求体 - 使用不存在的手机号
        JSONObject requestJson = new JSONObject();
        requestJson.put("phone", "19999999999");  // 不存在的手机号
        requestJson.put("password", "123456");
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestJson.toJSONString())
                .execute();
        
        Allure.addAttachment("response：", response.body());
        log.info("响应结果：{}", response.body());
        
        // 验证响应状态码
        Assert.assertEquals(response.getStatus(), 200);
        
        // 验证响应内容
        JSONObject responseJson = JSONObject.parseObject(response.body());
        
        // 验证返回的数据结构 - 这里可能返回错误信息或特定状态码
        // 根据实际接口行为进行断言
        if (responseJson.getInteger("status") == 0) {
            JSONObject dataObject = responseJson.getJSONObject("data");
            Assert.assertNotNull(dataObject);
            Assert.assertFalse(dataObject.getBooleanValue("passwordSuccess"));
        } else {
            // 如果接口对不存在的用户返回错误状态码
            Assert.assertNotEquals(responseJson.getInteger("status"), Integer.valueOf(0));
            Assert.assertTrue(responseJson.containsKey("msg"));
        }
    }
    
    @Story("预登录V2-参数校验")
    @Owner("Wufz")
    @Test(description = "预登录V2-参数校验测试")
    public void testPreLoginV2ParameterValidation() {
        String url = domain + "/tenant/user/query/pre-loginV2";
        log.info("测试预登录V2-参数校验");
        
        // 测试场景1: 手机号为空
        JSONObject requestJson1 = new JSONObject();
        requestJson1.put("phone", "");
        requestJson1.put("password", "123456");
        
        HttpResponse response1 = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestJson1.toJSONString())
                .execute();
        
        Allure.addAttachment("空手机号响应：", response1.body());
        log.info("空手机号响应结果：{}", response1.body());
        
        // 测试场景2: 密码为空
        JSONObject requestJson2 = new JSONObject();
        requestJson2.put("phone", "***********");
        requestJson2.put("password", "");
        
        HttpResponse response2 = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestJson2.toJSONString())
                .execute();
        
        Allure.addAttachment("空密码响应：", response2.body());
        log.info("空密码响应结果：{}", response2.body());
        
        // 测试场景3: 手机号格式不正确
        JSONObject requestJson3 = new JSONObject();
        requestJson3.put("phone", "1380013");  // 不符合手机号格式
        requestJson3.put("password", "123456");
        
        HttpResponse response3 = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestJson3.toJSONString())
                .execute();
        
        Allure.addAttachment("手机号格式不正确响应：", response3.body());
        log.info("手机号格式不正确响应结果：{}", response3.body());
        
        // 验证所有响应的状态码
        Assert.assertEquals(response1.getStatus(), 200);
        Assert.assertEquals(response2.getStatus(), 200);
        Assert.assertEquals(response3.getStatus(), 200);
        
        // 根据实际接口行为进行断言
        // 这里假设接口对参数错误会返回特定的错误信息
        JSONObject responseJson1 = JSONObject.parseObject(response1.body());
        JSONObject responseJson2 = JSONObject.parseObject(response2.body());
        JSONObject responseJson3 = JSONObject.parseObject(response3.body());
        
        // 验证参数错误的情况下是否有适当的错误提示
        // 注意：根据实际接口行为可能需要调整断言
        Assert.assertTrue(responseJson1.containsKey("status"));
        Assert.assertTrue(responseJson2.containsKey("status"));
        Assert.assertTrue(responseJson3.containsKey("status"));
    }
    
    @Story("预登录V2-锁定账户测试")
    @Owner("Wufz")
    @Test(description = "预登录V2-锁定账户测试", dependsOnMethods = "testPreLoginV2WrongPassword")
    public void testPreLoginV2AccountLock() {
        String url = domain + "/tenant/user/query/pre-loginV2";
        log.info("测试预登录V2-锁定账户");
        
        // 构建请求体 - 使用可能被锁定的账户
        JSONObject requestJson = new JSONObject();
        requestJson.put("phone", "***********");  // 测试环境可能被锁定的手机号
        requestJson.put("password", "wrong_password");  // 错误的密码
        
        // 多次尝试错误密码以触发锁定机制
        // 注意：实际测试中可能需要调整尝试次数，或者使用已知被锁定的账户
        for (int i = 0; i < 5; i++) {
            HttpRequest.post(url)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .body(requestJson.toJSONString())
                    .execute();
        }
        
        // 再次尝试登录，检查是否被锁定
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestJson.toJSONString())
                .execute();
        
        Allure.addAttachment("锁定账户响应：", response.body());
        log.info("锁定账户响应结果：{}", response.body());
        
        // 验证响应状态码
        Assert.assertEquals(response.getStatus(), 200);
        
        // 验证响应内容
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0));
        
        // 验证返回的数据结构
        Assert.assertTrue(responseJson.containsKey("data"));
        JSONObject dataObject = responseJson.getJSONObject("data");
        Assert.assertNotNull(dataObject);
        
        // 验证账户锁定状态
        // 注意：根据实际接口行为可能需要调整断言
        if (dataObject.getIntValue("remainCount") == 0) {
            // 如果剩余尝试次数为0，则应该有锁定时间
            Assert.assertTrue(dataObject.getLongValue("lockTime") > 0);
        }
    }
}