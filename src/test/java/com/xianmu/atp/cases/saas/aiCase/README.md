# AI SaaS接口测试用例

本目录包含AI SaaS相关接口的自动化测试用例，涵盖代门店下单、非现金子账户修改、交易流水查询等功能。

## 目录结构

```
aiCase/
├── AiSaasTest.java          # 测试用例执行层
├── AiSaasTestData.java      # 测试数据管理类
└── README.md                # 说明文档
```

## 相关工具类

- `com.xianmu.atp.generic.saas.AiSaasService` - AI SaaS接口操作工具类
- `com.xianmu.atp.generic.saas.SaasOrder` - SaaS订单通用工具类
- `com.xianmu.atp.cases.BaseTest` - 测试基类

## 测试接口覆盖

### 1. 交易流水查询接口
- **接口路径**: `/bill/list`
- **请求方式**: POST
- **功能描述**: 查询交易流水记录
- **测试用例**:
  - `testGetBillList()` - 基础查询测试

### 2. 线下支付下单完整流程
- **测试用例**: `testOfflinePaymentOrderFlow()`
- **流程步骤**:
  1. H5登录获取token
  2. 预下单获取payablePrice
  3. 生成订单（线下支付）
  4. 订单支付获取transAmt
  5. 查询订单列表获取orderNo
  6. 管理员登录
  7. 查询交易流水获取billPrice
  8. 验证billPrice = payablePrice

## 测试数据说明

### 默认测试数据
- **商品ID**: 49467
- **门店ID**: 1
- **支付类型**: 6（线下支付）
- **下单方式**: MANUAL（手动）

### 测试数据类
- `AgentOrderData` - 代门店下单数据
- `TenantFundAccountData` - 账户修改数据
- `BillListData` - 交易流水查询数据
- `OfflinePaymentData` - 线下支付流程数据

## 运行方式

### 1. 单个测试用例运行
```bash
# 运行完整流程测试
mvn test -Dtest=AiSaasTest#testOfflinePaymentOrderFlow

# 运行单个测试用例
mvn test -Dtest=AiSaasTest#testGetBillList
```

### 2. 运行所有测试用例
```bash
mvn test -Dtest=AiSaasTest
```

### 3. 在IDE中运行
- 右键点击测试类或测试方法
- 选择"Run"或"Debug"

## 环境要求

### 测试环境
- **管理端域名**: https://qamanage.cosfo.cn/
- **商城端域名**: https://qamall.cosfo.cn/
- **数据库**: repeater.interface_log表需要可访问

### 依赖组件
- Spring Boot Test
- TestNG
- Hutool HTTP客户端
- FastJSON
- Lombok

## 注意事项

### 1. 测试数据
- 测试使用的商品ID（49467）需要在测试环境中存在
- 门店ID（1）需要有效且可用
- 账户ID需要根据实际环境调整

### 2. 权限要求
- 需要有效的登录凭证
- 需要代门店下单权限
- 需要账户修改权限
- 需要交易流水查询权限

### 3. 网络环境
- 确保测试环境网络连通性
- 接口响应时间设置为3秒超时
- 建议在稳定网络环境下运行

### 4. 数据一致性
- 线下支付流程测试会产生真实订单数据
- 账户修改测试会实际修改账户信息
- 建议在专用测试环境运行

## 测试报告

测试执行后会生成详细的日志信息，包括：
- 接口请求和响应详情
- 关键业务数据提取结果
- 断言验证结果
- 错误信息和异常堆栈

## 扩展说明

### 1. 添加新测试用例
1. 在`AiSaasTest.java`中添加新的测试方法
2. 使用`@Test`注解标记
3. 在`AiSaasTestData.java`中添加相应测试数据
4. 在`AiSaasService.java`中添加新的接口操作方法（如需要）

### 2. 自定义测试数据
1. 修改`AiSaasTestData.java`中的默认值
2. 或创建新的测试数据获取方法
3. 在测试用例中使用自定义数据

### 3. 环境配置
1. 修改`BaseTest.java`中的域名配置
2. 调整`AiSaasService.java`中的超时设置
3. 根据需要修改重试策略

## 联系方式

如有问题或建议，请联系测试团队。

---

**最后更新**: 2025/08/11  
**版本**: 1.0.0  
**维护者**: AI Test Engineer