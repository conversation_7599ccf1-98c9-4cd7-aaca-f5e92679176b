package com.xianmu.atp.cases.saas;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import com.xianmu.atp.generic.saas.SaasOrder;
import com.xianmu.atp.generic.saas.SaasProduct;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.alibaba.fastjson.JSON.parseObject;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
@Epic("SaaS")
@Feature("商品货品")
@Owner("qwx")
@Slf4j
public class MarketTest extends BaseTest{

    @Resource
    private SaasProduct saasProduct;

    @Resource
    private SaasOrder saasOrder;

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    @Test(description = "saas创建无仓商品")
    public void market() throws Exception {

        String token = saasOrder.login();
        System.out.println(token);
        //新增spu
        String  marketId = saasProduct.addMarket(token);
        System.out.println(marketId);
        //新增sku
        String responses = saasProduct.addMarketItem(token,marketId);
        System.out.println(responses);
    }

    @Test(description = "校验后台自营货品")
    public void list() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询货品列表
        String  responsesList = saasProduct.list(token);
        String id = NumberUtil.getListFirstId(responsesList);
        System.out.println("货品id" + id);
        //货品详情
        String responses = saasProduct.detail(token,id);
        System.out.println("货品详情" + responses);
    }

    @Test(description = "saas创建自营仓商品")
    public void market1() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //新增货品
        String  marketId = saasProduct.addGoods(token);
        System.out.println(marketId);
        //根据货品新增sku
        String responses = saasProduct.addMarket1(token);
        System.out.println("新建自营品" + responses);
    }

    @Test(description = "saas编辑自营仓商品")
    public void updateProduct() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询货品列表
        String  responsesList = saasProduct.list(token);
        String id = NumberUtil.getListFirstId(responsesList);
        System.out.println("货品id" + id);
        //货品详情
        String detail = saasProduct.detail(token,id);
        //编辑货品信息
        System.out.println(detail);
        String responses = saasProduct.updateProduct(token,detail);
        System.out.println(responses);
    }

    @Test(description = "saas编辑自营仓商品-新增sku")
    public void addProductSku() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询货品列表
        String  responsesList = saasProduct.list(token);
        String id = NumberUtil.getListFirstId(responsesList);
        System.out.println("货品id" + id);
        //货品详情
        String detail = saasProduct.detail(token,id);
        //新增货品规格信息
        System.out.println(detail);
        Map<String, Object> map = new HashMap<>();
        JSONObject detailJson = parseObject(detail);
        map.put("id", detailJson.get("id"));
        map.put("categoryId", detailJson.get("categoryId"));
        List<Map<String, Object>> listMap = new ArrayList<>();
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put("placeType", 0);
        skuMap.put("specificationUnit", "包");
        skuMap.put("weight", 1);
        skuMap.put("specificationType", 0);
        skuMap.put("specification", "1mL*1mL");
        skuMap.put("volume", "");
        skuMap.put("volumeUnit", 1000);
        skuMap.put("customSkuCode", "1111");
        skuMap.put("taxRateValue", "0.01");
        listMap.add(skuMap);
        map.put("productSkuAddInputList", listMap);
        String responses = saasProduct.addProductSku(token,JSONObject.toJSONString(map));
        System.out.println(responses);
    }

    @Test(description = "编辑自营货品sku")
    public void updateProducts() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.updateProductSku(token);
        System.out.println(responses);

    }

    @Test(description = "编辑商品自有编码")
    public void upsertItemCode() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.upsertItemCode(token);
        System.out.println(responses);

    }

    @Test(description = "更新sku的停用状态")
    public void upsertSkuUseFlag() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.upsertSkuUseFlag(token);
        System.out.println(responses);
    }

    @Test(description = "搜索商品分组")
    public void search() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.search(token);
        System.out.println(responses);
    }

    @Test(description = "商品分页查询")
    public void paginationQuery() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.paginationQuery(token);
        System.out.println(responses);
    }

    @Test(description = "组合包列表")
    public void combineList() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.combineList(token);
        System.out.println(responses);
    }

    @Test(description = "组合包详情")
    public void combineDetail() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.combineDetail(token);
        System.out.println(responses);
    }

    @Test(description = "编辑组合包")
    public void combineUpdate() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.combineUpdate(token);
        System.out.println(responses);
    }

    @Test(description = "组合包选商品列表")
    public void combineItemList() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = saasProduct.combineItemList(token);
        System.out.println(responses);
    }


    @Test(description = "删除商品分组")
    public void deleteClassification() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);

        //新增分组
        String responses = saasProduct.saveClassification(token);
        System.out.println(responses);
        //查询分组列表
        String responsesList = saasProduct.listAll(token);
        System.out.println(responsesList);
        //获取返回Id
        JSONObject jsonObject = parseObject(responsesList);
        String data = jsonObject.getString("data");
        JSONArray jsonArray = JSON.parseArray(data);
        JSONObject dataObject = jsonArray.getJSONObject(0);
        JSONArray list = JSON.parseArray(dataObject.get("childList").toString());
        JSONObject json = list.getJSONObject(1);
        String id = json.getString("id");
        System.out.println(id);
        //删除商品分组
        String delete = saasProduct.deleteClassification(token,id);
        System.out.println(delete);

    }

    @Test(description = "saas自营仓商品-删除sku")
    public void delProductSku() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询货品列表
        String  responsesList = saasProduct.list(token);
        String id = NumberUtil.getListFirstId(responsesList);
        System.out.println("货品id" + id);
        //货品详情
        String detail = saasProduct.detail(token,id);
        System.out.println(detail);
        //获取规格列表信息
        JSONObject detailJson = parseObject(detail);
        String skuList = detailJson.get("productSkuVoList").toString();
        if(!skuList.isEmpty()){
            JSONArray jsonArray = JSON.parseArray(skuList);
            //获取第一个规格id
            String skuId = jsonArray.getJSONObject(0).getString("id");
            String responses = saasProduct.deleteProductSku(token,skuId);
            System.out.println(responses);
            //规格信息删除完毕，删除货品信息
            if(jsonArray.size() == 1){
                saasProduct.deleteProduct(token,id);
            }
        }
    }

    @Test(description = "saas商品代仓申请记录-列表")
    public void agentRecordList() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询列表
        String  responsesList = saasProduct.agentRecordList(token);
        System.out.println(responsesList);
    }

    @Test(description = "saas商品代仓申请记录-取消")
    public void agentRecordCancel() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询列表
        String  responsesList = saasProduct.agentRecordList(token);
        //获取第一条记录
        String id = NumberUtil.getListFirstId(responsesList);
        String  responses = saasProduct.upsertCancel(token, id);
        System.out.println(responses);
    }

    @Test(description = "saas商品代仓申请记录-日志")
    public void agentRecordBizLog() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询列表
        String  responsesList = saasProduct.agentRecordList(token);
        //获取第一条记录
        String id = NumberUtil.getListFirstId(responsesList);
        String  responses = saasProduct.bizLog(token, id);
        System.out.println(responses);
    }

}