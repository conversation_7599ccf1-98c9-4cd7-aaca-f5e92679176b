package com.xianmu.atp.cases.saas;

import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.saas.SaasExport;
import com.xianmu.atp.generic.saas.SaasOrder;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/30
 */
public class ExportTest extends BaseTest {


    @Resource
    private SaasOrder saasOrder;

    @Resource
    private SaasExport saasExport;

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    @Test(description = "saas帆台导出")
    public void bomExport() throws Exception {

        String token = saasOrder.login();
        System.out.println(token);
        //导出半成品
        String  bomExport = saasExport.bomExport(token);
        System.out.println(bomExport);
    }
    @Test(description = "saas帆台订单导出")
    public void orderExport() throws Exception {

        String token = saasOrder.login();
        System.out.println(token);
        //导出订单
        String  orderExport = saasExport.orderExport(token);
        System.out.println(orderExport);
    }
    @Test(description = "saas帆台售后单导出")
    public void afterExport() throws Exception {

        String token = saasOrder.login();
        System.out.println(token);
        //售后单导出
        String  orderExport = saasExport.afterExport(token);
        System.out.println(orderExport);
    }

}
