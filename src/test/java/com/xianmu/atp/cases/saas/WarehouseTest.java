package com.xianmu.atp.cases.saas;

import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.saas.SaasOrder;
import com.xianmu.atp.generic.saas.SaasWarehouse;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */
@Epic("SaaS")
@Feature("仓库")
@Owner("qwx")
@Slf4j
public class WarehouseTest extends BaseTest {

    @Resource
    private SaasWarehouse  sassWarehouse;

    @Resource
    private SaasOrder saasOrder;

    @Test(description = "saas查询仓库数据")
    public void save() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //查询实时库存-仓库数据
        String responses = sassWarehouse.list(token);
        System.out.println(responses);
        String responses1 = sassWarehouse.list1(token);
        System.out.println(responses1);
        String responses2 = sassWarehouse.list2(token);
        System.out.println(responses2);
        String responses3 = sassWarehouse.list3(token);
        System.out.println(responses3);

    }

    @Test(description = "导出出入库记录")
    public void exportWarehousing() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = sassWarehouse.exportWarehousing(token);
        System.out.println(responses);
    }

    @Test(description = "导出配送记录")
    public void exportDeliveryPlan() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = sassWarehouse.exportDeliveryPlan(token);
        System.out.println(responses);
    }


    @Test(description = "仓库维度-查询仓库数据")
    public void productAgentWarehouseList() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = sassWarehouse.productAgentWarehouseList(token);
        System.out.println(responses);
    }

    @Test(description = "货品维度-查询仓库-货品聚合数据")
    public void warehouseAggregationList() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = sassWarehouse.warehouseAggregationList(token);
        System.out.println(responses);
    }

    @Test(description = "查询仓库库存总览信息")
    public void warehouseOverview() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = sassWarehouse.warehouseOverview(token);
        System.out.println(responses);
    }

    @Test(description = "库存保质期查询")
    public void shelfLifeQuery() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = sassWarehouse.shelfLifeQuery(token);
        System.out.println(responses);
    }

    @Test(description = "查询库存变动记录")
    public void stockChangeRecord() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = sassWarehouse.stockChangeRecord(token);
        System.out.println(responses);
    }

}
