package com.xianmu.atp.cases.saas;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import com.xianmu.atp.generic.saas.SaasOrder;
import com.xianmu.atp.generic.saas.TenantAccount;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/12
 */

@Epic("SaaS")
@Feature("账号模块")
@Owner("qwx")
@Slf4j
public class TenantAccountTest extends BaseTest {

    @Resource
    private SaasOrder saasOrder;

    @Resource
    private TenantAccount tenantAccount;

    @Test(description = "saas账号登录")
    public void preLoginV2() throws Exception {
        //账号登录
        String responses = tenantAccount.preLoginV2();
        System.out.println(responses);
    }

    @Test(description = "saas账号查询绑定品牌方")
    public void list() throws Exception {
        //查询绑定品牌方
        String responses = tenantAccount.list();
        System.out.println(responses);
    }

    @Test(description = "saas账号登录-登录到工作台")
    public void login() throws Exception {
        //品牌方登录到工作台
        String responses = tenantAccount.login();
        System.out.println(responses);
    }

    @Test(description = "saas账号登录-查看品牌方信息")
    public void loginUserInfo() throws Exception {
        //品牌方登录token
        String token = saasOrder.login();
        //查看品牌方信息
        String responses = tenantAccount.loginUserInfo(token);
        System.out.println(responses);
    }

    @Test(description = "saas账号登录-查看品牌方账号列表")
    public void accountList() throws Exception {
        //品牌方登录token
        String token = saasOrder.login();
        //查看品牌方信息
        String responses = tenantAccount.accountList(token);
        System.out.println(responses);
    }

    @Test(description = "saas账号登录-新增品牌方账号")
    public void accountAdd() throws Exception {
        //品牌方登录token
        String token = saasOrder.login();
        //查看品牌方信息
        String responses = tenantAccount.addAccount(token);
        System.out.println(responses);
    }

    @Test(description = "saas账号登录-查看账号详情")
    public void accountInfo() throws Exception {
        //品牌方登录token
        String token = saasOrder.login();
        String responseList = tenantAccount.accountList(token);
        String id = NumberUtil.getListFirstId(responseList);
        //查看品牌方信息
        String responses = tenantAccount.userInfo(token, id);
        System.out.println(responses);
    }

    @Test(description = "saas账号登录-修改账号信息")
    public void accountUpdate() throws Exception {
        //品牌方登录token
        String token = saasOrder.login();
        String responseList = tenantAccount.accountList(token);
        String id = NumberUtil.getListFirstId(responseList);
        //查看品牌方信息
        String responsesDetail = tenantAccount.userInfo(token, id);
        JSONObject accountJson = JSON.parseObject(responsesDetail);
        //更新品牌方账号信息
        String responses = tenantAccount.update(token, accountJson.getString("data"));
        System.out.println(responses);
    }

    @Test(description = "saas账号登录-删除账号信息")
    public void accountDel() throws Exception {
        //品牌方登录token
        String token = saasOrder.login();
        String responseList = tenantAccount.accountList(token);
        String id = NumberUtil.getListFirstId(responseList);
        //删除品牌方信息
        String responses = tenantAccount.delete(token, id);
        System.out.println(responses);
    }

    @Test(description = "saas账号-查询账号类型列表")
    public void fundAccountPage() throws Exception {
        //品牌方登录token
        String token = saasOrder.login();
        String responses = tenantAccount.fundAccountPage(token);
        System.out.println(responses);
    }

}
