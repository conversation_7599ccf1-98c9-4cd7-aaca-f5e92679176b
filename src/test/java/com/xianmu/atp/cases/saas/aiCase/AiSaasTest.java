package com.xianmu.atp.cases.saas.aiCase;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.saas.AiSaasService;
import com.xianmu.atp.generic.saas.SaasOrder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * AI SaaS接口测试用例
 * 交易流水查询接口测试
 * 以及完整的线下支付下单流程测试
 * 
 * <AUTHOR> Test Engineer
 * @date 2025/08/11
 */
@Slf4j
public class AiSaasTest extends BaseTest {

    @Resource
    private AiSaasService aiSaasService;

    @Resource
    private SaasOrder saasOrder;


    /**
     * 线下支付下单流程完整测试
     * 测试场景：完整的线下支付下单流程，包含交易流水验证
     */
    @Test(description = "线下支付下单流程完整测试")
    public void testOfflinePaymentOrderFlow() throws Exception {
        log.info("开始执行线下支付下单流程完整测试");
        
        // 1. 调用h5LoginGet登录接口
        String h5Token = saasOrder.h5LoginGet("13451049394");
        Assert.assertNotNull(h5Token, "H5登录token不能为空");
        log.info("H5登录成功，token: {}", h5Token);
        
        // 2. 通过/order/pre-order预下单接口，itemId入参使用49467，获取接口Response的payablePrice的值
        AiSaasTestData.OfflinePaymentData paymentData = AiSaasTestData.OfflinePaymentData.getDefault();
        Long itemId = paymentData.getItemId();
        Integer amount = paymentData.getAmount();
        String preOrderResponse = aiSaasService.preOrder(h5Token, itemId, amount);
        Assert.assertNotNull(preOrderResponse, "预下单响应不能为空");
        
        // 验证预下单响应
        JSONObject preOrderJson = JSON.parseObject(preOrderResponse);
        Assert.assertTrue(preOrderJson.getInteger("code") == 200 || preOrderJson.getInteger("code") == 0, 
                "预下单应该成功");
        
        // 提取payablePrice
        Double payablePrice = aiSaasService.extractPayablePrice(preOrderResponse);
        Assert.assertNotNull(payablePrice, "payablePrice不能为空");
        Assert.assertTrue(payablePrice > 0, "payablePrice应该大于0");
        log.info("预下单成功，payablePrice: {}", payablePrice);
        
        // 3. 通过/order/place-order接口生成订单，payType入参6
        Integer payType = paymentData.getPayType(); // 线下支付
        String orderNo = aiSaasService.placeOrder(h5Token, itemId, payType);
        Assert.assertNotNull(orderNo, "订单号不能为空");
        log.info("生成订单成功，订单号: {}", orderNo);
        
        // 4. 通过/order/pay接口，进行支付，payType入参6，同时获取接口Response的transAmt值
        String payResponse = aiSaasService.orderPay(h5Token, orderNo, payType);
        Assert.assertNotNull(payResponse, "支付响应不能为空");
        
        // 验证支付响应
        JSONObject payJson = JSON.parseObject(payResponse);
        Assert.assertTrue(payJson.getInteger("code") == 200 || payJson.getInteger("code") == 0, 
                "支付应该成功");
        
        // 提取transAmt
        Double transAmt = aiSaasService.extractTransAmt(payResponse);
        Assert.assertNotNull(transAmt, "transAmt不能为空");
        log.info("支付成功，transAmt: {}", transAmt);
        
        // 5. 查看/order/list接口，获取接口Response的orderNo值
        String orderListResponse = aiSaasService.getOrderList(h5Token);
        Assert.assertNotNull(orderListResponse, "订单列表响应不能为空");
        
        // 验证订单列表响应
        JSONObject orderListJson = JSON.parseObject(orderListResponse);
        Assert.assertTrue(orderListJson.getInteger("code") == 200 || orderListJson.getInteger("code") == 0, 
                "订单列表查询应该成功");
        
        // 提取最新订单号
        String latestOrderNo = aiSaasService.extractLatestOrderNo(orderListResponse);
        Assert.assertNotNull(latestOrderNo, "最新订单号不能为空");
        log.info("查询订单列表成功，最新订单号: {}", latestOrderNo);
        
        // 6. 调用login登录接口
        String adminToken = saasOrder.login();
        Assert.assertNotNull(adminToken, "管理员登录token不能为空");
        log.info("管理员登录成功，token: {}", adminToken);
        
        // 7. 调用/bill/list接口，根据orderNo查询列表数据，获取billPrice的值，断言billPrice=payablePrice
        String billListResponse = aiSaasService.getBillList(adminToken, 1, 10, orderNo);
        Assert.assertNotNull(billListResponse, "交易流水响应不能为空");
        
        // 验证交易流水响应
        JSONObject billListJson = JSON.parseObject(billListResponse);
        Integer status = billListJson.getInteger("status");
        Integer code = billListJson.getInteger("code");
        Assert.assertTrue((status != null && status == 200) || (code != null && (code == 200 || code == 0)), 
                "交易流水查询应该成功");
        
        // 提取billPrice
        Double billPrice = aiSaasService.extractBillPrice(billListResponse, orderNo);
        Assert.assertNotNull(billPrice, "billPrice不能为空");
        log.info("查询交易流水成功，billPrice: {}", billPrice);
        
        // 8. 断言billPrice=payablePrice
        Assert.assertEquals(billPrice, payablePrice, 0.01, 
                String.format("billPrice应该等于payablePrice，billPrice: %s, payablePrice: %s", billPrice, payablePrice));
        
        log.info("线下支付下单流程完整测试成功完成！");
        log.info("测试结果汇总:");
        log.info("- 订单号: {}", orderNo);
        log.info("- 预下单金额(payablePrice): {}", payablePrice);
        log.info("- 支付金额(transAmt): {}", transAmt);
        log.info("- 账单金额(billPrice): {}", billPrice);
        log.info("- 金额验证: billPrice == payablePrice = {}", Math.abs(billPrice - payablePrice) < 0.01);
    }




}