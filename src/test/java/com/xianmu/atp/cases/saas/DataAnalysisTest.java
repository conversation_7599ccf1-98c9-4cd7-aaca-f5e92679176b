package com.xianmu.atp.cases.saas;

import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.saas.DataAnalysis;
import com.xianmu.atp.generic.saas.SaasOrder;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/28
 */
public class DataAnalysisTest extends BaseTest {

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    @Resource
    private DataAnalysis dataAnalysis;

    @Resource
    private SaasOrder saasOrder;
    @Test(description = "数据菜单导出汇总")
    public void export() throws Exception {

        String token = saasOrder.login();
        System.out.println(token);
        //导出采购汇总-按供应商
        String  list = dataAnalysis.exportSupplier(token);
        System.out.println(list);
        String  list1 = dataAnalysis.damageList(token);
        System.out.println(list1);
        String  list2 = dataAnalysis.supplierList(token);
        System.out.println(list2);
        String  list3 = dataAnalysis.reportQueryList(token);
        System.out.println(list3);
        String  list4 = dataAnalysis.exportCondition(token);
        System.out.println(list4);
        String  list5 = dataAnalysis.damageSaleRatioList(token);
        System.out.println(list5);
        String  list6 = dataAnalysis.exportStorePurchase(token);
        System.out.println(list6);
        String  list7 = dataAnalysis.exportProductSales(token);
        System.out.println(list7);
        String  list8 = dataAnalysis.exportStoreDimension(token);
        System.out.println(list8);
        String  list9 = dataAnalysis.exportProportion(token);
        System.out.println(list9);
        String  list10 = dataAnalysis.exportPurchaseDetail(token);
        System.out.println(list10);
        String  list11 = dataAnalysis.exportPurchaseBackDetail(token);
        System.out.println(list11);
        String  list12 = dataAnalysis.exportDamageDetail(token);
        System.out.println(list12);
        String  list13 = dataAnalysis.exportDamageSaleDetail(token);
        System.out.println(list13);
        String  list14 = dataAnalysis.exportSku(token);
        System.out.println(list14);

    }
}
