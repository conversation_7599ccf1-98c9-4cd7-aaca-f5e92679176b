package com.xianmu.atp.cases.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.saas.LadderPrice;
import com.xianmu.atp.generic.saas.SaasOrder;
import com.xianmu.atp.generic.saas.SaasProduct;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */


@Epic("SaaS")
@Feature("阶梯价")
@Owner("qwx")
@Slf4j
public class LadderPriceTest extends BaseTest {



    @Resource
    private LadderPrice ladderPrice;

    @Resource
    private SaasOrder saasOrder;

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    public String getQuantityRuleStatus(String token,String itemId)throws Exception{
        String quantityRuleBody = saasOrder.quantityRule(token, itemId);
        JSONObject quantityRuleJson = JSON.parseObject(quantityRuleBody);
        return quantityRuleJson.getString("status");
    }
    @Test(description = "下单一件不取阶梯价")
    public void order() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 58410;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":1}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        
        // 解析预下单响应，获取并验证data[0].orderItemVOS.totalPrice
        JSONObject jsonResponse = JSON.parseObject(body);
        
        // 验证响应状态
        Assert.assertEquals(response.getStatus(), 200, "预下单接口调用失败");
        // 获取data数组
        Assert.assertTrue(jsonResponse.containsKey("data"), "响应中必须包含data字段");
        Object dataObj = jsonResponse.get("data");
        Assert.assertNotNull(dataObj, "data字段不能为空");
        
        // 检查data是否为数组
        Assert.assertTrue(dataObj instanceof com.alibaba.fastjson.JSONArray, "data必须是数组类型");
        com.alibaba.fastjson.JSONArray dataArray = (com.alibaba.fastjson.JSONArray) dataObj;
        
        // 确保数组不为空且至少有一个元素
        Assert.assertTrue(dataArray.size() > 0, "data数组不能为空");
        
        // 获取第一个元素
        JSONObject firstDataItem = dataArray.getJSONObject(0);
        Assert.assertNotNull(firstDataItem, "data[0]不能为空");
        
        // 获取orderItemVOS
        Assert.assertTrue(firstDataItem.containsKey("orderItemVOS"), "data[0]中必须包含orderItemVOS字段");
        Object orderItemVOSObj = firstDataItem.get("orderItemVOS");
        Assert.assertNotNull(orderItemVOSObj, "orderItemVOS不能为空");
        
        Assert.assertTrue(orderItemVOSObj instanceof com.alibaba.fastjson.JSONArray, "orderItemVOS必须是数组类型");
        com.alibaba.fastjson.JSONArray orderItemVOSArray = (com.alibaba.fastjson.JSONArray) orderItemVOSObj;
        Assert.assertTrue(orderItemVOSArray.size() > 0, "orderItemVOS数组不能为空");
        
        // 获取第一个orderItem的totalPrice并断言等于20
        JSONObject firstOrderItem = orderItemVOSArray.getJSONObject(0);
        Assert.assertNotNull(firstOrderItem, "orderItemVOS[0]不能为空");
        Assert.assertTrue(firstOrderItem.containsKey("totalPrice"), "orderItemVOS[0]中必须包含totalPrice字段");
        
        Object totalPriceObj = firstOrderItem.get("totalPrice");
        Assert.assertNotNull(totalPriceObj, "totalPrice不能为空");
        
        // 转换totalPrice为数值类型并断言等于20
        double totalPrice;
        if (totalPriceObj instanceof Number) {
            totalPrice = ((Number) totalPriceObj).doubleValue();
        } else if (totalPriceObj instanceof String) {
            try {
                totalPrice = Double.parseDouble((String) totalPriceObj);
            } catch (NumberFormatException e) {
                Assert.fail("totalPrice字符串格式无效: " + totalPriceObj);
                return;
            }
        } else {
            Assert.fail("totalPrice类型不支持: " + totalPriceObj.getClass().getSimpleName());
            return;
        }
        
        System.out.println("获取到的totalPrice值: " + totalPrice);
        Assert.assertEquals(totalPrice, 20.0, 0.01, "totalPrice必须等于20");
        System.out.println("totalPrice断言验证通过: " + totalPrice + " == 20");

        //生成订单
        String orderNos = saasOrder.placeOrder(token, itemId);
    
        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);
    
        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);
    
        // 验证支付响应
        Assert.assertNotNull(responses, "支付响应不能为空");
        JSONObject payResponse = JSON.parseObject(responses);
    }

    @Test(description = "下单2件取阶梯价,断言阶梯价")
    public void order1() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 58410;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":2}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);

        // 解析预下单响应，获取并验证data[0].orderItemVOS.totalPrice
        JSONObject jsonResponse = JSON.parseObject(body);

        // 验证响应状态
        Assert.assertEquals(response.getStatus(), 200, "预下单接口调用失败");
        // 获取data数组
        Assert.assertTrue(jsonResponse.containsKey("data"), "响应中必须包含data字段");
        Object dataObj = jsonResponse.get("data");
        Assert.assertNotNull(dataObj, "data字段不能为空");

        // 检查data是否为数组
        Assert.assertTrue(dataObj instanceof com.alibaba.fastjson.JSONArray, "data必须是数组类型");
        com.alibaba.fastjson.JSONArray dataArray = (com.alibaba.fastjson.JSONArray) dataObj;

        // 确保数组不为空且至少有一个元素
        Assert.assertTrue(dataArray.size() > 0, "data数组不能为空");

        // 获取第一个元素
        JSONObject firstDataItem = dataArray.getJSONObject(0);
        Assert.assertNotNull(firstDataItem, "data[0]不能为空");

        // 获取orderItemVOS
        Assert.assertTrue(firstDataItem.containsKey("orderItemVOS"), "data[0]中必须包含orderItemVOS字段");
        Object orderItemVOSObj = firstDataItem.get("orderItemVOS");
        Assert.assertNotNull(orderItemVOSObj, "orderItemVOS不能为空");

        Assert.assertTrue(orderItemVOSObj instanceof com.alibaba.fastjson.JSONArray, "orderItemVOS必须是数组类型");
        com.alibaba.fastjson.JSONArray orderItemVOSArray = (com.alibaba.fastjson.JSONArray) orderItemVOSObj;
        Assert.assertTrue(orderItemVOSArray.size() > 0, "orderItemVOS数组不能为空");

        // 获取第一个orderItem的totalPrice并断言等于30
        JSONObject firstOrderItem = orderItemVOSArray.getJSONObject(0);
        Assert.assertNotNull(firstOrderItem, "orderItemVOS[0]不能为空");
        Assert.assertTrue(firstOrderItem.containsKey("totalPrice"), "orderItemVOS[0]中必须包含totalPrice字段");

        Object totalPriceObj = firstOrderItem.get("totalPrice");
        Assert.assertNotNull(totalPriceObj, "totalPrice不能为空");

        // 转换totalPrice为数值类型并断言等于20
        double totalPrice;
        if (totalPriceObj instanceof Number) {
            totalPrice = ((Number) totalPriceObj).doubleValue();
        } else if (totalPriceObj instanceof String) {
            try {
                totalPrice = Double.parseDouble((String) totalPriceObj);
            } catch (NumberFormatException e) {
                Assert.fail("totalPrice字符串格式无效: " + totalPriceObj);
                return;
            }
        } else {
            Assert.fail("totalPrice类型不支持: " + totalPriceObj.getClass().getSimpleName());
            return;
        }

        System.out.println("获取到的totalPrice值: " + totalPrice);
        Assert.assertEquals(totalPrice, 30.0, 0.01, "totalPrice必须等于30");
        System.out.println("totalPrice断言验证通过: " + totalPrice + " == 30");

        //生成订单
        String orderNos = ladderPrice.placeOrder(token, itemId);


        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);

        // 验证支付响应
        Assert.assertNotNull(responses, "支付响应不能为空");
        JSONObject payResponse = JSON.parseObject(responses);
    }

    @Test(description = "阶梯价倒挂-以自定义价格售卖")
    public void order2() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 58471;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":1}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);

        // 解析预下单响应，获取并验证data[0].orderItemVOS.totalPrice
        JSONObject jsonResponse = JSON.parseObject(body);

        // 验证响应状态
        Assert.assertEquals(response.getStatus(), 200, "预下单接口调用失败");
        // 获取data数组
        Assert.assertTrue(jsonResponse.containsKey("data"), "响应中必须包含data字段");
        Object dataObj = jsonResponse.get("data");
        Assert.assertNotNull(dataObj, "data字段不能为空");

        // 检查data是否为数组
        Assert.assertTrue(dataObj instanceof com.alibaba.fastjson.JSONArray, "data必须是数组类型");
        com.alibaba.fastjson.JSONArray dataArray = (com.alibaba.fastjson.JSONArray) dataObj;

        // 确保数组不为空且至少有一个元素
        Assert.assertTrue(dataArray.size() > 0, "data数组不能为空");

        // 获取第一个元素
        JSONObject firstDataItem = dataArray.getJSONObject(0);
        Assert.assertNotNull(firstDataItem, "data[0]不能为空");

        // 获取orderItemVOS
        Assert.assertTrue(firstDataItem.containsKey("orderItemVOS"), "data[0]中必须包含orderItemVOS字段");
        Object orderItemVOSObj = firstDataItem.get("orderItemVOS");
        Assert.assertNotNull(orderItemVOSObj, "orderItemVOS不能为空");

        Assert.assertTrue(orderItemVOSObj instanceof com.alibaba.fastjson.JSONArray, "orderItemVOS必须是数组类型");
        com.alibaba.fastjson.JSONArray orderItemVOSArray = (com.alibaba.fastjson.JSONArray) orderItemVOSObj;
        Assert.assertTrue(orderItemVOSArray.size() > 0, "orderItemVOS数组不能为空");

        // 获取第一个orderItem的totalPrice并断言等于2
        JSONObject firstOrderItem = orderItemVOSArray.getJSONObject(0);
        Assert.assertNotNull(firstOrderItem, "orderItemVOS[0]不能为空");
        Assert.assertTrue(firstOrderItem.containsKey("totalPrice"), "orderItemVOS[0]中必须包含totalPrice字段");

        Object totalPriceObj = firstOrderItem.get("totalPrice");
        Assert.assertNotNull(totalPriceObj, "totalPrice不能为空");

        // 转换totalPrice为数值类型并断言等于20
        double totalPrice;
        if (totalPriceObj instanceof Number) {
            totalPrice = ((Number) totalPriceObj).doubleValue();
        } else if (totalPriceObj instanceof String) {
            try {
                totalPrice = Double.parseDouble((String) totalPriceObj);
            } catch (NumberFormatException e) {
                Assert.fail("totalPrice字符串格式无效: " + totalPriceObj);
                return;
            }
        } else {
            Assert.fail("totalPrice类型不支持: " + totalPriceObj.getClass().getSimpleName());
            return;
        }

        System.out.println("获取到的totalPrice值: " + totalPrice);
        Assert.assertEquals(totalPrice, 2.0, 0.01, "totalPrice必须等于2");
        System.out.println("totalPrice断言验证通过: " + totalPrice + " == 2");

        //生成订单
        String orderNos = saasOrder.placeOrder(token, itemId);

        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);

        // 验证支付响应
        Assert.assertNotNull(responses, "支付响应不能为空");
        JSONObject payResponse = JSON.parseObject(responses);
    }


    @Test(description = "阶梯价倒挂-以供应价格售卖")
    public void order3() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 58472;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":1}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);

        // 解析预下单响应，获取并验证data[0].orderItemVOS.totalPrice
        JSONObject jsonResponse = JSON.parseObject(body);

        // 验证响应状态
        Assert.assertEquals(response.getStatus(), 200, "预下单接口调用失败");
        // 获取data数组
        Assert.assertTrue(jsonResponse.containsKey("data"), "响应中必须包含data字段");
        Object dataObj = jsonResponse.get("data");
        Assert.assertNotNull(dataObj, "data字段不能为空");

        // 检查data是否为数组
        Assert.assertTrue(dataObj instanceof com.alibaba.fastjson.JSONArray, "data必须是数组类型");
        com.alibaba.fastjson.JSONArray dataArray = (com.alibaba.fastjson.JSONArray) dataObj;

        // 确保数组不为空且至少有一个元素
        Assert.assertTrue(dataArray.size() > 0, "data数组不能为空");

        // 获取第一个元素
        JSONObject firstDataItem = dataArray.getJSONObject(0);
        Assert.assertNotNull(firstDataItem, "data[0]不能为空");

        // 获取orderItemVOS
        Assert.assertTrue(firstDataItem.containsKey("orderItemVOS"), "data[0]中必须包含orderItemVOS字段");
        Object orderItemVOSObj = firstDataItem.get("orderItemVOS");
        Assert.assertNotNull(orderItemVOSObj, "orderItemVOS不能为空");

        Assert.assertTrue(orderItemVOSObj instanceof com.alibaba.fastjson.JSONArray, "orderItemVOS必须是数组类型");
        com.alibaba.fastjson.JSONArray orderItemVOSArray = (com.alibaba.fastjson.JSONArray) orderItemVOSObj;
        Assert.assertTrue(orderItemVOSArray.size() > 0, "orderItemVOS数组不能为空");

        // 获取第一个orderItem的totalPrice并断言等于2
        JSONObject firstOrderItem = orderItemVOSArray.getJSONObject(0);
        Assert.assertNotNull(firstOrderItem, "orderItemVOS[0]不能为空");
        Assert.assertTrue(firstOrderItem.containsKey("totalPrice"), "orderItemVOS[0]中必须包含totalPrice字段");

        Object totalPriceObj = firstOrderItem.get("totalPrice");
        Assert.assertNotNull(totalPriceObj, "totalPrice不能为空");

        // 转换totalPrice为数值类型并断言等于20
        double totalPrice;
        if (totalPriceObj instanceof Number) {
            totalPrice = ((Number) totalPriceObj).doubleValue();
        } else if (totalPriceObj instanceof String) {
            try {
                totalPrice = Double.parseDouble((String) totalPriceObj);
            } catch (NumberFormatException e) {
                Assert.fail("totalPrice字符串格式无效: " + totalPriceObj);
                return;
            }
        } else {
            Assert.fail("totalPrice类型不支持: " + totalPriceObj.getClass().getSimpleName());
            return;
        }

        System.out.println("获取到的totalPrice值: " + totalPrice);
        Assert.assertEquals(totalPrice, 5.0, 0.01, "totalPrice必须等于5");
        System.out.println("totalPrice断言验证通过: " + totalPrice + " == 5");

        //生成订单
        String orderNos = saasOrder.placeOrder(token, itemId);

        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);

        // 验证支付响应
        Assert.assertNotNull(responses, "支付响应不能为空");
        JSONObject payResponse = JSON.parseObject(responses);
    }




}


