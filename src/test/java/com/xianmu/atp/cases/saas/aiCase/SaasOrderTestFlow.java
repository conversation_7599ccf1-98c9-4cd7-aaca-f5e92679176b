
package com.xianmu.atp.cases.saas.aiCase;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.saas.AiSaasService;
import com.xianmu.atp.generic.saas.SaasOrder;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.generic.saas.AiOrderService;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;


/**
 * SaaS订单完整流程测试类
 * 实现从H5登录到售后申请的完整10步流程
 * 
 * 商城下单，帆台后台申请配送后售后退款、退货退款、补发，包含下列步骤：
 * <AUTHOR> Test Engineer  
 * @date 2025/08/11
 */

@Slf4j
public class SaasOrderTestFlow extends BaseTest {

    @Resource
    private SaasOrder saasOrder;

    @Resource
    private AiSaasService aiSaasService;


    @Resource
    private LoginHttp loginHttp;

    // 新增：注入AiOrderService以修复变量未定义错误
    @Resource
    private AiOrderService aiOrderService;


/**
     * 完整的商城下单到售后申请流程测试
     * 涵盖10个步骤的完整流程
     */

    @Test(description = "商城下单后台申请售后完整流程测试")
    public void testCompleteOrderToAfterSalesFlow() throws Exception {
        log.info("=== 开始执行商城下单后台申请售后完整流程测试 ===");
        
        // 测试参数定义（优先使用测试数据类中的常量）
        Long itemId = AiSaasTestData.TestItems.STANDARD_ITEM_ID;
        Integer amount = 6;
        Integer payType = AiSaasTestData.PayTypes.COD_PAY;
        String testPhone = "13409291204";
        
        // === 步骤1: 调用h5LoginGet登录接口 ===
        log.info("步骤1: H5登录");
        String h5Token = saasOrder.h5LoginGet(testPhone);
        Assert.assertNotNull(h5Token, "H5登录token不能为空");
        log.info("H5登录成功，token: {}", h5Token);
        
        // === 步骤2: 通过/order/pre-order预下单接口，itemId入参使用49467，amount入参6 ===
        log.info("步骤2: 预下单");
        String preOrderResponse = aiSaasService.preOrder(h5Token, itemId, amount);
        Assert.assertNotNull(preOrderResponse, "预下单响应不能为空");
        
        JSONObject preOrderJson = JSON.parseObject(preOrderResponse);
        Assert.assertTrue(preOrderJson.getInteger("code") == 200 || preOrderJson.getInteger("code") == 0, 
                "预下单应该成功");
        log.info("预下单成功，响应: {}", preOrderResponse);
        
        // === 步骤3: 通过/order/place-order接口生成订单，payType入参2 ===
        log.info("步骤3: 生成订单");
        String orderNo = aiSaasService.placeOrder(h5Token, itemId, payType);
        Assert.assertNotNull(orderNo, "订单号不能为空");
        log.info("生成订单成功，订单号: {}", orderNo);
        
        // === 步骤4: 通过/order/pay接口，进行支付，payType入参2 ===
        log.info("步骤4: 订单支付");
        String payResponse = aiSaasService.orderPay(h5Token, orderNo, payType);
        Assert.assertNotNull(payResponse, "支付响应不能为空");
        
        JSONObject payJson = JSON.parseObject(payResponse);
        Assert.assertTrue(payJson.getInteger("code") == 200 || payJson.getInteger("code") == 0, 
                "支付应该成功");
        log.info("支付成功，响应: {}", payResponse);
        
        // === 步骤5: 查看/order/list接口，获取接口Response的orderNo值 ===
        log.info("步骤5: 查看订单列表");
        String orderListResponse = aiSaasService.getOrderList(h5Token);
        Assert.assertNotNull(orderListResponse, "订单列表响应不能为空");
        
        JSONObject orderListJson = JSON.parseObject(orderListResponse);
        Assert.assertTrue(orderListJson.getInteger("code") == 200 || orderListJson.getInteger("code") == 0, 
                "订单列表查询应该成功");
        
        String latestOrderNo = aiSaasService.extractLatestOrderNo(orderListResponse);
        Assert.assertNotNull(latestOrderNo, "最新订单号不能为空");
        log.info("订单列表查询成功，最新订单号: {}", latestOrderNo);
        
        // === 步骤6: 调用login登录接口 ===
        log.info("步骤6: 管理后台登录");
        String adminToken = saasOrder.login();
        Assert.assertNotNull(adminToken, "管理员登录token不能为空");
        log.info("管理后台登录成功，token: {}", adminToken);
        
        // === 步骤7: 调用/order/listAll接口，orderNo入参使用orderNo值，同时获取response下data的list下面的orderItemVOS的price值 ===
        log.info("步骤7: 查询订单详情(不访问数据源)");
        String orderDetailStr = saasOrder.orderList(adminToken, latestOrderNo);
        Assert.assertNotNull(orderDetailStr, "订单详情响应不能为空");
        JSONObject orderDetailResponse = JSON.parseObject(orderDetailStr);
        Assert.assertTrue(
                (orderDetailResponse.getInteger("code") != null && (orderDetailResponse.getInteger("code") == 200 || orderDetailResponse.getInteger("code") == 0))
                        || "200".equals(orderDetailResponse.getString("status")),
                "订单详情查询应该成功");
        
        Long orderId = aiOrderService.extractOrderId(orderDetailResponse);
        Assert.assertNotNull(orderId, "orderId不能为空");
        
        Double price = aiOrderService.extractOrderItemPrice(orderDetailResponse);
        Assert.assertNotNull(price, "价格不能为空");
        log.info("订单详情查询成功，orderId: {}, price: {}", orderId, price);
        // 在执行步骤8之前等待2秒，确保后端配送数据已就绪
        log.info("准备执行步骤8，等待5秒以保证后端数据就绪");
        Thread.sleep(5000);
        // === 步骤8: 调用/order/query/delivery-details接口，入参orderId等于上个接口获取的orderId值，同时获取response下data的waitDeliveryItemList的0下面的orderItemId值 ===
        log.info("步骤8: 查询配送明细(不访问数据源)");
        String deliveryDetailsStr = saasOrder.queryDeliveryDetailsNoDB(adminToken, orderId);
        Assert.assertNotNull(deliveryDetailsStr, "配送明细响应不能为空");
        JSONObject deliveryDetailsResponse = JSON.parseObject(deliveryDetailsStr);
        Assert.assertTrue(
                (deliveryDetailsResponse.getInteger("code") != null && (deliveryDetailsResponse.getInteger("code") == 200 || deliveryDetailsResponse.getInteger("code") == 0))
                        || "200".equals(deliveryDetailsResponse.getString("status")),
                "配送明细查询应该成功");
        Long orderItemId = aiOrderService.extractOrderItemId(deliveryDetailsResponse);
        Assert.assertNotNull(orderItemId, "orderItemId不能为空");
        log.info("配送明细查询成功，orderItemId: {}", orderItemId);
        
        // === 步骤9: 调用/order/upsert/order-delivery接口，入参orderId等于上个接口入参的orderId，orderItemId等上个接口获取的orderItemId值 ===
        log.info("步骤9: 无仓订单立即配送(不访问数据源)");
        String orderDeliveryStr = saasOrder.orderDeliveryNoDB(adminToken, orderId, orderItemId);
        Assert.assertNotNull(orderDeliveryStr, "无仓订单立即配送响应不能为空");
        JSONObject orderDeliveryResponse = JSON.parseObject(orderDeliveryStr);
        Assert.assertTrue(
                (orderDeliveryResponse.getInteger("code") != null && (orderDeliveryResponse.getInteger("code") == 200 || orderDeliveryResponse.getInteger("code") == 0))
                        || "200".equals(orderDeliveryResponse.getString("status")),
                "无仓订单立即配送应该成功");
        log.info("无仓订单立即配送成功，响应: {}", orderDeliveryResponse);
         
         // === 步骤10: 售后申请 ===
         log.info("步骤10: 售后申请(不访问数据源)");
         Integer afterSaleAmount = 1;
         Double applyPrice = price;
         String proofPicture = "test/uwiqwy8fpvhz4h3i.jpg";
         Integer afterSaleType = 0;
         Integer serviceType = 1;
         
        String afterSaleStr = saasOrder.afterSaleAddNoDB(adminToken, orderItemId, afterSaleAmount,
                applyPrice, proofPicture, afterSaleType, serviceType);
        Assert.assertNotNull(afterSaleStr, "售后申请响应不能为空");
        JSONObject afterSaleResponse = JSON.parseObject(afterSaleStr);
        Assert.assertTrue(
                (afterSaleResponse.getInteger("code") != null && (afterSaleResponse.getInteger("code") == 200 || afterSaleResponse.getInteger("code") == 0))
                        || "200".equals(afterSaleResponse.getString("status")),
                "售后申请应该成功");
        log.info("售后申请成功，响应: {}", afterSaleResponse);
        
        // === 步骤11: 调用/order/after/sale/add接口，入参orderItemId等于上个接口获取的orderItemId值，入参amount等于1，入参applyPrice等于上面接口获取的price，入参proofPicture等于test/uwiqwy8fpvhz4h3i.jpg，入参afterSaleType等于0，入参serviceType等于3 ===
        log.info("步骤11: 售后申请(服务类型3，不访问数据源)");
        Integer afterSaleAmount2 = 1;
        Double applyPrice2 = price;
        String proofPicture2 = "test/uwiqwy8fpvhz4h3i.jpg";
        Integer afterSaleType2 = 0;
        Integer serviceType2 = 3;
        
        String afterSaleStr2 = saasOrder.afterSaleAddNoDB(adminToken, orderItemId, afterSaleAmount2,
                applyPrice2, proofPicture2, afterSaleType2, serviceType2);
        Assert.assertNotNull(afterSaleStr2, "步骤11-售后申请响应不能为空");
        JSONObject afterSaleResponse2 = JSON.parseObject(afterSaleStr2);
        Assert.assertTrue(
                (afterSaleResponse2.getInteger("code") != null && (afterSaleResponse2.getInteger("code") == 200 || afterSaleResponse2.getInteger("code") == 0))
                        || "200".equals(afterSaleResponse2.getString("status")),
                "步骤11-售后申请应该成功");
        log.info("步骤11-售后申请成功，响应: {}", afterSaleResponse2);
        
        // === 步骤12: 调用/order/after/sale/add接口，入参orderItemId等于上个接口获取的orderItemId值，入参amount等于1，入参applyPrice等于0，入参proofPicture等于test/uwiqwy8fpvhz4h3i.jpg，入参afterSaleType等于0，入参serviceType等于6 ===
        log.info("步骤12: 售后申请(服务类型6，不访问数据源)");
        Integer afterSaleAmount3 = 1;
        Double applyPrice3 = 0D;
        String proofPicture3 = "test/uwiqwy8fpvhz4h3i.jpg";
        Integer afterSaleType3 = 0;
        Integer serviceType3 = 6;
        
        String afterSaleStr3 = saasOrder.afterSaleAddNoDB(adminToken, orderItemId, afterSaleAmount3,
                applyPrice3, proofPicture3, afterSaleType3, serviceType3);
        Assert.assertNotNull(afterSaleStr3, "步骤12-售后申请响应不能为空");
        JSONObject afterSaleResponse3 = JSON.parseObject(afterSaleStr3);
        Assert.assertTrue(
                (afterSaleResponse3.getInteger("code") != null && (afterSaleResponse3.getInteger("code") == 200 || afterSaleResponse3.getInteger("code") == 0))
                        || "200".equals(afterSaleResponse3.getString("status")),
                "步骤12-售后申请应该成功");
        log.info("步骤12-售后申请成功，响应: {}", afterSaleResponse3);
        
        // === 商城下单后台申请售后完整流程结束 ===
        log.info("=== 商城下单后台申请售后完整流程测试成功完成！===");
        log.info("测试结果汇总:");
        log.info("- H5用户手机号: {}", testPhone);
        log.info("- 商品ID: {}", itemId);
        log.info("- 商品数量: {}", amount);
        log.info("- 支付类型: {}", payType);
        log.info("- 订单号: {}", orderNo);
        log.info("- 订单ID: {}", orderId);
        log.info("- 商品价格: {}", price);
        log.info("- 订单项ID: {}", orderItemId);
        log.info("- 售后申请价格: {}", applyPrice);
        log.info("- 售后申请数量: {}", afterSaleAmount);
    }

}
