package com.xianmu.atp.cases.saas;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.saas.OrderDao;
import com.xianmu.atp.generic.saas.SaasOrder;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.Test;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
@Epic("SaaS")
@Feature("订单")
@Owner("qwx")
@Slf4j
public class SaasOrderTest extends BaseTest{

    @Resource
    private SaasOrder saasOrder;
    @Resource
    private OrderDao orderDao;

    @Value("https://qamall.cosfo.cn/")
    private String domain1;

    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    public String getQuantityRuleStatus(String token,String itemId)throws Exception{
        String quantityRuleBody = saasOrder.quantityRule(token, itemId);
        JSONObject quantityRuleJson = JSON.parseObject(quantityRuleBody);
        return quantityRuleJson.getString("status");
    }


    @Test(description = "saas无仓商品下单")
    public void order() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 49467;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":\"" + itemId + "\",\"amount\":1}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .timeout(3000)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);
    
        //生成订单
        String orderNos = saasOrder.placeOrder(token, itemId);
        
        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);
    
        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);
        
        // 验证支付响应
        Assert.assertNotNull(responses, "支付响应不能为空");
        JSONObject payResponse = JSON.parseObject(responses);
    }



    @Test(description = "saas自营仓商品下单")
    public void order1() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 49691;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":"+itemId+",\"amount\":2}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);

        //生成订单
        String orderNos = saasOrder.placeOrder1(token, itemId);
        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);

    }

    @Test(description = "saas代仓商品下单")
    public void order2() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 49692;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":"+itemId+",\"amount\":1}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);

        //生成订单
        String orderNos = saasOrder.placeOrder2(token, itemId);
        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);

    }



    @Test(description = "saas鲜沐直供商品下单")
    public void order3() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 51156;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":"+itemId+",\"amount\":1}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);

        //生成订单
        String orderNos = saasOrder.placeOrder3(token, itemId);
        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        Assert.assertEquals(JSON.parseObject(responses).getString("code"),"200");
        System.out.println(responses);


    }


    @Test(description = "saas组合包下单")
    public void order4() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        long itemId = 49891;
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":"+itemId+",\"amount\":1,itemType:2}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);

        //生成订单
        String orderNos = saasOrder.placeOrder4(token, itemId);
        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        Assert.assertEquals(JSON.parseObject(responses).getString("code"),"200");
        System.out.println(responses);


    }



    @Test(description = "账期应收-查看明细")
    public void storeBill() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String url = domain + "storeBill/order/detail?billId=4268";
        HttpResponse response = HttpRequest.get(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        System.out.println(response);
        String res = response.body();
        Assert.assertEquals(JSON.parseObject(res).getString("code"),"200");
    }



    @Test(description = "校验流程设置")
    public void scheme() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String list = saasOrder.schemeList(token);
        System.out.println(list);
        String detail = saasOrder.schemeDetail(token);
        System.out.println(detail);
        String update = saasOrder.schemeUpdate(token);
        System.out.println(update);

    }

    @Test(description = "代下单-再来一单")
    public void agentAgain() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);

        //查询列表
        String responsesList = saasOrder.agentOrderList(token);
        System.out.println(responsesList);
        //获取返回agentOrderNo
        JSONObject jsonObject = JSON.parseObject(responsesList);
        String data = jsonObject.getString("data");
        JSONObject dataObject = JSON.parseObject(data);
        String list = dataObject.getString("list");
        JSONArray jsonArray = JSON.parseArray(list);
        JSONObject json = jsonArray.getJSONObject(0);
        String agentOrderNo = json.getString("agentOrderNo");
        System.out.println(agentOrderNo);
        //再来一单
        String responsesList1 = saasOrder.agentAgain(token, agentOrderNo);
        System.out.println(responsesList1);

    }

    @Test(description = "代下单-一键提醒")
    public void agentNotifyAll() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //一键提醒
        String responsesList1 = saasOrder.agentNotifyAll(token);
        System.out.println(responsesList1);
    }


    @Test(description = "订单改单")
    public void changeOrder() throws Exception {
        String token = saasOrder.h5LoginGet("13429645097");
        System.out.println(token);
        String token1 = saasOrder.login();
        System.out.println(token1);
        long itemId = 49691;
        String status = getQuantityRuleStatus(token,itemId+"");
        if(!"200".equals(status)) {
            System.out.println("起订量检测失败！");
            return;
        }
        //预下单
        String url = domain1 + "order/pre-order";
        String requestBody = "{\"orderItemDTOS\":[{\"itemId\":"+itemId+",\"amount\":2}]}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        System.out.println(response);
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);

        //生成订单
        String orderNos = saasOrder.placeOrder1(token, itemId);
        System.out.println(orderNos);
        // 直接从placeOrder返回的响应中验证订单号
        Assert.assertNotNull(orderNos, "订单生成失败，placeOrder返回为空");
        Assert.assertFalse(orderNos.trim().isEmpty(), "订单号不能为空字符串");
        System.out.println("生成的订单号: " + orderNos);

        //订单进行支付
        String responses = saasOrder.orderPay(token, orderNos);
        System.out.println(responses);

        //商城订单列表
        String responses1 = saasOrder.mallOrderList(token);
        System.out.println(responses1);
        //获取orderNo
        JSONObject jsonObject = JSON.parseObject(responses1);
        String data = jsonObject.getString("data");
        JSONArray jsonArray = JSON.parseArray(data);
        JSONObject json = jsonArray.getJSONObject(0);
        String orderNo1 = json.getString("orderNo");
        //后台订单列表,获取orderId、orderItemId
        String responsesList = saasOrder.orderList(token1,orderNo1);
        JSONObject jsonObject1 = JSON.parseObject(responsesList);
        String data1 = jsonObject1.getString("data");
        JSONObject dataObject = JSON.parseObject(data1);
        String list1 = dataObject.getString("list");
        JSONArray jsonArray1 = JSON.parseArray(list1);
        JSONObject json2 = jsonArray1.getJSONObject(0);
        String orderId = json2.getString("orderId");
        System.out.println(orderId);
        //获取orderItemId
        String list2 = json2.getString("orderItemVOS");
        JSONArray jsonArray2 = JSON.parseArray(list2);
        JSONObject json3 = jsonArray2.getJSONObject(0);
        String orderItemId = json3.getString("id");
        System.out.println(orderItemId);

        //改单
        String changeList = saasOrder.changeOrder(token1,orderId,orderItemId);
        System.out.println(changeList);
    }


}