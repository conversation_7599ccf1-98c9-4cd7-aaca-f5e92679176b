package com.xianmu.atp.cases.saas.aiCase;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * AI SaaS接口测试数据类
 * 管理测试用例中使用的各种测试数据
 * 
 * <AUTHOR> Test Engineer
 * @date 2025/08/11
 */
public class AiSaasTestData {

    /**
     * 获取AI SaaS基础URL
     * @return 基础URL
     */
    public static String getAiSaasBaseUrl() {
        return "https://qamanage.cosfo.cn/";
    }



    /**
     * 非现金子账户修改测试数据
     */
    @Data
    @Accessors(chain = true)
    public static class TenantFundAccountData {
        /** 账户ID */
        private Long id = 1L;
        /** 账户名称 */
        private String accountName;
        
        /**
         * 获取默认测试数据
         */
        public static TenantFundAccountData getDefault() {
            return new TenantFundAccountData()
                    .setAccountName("自动化测试账户_" + System.currentTimeMillis());
        }
        
        /**
         * 获取无效账户ID的测试数据
         */
        public static TenantFundAccountData getInvalidId() {
            return new TenantFundAccountData()
                    .setId(-1L)
                    .setAccountName("异常测试账户");
        }
        
        /**
         * 获取空账户名的测试数据
         */
        public static TenantFundAccountData getEmptyName() {
            return new TenantFundAccountData()
                    .setAccountName("");
        }
    }

    /**
     * 交易流水查询测试数据
     */
    @Data
    @Accessors(chain = true)
    public static class BillListData {
        /** 页码 */
        private Integer pageNum = 1;
        /** 页面大小 */
        private Integer pageSize = 10;
        /** 订单编号 */
        private String recordNo;
        /** 交易类型 */
        private String type;
        /** 开始时间 */
        private String startTime;
        /** 结束时间 */
        private String endTime;
        
        /**
         * 获取默认测试数据
         */
        public static BillListData getDefault() {
            return new BillListData();
        }
        
        /**
         * 获取分页测试数据
         */
        public static BillListData getPagination(Integer pageNum, Integer pageSize) {
            return new BillListData()
                    .setPageNum(pageNum)
                    .setPageSize(pageSize);
        }
        
        /**
         * 获取指定订单号的测试数据
         */
        public static BillListData getByOrderNo(String orderNo) {
            return new BillListData()
                    .setRecordNo(orderNo);
        }
        
        /**
         * 获取时间范围查询的测试数据
         */
        public static BillListData getByTimeRange(String startTime, String endTime) {
            return new BillListData()
                    .setStartTime(startTime)
                    .setEndTime(endTime);
        }
    }

    /**
     * 线下支付下单流程测试数据
     */
    @Data
    @Accessors(chain = true)
    public static class OfflinePaymentData {
        /** 商品ID */
        private Long itemId = 49467L;
        /** 商品数量 */
        private Integer amount = 1;
        /** 支付类型（6-线下支付） */
        private Integer payType = 6;
        
        /**
         * 获取默认测试数据
         */
        public static OfflinePaymentData getDefault() {
            return new OfflinePaymentData();
        }
        
        /**
         * 获取多商品测试数据
         */
        public static OfflinePaymentData getMultipleItems() {
            return new OfflinePaymentData()
                    .setAmount(3);
        }
        
        /**
         * 获取其他支付方式测试数据
         */
        public static OfflinePaymentData getOnlinePayment() {
            return new OfflinePaymentData()
                    .setPayType(1); // 在线支付
        }
    }

    /**
     * 常用测试商品数据
     */
    public static class TestItems {
        /** 标准测试商品ID */
        public static final Long STANDARD_ITEM_ID = 49467L;
        /** 高价商品ID */
        public static final Long HIGH_PRICE_ITEM_ID = 49468L;
        /** 低价商品ID */
        public static final Long LOW_PRICE_ITEM_ID = 49466L;
        /** 无效商品ID */
        public static final Long INVALID_ITEM_ID = -1L;
    }

    /**
     * 常用测试用户数据
     */
    public static class TestUsers {
        /** H5登录测试手机号 */
        public static final String H5_LOGIN_PHONE = "13900099909";
        /** 管理员登录手机号 */
        public static final String ADMIN_LOGIN_PHONE = "13429645111";
        /** 测试联系人ID */
        public static final Integer TEST_CONTACT_ID = 350821;
    }

    /**
     * 常用测试门店数据
     */
    public static class TestStores {
        /** 标准测试门店ID */
        public static final Long STANDARD_STORE_ID = 1L;
        /** 大型门店ID */
        public static final Long LARGE_STORE_ID = 2L;
        /** 小型门店ID */
        public static final Long SMALL_STORE_ID = 3L;
        /** 无效门店ID */
        public static final Long INVALID_STORE_ID = -1L;
    }

    /**
     * 支付类型常量
     */
    public static class PayTypes {
        /** 在线支付 */
        public static final Integer ONLINE_PAY = 1;
        /** 货到付款 */
        public static final Integer COD_PAY = 2;
        /** 月结支付 */
        public static final Integer MONTHLY_PAY = 3;
        /** 线下支付 */
        public static final Integer OFFLINE_PAY = 6;
    }

    /**
     * 下单方式常量
     */
    public static class PlanTypes {
        /** 手动下单 */
        public static final String MANUAL = "MANUAL";
        /** 自动下单 */
        public static final String AUTO = "AUTO";
        /** 定时下单 */
        public static final String SCHEDULED = "SCHEDULED";
    }

    /**
     * 响应状态码常量
     */
    public static class ResponseCodes {
        /** 成功 */
        public static final Integer SUCCESS = 200;
        /** 成功（备用） */
        public static final Integer SUCCESS_ALT = 0;
        /** 参数错误 */
        public static final Integer PARAM_ERROR = 400;
        /** 未授权 */
        public static final Integer UNAUTHORIZED = 401;
        /** 禁止访问 */
        public static final Integer FORBIDDEN = 403;
        /** 资源不存在 */
        public static final Integer NOT_FOUND = 404;
        /** 服务器错误 */
        public static final Integer SERVER_ERROR = 500;
    }

    /**
     * 测试环境配置
     */
    @Data
    @Accessors(chain = true)
    public static class TestConfig {
        /** 请求超时时间（毫秒） */
        private Integer timeout = 3000;
        /** 重试次数 */
        private Integer retryCount = 3;
        /** 重试间隔（毫秒） */
        private Integer retryInterval = 1000;
        /** 是否启用详细日志 */
        private Boolean verboseLogging = true;
        
        /**
         * 获取默认配置
         */
        public static TestConfig getDefault() {
            return new TestConfig();
        }
        
        /**
         * 获取快速测试配置
         */
        public static TestConfig getFast() {
            return new TestConfig()
                    .setTimeout(1000)
                    .setRetryCount(1)
                    .setVerboseLogging(false);
        }
        
        /**
         * 获取稳定测试配置
         */
        public static TestConfig getStable() {
            return new TestConfig()
                    .setTimeout(5000)
                    .setRetryCount(5)
                    .setRetryInterval(2000);
        }
    }
}