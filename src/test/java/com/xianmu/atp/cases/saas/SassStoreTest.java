package com.xianmu.atp.cases.saas;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import com.xianmu.atp.generic.saas.SaasOrder;
import com.xianmu.atp.generic.saas.SassStore;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Epic("SaaS")
@Feature("门店")
@Owner("qwx")
@Slf4j
public class SassStoreTest extends BaseTest {

    @Resource
    private SassStore sassStore;

    @Resource
    private SaasOrder saasOrder;

    @Test(description = "saas门店新增")
    public void save() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //新增门店
        String responses = sassStore.save(token);
        System.out.println(responses);
    }

    @Test(description = "saas门店列表")
    public void list() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //门店列表
        String responses = sassStore.listAll(token);
        System.out.println(responses);
    }

    @Test(description = "saas门店详情")
    public void detail() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responsesList = sassStore.listAll(token);
        String id = NumberUtil.getListFirstId(responsesList);
        //门店详情
        String responses = sassStore.detail(token, id);
        System.out.println(responses);
    }

    @Test(description = "saas门店修改")
    public void update() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responsesList = sassStore.listAll(token);
        String id = NumberUtil.getListFirstId(responsesList);
        String storeDetail = sassStore.detail(token, id);
        //修改门店
        JSONObject storeJson = JSON.parseObject(storeDetail);
        String responses = sassStore.update(token, storeJson.getString("data"));
        System.out.println(responses);
    }

    @Test(description = "saas门店调整余额")
    public void adjustBalance() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responsesList = sassStore.listAll(token);
        String id = NumberUtil.getListFirstId(responsesList);
        //根据门店id调整余额
        String responses = sassStore.adjustBalance(token, id);
        System.out.println(responses);
    }

    @Test(description = "saas门店删除联系人")
    public void deleteContact() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responsesList = sassStore.listAll(token);
        String id = NumberUtil.getListFirstId(responsesList);
        //门店详情
        JSONObject storeJson = JSON.parseObject(sassStore.detail(token, id));
        //根据门店id删除联系人
        if (storeJson != null) {
            String list = JSON.parseObject(storeJson.get("data").toString()).getString("contactList");
            JSONArray jsonArray = JSON.parseArray(list);
            if(!jsonArray.isEmpty() && jsonArray.size() > 1){
                JSONObject contactJson = jsonArray.getJSONObject(1);
                String responses = sassStore.deleteContact(token, contactJson.getString("contactId"), id);
                System.out.println(responses);
            }

        }
    }

    @Test(description = "saas门店删除员工")
    public void deleteAccount() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responsesList = sassStore.listAll(token);
        String id = NumberUtil.getListFirstId(responsesList);
        //门店详情
        JSONObject storeJson = JSON.parseObject(sassStore.detail(token, id));
        //根据id删除员工
        if (storeJson != null) {
            String list = JSON.parseObject(storeJson.get("data").toString()).getString("accountList");
            JSONArray jsonArray = JSON.parseArray(list);
            if(!jsonArray.isEmpty() && jsonArray.size() > 1){
                JSONObject accountJson = jsonArray.getJSONObject(1);
                String responses = sassStore.deleteAccount(token, accountJson.getString("id"));
                System.out.println(responses);
            }

        }
    }

    @Test(description = "saas门店分组可关联的门店列表")
    public void listAllDefault() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //门店列表
        String responses = sassStore.listAllDefault(token);
        System.out.println(responses);
    }

    @Test(description = "saas门店新增分组")
    public void addStoreGroup() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //获取可关联的门店列表
        String responsesList = sassStore.listAllDefault(token);
        String id = NumberUtil.getListFirstId(responsesList);
        if(id != null){
            //新增分组，并关联列表第一个门店
            sassStore.addStoreGroup(token, id);
        }
    }

    @Test(description = "saas门店分组列表")
    public void groupList() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //门店分组列表
        String responses = sassStore.groupList(token);
        System.out.println(responses);
    }

    @Test(description = "saas门店列表-根据分组id查询")
    public void storeListByGroupId() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //门店分组列表
        String responsesList = sassStore.groupList(token);
        String id = NumberUtil.getListIdByNum(responsesList, 2);
        if(id != null){
            String responses = sassStore.storeListByGroupId(token, id);
            System.out.println(responses);
        }
    }

    @Test(description = "saas门店分组编辑")
    public void groupUpdate() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //门店分组列表
        String responsesList = sassStore.groupList(token);
        String id = NumberUtil.getListIdByNum(responsesList, 2);
        if(id != null){
            //获取可关联的门店列表
            String storeList = sassStore.listAllDefault(token);
            String storeId = NumberUtil.getListFirstId(storeList);
            if(storeId != null){
                long [] storeIds = {Long.parseLong(storeId)};
                sassStore.updateStoreGroup(token, storeIds, NumberUtil.getNameRandom("编辑分组名称"), id);
            }
        }
    }

    @Test(description = "saas门店分组删除")
    public void groupDel() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        //门店分组列表
        String responsesList = sassStore.groupList(token);
        String id = NumberUtil.getListIdByNum(responsesList, 2);
        if(id != null){
            sassStore.delStoreGroup(token, id);
        }
    }



}
