package com.xianmu.atp.cases.saas.tools;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
public class TimeUtil {

    /**
     * 获取当前时间变动月数时间
     * @param month
     * @return
     */
    public static String getTimePeriodByMonth(int month){
        LocalDateTime localDateTime = LocalDateTime.now();
        localDateTime = localDateTime.plusMonths(month);
        return localDateTime.toLocalDate().toString();
    }

    /**
     * 获取当前时间变动天数时间
     * @param day
     * @return
     */
    public static String getTimePeriodByDay(int day){
        LocalDateTime localDateTime = LocalDateTime.now();
        localDateTime = localDateTime.plusDays(day);
        return localDateTime.toLocalDate().toString();
    }
}
