package com.xianmu.atp.cases.saas.aiCase;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.saas.AiOrderService;
import com.xianmu.atp.generic.saas.AiSaasService;
import com.xianmu.atp.generic.saas.SaasOrder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

/**
 * 用例ID: TC_API_SAAS_SELF_LIFTING_001
 * 用例名称: 商城下单-后台查询自提信息-订单自提完整业务流程
 * 优先级: P0
 * 测试类型: 功能测试
 * Author: AI Test Engineer
 * 接口名称: /order/query/self-lifting, /order/self-lifting
 * 请求方法: GET, POST
 * 协议版本: HTTP/1.1
 * 
 * 前置条件:
 * - H5用户可正常登录
 * - 指定商品可正常下单并支付
 * 
 * 测试步骤:
 * 1. 调用h5LoginGet登录接口
 * 2. 通过/order/pre-order预下单接口，itemId=51156，amount=3
 * 3. 通过/order/place-order接口生成订单，payType=2
 * 4. 通过/order/pay接口进行支付，payType=2
 * 5. 查看/order/list接口，获取最新订单的orderNo
 * 6. 调用login登录接口(管理后台)
 * 7. 调用/order/query/self-lifting接口，orderNo=上一步获取的orderNo
 * 8. 调用/order/self-lifting接口，提交自提：address=浙江省杭州市西湖区龙章路6号，expectTime=当天23:00:00，warehouseNo=1
 * 
 * 预期结果:
 * - 预下单、下单、支付、查询订单列表成功(code为200或0)
 * - 查询自提信息成功(code为200或0)，响应体包含data
 * - 提交自提成功(code为200或0)
 */
@Slf4j
public class AiSelfLiftingTest extends BaseTest {

    @Resource
    private SaasOrder saasOrder;

    @Resource
    private AiSaasService aiSaasService;

    @Resource
    private AiOrderService aiOrderService;

    private static final DateTimeFormatter DF_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private String todayAt230000() {
        return LocalDate.now().format(DF_DATE) + " 23:00:00";
    }

    /**
     * 工具方法：创建已支付订单并返回订单号
     */
    private String createPaidOrderAndGetOrderNo(Long itemId, int amount, int payType, String phone) throws Exception {
        // 步骤1：H5登录
        String h5Token = saasOrder.h5LoginGet(phone);
        Assert.assertNotNull(h5Token, "H5登录token不能为空");

        // 步骤2：预下单（amount 固定为 4）
        String preOrderResp = aiSaasService.preOrder(h5Token, itemId, 4);
        Assert.assertNotNull(preOrderResp, "预下单响应不能为空");
        JSONObject preJson = JSON.parseObject(preOrderResp);
        Assert.assertTrue(preJson.getInteger("code") == 200 || preJson.getInteger("code") == 0, "预下单应成功");

        // 步骤3：下单（amount 固定为 4）
        String orderNo = aiSaasService.placeOrder(h5Token, itemId, payType, 4);
        Assert.assertNotNull(orderNo, "下单返回的订单号不能为空");

        // 步骤4：支付
        String payResp = aiSaasService.orderPay(h5Token, orderNo, payType);
        Assert.assertNotNull(payResp, "支付响应不能为空");
        JSONObject payJson = JSON.parseObject(payResp);
        Assert.assertTrue(payJson.getInteger("code") == 200 || payJson.getInteger("code") == 0, "支付应成功");

        // 步骤5：查询订单列表，校验能拿到最新订单号
        String orderListResp = aiSaasService.getOrderList(h5Token);
        Assert.assertNotNull(orderListResp, "订单列表响应不能为空");
        JSONObject listJson = JSON.parseObject(orderListResp);
        Assert.assertTrue(listJson.getInteger("code") == 200 || listJson.getInteger("code") == 0, "订单列表查询应成功");
        String latestOrderNo = aiSaasService.extractLatestOrderNo(orderListResp);
        Assert.assertNotNull(latestOrderNo, "最新订单号不能为空");
        // 返回订单列表中的最新订单号，严格符合第5步要求
        return latestOrderNo;
    }

    @Test(description = "P0_商城下单->后台查询自提信息->订单自提_完整流程验证")
    public void testOrderSelfLiftingHappyPath() throws Exception {
        // 测试数据
        Long itemId = 51156L; // 指定商品
        int amount = 4;  // 统一改为 4
        int payType = 2;
        String phone = AiSaasTestData.TestUsers.H5_LOGIN_PHONE;
        String address = "浙江省杭州市西湖区龙章路6号";
        String expectTime = todayAt230000();
        Integer warehouseNo = 1;

        // 1-5：创建已支付订单并拿到orderNo
        String orderNo = createPaidOrderAndGetOrderNo(itemId, amount, payType, phone);
        Assert.assertNotNull(orderNo, "订单号不能为空");

        // 6：管理后台登录
        String adminToken = saasOrder.login();
        Assert.assertNotNull(adminToken, "管理后台登录token不能为空");

        // 7：查询自提信息
        JSONObject querySelfLiftResp = aiOrderService.querySelfLifting(adminToken, orderNo);
        Integer qCode = querySelfLiftResp.getInteger("code");
        String qStatus = querySelfLiftResp.getString("status");
        Assert.assertTrue((qCode != null && (qCode == 200 || qCode == 0)) || "200".equals(qStatus), "查询自提信息应成功");

        // 8：提交自提
        JSONObject selfLiftResp = aiOrderService.selfLifting(adminToken, orderNo, address, expectTime, warehouseNo);
        Integer sCode = selfLiftResp.getInteger("code");
        String sStatus = selfLiftResp.getString("status");
        Assert.assertTrue((sCode != null && (sCode == 200 || sCode == 0)) || "200".equals(sStatus), "提交自提应成功");

        // 步骤9：调用 /tenant/user/query/loginV2
        // 用例ID: TC_API_SAAS_LOGINV2_009
        // 用例名称: 管理后台loginV2正常登录

        String loginV2Url = "https://qaboss.cosfo.cn/tenant/user/query/loginV2";
        String loginV2Body = "{\"phone\":\"18224535932\",\"password\":\"Hello1234.\",\"tenantId\":0}";
        HttpResponse loginV2Resp = HttpRequest.post(loginV2Url)
                .header(Header.CONTENT_TYPE, "application/json")
                .timeout(3000)
                .body(loginV2Body)
                .execute();
        String loginV2Str = loginV2Resp.body();
        JSONObject loginV2Json = JSON.parseObject(loginV2Str);
        Integer lv2Code = loginV2Json.getInteger("code");
        String lv2Status = loginV2Json.getString("status");
        Assert.assertTrue((lv2Code != null && (lv2Code == 200 || lv2Code == 0)) || "200".equals(lv2Status), "loginV2 应成功");
        String bossToken = loginV2Json.getJSONObject("data") != null ? loginV2Json.getJSONObject("data").getString("token") : null;
        Assert.assertNotNull(bossToken, "loginV2 返回token不能为空");
    
        // 步骤10：调用 /order/listAll/1/10，按订单号查询并获取 orderId
        // 用例ID: TC_API_SAAS_LISTALL_010
        // 用例名称: 订单列表(分页路径版)按订单号查询

        String listAllUrl = "https://qaboss.cosfo.cn/order/listAll/1/10?orderNo=" + orderNo;
        HttpResponse listAllResp = HttpRequest.get(listAllUrl)
                .header("token", bossToken)
                .timeout(3000)
                .execute();
        String listAllStr = listAllResp.body();
        JSONObject listAllJson = JSON.parseObject(listAllStr);
        Integer laCode = listAllJson.getInteger("code");
        String laStatus = listAllJson.getString("status");
        Assert.assertTrue((laCode != null && (laCode == 200 || laCode == 0)) || "200".equals(laStatus), "订单列表查询 应成功");
        Assert.assertNotNull(listAllJson.getJSONObject("data"), "订单列表 data 不能为空");
        Assert.assertTrue(
                listAllJson.getJSONObject("data").getJSONArray("list") != null &&
                listAllJson.getJSONObject("data").getJSONArray("list").size() > 0,
                "订单列表 list 应非空");
        Long orderId = listAllJson.getJSONObject("data").getJSONArray("list").getJSONObject(0).getLong("orderId");
        Assert.assertNotNull(orderId, "未获取到 orderId");
    
        // 步骤11：调用 /order/detail，按 orderId 查询并获取首个订单项 id
        // 用例ID: TC_API_SAAS_DETAIL_011
        // 用例名称: 订单详情查询并获取首个订单项ID

        String detailUrl = "https://qaboss.cosfo.cn/order/detail?orderId=" + orderId;
        HttpResponse detailResp = HttpRequest.get(detailUrl)
                .header("token", bossToken)
                .timeout(3000)
                .execute();
        String detailStr = detailResp.body();
        JSONObject detailJson = JSON.parseObject(detailStr);
        Integer dCode = detailJson.getInteger("code");
        String dStatus = detailJson.getString("status");
        Assert.assertTrue((dCode != null && (dCode == 200 || dCode == 0)) || "200".equals(dStatus), "订单详情查询 应成功");
        Assert.assertNotNull(detailJson.getJSONObject("data"), "订单详情 data 不能为空");
        Assert.assertTrue(
                detailJson.getJSONObject("data").getJSONArray("orderItemVOS") != null &&
                detailJson.getJSONObject("data").getJSONArray("orderItemVOS").size() > 0,
                "订单详情 orderItemVOS 应非空");
        Long orderItemId = detailJson.getJSONObject("data").getJSONArray("orderItemVOS").getJSONObject(0).getLong("id");
        Assert.assertNotNull(orderItemId, "未获取到 orderItemId");
    
        // 步骤12：调用 /order/after/sale/add，发起售后
        // 用例ID: TC_API_SAAS_AFTERSALE_ADD_012
        // 用例名称: boss发起售后-补发(服务类型5-补发/包装问题)

        String afterSaleUrl = "https://qaboss.cosfo.cn/order/after/sale/add";
        JSONObject afterBody = new JSONObject();
        afterBody.put("orderItemId", orderItemId);
        afterBody.put("afterSaleType", 0);
        afterBody.put("serviceType", 5);
        afterBody.put("reason", "包装问题");
        afterBody.put("userRemark", "11");
        afterBody.put("amount", 1);
        afterBody.put("applyPrice", 0);
        afterBody.put("proofPicture", "test/09zo5vx7ew2qkvckri.jpg");
        HttpResponse afterResp = HttpRequest.post(afterSaleUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", bossToken)
                .timeout(3000)
                .body(afterBody.toJSONString())
                .execute();
        String afterStr = afterResp.body();
        JSONObject afterJson = JSON.parseObject(afterStr);
        Integer aCode = afterJson.getInteger("code");
        String aStatus = afterJson.getString("status");
        Assert.assertTrue((aCode != null && (aCode == 200 || aCode == 0)) || "200".equals(aStatus), "售后申请 应成功");

        // 步骤13：调用 /order/after/sale/add（服务类型6）
        // 用例ID: TC_API_SAAS_AFTERSALE_ADD_013
        // 用例名称: boss发起售后-换货(服务类型6)

        String afterSaleUrl2 = "https://qaboss.cosfo.cn/order/after/sale/add";
        JSONObject afterBody2 = new JSONObject();
        afterBody2.put("orderItemId", orderItemId);
        afterBody2.put("afterSaleType", 0);
        afterBody2.put("serviceType", 6);
        afterBody2.put("reason", "包装问题");
        afterBody2.put("userRemark", "22");
        afterBody2.put("amount", 1);
        afterBody2.put("applyPrice", 0);
        afterBody2.put("proofPicture", "test/1l9fw64jwevkzuqj6.jpg");
        HttpResponse afterResp2 = HttpRequest.post(afterSaleUrl2)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", bossToken)
                .timeout(3000)
                .body(afterBody2.toJSONString())
                .execute();
        String afterStr2 = afterResp2.body();
        JSONObject afterJson2 = JSON.parseObject(afterStr2);
        Integer aCode2 = afterJson2.getInteger("code");
        String aStatus2 = afterJson2.getString("status");
        Assert.assertTrue((aCode2 != null && (aCode2 == 200 || aCode2 == 0)) || "200".equals(aStatus2), "售后申请(服务类型6) 应成功");
    
        // 步骤14：调用 /order/after/sale/add（服务类型3）
        // 用例ID: TC_API_SAAS_AFTERSALE_ADD_014
        // 用例名称: boss发起售后-退货退款(服务类型3)

        String afterSaleUrl3 = "https://qaboss.cosfo.cn/order/after/sale/add";
        JSONObject afterBody3 = new JSONObject();
        afterBody3.put("orderItemId", orderItemId);
        afterBody3.put("afterSaleType", 0);
        afterBody3.put("serviceType", 3);
        afterBody3.put("reason", "包装问题");
        afterBody3.put("userRemark", "1");
        afterBody3.put("amount", 1);
        afterBody3.put("applyPrice", 1);
        afterBody3.put("proofPicture", "test/8ayw66njikll0e4rv.jpg");
        HttpResponse afterResp3 = HttpRequest.post(afterSaleUrl3)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", bossToken)
                .timeout(3000)
                .body(afterBody3.toJSONString())
                .execute();
        String afterStr3 = afterResp3.body();
        JSONObject afterJson3 = JSON.parseObject(afterStr3);
        Integer aCode3 = afterJson3.getInteger("code");
        String aStatus3 = afterJson3.getString("status");
        Assert.assertTrue((aCode3 != null && (aCode3 == 200 || aCode3 == 0)) || "200".equals(aStatus3), "售后申请(服务类型3) 应成功");
    
        // 步骤15：调用 /order/after/sale/add（服务类型1，amount为字符串）
        // 用例名称: boss发起售后-退款(服务类型1，amount字符串类型覆盖)

        String afterSaleUrl4 = "https://qaboss.cosfo.cn/order/after/sale/add";
        JSONObject afterBody4 = new JSONObject();
        afterBody4.put("orderItemId", orderItemId);
        afterBody4.put("afterSaleType", 0);
        afterBody4.put("serviceType", 1);
        afterBody4.put("reason", "包装问题");
        afterBody4.put("userRemark", "44");
        afterBody4.put("amount", "222"); // 字符串类型覆盖
        afterBody4.put("applyPrice", 1);
        afterBody4.put("proofPicture", "test/v04tmt2yz0al0id17.jpg");
        HttpResponse afterResp4 = HttpRequest.post(afterSaleUrl4)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", bossToken)
                .timeout(3000)
                .body(afterBody4.toJSONString())
                .execute();
        String afterStr4 = afterResp4.body();
        JSONObject afterJson4 = JSON.parseObject(afterStr4);
        Integer aCode4 = afterJson4.getInteger("code");
        String aStatus4 = afterJson4.getString("status");
        Assert.assertTrue((aCode4 != null && (aCode4 == 200 || aCode4 == 0)) || "200".equals(aStatus4), "售后申请(服务类型1) 应成功");
    }

}