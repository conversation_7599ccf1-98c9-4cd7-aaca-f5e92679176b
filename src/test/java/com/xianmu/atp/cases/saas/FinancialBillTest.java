package com.xianmu.atp.cases.saas;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.saas.tools.NumberUtil;
import com.xianmu.atp.cases.saas.tools.TimeUtil;
import com.xianmu.atp.generic.saas.FinancialBill;
import com.xianmu.atp.generic.saas.SaasOrder;
import org.springframework.beans.factory.annotation.Value;
import org.testng.SkipException;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
public class FinancialBillTest extends BaseTest {

    @Resource
    private FinancialBill financialBill;

    @Resource
    private SaasOrder saasOrder;


    @Value("https://qamanage.cosfo.cn/")
    private String domain;

    @Test(description = "账期应收待收款列表-收款")
    public void confirm() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String createBillStartTime = TimeUtil.getTimePeriodByMonth(-3);
        String createBillEndTime = TimeUtil.getTimePeriodByMonth(0);

        //查询列表
        String responsesList = financialBill.list(token,createBillStartTime, createBillEndTime);
        System.out.println(responsesList);
        //获取返回billId
        JSONObject jsonObject = JSON.parseObject(responsesList);
        String data = jsonObject.getString("data");
        JSONObject dataObject = JSON.parseObject(data);
        String list = dataObject.getString("list");
        JSONArray jsonArray = JSON.parseArray(list);
        JSONObject json = jsonArray.getJSONObject(0);
        String billId = json.getString("billId");
        System.out.println("获取到的billId: " + billId);
        //收款上传凭证
        String responsesList1 = financialBill.addCredentials(token, billId);
        System.out.println(responsesList1);
        //确认收款
        String responsesList2 = financialBill.confirm(token, billId);
        System.out.println(responsesList2);

    }

    @Test(description = "订单/售后明细")
    public void detail() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String createBillStartTime = TimeUtil.getTimePeriodByMonth(-3);
        String createBillEndTime = TimeUtil.getTimePeriodByMonth(0);

        //查询列表
        String responsesList = financialBill.list(token,createBillStartTime, createBillEndTime);
        System.out.println(responsesList);
        //获取返回billId
        JSONObject jsonObject = JSON.parseObject(responsesList);
        String data = jsonObject.getString("data");
        JSONObject dataObject = JSON.parseObject(data);
        String list = dataObject.getString("list");
        JSONArray jsonArray = JSON.parseArray(list);
        JSONObject json = jsonArray.getJSONObject(0);
        String billId = json.getString("billId");
        System.out.println("获取到的billId: " + billId);
        //订单明细
        String responsesList1 = financialBill.detail(token, billId);
        System.out.println(responsesList1);
        //查看售后明细
        String responsesList2 = financialBill.orderAfterSaleDetail(token, billId);
        System.out.println(responsesList2);
    }

    @Test(description = "财务-账期应收-数据合计")
    public void storeBillTotalData() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = financialBill.storeBillTotalData(token);
        System.out.println(responses);
    }

    @Test(description = "结算明细-导出结算明细")
    public void exportSettlement() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = financialBill.exportSettlement(token);
        System.out.println(responses);
    }

    @Test(description = "查询分账明细-分账金额合计")
    public void queryProfitCount() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = financialBill.queryProfitCount(token);
        System.out.println(responses);
    }

    @Test(description = "查询结算明细金额合计")
    public void querySettlementCount() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = financialBill.querySettlementCount(token);
        System.out.println(responses);
    }


    @Test(description = "预付概率详情")
    public void prepaymentDetail() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responsesList = financialBill.prepaymentList(token);
        System.out.println(responsesList);
        String id = NumberUtil.getListFirstId(responsesList);
        System.out.println(id);
        String responses = financialBill.prepaymentDetail(token, id);
        System.out.println(responses);
    }


    @Test(description = "导出预付明细")
    public void prepaymentDownload() throws Exception {
        String token = saasOrder.login();
        System.out.println(token);
        String responses = financialBill.prepaymentDownload(token);
        System.out.println(responses);
    }


}
