package com.xianmu.atp.cases.saas.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Random;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
public class NumberUtil {

    /**
     * 获取当前时间戳最后几位
     * @param length
     * @return
     */
    public static String getTimeNumber(int length){
        long timestamp = System.currentTimeMillis();
        String timestampStr = String.valueOf(timestamp);
        int len = timestampStr.length();
        return timestampStr.substring(len - length, len);
    }

    /**
     * 根据时间戳+手机前几位，获取拼接随机手机号
     * @param phoneNum
     * @param length
     * @return
     */
    public static String getPhoneNumber(String phoneNum, int length){
        long timestamp = System.currentTimeMillis();
        String timestampStr = String.valueOf(timestamp);
        int len = timestampStr.length();
        return phoneNum + timestampStr.substring(len - length, len);
    }

    /**
     * 根据名称前缀，拼接时间戳后6位，组成随机名称
     * @param name
     * @return
     */
    public static String getNameRandom(String name){
        return name + NumberUtil.getTimeNumber(6);
    }

    /**
     * 获取返回结果集合中第一条数据的id
     * @param responsesList
     * @return
     * @throws Exception
     */
    public static String getListFirstId(String responsesList)throws Exception{
        JSONObject jsonObject = JSON.parseObject(responsesList);
        String data = jsonObject.getString("data");
        JSONObject dataObject = JSON.parseObject(data);
        String list = dataObject.getString("list");
        JSONArray jsonArray = JSON.parseArray(list);
        if(!jsonArray.isEmpty()){
            JSONObject json = jsonArray.getJSONObject(0);
            return json.getString("id");
        }
        return null;
    }

    /**
     * 获取返回结果集合中指定条数据的id
     * @param responsesList
     * @return
     * @throws Exception
     */
    public static String getListIdByNum(String responsesList, int num)throws Exception{
        JSONObject jsonObject = JSON.parseObject(responsesList);
        String data = jsonObject.getString("data");
        JSONObject dataObject = JSON.parseObject(data);
        String list = dataObject.getString("list");
        JSONArray jsonArray = JSON.parseArray(list);
        if(!jsonArray.isEmpty() && jsonArray.size() >= num){
            JSONObject json = jsonArray.getJSONObject(num - 1);
            return json.getString("id");
        }
        return null;
    }

    /**
     * 根据最小，最大数字，获取区间范围内随机数字
     * @param minInt
     * @param maxInt
     * @return
     * @throws Exception
     */
    public static int getRandomInt(int minInt, int maxInt)throws Exception{
        Random random = new Random();
        return random.nextInt(maxInt - minInt + 1) + minInt;
    }
}
