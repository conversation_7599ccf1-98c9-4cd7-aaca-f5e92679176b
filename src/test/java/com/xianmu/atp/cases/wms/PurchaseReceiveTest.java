package com.xianmu.atp.cases.wms;


import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.SkuInfoDao;
import com.xianmu.atp.dal.mapper.SkuInfoMapper;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.generic.supply.Purchase;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.CsvDataProvider;
import com.xianmu.atp.util.testng.XMListener;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;

@Epic("注解测试")
@Feature("采购入库")
@Owner("xianmu")
@Listeners({XMListener.class})
@Slf4j
public class PurchaseReceiveTest extends BaseTest {
    @Resource
    private Purchase purchase;
    @Resource
    private SkuInfoDao skuInfoDao;
    @Value("**********")
    private String sku;
    @Value("380")
    private String warehouseNo;
    @Value("5")
    private Integer quantity;

    @Resource
    private LoginHttp login;
    @Value("${app.manage-domain}")
    private String domain;
    @Value("${app.openapi_domain}")
    private String openapi_domain;
    @Value("${app.appKey}")
    private String appKey;
    @Value("${app.appSecret}")
    private String appSecret;
    @Story("采购入库")
    @Owner("xianmu")
    @Test(description = "测试用例", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void PurchaseReceive() {
        log.info("-------"+sku);
        String token = login.login();
        String createURL =domain +
                "/summerfarm-wms/in-store/query/detail";
        String thirdInboundURL = openapi_domain+"wms.inbound.operate";
        String supplierId ="4610";
        String purchaseNo = purchase.PurchaseCreate(sku,warehouseNo,quantity,supplierId);
        //查看入库任务
        String listBody = "{\n" +
                "\t\"dates\": [],\n" +
                "\t\"sourceId\": \""+purchaseNo+"\",\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 50,\n" +
                "\t\"queryType\": 0\n" +
                "}";
        String inStoreURL =domain+"/summerfarm-wms/in-store/query/list";
        HttpResponse taskResponse = HttpRequest.post(inStoreURL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token",token)
                .body(listBody)
                .execute();
        log.info(taskResponse.body());
        JSONObject dataObject = JSON.parseObject(JSON.parseObject(taskResponse.body()).getString("data"))   ;
        JSONObject taskObject  = dataObject.getJSONArray("list").getJSONObject(0);
        int taskId  = taskObject.getInteger("taskId");

        SkuInfo productInfo = skuInfoDao.getProduct(sku);
        String skuName = productInfo.getPdName();
        //三方入库回告
        LocalDate currentDate = LocalDate.now();
        LocalDate dateAfter30Days = currentDate.plusDays(30);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String qualityDate = dateAfter30Days.format(formatter);
        String productionDate = currentDate.format(formatter);
        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String inStoreTime = currentDateTime.format(formatter1);
        Random random = new Random();
        int randomNumber = random.nextInt(100000);
        String thirdBody ="{\n" +
                "  \"idempotentNo\": \""+randomNumber+"\",\n" +
                "  \"inboundTaskId\": \""+taskId+"\",\n" +
                "  \"operator\": \""+sku+"\",\n" +
                "  \"inWarehouseNo\": \""+warehouseNo+"\",\n"+
                "  \"inStoreTime\": \""+inStoreTime+"\",\n" +
                "  \"detailList\": [\n" +
                "     {\n" +
                "      \"skuCode\": \""+sku+"\",\n" +
                "      \"skuName\": \""+skuName+"\",\n" +
                "      \"batch\": \""+purchaseNo+"\",\n" +
                "      \"productionDate\": \""+productionDate+"\",\n" +
                "      \"qualityDate\": \""+qualityDate+"\",\n" +
                "      \"quantity\": "+quantity+"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        HttpResponse thirdResponse = HttpRequest.post(thirdInboundURL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("appKey",appKey)
                .header("appSecret",appSecret)
                .body(thirdBody)
                .execute();
        log.info(thirdResponse.body());
    }
}
