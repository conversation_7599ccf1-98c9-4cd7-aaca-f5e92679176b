package com.xianmu.atp.cases.wms;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.CsvDataProvider;
import com.xianmu.atp.util.testng.XMListener;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;
import com.alibaba.fastjson.JSON;
import javax.annotation.Resource;

@Epic("注解测试")
@Feature("库内转换")
@Owner("xianmu")
@Listeners({XMListener.class})
@Slf4j
public class StockTransferTest extends BaseTest {
    @Value("${app.manage-domain}")
    private String domain;
    @Value("2160284716735")
    private String transferInSku;
    @Resource
    private LoginHttp login;

    @Value("2160284716848")
    private String transferOutSku;
    @Value("380")
    private String warehouseNo;
    @Story("批次转换")
    @Owner("xianmu")
    @Test(description = "测试用例", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            },dataProvider = "CsvDataProvider", dataProviderClass = CsvDataProvider.class)
    public void stockTransfer(String caseNum,String sku){
        log.info("-------"+sku);
        String token = login.login();
        String createURL =domain +
                "/summerfarm-wms/stockTransfer/create";
        log.info("token:{}", token);
        String createJson = "{\n" +
                "    \"transferInInfos\": [\n" +
                "        {\n" +
                "            \"transferInSku\": \""+sku+"\",\n" +
                "            \"transferInNum\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"warehouseNo\": "+warehouseNo+",\n" +
                "    \"transferDimension\": 0\n" +
                "}";
        HttpResponse response = HttpRequest.post(createURL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(createJson)
                .execute();
        log.info(response.body());
        String kStockTransferId= JSON.parseObject(response.body()).getString("data");
        Assert.assertTrue(JSON.parseObject(response.body()).getString("status") != null, "200");

    }
}
