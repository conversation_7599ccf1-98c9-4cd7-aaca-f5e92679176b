package com.xianmu.atp.cases.wnc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.json.JsonUtil;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

import static org.testng.Assert.*;

@Slf4j
@Epic("WNC") // 定义测试类别
@Feature("dubbo") // 定义测试特性
@Owner("xianmu") // 定义测试负责人
public class WncDubboTest extends BaseTest {

    private String sku; // SKU 变量
    private List<Integer> warehouseNos; // 仓库编号列表

    @Resource
    private DefaultDubboConfig defaultDubboConfig; // Dubbo 配置

    @Value("qa")
    private String env;

    @Test(description = "根据sku和仓库信息查询仓库围栏信息等") // 测试方法描述
    public void test001() {
        // 创建参数 Map，用于存储请求参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("sku", "19570010888"); // 设置 SKU
        paramMap.put("warehouseNos", Arrays.asList(1)); // 使用 List<Integer> 设置仓库编号
        // 将参数 Map 放入 List 中
        List<Map<String, Object>> paramsList = Collections.singletonList(paramMap);
        // 构建 ATPRequestDto 对象，封装请求信息
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageFenceQueryProvider") // 接口名
                .methodName("queryWarehouseSkuFence") // 方法名
                .paramTypes(new String[]{"java.util.List"}) // 参数类型
                .params(new Object[]{paramsList}) // 请求参数
                .build();

        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto); // 记录调用结果
        // assert TODO: 2025/断言逻辑需添加
    }

//    @Test(description = "根据查询条件获取配送规则")
//    public void test002() {
//        // 构造请求参数
//        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactId\":162303,\"needPagination\":true,\"pageIndex\":1,\"pageSize\":20,\"poi\":\"120.058591,30.279943\",\"skuList\":[\"2234176678686\"],\"startRow\":0,\"tenantId\":2}");
//        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
//                .env(env)
//                .interfaceName("net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageFenceQueryProvider")
//                .methodName("queryWarehouseStorageFence")
//                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq"})
//                .params(new Object[]{map})
//                .build();
//
//        log.info("调用请求参数：{}", atpRequestDto);
//
//        try {
//            // 调用获取结果
//            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
//            log.info("调用结果：{}", atpDto);
//
//            // 确保 atpDto 不为 null
//            assertNotNull(atpDto, "atpDto 不能为 null");
//
//            // 获取并解析 JSON 响应
//            String jsonResponse = atpDto.getDubboResponse() != null ? atpDto.getDubboResponse().toString() : null;
//            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应
//
//            // 检查 jsonResponse 是否为 null 或空字符串
//            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
//            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");
//
//            JSONObject responseObject = JSON.parseObject(jsonResponse);
//
//            // 检查状态码
//            int status = responseObject.getIntValue("status");
//            assertEquals(status, 200, "状态码应该是 200");
//
//            // 解析数据
//            JSONArray dataArray = responseObject.getJSONArray("data");
//            assertNotNull(dataArray, "数据数组不能为 null");
//
//            List<JSONObject> data = dataArray.toJavaList(JSONObject.class);
//            assertNotNull(data, "数据列表不能为 null");
//
//            LocalDate tomorrow = LocalDate.now().plusDays(1); // 计算明天的日期
//            LocalDate dayAfterTomorrow = LocalDate.now().plusDays(2); // 计算后天的日期
//            LocalTime now = LocalTime.now(); // 获取当前时间
//
//            // 遍历每个配送规则，验证 deliveryTime 和 closeTime
//            for (JSONObject item : data) {
//                JSONObject deliveryTimeJson = item.getJSONObject("deliveryTime");
//                if (deliveryTimeJson == null) {
//                    log.warn("配送规则中 deliveryTime 为 null，跳过该项");
//                    continue;
//                }
//
//                // 解析 deliveryTime
//                int year = deliveryTimeJson.getIntValue("year");
//                int monthValue = deliveryTimeJson.getIntValue("monthValue");
//                int dayOfMonth = deliveryTimeJson.getIntValue("dayOfMonth");
//                LocalDate deliveryDate = LocalDate.of(year, monthValue, dayOfMonth);
//
//                // 获取 closeTime
//                JSONObject closeTimeJson = item.getJSONObject("closeTime");
//                if (closeTimeJson == null) {
//                    log.warn("配送规则中 closeTime 为 null，跳过该项");
//                    continue;
//                }
//
//                LocalTime closeTime = LocalTime.of(
//                        closeTimeJson.getIntValue("hour"),
//                        closeTimeJson.getIntValue("minute"),
//                        closeTimeJson.getIntValue("second"),
//                        closeTimeJson.getIntValue("nano")
//                );
//                log.info("当前时间: {}", now);
//                log.info("关闭时间: {}", closeTime);
//                log.info("配送日期: {}", deliveryDate);
//
//                // 进行日期和时间的比较
//                if (closeTime.isBefore(now)) {
//                    // 如果 closeTime 小于当前时间，则断言 deliveryDate 为明天
//                    assertEquals(deliveryDate, tomorrow, "配送时间应为明天: " + deliveryDate);
//                } else {
//                    // 如果 closeTime 大于等于当前时间，则断言 deliveryDate 为后天
//                    assertEquals(deliveryDate, dayAfterTomorrow, "配送时间应为后天: " + deliveryDate);
//                }
//            }
//        } catch (Exception e) {
//            log.error("调用 Dubbo 接口时发生异常", e);
//            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
//        }
//    }

}
