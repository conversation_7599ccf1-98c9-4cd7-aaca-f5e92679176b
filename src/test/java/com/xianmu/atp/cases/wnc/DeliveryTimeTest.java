package com.xianmu.atp.cases.wnc;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.json.JsonUtil;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.testng.Assert.*;

@Slf4j
@Epic("WNC") // 定义测试类别
@Feature("dubbo") // 定义测试特性
@Owner("xianmu") // 定义测试负责人

public class DeliveryTimeTest extends BaseTest {
    @Resource
    private DefaultDubboConfig defaultDubboConfig; // Dubbo 配置

    @Value("dev")
    private String env;

    @Test(description = "实时-日配区域，截单前-杭州城配仓")
    public void test001() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"金山区\",\"city\":\"上海市\",\"contactId\":166262,\"orderTime\":\"2025-04-16T10:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-19"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-20"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-日配区域，截单后加单前")
    public void test002() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"金山区\",\"city\":\"上海市\",\"contactId\":166262,\"orderTime\":\"2025-04-16T16:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-19"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-20"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-日配区域，截单后加单后")
    public void test003() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"金山区\",\"city\":\"上海市\",\"contactId\":166262,\"orderTime\":\"2025-04-16T17:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-20"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-20"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-21"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-非日配区域，截单前-金华城配仓")
    public void test004() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"崇明区\",\"city\":\"上海市\",\"contactId\":166263,\"orderTime\":\"2025-04-16T13:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-19"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-22"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-非日配区域，截单后加单前")
    public void test005() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"崇明区\",\"city\":\"上海市\",\"contactId\":166263,\"orderTime\":\"2025-04-16T16:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-19"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-22"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-非日配区域，截单后加单后")
    public void test006() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"崇明区\",\"city\":\"上海市\",\"contactId\":166263,\"orderTime\":\"2025-04-16T17:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-22"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-22"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-24"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-快递日配，截单前-嘉兴快递城配仓")
    public void test007() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"丰润区\",\"city\":\"唐山市\",\"contactId\":166260,\"orderTime\":\"2025-04-16T13:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-17"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-17"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-18"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-快递日配，截单后，加单前")
    public void test008() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"丰润区\",\"city\":\"唐山市\",\"contactId\":166260,\"orderTime\":\"2025-04-16T16:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-17"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-17"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-18"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-快递日配，截单后，加单后")
    public void test009() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"丰润区\",\"city\":\"唐山市\",\"contactId\":166260,\"orderTime\":\"2025-04-16T17:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-18"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-19"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-快递非日配，截单前-长沙快递城配仓")
    public void test010() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"七里河区\",\"city\":\"兰州市\",\"contactId\":166261,\"orderTime\":\"2025-04-16T13:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-18"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-21"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-快递非日配，截单后，加单前")
    public void test011() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"七里河区\",\"city\":\"兰州市\",\"contactId\":166261,\"orderTime\":\"2025-04-16T16:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-18"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-21"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-快递非日配，截单后，加单后")
    public void test012() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"七里河区\",\"city\":\"兰州市\",\"contactId\":166261,\"orderTime\":\"2025-04-16T17:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-21"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-21"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-23"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }


    @Test(description = "缓存-日配区域，截单前")
    public void test0013() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"金山区\",\"city\":\"上海市\",\"contactId\":166262,\"orderTime\":\"2025-04-16T10:36:23.531\",\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryCacheDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取并格式化数据
            JSONObject optimizedResponse = new JSONObject();
            optimizedResponse.put("code", responseObject.getString("code"));
            optimizedResponse.put("msg", responseObject.getString("msg"));
            optimizedResponse.put("status", status);
            optimizedResponse.put("success", responseObject.getBoolean("success"));

            // 处理 closeTime 和 deliveryCloseTime
            JSONObject closeTimeJson = dataObject.getJSONObject("closeTime");
            String closeTime = String.format("%02d:%02d", closeTimeJson.getIntValue("hour"), closeTimeJson.getIntValue("minute"));
            optimizedResponse.put("closeTime", closeTime);

            // 获取 deliveryCloseTime
            JSONObject deliveryCloseTimeJson = dataObject.getJSONObject("deliveryCloseTime");
            String deliveryCloseTime = String.format("%04d-%02d-%02dT%02d:%02d:%02d",
                    deliveryCloseTimeJson.getIntValue("year"),
                    deliveryCloseTimeJson.getIntValue("monthValue"),
                    deliveryCloseTimeJson.getIntValue("dayOfMonth"),
                    deliveryCloseTimeJson.getIntValue("hour"),
                    deliveryCloseTimeJson.getIntValue("minute"),
                    deliveryCloseTimeJson.getIntValue("second"));
            optimizedResponse.put("deliveryCloseTime", deliveryCloseTime);

            // 提取 deliveryTimes
            JSONArray deliveryTimesArray = dataObject.getJSONArray("deliveryTimes");
            List<String> deliveryDates = new ArrayList<>();
            for (JSONObject deliveryTimeJson : deliveryTimesArray.toJavaList(JSONObject.class)) {
                String deliveryDate = String.format("%04d-%02d-%02d",
                        deliveryTimeJson.getIntValue("year"),
                        deliveryTimeJson.getIntValue("monthValue"),
                        deliveryTimeJson.getIntValue("dayOfMonth"));
                deliveryDates.add(deliveryDate);
            }
            optimizedResponse.put("deliveryTimes", deliveryDates);

            // 提取 firstMerchantDeliveryTime
            JSONObject firstMerchantDeliveryTimeJson = dataObject.getJSONObject("firstMerchantDeliveryTime");
            String firstMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    firstMerchantDeliveryTimeJson.getIntValue("year"),
                    firstMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    firstMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("firstMerchantDeliveryTime", firstMerchantDeliveryDate);

            // 提取 nextMerchantDeliveryTime
            JSONObject nextMerchantDeliveryTimeJson = dataObject.getJSONObject("nextMerchantDeliveryTime");
            String nextMerchantDeliveryDate = String.format("%04d-%02d-%02d",
                    nextMerchantDeliveryTimeJson.getIntValue("year"),
                    nextMerchantDeliveryTimeJson.getIntValue("monthValue"),
                    nextMerchantDeliveryTimeJson.getIntValue("dayOfMonth"));
            optimizedResponse.put("nextMerchantDeliveryTime", nextMerchantDeliveryDate);

            // 提取 addOrderHourDuration
            double addOrderHourDuration = dataObject.getDoubleValue("addOrderHourDuration");
            optimizedResponse.put("addOrderHourDuration", addOrderHourDuration);

            // 提取 fulfillmentType 和 isEveryDayFlag
            optimizedResponse.put("fulfillmentType", dataObject.getIntValue("fulfillmentType"));
            optimizedResponse.put("isEveryDayFlag", dataObject.getIntValue("isEveryDayFlag"));

            // 输出优化后的 JSON
            log.info("优化后的响应 JSON: {}", optimizedResponse.toJSONString());

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值
            String expectedFirstMerchantDeliveryTime = "2025-04-19"; // firstMerchantDeliveryTime 的预期值
            String expectedNextMerchantDeliveryTime = "2025-04-20"; // nextMerchantDeliveryTime 的预期值

// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDates.get(0), "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

// 断言 firstMerchantDeliveryTime 是特定日期
            assertEquals(expectedFirstMerchantDeliveryTime, firstMerchantDeliveryDate, "firstMerchantDeliveryTime 应该是 " + expectedFirstMerchantDeliveryTime + " 的日期");

// 断言 nextMerchantDeliveryTime 是特定日期
            assertEquals(expectedNextMerchantDeliveryTime, nextMerchantDeliveryDate, "nextMerchantDeliveryTime 应该是 " + expectedNextMerchantDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-日配区域，截单前")
    public void test0014() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"金山区\",\"city\":\"上海市\",\"contactId\":166262,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T10:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-日配区域，截单后，加单前")
    public void test0015() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"金山区\",\"city\":\"上海市\",\"contactId\":166262,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T16:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-日配区域，截单后，加单后")
    public void test0016() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"金山区\",\"city\":\"上海市\",\"contactId\":166262,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T17:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-20"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-非日配区域，截单后，加单前")
    public void test0017() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"崇明区\",\"city\":\"上海市\",\"contactId\":166263,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T16:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-非日配区域，截单后，加单后")
    public void test0018() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"崇明区\",\"city\":\"上海市\",\"contactId\":166263,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T17:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-22"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-快递，截单后，加单前")
    public void test0019() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"丰润区\",\"city\":\"唐山市\",\"contactId\":166260,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T16:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-17"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-快递，截单后，加单后")
    public void test0020() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"丰润区\",\"city\":\"唐山市\",\"contactId\":166260,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T17:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-快递非日配，截单后，加单前")
    public void test0021() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"七里河区\",\"city\":\"兰州市\",\"contactId\":166261,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T16:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-快递非日配，截单后，加单后")
    public void test0022() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"七里河区\",\"city\":\"兰州市\",\"contactId\":166261,\"orderTime\":\"2025-04-16T10:36:23.531\",\"payTime\":\"2025-04-16T17:36:24.531\",\"skus\":[\"1052658350813\"],\"source\":\"SAAS_MALL\",\"tenantId\":245143,\"AddOrderFlag\":\"true\",\"noNeedToStockUp\":\"false\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-21"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-鲜沐经销-上海城配仓")
    public void test0023() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"needPagination\":true,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176130525128\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-鲜沐-代销自营仓")
    public void test0024() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"needPagination\":true,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176804774102\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-鲜沐-代销非自营仓白名单")
    public void test0025() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"needPagination\":true,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176051743000\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-鲜沐-代销非自营仓非白名单")
    public void test0026() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"needPagination\":true,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176852534653\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-鲜沐经销-支付时间")
    public void test0027() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"payTime\":\"2025-04-17T23:36:24.531\",\"skus\":[\"2176130525128\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-鲜沐代销自营仓-下单时间")
    public void test0028() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T10:24:01.650\",\"payTime\":\"2025-04-18T13:36:24.531\",\"skus\":[\"2176804774102\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-代销非自营仓白名单-下单时间")
    public void test0029() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T10:24:01.650\",\"payTime\":\"2025-04-18T13:36:24.531\",\"skus\":[\"2176051743000\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-代销非自营仓非白名单-支付单时间")
    public void test0030() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":350613,\"merchantId\":256714,\"Source\":\"XM_MALL\",\"orderTime\":\"2025-04-17T10:24:01.650\",\"payTime\":\"2025-04-18T10:36:24.531\",\"skus\":[\"2176852534653\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-pop-纯pop-截单时间前-nacos")
    public void test0031() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactId\":349711,\"merchantId\":349842,\"needPagination\":true,\"Source\":\"POP_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2210434214760\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-pop-纯pop-截单时间后-nacos")
    public void test0032() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactId\":349711,\"merchantId\":349842,\"needPagination\":true,\"Source\":\"POP_MALL\",\"orderTime\":\"2025-04-17T20:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2210434214760\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值

            // 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryDate 应该是 " + expectedDeliveryTime);

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-pop-鲜沐映射-截单时间前-pop城配仓")
    public void test0033() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactId\":349711,\"merchantId\":349842,\"needPagination\":true,\"Source\":\"POP_MALL\",\"orderTime\":\"2025-05-14T08:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2215174506550\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-05-15"; // deliveryTimes 的预期值

            // 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryDate 应该是 " + expectedDeliveryTime);

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-pop-鲜沐映射-截单时后")
    public void test0034() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactId\":349711,\"merchantId\":349842,\"needPagination\":true,\"Source\":\"POP_MALL\",\"orderTime\":\"2025-05-14T23:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2215174506550\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-05-16"; // deliveryTimes 的预期值

            // 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryDate 应该是 " + expectedDeliveryTime);

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-pop-下单时间")
    public void test0035() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactId\":349711,\"merchantId\":349842,\"Source\":\"POP_MALL\",\"orderTime\":\"2025-04-17T10:24:01.650\",\"payTime\":\"2025-04-18T10:36:24.531\",\"skus\":[\"2215174506550\"],\"startRow\":0,\"tenantId\":1}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-SaaS报价经销-上海城配仓")
    public void test0036() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"needPagination\":true,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176130525128\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-SaaS报价代销自营仓")
    public void test0037() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"needPagination\":true,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176804774102\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-SaaS报价代销非自营仓白名单")
    public void test0038() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"needPagination\":true,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176051743000\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-SaaS报价代销非自营仓非白名单")
    public void test0039() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"needPagination\":true,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"2176852534653\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "实时-SaaS自营仓")
    public void test0040() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"addOrderFlag\":false,\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"needPagination\":true,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T17:24:01.650\",\"pageIndex\":1,\"pageSize\":20,\"skus\":[\"50540501555\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryDeliveryDateInfo")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-18"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-SaaS报价经销-支付时间")
    public void test0041() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T09:24:01.650\",\"payTime\":\"2025-04-17T23:36:24.531\",\"skus\":[\"2176130525128\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-SaaS报价代销自营仓-下单时间")
    public void test0042() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T09:24:01.650\",\"payTime\":\"2025-04-17T23:36:24.531\",\"skus\":[\"2176804774102\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-SaaS报价代销非自营仓白名单-下单时间")
    public void test0043() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T09:24:01.650\",\"payTime\":\"2025-04-17T23:36:24.531\",\"skus\":[\"2176051743000\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-SaaS报价代销非自营仓非白名单-支付时间")
    public void test0044() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T09:24:01.650\",\"payTime\":\"2025-04-17T23:36:24.531\",\"skus\":[\"2176852534653\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }

    @Test(description = "支付-SaaS自营仓-支付时间")
    public void test0045() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"contactId\":4117,\"Source\":\"SAAS_MALL\",\"orderTime\":\"2025-04-17T09:24:01.650\",\"payTime\":\"2025-04-17T23:36:24.531\",\"skus\":[\"50540501555\"],\"startRow\":0,\"tenantId\":2}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider")
                .methodName("queryPayAfterDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.wnc.client.req.delivery.PayAfterDeliveryDateQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);

        try {
            // 调用获取结果
            ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
            assertNotNull(atpDto, "atpDto 不能为 null");

            JSONObject object = (JSONObject) atpDto.getDubboResponse();

            // 获取并解析 JSON 响应
            String jsonResponse = object != null ? atpDto.getDubboResponse().toString() : null;
            log.info("响应 JSON: {}", jsonResponse); // 打印完整的响应

            // 检查 jsonResponse 是否为 null 或空字符串
            assertNotNull(jsonResponse, "响应 JSON 不能为 null");
            assertFalse(jsonResponse.isEmpty(), "响应 JSON 不能为空");

            JSONObject responseObject = JSON.parseObject(jsonResponse);

            // 检查状态
            int status = responseObject.getIntValue("status");
            assertEquals(200, status, "状态码应该是 200");

            // 获取 data 对象
            JSONObject dataObject = responseObject.getJSONObject("data");
            assertNotNull(dataObject, "data 对象不能为 null");

            // 提取 deliveryDate
            JSONArray skuDeliveryDateList = dataObject.getJSONArray("skuDeliveryDateList");
            assertNotNull(skuDeliveryDateList, "skuDeliveryDateList 不能为 null");
            assertFalse(skuDeliveryDateList.isEmpty(), "skuDeliveryDateList 不能为空");

            // 获取第一个 SKU 的 deliveryDate
            JSONObject firstSkuDeliveryDate = skuDeliveryDateList.getJSONObject(0);
            JSONObject deliveryDateJson = firstSkuDeliveryDate.getJSONObject("deliveryDate");

            String deliveryDate = String.format("%04d-%02d-%02d",
                    deliveryDateJson.getIntValue("year"),
                    deliveryDateJson.getIntValue("monthValue"),
                    deliveryDateJson.getIntValue("dayOfMonth"));

            // 输出提取的 deliveryDate·
            log.info("提取的 deliveryDate: {}", deliveryDate);

            String expectedDeliveryTime = "2025-04-19"; // deliveryTimes 的预期值



// 断言 deliveryTimes 是特定日期
            assertEquals(expectedDeliveryTime, deliveryDate, "deliveryTimes 应该是 " + expectedDeliveryTime + " 的日期");

        } catch (Exception e) {
            log.error("调用 Dubbo 接口时发生异常: ", e);
            fail("调用 Dubbo 接口时发生异常: " + e.getMessage());
        }
    }
}


