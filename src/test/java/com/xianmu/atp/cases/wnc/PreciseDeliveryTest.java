package com.xianmu.atp.cases.wnc;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.dal.dao.*;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Epic("WNC")
@Feature("精准送配置")
@Owner("xianmu")

public class PreciseDeliveryTest extends BaseTest{
    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    @Value("630104")
    private String adCode;

    @Autowired
    private PreciseDeliveryAreaDetailDao preciseDeliveryAreaDetailDao;

    @Autowired
    private PreciseDeliveryConfigDao preciseDeliveryConfigDao;

    @Autowired
    private PreciseDeliveryTimeDetailDao preciseDeliveryTimeDetailDao;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "新增精准送配置")
    public void case001() {
        Long precisedeliveryconfigid = preciseDeliveryAreaDetailDao.getPreciseDeliveryAreaDetailConfigId(adCode);
        // 先删除相关数据
        if (precisedeliveryconfigid != null) {
            preciseDeliveryTimeDetailDao.delcetPreciseDeliveryTimeDetail(precisedeliveryconfigid);
            preciseDeliveryAreaDetailDao.delcetPreciseDeliveryAreaDetail(adCode);
            preciseDeliveryConfigDao.delcetPreciseDeliveryConfig(precisedeliveryconfigid);
        } else {
            log.warn("No driver found with phone: {}; Skipping deletion.", precisedeliveryconfigid);
        }
        String helpUrl = domain + "/summerfarm-wnc/precise-delivery-config/upsert/save";
        Map<String, Object> params = new HashMap<>();
        params.put("configId", "");
        params.put("province", "青海");
        params.put("city", "西宁市");

        // 添加 areaList
        List<Map<String, String>> areaList = new ArrayList<>();
        Map<String, String> area = new HashMap<>();
        area.put("adCode", adCode);
        area.put("city", "西宁市");
        area.put("area", "城西区");
        areaList.add(area);
        params.put("areaList", areaList);

        // 添加 timeList
        List<Map<String, String>> timeList = new ArrayList<>();
        Map<String, String> time = new HashMap<>();
        time.put("beginTime", "13:00");
        time.put("endTime", "14:00");
        timeList.add(time);
        params.put("timeList", timeList);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "精准送配置列表", dependsOnMethods = "case001")
    public void case002() {
        String helpUrl = domain + "/summerfarm-wnc/precise-delivery-config/query/page";
        Map<String, Object> params = new HashMap<>();
        params.put("pageIndex", 1);
        params.put("pageSize", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "编辑精准送配置", dependsOnMethods = "case002")
    public void case003() {
        String precisedeliveryconfigid = String.valueOf(preciseDeliveryAreaDetailDao.getPreciseDeliveryAreaDetailConfigId(adCode));
        String precisedeliverareadetailid = String.valueOf(preciseDeliveryAreaDetailDao.getPreciseDeliveryAreaDetailId(adCode));
        LocalDateTime now = LocalDateTime.now();
        // 格式化当前时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = now.format(formatter);
        String helpUrl = domain + "/summerfarm-wnc/precise-delivery-config/upsert/edit";
        Map<String, Object> params = new HashMap<>();
        params.put("configId", precisedeliveryconfigid);
        params.put("province", "青海");
        params.put("city", "西宁市");

        // 添加 areaList
        List<Map<String, String>> areaList = new ArrayList<>();
        Map<String, String> area = new HashMap<>();
        area.put("id", precisedeliverareadetailid);
        area.put("configId", precisedeliveryconfigid);
        area.put("adCode", adCode);
        area.put("city", "西宁市");
        area.put("area", "城西区");
        area.put("createTime", currentTime);
        areaList.add(area);
        params.put("areaList", areaList);

        // 添加 timeList
        List<Map<String, String>> timeList = new ArrayList<>();
        Map<String, String> time = new HashMap<>();
        time.put("beginTime", "13:00");
        time.put("endTime", "14:10");
        timeList.add(time);
        params.put("timeList", timeList);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "删除精准送配置", dependsOnMethods = "case003")
    public void case004() {
        Long precisedeliveryconfigid = preciseDeliveryAreaDetailDao.getPreciseDeliveryAreaDetailConfigId(adCode);
        String helpUrl = domain + "/summerfarm-wnc/precise-delivery-config/upsert/remove";
        Map<String, Object> params = new HashMap<>();
        params.put("configId", precisedeliveryconfigid);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }
}
