package com.xianmu.atp.cases.wnc;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Epic("WNC")
@Feature("租户城配商品黑名单")
@Owner("xianmu")

public class TenantStoreSkuBlackConfigTest extends BaseTest{
    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;


    private int TenantStoreSkuBlackConfigId;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "新增租户城配仓黑名单")
    public void case005() {
        String helpUrl = domain + "/summerfarm-wnc/wncTenantStoreSkuBlackConfig/upsert/insert";
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", 2);
        params.put("sku", 124);
        params.put("storeNo", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "租户城配仓黑名单列表", dependsOnMethods = "case005")
    public void case006() {
        String helpUrl = domain + "/summerfarm-wnc/wncTenantStoreSkuBlackConfig/query/page";
        Map<String, Object> params = new HashMap<>();
        params.put("pageIndex", 1);
        params.put("pageSize", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
        HttpResponse response = sendRequest(helpUrl, json, true);
        assertResponse(response);
        JSONObject jsonResponse = JSON.parseObject(response.body());
        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("list");
        if (dataList != null && !dataList.isEmpty()) {
            TenantStoreSkuBlackConfigId = Integer.parseInt(dataList.getJSONObject(0).getString("id"));
            log.info("第一个履约审核任务的 ID: {}", TenantStoreSkuBlackConfigId);
        } else {
            log.warn("返回的数据列表为空");
        }
    }

    @Test(description = "查询租户城配仓黑名单详情", dependsOnMethods = "case006")
    public void case007() {
        String helpUrl = domain + "/summerfarm-wnc/wncTenantStoreSkuBlackConfig/query/detail";
        Map<String, Object> params = new HashMap<>();
        params.put("id", TenantStoreSkuBlackConfigId);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "更新租户城配仓黑名单", dependsOnMethods = "case007")
    public void case008() {
        String helpUrl = domain + "/summerfarm-wnc/wncTenantStoreSkuBlackConfig/upsert/update";
        Map<String, Object> params = new HashMap<>();
        params.put("id", TenantStoreSkuBlackConfigId);
        // 获取当前时间并格式化
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedCurrentTime = now.format(formatter);
        params.put("createTime", formattedCurrentTime);
        params.put("updateTime", null);
        params.put("tenantId", 2);
        params.put("sku", 124);
        params.put("storeNo", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "删除租户城配仓黑名单", dependsOnMethods = "case008")
    public void case009() {
        String helpUrl = domain + "/summerfarm-wnc/wncTenantStoreSkuBlackConfig/upsert/delete";
        Map<String, Object> params = new HashMap<>();
        params.put("id", TenantStoreSkuBlackConfigId);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }
}
