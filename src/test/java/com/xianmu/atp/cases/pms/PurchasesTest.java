package com.xianmu.atp.cases.pms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.step.PurchasesStep;
import com.xianmu.atp.dal.dao.common.BaseInfoService;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import com.xianmu.atp.generic.common.*;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Epic("PMS")
@Feature("采购单")
@Owner("大鹏")
public class PurchasesTest extends BaseTest {

    //全局变量
    @Resource
    private EnvVariable envVar;

    //相关请求工具
    @Resource
    private Request request;
    @Resource
    private UtilRequest utilRequest;

    @Resource
    private PurchasesStep purchasesStep;

    @Resource
    private BaseInfoService baseInfoService;


    private Supplier supplierInfo;
    private SkuInfo skuInfo;

    private WarehouseStorageCenter warehouseInfo;


    @BeforeMethod(description = "初始化操作--登录")
    public void login(){
        String URL = request.urlBuild(xmAdminDomain, LoginEnumsInterface.xianmuLogin.getUrl());
        HashMap<String, String> loginBody = new HashMap<>();
        loginBody.put("username", envVar.getAdminUserName());
        loginBody.put("password", envVar.getAdminpassword());
        HttpResponseWrapper loginRes= request.sendRequest(URL,
                LoginEnumsInterface.xianmuLogin.getMethod(),
                LoginEnumsInterface.xianmuLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        request.setToken(token);
    }

    @BeforeMethod(description = "初始化操作--获取商品、供应商、仓库等基础数据")
    public void getBaseInfo(){
        skuInfo =baseInfoService.getProductInfo(envVar.getSku());
        supplierInfo = baseInfoService.selectById(envVar.getSupplierId());
        warehouseInfo = baseInfoService.getWarehouseInfo(envVar.getWarehouseNo());

    }

    //每个用例执行后打印和移除临时变量
    @AfterMethod(description = "临时变量")
    public void tear_down(){
        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
        TemVar.remove();
    }
    //获取用例生成的临时变量
    public TemVar temVar(){
        return TemVar.getCurrent();
    }


    @Story("导入流程")
    @Test(description = "采购导入")
    public void purchases01() {

        List<String> purchasesTitleList = new ArrayList<>(Arrays.asList(
                "sku(必填)", "商品名称", "数量(必填)", "总价(必填)","供应商(必填)"));
        List<Object> purchasesValueList = new ArrayList<>();
        purchasesValueList.add(new HashMap<String, Object>(){{
            put("1",skuInfo.getPdNo());
            put("2",skuInfo.getPdName());
            put("3",10);
            put("4",100);
            put("5",supplierInfo.getName());
        }});
        JSONObject purchasesAddExcelInfo =utilRequest.excelCreate(new HashMap<String, Object>(){{
            put("fileName","采购新增导入-自动化测试.xlsx");
            put("sheetInfo", Lists.newArrayList(new HashMap<String, Object>(){{
                put("titleList",purchasesTitleList);
                put("valueList",purchasesValueList);
            }}));
        }});

        purchasesStep.upload(new HashMap<String, Object>(){{
            put("url","test-app-temp:"+purchasesAddExcelInfo.getString("data"));
            put("warehouseNo",envVar.getPopWarehouse());
        }});//excel--上传

    }

    @Story("导出流程")
    @Test(description = "采购相关导出功能")
    public void purchases02() {

        // 步骤1: 创建采购单并入库
        int quantity = 10;
        BigDecimal advancePrice=new BigDecimal(quantity);//预付金额

        Map<String, Object> purchaseBody = new HashMap<>();
        purchaseBody.put("supplierNo", supplierInfo.getId());
        purchaseBody.put("warehouseNo", warehouseInfo.getWarehouseNo());
        purchaseBody.put("quantity", quantity);
        purchaseBody.put("skuList", Lists.newArrayList(envVar.getSku()));
        purchaseBody.put("flag", 2);
        purchaseBody.put("envFlag", env);
        String purchaseNo = utilRequest.createPurchaseOrder(purchaseBody);
        purchasesStep.export(new HashMap<String, Object>(){{
            put("purchaseNo",purchaseNo);
        }});//采购单--详情里导出
        utilRequest.getResult(new HashMap<String, Object>(){{
            put("resourceId",temVar().getExcelResId());
        }});
        purchasesStep.exportList(new HashMap<String, Object>(){{
            put("ids", Collections.singletonList(purchaseNo));
        }});//采购单--列表导出
        utilRequest.getResult(new HashMap<String, Object>(){{
            put("resourceId",temVar().getExcelResId());
        }});

    }


}
