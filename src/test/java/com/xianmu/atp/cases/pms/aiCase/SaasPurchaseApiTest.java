package com.xianmu.atp.cases.pms.aiCase;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.aiCase.env.SaasEnvVariable;
import com.xianmu.atp.cases.pms.aiCase.step.SaasPurchaseStep;
import com.xianmu.atp.dal.dao.common.BaseInfoService;
import com.xianmu.atp.dal.model.Purchases;
import com.xianmu.atp.dal.model.StockTaskStorage;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import com.xianmu.atp.generic.common.DateUtil;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.generic.common.UtilRequest;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.*;

/**
 * SaaS采购相关接口测试用例
 * 包含采购单、预约单、退货单等功能的测试
 * 
 * <AUTHOR> Test Engineer
 */
@Slf4j
@Epic("Saas-PMS")
@Feature("saas采购单场景")
@Owner("大鹏")
public class SaasPurchaseApiTest extends BaseTest {

    @Resource
    private SaasPurchaseStep saasPurchaseStep;

    @Resource
    private Request request;

    @Resource
    private SaasEnvVariable saasEnvVar;

    @Resource
    private UtilRequest utilRequest;

    @Resource
    private BaseInfoService baseInfoService;

    private  String saasSkuId;
    private  Integer saasSupplierId;
    private  Integer ownWarehouseNo;
    private Integer tenantId;
    private Map<String, Object> skuInfo;//saas货品信息
    private WarehouseStorageCenter warehouseInfo;
    private Supplier supplierInfo;
    private  Integer saasSupplierId2;
    private  Integer ownWarehouseNo2;

    /**
     * 测试初始化
     * 执行SaaS登录并设置token信息
     */
    @BeforeClass
    public void setUp() {
        log.info("=== 开始执行SaaS供应商测试用例初始化 ===");

        // 调用saasLogin进行登录并设置token
        String token = request.saasLogin(saasEnvVar.getUsername(), saasEnvVar.getPassword());
        Assert.assertNotNull(token, "SaaS登录失败，token为空");
        // 设置全局token
        request.setToken(token);
        //初始化测试变量
        saasSkuId= saasEnvVar.getSaasSkuId();//继承父类变量--saas自营货品SKUID
        saasSupplierId= saasEnvVar.getSaasSupplierId();//继承父类变量--saas供应商ID
        ownWarehouseNo = saasEnvVar.getOwnWarehouseNo();//继承父类变量--saas自营仓库ID
        tenantId= saasEnvVar.getTenantID();//继承父类变量--租户ID
        saasSupplierId2= saasEnvVar.getSaasSupplierId2();
        ownWarehouseNo2 = saasEnvVar.getOwnWarehouseNo2();
        //初始化saas货品信息
        Map<String, Object> saasSkuQueryParams = new HashMap<>();
        saasSkuQueryParams.put("saasSkuId", Long.valueOf(saasSkuId));
        saasSkuQueryParams.put("pageIndex", 1);
        saasSkuQueryParams.put("pageSize", 10);
        saasSkuQueryParams.put("createTypeList", Lists.newArrayList(0,2,3) );
        //初始化获取供应商、仓库信息
        supplierInfo= baseInfoService.selectById(saasSupplierId);
        warehouseInfo= baseInfoService.getWarehouseInfo(ownWarehouseNo.toString());

        JSONArray skuList = utilRequest.selectLikeBySkuOrName(saasSkuQueryParams);
        Assert.assertNotNull(skuList, "查询货品列表结果为空");
        skuInfo= skuList.getJSONObject(0);
    }

    // ==================== 采购单管理测试用例 ====================

    @Story("自营仓采购流程")
    @Test( description = "采购单新增计划单>列表>详情>删除")
    public void testPurchasePage001() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        // 步骤1:新增采购计划单
        String remark="saas自动化采购"+currentDateTime;
        Integer num=10;//采购数量,默认单价1
        Map<String, Object> params = saasPurchaseStep.buildPurchaseParmams(skuInfo, warehouseInfo,
                supplierInfo,remark,0, num,true);

        saasPurchaseStep.savePurchase(params);
        //断言采购单生成
        Purchases purchase = saasPurchaseStep.queryPurchaseByRemark(remark,ownWarehouseNo,tenantId);
        Assert.assertNotNull(purchase, "查询采购单结果为空");
        Assert.assertEquals(purchase.getState(),Integer.valueOf(0),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                purchase.getPurchaseNo(), purchase.getState()));
        //步骤2:查询采购单列表
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("isShowDetail", true);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("purchaseNo", purchase.getPurchaseNo());
        queryParams.put("saasSkuId", saasSkuId);
        queryParams.put("state", 0);
        queryParams.put("supplierId", saasSupplierId.toString());
        queryParams.put("warehouseNoList", Collections.singletonList(ownWarehouseNo));
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);

        //步骤3:查询采购单详情
        Map<String, Object> detailParams = new HashMap<>();
        detailParams.put("purchaseNo", purchase.getPurchaseNo());
        saasPurchaseStep.getPurchaseDetail(detailParams);

        //步骤4:删除采购单
        Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("purchaseId", purchase.getId());
        saasPurchaseStep.deletePurchase(deleteParams);

        //步骤5:列表查询验证删除采购单成功
        saasPurchaseStep.queryPurchasePageV2(queryParams,false);

    }

    @Story("自营仓采购流程")
    @Test( description = "采购单新增计划单>发布并预约>列表>详情>查看预约单>撤销预约单>作废采购单")
    public void testPurchasePage002() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        // 新增采购计划单
        String remark="saas自动化采购"+currentDateTime;
        Integer num=10;//采购数量,默认单价1

        Map<String, Object> addPurchaseParams = saasPurchaseStep.buildPurchaseParmams(skuInfo, warehouseInfo,
                supplierInfo,remark,0,num,true);

        saasPurchaseStep.savePurchase(addPurchaseParams);
        //断言采购单生成
        Purchases purchase = saasPurchaseStep.queryPurchaseByRemark(remark,ownWarehouseNo,tenantId);
        Assert.assertNotNull(purchase, "查询采购单结果为空");
        Assert.assertEquals(purchase.getState(),Integer.valueOf(0),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                purchase.getPurchaseNo(), purchase.getState()));
        //查询采购单列表
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("isShowDetail", true);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("purchaseNo", purchase.getPurchaseNo());
        queryParams.put("saasSkuId", saasSkuId);
        queryParams.put("state", 0);
        queryParams.put("supplierId", saasSupplierId.toString());
        queryParams.put("warehouseNoList", Collections.singletonList(ownWarehouseNo));
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);


        //编辑发布采购单
        addPurchaseParams.put("purchaseId", purchase.getId().toString());
        addPurchaseParams.put("purchaseNo", purchase.getPurchaseNo());
        addPurchaseParams.put("state", 1);//设置状态=已发布

        saasPurchaseStep.updatePurchase(addPurchaseParams);

        //列表查询采购单验证更新成功
        queryParams.put("state", 1);
        saasPurchaseStep.queryPurchasePageV2(queryParams,true);

        //查看对应预约单生成
        Map<String, Object> queryArrangeParams = new HashMap<>();
        queryArrangeParams.put("purchasesNo", purchase.getPurchaseNo());
        JSONArray arrangeList = saasPurchaseStep.listArrangePurchasesNo(queryArrangeParams);
        JSONObject arrangeInfo = arrangeList.getJSONObject(0);

        //查看预约单详情
        Map<String, Object> detailArrangeParams = new HashMap<>();
        detailArrangeParams.put("stockArrangeId", arrangeInfo.getString("id"));
        saasPurchaseStep.getArrangeDetailByStockArrangeId(detailArrangeParams,0);

        //撤销预约单
        Map<String, Object> cancelArrangeParams = new HashMap<>();
        cancelArrangeParams.put("stockArrangeId", arrangeInfo.getString("id"));
        saasPurchaseStep.closeArrange(cancelArrangeParams);

        //查询采购单详情
        Map<String, Object> detailParams = new HashMap<>();
        detailParams.put("purchaseNo", purchase.getPurchaseNo());
        JSONObject purchaseDetail = saasPurchaseStep.getPurchaseDetail(detailParams);

        //作废采购单
        purchaseDetail.put("state", -1);//设置状态=作废
        purchaseDetail.put("purchaseId", purchase.getId());
        saasPurchaseStep.updatePurchase(purchaseDetail);

        //列表查询采购单验证更新成功
        queryParams.put("state", -1);
        saasPurchaseStep.queryPurchasePageV2(queryParams,true);

    }

    @Story("自营仓采购流程")
    @Test( description = "新增已发布采购单并预约>列表>详情>查看预约单和入库任务>仓库关闭入库任务>作废采购单")
    public void testPurchasePage003() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        // 新增采购单已发布
        String remark="saas自动化采购"+currentDateTime;
        Integer num= 10;//采购数量,默认单价1
        Map<String, Object> addPurchaseParams = saasPurchaseStep.buildPurchaseParmams(skuInfo, warehouseInfo,
                supplierInfo,remark,1, num,true);

        saasPurchaseStep.savePurchase(addPurchaseParams);
        //断言采购单生成
        Purchases purchase = saasPurchaseStep.queryPurchaseByRemark(remark,ownWarehouseNo,tenantId);
        Assert.assertNotNull(purchase, "查询采购单结果为空");
        Assert.assertEquals(purchase.getState(),Integer.valueOf(1),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                purchase.getPurchaseNo(), purchase.getState()));
        //查询采购单列表
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("isShowDetail", true);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("purchaseNo", purchase.getPurchaseNo());
        queryParams.put("saasSkuId", saasSkuId);
        queryParams.put("state", 1);
        queryParams.put("supplierId", saasSupplierId.toString());
        queryParams.put("warehouseNoList", Collections.singletonList(ownWarehouseNo));
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);


        //查看对应预约单生成
        Map<String, Object> queryArrangeParams = new HashMap<>();
        queryArrangeParams.put("purchasesNo", purchase.getPurchaseNo());
        JSONArray arrangeList = saasPurchaseStep.listArrangePurchasesNo(queryArrangeParams);
        JSONObject arrangeInfo = arrangeList.getJSONObject(0);

        //断言入库任务生成
        StockTaskStorage taskInfo = saasPurchaseStep.queryTaskByPurchaseNo(purchase.getPurchaseNo());
        Assert.assertNotNull(taskInfo, "查询入库任务结果为空");
        Assert.assertEquals(taskInfo.getState(),Integer.valueOf(0),  String.format("入库任务[%s]状态不符合预期，当前状态: %d",
                taskInfo.getId(), taskInfo.getState()));


        //关闭入库任务
        Map<String, Object> cancelTaskParams = new HashMap<>();
        cancelTaskParams.put("stockStorageId", taskInfo.getId());
        cancelTaskParams.put("reason", "自动关闭"+currentDateTime);
        saasPurchaseStep.closeTask(cancelTaskParams);

        //查看预约单详情
        Map<String, Object> detailArrangeParams = new HashMap<>();
        detailArrangeParams.put("stockArrangeId", arrangeInfo.getString("id"));
        saasPurchaseStep.getArrangeDetailByStockArrangeId(detailArrangeParams,3);

        //查询采购单详情
        Map<String, Object> detailParams = new HashMap<>();
        detailParams.put("purchaseNo", purchase.getPurchaseNo());
        JSONObject purchaseDetail = saasPurchaseStep.getPurchaseDetail(detailParams);

        //作废采购单
        purchaseDetail.put("state", -1);//设置状态=作废
        purchaseDetail.put("purchaseId", purchase.getId());
        saasPurchaseStep.updatePurchase(purchaseDetail);

        //列表查询采购单验证更新成功
        queryParams.put("state", -1);
        saasPurchaseStep.queryPurchasePageV2(queryParams,true);

    }

    @Story("自营仓采购流程")
    @Test( description = "新增已发布采购单并预约>查看产能证件>列表查询>详情查看>查看预约单和入库任务>仓库操作入库>再来一单并入库")
    public void testPurchasePage004() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        String tomorrow = DateUtil.getOffsetDate(1);
        Integer num= 10;//采购数量,默认单价1
        //校验仓库产能
        Map<String, Object> queryCapacityParams = new HashMap<>();
        Map<String, Object> skuInput = new HashMap<>();
        Map<String, Object> warehouseCapacityInput = new HashMap<>();
        skuInput.put("sku", skuInfo.get("sku"));
        skuInput.put("quantity", num);
        warehouseCapacityInput.put("warehouseNo", ownWarehouseNo);
        warehouseCapacityInput.put("expectTime", tomorrow);
        warehouseCapacityInput.put("skuInputList", Collections.singletonList(skuInput));
        queryCapacityParams.put("warehouseCapacityInputList", Collections.singletonList(warehouseCapacityInput));
        saasPurchaseStep.queryWarehouseCapacityBatch(queryCapacityParams);
        //查询证件信息
        Map<String, Object> queryCertificateParams = new HashMap<>();
        Map<String, Object> queryCertificateInfo = new HashMap<>();
        queryCertificateInfo.put("warehouseNo", ownWarehouseNo.toString());
        queryCertificateInfo.put("skuList",Collections.singletonList(skuInfo.get("sku")));
        queryCertificateParams.put("proveStandardQueryInputList", Collections.singletonList(queryCertificateInfo));
        saasPurchaseStep.queryBatchStandardBatch(queryCertificateParams);

        // 新增采购单已发布
        String remark="saas自动化采购"+currentDateTime;
        Map<String, Object> addPurchaseParams = saasPurchaseStep.buildPurchaseParmams(skuInfo, warehouseInfo,
                supplierInfo,remark,1, num,true);

        saasPurchaseStep.savePurchase(addPurchaseParams);
        //断言采购单生成
        Purchases purchase = saasPurchaseStep.queryPurchaseByRemark(remark,ownWarehouseNo,tenantId);
        Assert.assertNotNull(purchase, "查询采购单结果为空");
        Assert.assertEquals(purchase.getState(),Integer.valueOf(1),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                purchase.getPurchaseNo(), purchase.getState()));
        //查询采购单列表
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("isShowDetail", true);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("purchaseNo", purchase.getPurchaseNo());
        queryParams.put("saasSkuId", saasSkuId);
        queryParams.put("state", 1);
        queryParams.put("supplierId", saasSupplierId.toString());
        queryParams.put("warehouseNoList", Collections.singletonList(ownWarehouseNo));
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);


        //查看对应预约单生成
        Map<String, Object> queryArrangeParams = new HashMap<>();
        queryArrangeParams.put("purchasesNo", purchase.getPurchaseNo());
        JSONArray arrangeList = saasPurchaseStep.listArrangePurchasesNo(queryArrangeParams);
        JSONObject arrangeInfo = arrangeList.getJSONObject(0);

        //断言入库任务生成
        StockTaskStorage taskInfo = saasPurchaseStep.queryTaskByPurchaseNo(purchase.getPurchaseNo());
        Assert.assertNotNull(taskInfo, "查询入库任务结果为空");
        Assert.assertEquals(taskInfo.getState(),Integer.valueOf(0),  String.format("入库任务[%s]状态不符合预期，当前状态: %d",
                taskInfo.getId(), taskInfo.getState()));
        //采购任务操作入库
        utilRequest.createInBound(taskInfo.getId(), "11");

        //查看预约单详情
        Map<String, Object> detailArrangeParams = new HashMap<>();
        detailArrangeParams.put("stockArrangeId", arrangeInfo.getString("id"));
        saasPurchaseStep.getArrangeDetailByStockArrangeId(detailArrangeParams,1);

        //查询采购单列表
        queryParams.put("stockState", "2");//入库状态=全部入库
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);

        //查询出入库明细
        Map<String, Object> queryInOutDetailParams = new HashMap<>();
        queryInOutDetailParams.put("purchaseNo", purchase.getPurchaseNo());
        queryInOutDetailParams.put("pageIndex", 1);
        queryInOutDetailParams.put("pageSize", 10);
        saasPurchaseStep.queryPurchaseInOutDetail(queryInOutDetailParams);

        //再来一单校验
        Map<String, Object> copyCheckParams = new HashMap<>();
        copyCheckParams.put("purchaseNo", purchase.getPurchaseNo());
        saasPurchaseStep.copyCheck(copyCheckParams);

        //再来一单
        String copyRemark="saas自动化采购再来一单"+currentDateTime;
        addPurchaseParams.put("purchaseNo", purchase.getPurchaseNo());
        addPurchaseParams.put("remark", copyRemark);
        saasPurchaseStep.savePurchase(addPurchaseParams);

        //断言采购单生成
        Purchases copyPurchaseInfo = saasPurchaseStep.queryPurchaseByRemark(copyRemark,ownWarehouseNo,tenantId);
        Assert.assertNotNull(copyPurchaseInfo, "查询采购单结果为空");
        Assert.assertEquals(copyPurchaseInfo.getState(),Integer.valueOf(1),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                copyPurchaseInfo.getPurchaseNo(), copyPurchaseInfo.getState()));

        //断言入库任务生成
        StockTaskStorage taskInfo1 = saasPurchaseStep.queryTaskByPurchaseNo(copyPurchaseInfo.getPurchaseNo());
        Assert.assertNotNull(taskInfo1, "查询入库任务结果为空");
        Assert.assertEquals(taskInfo1.getState(),Integer.valueOf(0),  String.format("入库任务[%s]状态不符合预期，当前状态: %d",
                taskInfo1.getId(), taskInfo1.getState()));


        //采购任务操作入库
        utilRequest.createInBound(taskInfo1.getId(), "11");

        //查询出入库明细
        queryInOutDetailParams.put("purchaseNo", copyPurchaseInfo.getPurchaseNo());
        saasPurchaseStep.queryPurchaseInOutDetail(queryInOutDetailParams);


        //查询采购单列表
        queryParams.put("stockState", "2");//入库状态=全部入库
        queryParams.put("purchaseNo", copyPurchaseInfo.getPurchaseNo());
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);

    }

    @Story("自营仓采购流程")
    @Test( description = "新增采购计划单>查看产能证件>发布>创建预约单>列表查询>查看预约单和入库任务>仓库操作入库>详情查看>采购单导出")
    public void testPurchasePage005() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        String tomorrow = DateUtil.getOffsetDate(1);
        Integer num= 10;//采购数量,默认单价1
        //校验仓库产能
        Map<String, Object> queryCapacityParams = new HashMap<>();
        Map<String, Object> skuInput = new HashMap<>();
        Map<String, Object> warehouseCapacityInput = new HashMap<>();
        skuInput.put("sku", skuInfo.get("sku"));
        skuInput.put("quantity", num);
        warehouseCapacityInput.put("warehouseNo", ownWarehouseNo);
        warehouseCapacityInput.put("expectTime", tomorrow);
        warehouseCapacityInput.put("skuInputList", Collections.singletonList(skuInput));
        queryCapacityParams.put("warehouseCapacityInputList", Collections.singletonList(warehouseCapacityInput));
        saasPurchaseStep.queryWarehouseCapacityBatch(queryCapacityParams);
        //查询证件信息
        Map<String, Object> queryCertificateParams = new HashMap<>();
        Map<String, Object> queryCertificateInfo = new HashMap<>();
        queryCertificateInfo.put("warehouseNo", ownWarehouseNo.toString());
        queryCertificateInfo.put("skuList",Collections.singletonList(skuInfo.get("sku")));
        queryCertificateParams.put("proveStandardQueryInputList", Collections.singletonList(queryCertificateInfo));
        saasPurchaseStep.queryBatchStandardBatch(queryCertificateParams);

        // 查询允供关系列表
        Map<String, Object> querySkuSupplyParams = new HashMap<>();
        querySkuSupplyParams.put("sku", skuInfo.get("sku"));
        querySkuSupplyParams.put("queryUseSupplier", true);
        saasPurchaseStep.querySkuSupplyList(querySkuSupplyParams);

        // 新增采购单--计划状态
        String remark="saas自动化采购"+currentDateTime;
        Map<String, Object> addPurchaseParams = saasPurchaseStep.buildPurchaseParmams(skuInfo, warehouseInfo,
                supplierInfo,remark,0, num,false);

        saasPurchaseStep.savePurchase(addPurchaseParams);
        //断言采购单生成
        Purchases purchase = saasPurchaseStep.queryPurchaseByRemark(remark,ownWarehouseNo,tenantId);
        Assert.assertNotNull(purchase, "查询采购单结果为空");
        Assert.assertEquals(purchase.getState(),Integer.valueOf(0),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                purchase.getPurchaseNo(), purchase.getState()));
        //查询采购单列表
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("isShowDetail", true);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("purchaseNo", purchase.getPurchaseNo());
        queryParams.put("saasSkuId", saasSkuId);
        queryParams.put("state", 0);
        queryParams.put("supplierId", saasSupplierId.toString());
        queryParams.put("warehouseNoList", Collections.singletonList(ownWarehouseNo));
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);

        //编辑发布采购单
        addPurchaseParams.put("purchaseId", purchase.getId().toString());
        addPurchaseParams.put("purchaseNo", purchase.getPurchaseNo());
        addPurchaseParams.put("state", 1);//设置状态=已发布

        saasPurchaseStep.updatePurchase(addPurchaseParams);

        //列表查询采购单验证更新成功
        queryParams.put("state", 1);
        saasPurchaseStep.queryPurchasePageV2(queryParams,true);

        //查询可预约SKU列表
        Map<String, Object> querySkuParams = new HashMap<>();
        querySkuParams.put("purchaseNo", purchase.getPurchaseNo());
        JSONArray skuArrangeList = saasPurchaseStep.querySkuArrange(querySkuParams);
        skuArrangeList.stream()
                .filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject) obj)
                .forEach(skuItem -> {
                    Object arrangeQuantity = skuItem.get("arrangeQuantity");
                    if (arrangeQuantity != null) {
                        skuItem.put("arrQuantity", arrangeQuantity);
                    }
                });
        //入库预约
        Map<String, Object> addArrangeParams = new HashMap<>();
        addArrangeParams.put("areaNo", ownWarehouseNo);
        addArrangeParams.put("arrangeRemark", remark);
        addArrangeParams.put("arrangeTime", DateUtil.getOffsetDate(1));
        addArrangeParams.put("purchaseNo", purchase.getPurchaseNo());
        addArrangeParams.put("stockArrangeItemDetails", skuArrangeList);
        String arrangeId = saasPurchaseStep.addArrange(addArrangeParams);


        //查看预约单详情--验证单据状态=待收货
        Map<String, Object> detailArrangeParams = new HashMap<>();
        detailArrangeParams.put("stockArrangeId", arrangeId);
        saasPurchaseStep.getArrangeDetailByStockArrangeId(detailArrangeParams,0);


        //断言入库任务生成
        StockTaskStorage taskInfo = saasPurchaseStep.queryTaskByPurchaseNo(purchase.getPurchaseNo());
        Assert.assertNotNull(taskInfo, "查询入库任务结果为空");
        Assert.assertEquals(taskInfo.getState(),Integer.valueOf(0),  String.format("入库任务[%s]状态不符合预期，当前状态: %d",
                taskInfo.getId(), taskInfo.getState()));
        //采购任务操作入库
        utilRequest.createInBound(taskInfo.getId(), "11");

        //查询出入库明细
        Map<String, Object> queryInOutDetailParams = new HashMap<>();
        queryInOutDetailParams.put("purchaseNo", purchase.getPurchaseNo());
        queryInOutDetailParams.put("pageIndex", 1);
        queryInOutDetailParams.put("pageSize", 10);
        saasPurchaseStep.queryPurchaseInOutDetail(queryInOutDetailParams);

        //查询出入库明细汇总
        Map<String, Object> queryInOutStatisticsParams = new HashMap<>();
        queryInOutStatisticsParams.put("pageIndex", 1);
        queryInOutStatisticsParams.put("pageSize", 99);
        queryInOutStatisticsParams.put("purchaseNo", purchase.getPurchaseNo());
        saasPurchaseStep.inOutStatistics(queryInOutStatisticsParams);

        //查询采购退货单列表
        Map<String, Object> queryBackParams = new HashMap<>();
        queryBackParams.put("pageIndex", 1);
        queryBackParams.put("pageSize", 100);
        queryBackParams.put("purchasesNo", purchase.getPurchaseNo());
        saasPurchaseStep.queryPurchaseBackPage(queryBackParams);

        //查询采购单列表
        queryParams.put("stockState", "2");//入库状态=全部入库
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);

        //采购单列表导出
        Map<String, Object> exportParams = new HashMap<>();
        Map<String, Object> paramJson = new HashMap<>();
        paramJson.put("采购批次", purchase.getPurchaseNo());
        paramJson.put("采购状态", "已发布");
        exportParams.put("purchaseNo", purchase.getPurchaseNo());
        exportParams.put("state", "1");
        exportParams.put("paramJson", paramJson);
        saasPurchaseStep.exportV2(exportParams);

        //采购单详情导出
        Map<String, Object> exportDetailParams = new HashMap<>();
        exportDetailParams.put("purchaseNo", purchase.getPurchaseNo());
        saasPurchaseStep.exportByPurchaseNo(exportDetailParams);

    }

    @Story("自营仓采购流程")
    @Test( description = "新增采购单计划单>发布预约>列表查询>详情查看>查看预约单和入库任务>更新预约单>仓库操作入库")
    public void testPurchasePage006() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        String tomorrow = DateUtil.getOffsetDate(1);
        Integer num= 10;//采购数量,默认单价1

        // 新增采购单--计划状态
        String remark="saas自动化采购"+currentDateTime;
        Map<String, Object> addPurchaseParams = saasPurchaseStep.buildPurchaseParmams(skuInfo, warehouseInfo,
                supplierInfo,remark,0, num,false);

        saasPurchaseStep.savePurchase(addPurchaseParams);
        //断言采购单生成
        Purchases purchase = saasPurchaseStep.queryPurchaseByRemark(remark,ownWarehouseNo,tenantId);
        Assert.assertNotNull(purchase, "查询采购单结果为空");
        Assert.assertEquals(purchase.getState(),Integer.valueOf(0),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                purchase.getPurchaseNo(), purchase.getState()));
        //查询采购单列表
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("isShowDetail", true);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("purchaseNo", purchase.getPurchaseNo());
        queryParams.put("saasSkuId", saasSkuId);
        queryParams.put("state", 0);
        queryParams.put("supplierId", saasSupplierId.toString());
        queryParams.put("warehouseNoList", Collections.singletonList(ownWarehouseNo));
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);

        //编辑发布采购单
        addPurchaseParams.put("purchaseId", purchase.getId().toString());
        addPurchaseParams.put("purchaseNo", purchase.getPurchaseNo());
        addPurchaseParams.put("state", 1);//设置状态=已发布

        saasPurchaseStep.updatePurchase(addPurchaseParams);

        //列表查询采购单验证更新成功
        queryParams.put("state", 1);
        saasPurchaseStep.queryPurchasePageV2(queryParams,true);

        //查询可预约SKU列表
        Map<String, Object> querySkuParams = new HashMap<>();
        querySkuParams.put("purchaseNo", purchase.getPurchaseNo());
        JSONArray skuArrangeList = saasPurchaseStep.querySkuArrange(querySkuParams);
        skuArrangeList.stream()
                .filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject) obj)
                .forEach(skuItem -> {
                    Object arrangeQuantity = skuItem.get("arrangeQuantity");
                    if (arrangeQuantity != null) {
                        skuItem.put("arrQuantity", arrangeQuantity);
                    }
                });
        //入库预约
        Map<String, Object> addArrangeParams = new HashMap<>();
        addArrangeParams.put("areaNo", ownWarehouseNo);
        addArrangeParams.put("arrangeRemark", remark);
        addArrangeParams.put("arrangeTime", tomorrow);
        addArrangeParams.put("purchaseNo", purchase.getPurchaseNo());
        addArrangeParams.put("stockArrangeItemDetails", skuArrangeList);
        String arrangeId = saasPurchaseStep.addArrange(addArrangeParams);


        //查看预约单详情--验证单据状态=待收货
        Map<String, Object> detailArrangeParams = new HashMap<>();
        detailArrangeParams.put("stockArrangeId", arrangeId);
        saasPurchaseStep.getArrangeDetailByStockArrangeId(detailArrangeParams,0);


        //断言入库任务生成
        StockTaskStorage taskInfo = saasPurchaseStep.queryTaskByPurchaseNo(purchase.getPurchaseNo());
        Assert.assertNotNull(taskInfo, "查询入库任务结果为空");
        Assert.assertEquals(taskInfo.getState(),Integer.valueOf(0),  String.format("入库任务[%s]状态不符合预期，当前状态: %d",
                taskInfo.getId(), taskInfo.getState()));

        //更新预约备注
        String updateRemark="saas自动化采购"+currentDateTime+"预约备注更新";
        Map<String, Object> updateRemarkParams = new HashMap<>();
        updateRemarkParams.put("id", arrangeId);
        updateRemarkParams.put("remark", updateRemark);
        saasPurchaseStep.updateArrangeRemark(updateRemarkParams);
        //更新预约时间
        addArrangeParams.remove("arrangeRemark");
        addArrangeParams.put("arrangeTime", DateUtil.getOffsetDate(2));
        addArrangeParams.put("id", arrangeId);
        saasPurchaseStep.updateArrange(addArrangeParams);


        //采购任务操作入库
        utilRequest.createInBound(taskInfo.getId(), "11");

        //查询出入库明细
        Map<String, Object> queryInOutDetailParams = new HashMap<>();
        queryInOutDetailParams.put("purchaseNo", purchase.getPurchaseNo());
        queryInOutDetailParams.put("pageIndex", 1);
        queryInOutDetailParams.put("pageSize", 10);
        saasPurchaseStep.queryPurchaseInOutDetail(queryInOutDetailParams);

        //查询出入库明细汇总
        Map<String, Object> queryInOutStatisticsParams = new HashMap<>();
        queryInOutStatisticsParams.put("pageIndex", 1);
        queryInOutStatisticsParams.put("pageSize", 99);
        queryInOutStatisticsParams.put("purchaseNo", purchase.getPurchaseNo());
        saasPurchaseStep.inOutStatistics(queryInOutStatisticsParams);

        //查询采购单列表
        queryParams.put("stockState", "2");//入库状态=全部入库
        saasPurchaseStep.queryPurchasePageV2(queryParams, true);


    }

    @Story("自营仓采购流程")
    @Test( description = "多仓多供应商批次发布采购单并预约>查看预约单和入库任务")
    public void testPurchasePage007() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        Integer num= 10;//采购数量,默认单价1

        List<Supplier> supplierList = baseInfoService.selectSupplierByIds(Arrays.asList(saasSupplierId, saasSupplierId2));
        List<WarehouseStorageCenter> warehouseList = baseInfoService.getWarehouseByIds(Arrays.asList(ownWarehouseNo, ownWarehouseNo2));

        // 新增采购单已发布
        String remark="saas自动化多仓发布采购"+currentDateTime;
        Map<String, Object> addPurchaseParams = saasPurchaseStep.buildBatchPurchaseParmams(skuInfo,
                warehouseList,
                supplierList,
                remark, num,true);

        saasPurchaseStep.saveBatchPurchase(addPurchaseParams);
        //断言采购单生成和相关状态
        List<Purchases> purchasesList = saasPurchaseStep.queryPurchaseListByRemark(remark,tenantId);
        Assert.assertNotNull(purchasesList, "查询采购单结果为空");
        Assert.assertFalse(purchasesList.isEmpty(), "查询采购单结果为空");
        Assert.assertEquals(purchasesList.size(), 2, "结果不符合预期,应返回2条数据");
        for (Purchases purchase : purchasesList){
            Assert.assertEquals(purchase.getState(),Integer.valueOf(1),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                    purchase.getPurchaseNo(), purchase.getState()));
            //查看对应预约单生成
            Map<String, Object> queryArrangeParams = new HashMap<>();
            queryArrangeParams.put("purchasesNo", purchase.getPurchaseNo());
            JSONArray arrangeList = saasPurchaseStep.listArrangePurchasesNo(queryArrangeParams);
            JSONObject arrangeInfo = arrangeList.getJSONObject(0);

            //断言入库任务生成
            StockTaskStorage taskInfo = saasPurchaseStep.queryTaskByPurchaseNo(purchase.getPurchaseNo());
            Assert.assertNotNull(taskInfo, "查询入库任务结果为空");
            Assert.assertEquals(taskInfo.getState(),Integer.valueOf(0),  String.format("入库任务[%s]状态不符合预期，当前状态: %d",
                    taskInfo.getId(), taskInfo.getState()));

        }


    }

    @Story("自营仓采购流程")
    @Test( description = "多仓单供应商批次发布采购单并预约>查看预约单和入库任务")
    public void testPurchasePage008() {
        String currentDateTime = DateUtil.getCurrentDateTime();
        Integer num= 10;//采购数量,默认单价1

        List<Supplier> supplierList = baseInfoService.selectSupplierByIds(Collections.singletonList(saasSupplierId));
        List<WarehouseStorageCenter> warehouseList = baseInfoService.getWarehouseByIds(Arrays.asList(ownWarehouseNo, ownWarehouseNo2));

        // 新增采购单已发布
        String remark="saas自动化多仓发布采购"+currentDateTime;
        Map<String, Object> addPurchaseParams = saasPurchaseStep.buildBatchPurchaseParmams(skuInfo,
                warehouseList,
                supplierList,
                remark, num,true);

        saasPurchaseStep.saveBatchPurchase(addPurchaseParams);
        //断言采购单生成和相关状态
        List<Purchases> purchasesList = saasPurchaseStep.queryPurchaseListByRemark(remark,tenantId);
        Assert.assertNotNull(purchasesList, "查询采购单结果为空");
        Assert.assertFalse(purchasesList.isEmpty(), "查询采购单结果为空");
        Assert.assertEquals(purchasesList.size(), 2, "结果不符合预期,应返回1条数据");
        for (Purchases purchase : purchasesList){
            Assert.assertEquals(purchase.getState(),Integer.valueOf(1),  String.format("采购单[%s]状态不符合预期，当前状态: %d",
                    purchase.getPurchaseNo(), purchase.getState()));
            //查看对应预约单生成
            Map<String, Object> queryArrangeParams = new HashMap<>();
            queryArrangeParams.put("purchasesNo", purchase.getPurchaseNo());
            JSONArray arrangeList = saasPurchaseStep.listArrangePurchasesNo(queryArrangeParams);
            JSONObject arrangeInfo = arrangeList.getJSONObject(0);

            //断言入库任务生成
            StockTaskStorage taskInfo = saasPurchaseStep.queryTaskByPurchaseNo(purchase.getPurchaseNo());
            Assert.assertNotNull(taskInfo, "查询入库任务结果为空");
            Assert.assertEquals(taskInfo.getState(),Integer.valueOf(0),  String.format("入库任务[%s]状态不符合预期，当前状态: %d",
                    taskInfo.getId(), taskInfo.getState()));

        }


    }



}