package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.dal.mapper.StockTaskMapper;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.dal.model.StockTask;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 直发采购业务操作步骤封装类
 * 封装直发采购流程中的各个接口操作步骤
 * 
 * <AUTHOR> Test Engineer
 */
@Slf4j
@Component
public class DirectPurchaseStep {

    @Resource
    private Request request;

    @Resource
    private EnvVariable envVar;

    @Resource
    private StockTaskMapper stockTaskMapper;

    /**
     * 新建直发采购单
     * 通过/directpurchase/order/placeOrder接口新建直发采购单
     * @param params 采购单参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void placeDirectPurchaseOrder(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.placeOrder.getUrl());
        
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.placeOrder.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.placeOrder.getContentType(),
                params);
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("code"), "SUCCESS", "新建直发采购单异常");

    }

    /**
     * 查询采购单详情
     * 通过/pms-service/po/query/detail接口查询采购单状态
     * @param purchaseNo 采购单号
     */
    @Retry(attempts = 5, delay = 2000, onExceptions = {AssertionError.class})
    public void queryPurchaseOrderDetail(String purchaseNo) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.queryDetail.getUrl());
        
        Map<String, Object> params = new HashMap<>();
        params.put("purchaseNo", purchaseNo);
        
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.queryDetail.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.queryDetail.getContentType(),
                params);
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "查询采购单详情失败");

        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "采购单详情数据不能为空");
        Assert.assertEquals(data.getString("state"), "1", "采购单状态不符合预期(已发布)");
    }
    /**
     * 查询采购单详情
     * 通过/pms-service/po/query/detail接口查询采购单状态
     * @param purchaseNo 采购单号
     * @return 直发采购单对应直发订单号
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String queryDirectOrderDetail(String purchaseNo) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.queryDirectOrderDetail.getUrl()+"/"+purchaseNo);

        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.queryDirectOrderDetail.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.queryDirectOrderDetail.getContentType(),
                null);

        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("code"), "SUCCESS", "查询采购单详情失败");

        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "采购单详情数据不能为空");
        String directOrderNo= data.getString("orderNo");
        Assert.assertNotNull(directOrderNo,"直发采购单对应直发订单为空");
        return directOrderNo;

    }
    /**
     * 绑定预付金额
     * 通过/pms-service/po/advance/binding接口绑定预付金额
     * @param params 绑定参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void bindAdvancePayment(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.advanceBinding.getUrl());
        
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.advanceBinding.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.advanceBinding.getContentType(),
                params);
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "绑定预付金额失败");
    }

    /**
     * 一键发货
     * 通过/pms-service/po/upsert/oneClickDelivery接口一键发货
     * 
     * @param params 发货参数
     */
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public void oneClickDelivery(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.oneClickDelivery.getUrl());
        
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.oneClickDelivery.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.oneClickDelivery.getContentType(),
                params);
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "一键发货失败");
    }

    /**
     * 批量获取生效中阶梯价格
     * 通过/pms-service/srm-supplier-offer/select/StepPrice/batch接口批量获取生效中阶梯价格
     * 
     * @param params 自定义参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void getStepPriceBatch(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.getStepPriceBatch.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.getStepPriceBatch.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.getStepPriceBatch.getContentType(),
                params);
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "批量获取阶梯价格失败");

    }

    /**
     * 查询采购单及供应商的付款信息
     *
     * @param params 自定义参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public JSONObject queryPaymentBySupplier(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.queryPaymentBySupplier.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.queryPaymentBySupplier.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.queryPaymentBySupplier.getContentType(),
                params);

        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "查询采购单及供应商的付款信息失败");
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "采购单及供应商的付款信息数据不能为空");
        return data;

    }

    /**
     * 解绑预付金额
     * 通过/pms-service/po/advance/unbinding接口解绑预付金额
     * 
     * @param params 解绑参数
     */
    @Retry(delay = 2000, onExceptions = {AssertionError.class})
    public void unbindAdvancePayment(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.getXmAdminDomain(), PmsEnumsInterface.DirectPurchaseEnum.advanceUnbinding.getUrl());
        
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.advanceUnbinding.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.advanceUnbinding.getContentType(),
                params);
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "解绑预付金额失败");
    }

    /**
     * 采购新增--导入
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void uploadV2(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.DirectPurchaseEnum.uploadV2.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.uploadV2.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.uploadV2.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        Assert.assertNotNull(result.getString("data"));
    }

    /**
     * 直发采购导出
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void exportDetail(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.DirectPurchaseEnum.exportDetail.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.DirectPurchaseEnum.exportDetail.getMethod(),
                PmsEnumsInterface.DirectPurchaseEnum.exportDetail.getContentType(),
                params);
        Assert.assertEquals(res.getStatusCode() , 200, "采购导出失败");
    }
    /**
     * 构造直发采购参数
     * @param skuInfo 商品信息
     * @param userInfo 当前用户信息
     * @param supplierId 供应商ID
     * @param merchantInfo 下单门店信息
     * @return 构造好的直发采购参数
     */
    public Map<String, Object> buildPurchaseParams(SkuInfo skuInfo,String sku, Map<String, Object> userInfo, Integer supplierId,
                                                   Map<String, Object> merchantInfo, String remark) {
        Map<String, Object> params = new HashMap<>();
        JSONArray contactsInfo= (JSONArray) merchantInfo.get("contacts");
        // 下单基础信息
        params.put("mId", merchantInfo.get("mId"));
        params.put("mname", merchantInfo.get("mname"));
        params.put("contactPhone", contactsInfo.getJSONObject(0).getLongValue("phone"));
        params.put("contactId", contactsInfo.getJSONObject(0).getIntValue("contactId"));
        params.put("deliveryFee", 0);
        params.put("receivePlace",   contactsInfo.getJSONObject(0).getString("address"));
        params.put("logisticsInfo", "物流信息"+System.currentTimeMillis());
        
        // 构造purchasesResultVO
        Map<String, Object> purchasesResultVO = new HashMap<>();
        purchasesResultVO.put("purchaser", userInfo.get("realname"));
        purchasesResultVO.put("remark", remark);
        purchasesResultVO.put("purchaseTime", LocalDate.now().toString());
        purchasesResultVO.put("state", 1);
        purchasesResultVO.put("purchasesType", 1);
        purchasesResultVO.put("creatorId", userInfo.get("adminId"));
        purchasesResultVO.put("areaNo", 1);
        
        // 构造purchasesPlanResultVOS
        List<Map<String, Object>> purchasesPlanResultVOS = new ArrayList<>();
        Map<String, Object> planItem = new HashMap<>();
        planItem.put("price", 10);
        planItem.put("quantity", 10);
        planItem.put("singlePlice", 1);
        planItem.put("sku", sku);
        planItem.put("pdName", skuInfo.getPdName());
        planItem.put("extType", 0);
        planItem.put("type", 0);
        planItem.put("priceType", 0);
        planItem.put("supplierId", supplierId);
        purchasesPlanResultVOS.add(planItem);
        
        purchasesResultVO.put("purchasesPlanResultVOS", purchasesPlanResultVOS);
        params.put("purchasesResultVO", purchasesResultVO);
        
        // 构造orderItems
        List<Map<String, Object>> orderItems = new ArrayList<>();
        Map<String, Object> orderItem = new HashMap<>();
        orderItem.put("sku", sku);
        orderItem.put("price", 1);
        orderItem.put("amount", 11);
        orderItem.put("totalPrice", 11);
        orderItems.add(orderItem);
        
        params.put("orderItems", orderItems);
        
        return params;
    }

    /**
     * 查询出库任务
     * @param taskNo 任务编号
     * @param state 状态
     */
    @Retry(attempts = 3, delay = 3000, onExceptions = {AssertionError.class})
    public void queryStockTask(String taskNo,Integer  state){
        LambdaQueryWrapper<StockTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockTask::getTaskNo, taskNo);
        queryWrapper.eq(StockTask::getState, state);
        List<StockTask> outStockTasks = stockTaskMapper.selectList(queryWrapper);
        Assert.assertNotNull(outStockTasks,"未查询到对应出入库任务"+taskNo);

    }

}