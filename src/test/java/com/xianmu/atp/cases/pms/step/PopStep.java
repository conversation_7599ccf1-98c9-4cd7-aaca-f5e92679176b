package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.dal.dao.common.DingdingProcessFlowService;
import com.xianmu.atp.dal.dao.pms.PurchaseAdvancedOrderService;
import com.xianmu.atp.dal.dao.pms.SupplierDomainService;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.FlowRequest;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class PopStep{
    @Resource
    private Request request;

    @Resource
    private EnvVariable envVariable;


    /**
     * POP商品管理--查询商品类目接口
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void categoryIndex(Map<String, Object> params) {
        String URL = request.urlBuild(envVariable.xmAdminDomain, PmsEnumsInterface.PopEnum.categoryIndex.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PopEnum.categoryIndex.getMethod(),
                PmsEnumsInterface.PopEnum.categoryIndex.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("categoryList");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "未查询到类目数据");
    }

    /**
     * POP商品管理--查询商品列表接口
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void skuIndex(Map<String, Object> params) {
        String URL = request.urlBuild(envVariable.xmAdminDomain, PmsEnumsInterface.PopEnum.skuIndex.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PopEnum.skuIndex.getMethod(),
                PmsEnumsInterface.PopEnum.skuIndex.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "未查询到商品列表数据");
        TemVar.getCurrent().setSupplierWeightPrice(res_list.getJSONObject(0).getBigDecimal(("supplierWeightPrice")));
        TemVar.getCurrent().setSupplierPrice(res_list.getJSONObject(0).getBigDecimal(("supplierPrice")));
    }

    /**
     *   POP商品管理--价格提报
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void priceUpsert(Map<String, Object> params) {
        String URL = request.urlBuild(envVariable.xmAdminDomain, PmsEnumsInterface.PopEnum.priceUpsert.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PopEnum.priceUpsert.getMethod(),
                PmsEnumsInterface.PopEnum.priceUpsert.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     *   POP商品管理--供应商库存提报
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void stockUpsert(Map<String, Object> params) {
        String URL = request.urlBuild(envVariable.xmAdminDomain, PmsEnumsInterface.PopEnum.stockUpsert.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PopEnum.stockUpsert.getMethod(),
                PmsEnumsInterface.PopEnum.stockUpsert.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     *   POP商品管理--提报记录
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public JSONObject stockChangeRecord(Map<String, Object> params) {

        String URL = request.urlBuild(envVariable.xmAdminDomain, PmsEnumsInterface.PopEnum.stockChangeRecord.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PopEnum.stockChangeRecord.getMethod(),
                PmsEnumsInterface.PopEnum.stockChangeRecord.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "提报记录为空");
        Assert.assertEquals(result.getString("msg"),"请求成功");
        return res_list.getJSONObject(0);
    }

    /**
     *   POP商品上新--发布商品
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public String publishPopProduct(Map<String, Object> params) {

        String URL = request.urlBuild(envVariable.xmAdminDomain, PmsEnumsInterface.PopEnum.publish_pop_product.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PopEnum.publish_pop_product.getMethod(),
                PmsEnumsInterface.PopEnum.publish_pop_product.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        return result.getJSONObject("data").getString("pdId");
    }

}
