package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.enums.api.common.CommonEnumsInterface;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class PurchasesStep {

    @Resource
    private Request request;

    //场景变量
    @Resource
    private EnvVariable envVar;



    /**
     * 采购新增--导入
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void upload(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseEnum.uploadV3.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseEnum.uploadV3.getMethod(),
                PmsEnumsInterface.PurchaseEnum.uploadV3.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        Assert.assertNotNull(result.getString("data"));
    }

    /**
     * 采购单导出
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void export(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseEnum.exportDetail.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseEnum.exportDetail.getMethod(),
                PmsEnumsInterface.PurchaseEnum.exportDetail.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        Assert.assertNotNull(result.getString("data"));
        TemVar.getCurrent().setExcelResId(result.getJSONObject("data").getString("resId"));//设置结果id

    }

    /**
     * 采购单列表导出
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void exportList(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseEnum.exportList.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseEnum.exportList.getMethod(),
                PmsEnumsInterface.PurchaseEnum.exportList.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        Assert.assertNotNull(result.getString("data"));
        TemVar.getCurrent().setExcelResId(result.getJSONObject("data").getString("resId"));//设置结果id

    }

    /**
     * 采购单导出结果
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void exportResult(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseEnum.uploadV3.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseEnum.uploadV3.getMethod(),
                PmsEnumsInterface.PurchaseEnum.uploadV3.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        Assert.assertNotNull(result.getString("data"));
    }

}
