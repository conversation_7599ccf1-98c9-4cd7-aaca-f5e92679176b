package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.dal.dao.common.DingdingProcessFlowService;
import com.xianmu.atp.dal.dao.fms.FinanceReceiptBillService;
import com.xianmu.atp.dal.dao.pms.SupplierDomainService;
import com.xianmu.atp.dal.model.FinanceReceiptBill;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.FlowRequest;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class PurchaseRefundStep{
    @Resource
    private Request request;
    //场景变量
    @Resource
    private EnvVariable envVar;
    //数据库操作
    @Resource
    private SupplierDomainService supplierDomainService;

    @Resource
    private FinanceReceiptBillService financeReceiptBillService;
    @Resource
    private DingdingProcessFlowService dingdingProcessFlowService;


    /**
     * 新增预付退款单
     */
    public void addPurchaseRefund() {
        //获取供应商ID
        Integer supplierId = envVar.getSupplierId();
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.add.getUrl());
        JSONObject bankFlowingWaterInfo = selectBankFlowingWaterBySupplier(String.valueOf(supplierId));
        bankFlowingWaterInfo.put("paymentAmount",1);
        bankFlowingWaterInfo.put("paymentId", bankFlowingWaterInfo.getString("id"));
        HashMap<String, Object> body = new HashMap<>();
        //设置退款单新增参数--退款金额=1，类型=预付款退款
        body.put("paymentList", Collections.singletonList(bankFlowingWaterInfo));
        body.put("refundAmount", 1);
        body.put("refundType", 1);
        body.put("supplierId", supplierId);

        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseAdvanceEnum.add.getMethod(),
                PmsEnumsInterface.PurchaseAdvanceEnum.add.getContentType(),
                body);
        Assert.assertNotNull(res.getBody(),"新增预付退款单失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200,"'新增预付退款单失败'");
        TemVar.getCurrent().setPurchaseRefundId(result.getString("data")); //环境变量--设置预付退款单id
        FinanceReceiptBill financeReceiptBillInfo =financeReceiptBillService.selectAllByFinanceOrderIdAndSourceNo(result.getInteger("data"));
        TemVar.getCurrent().setFinanceReceiptId(financeReceiptBillInfo.getFinanceReceiptId().toString()); //环境变量--设置退款审核单id


    }

    /**
     * 根据供应商名称查询流水信息
     */
    public JSONObject selectBankFlowingWaterBySupplier(String supplierId) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.financeBankFlowingWaterQuery.getUrl());
        HashMap<String, Object> body = new HashMap<>();
        Supplier supplierInfo = supplierDomainService.selectById(Integer.valueOf(supplierId));
        body.put("payType","2");
        body.put("claimStatusList","0,1");
        body.put("pageIndex","1");
        body.put("pageSize","10");
        body.put("userName",supplierInfo.getName());

        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseRefundEnum.financeBankFlowingWaterQuery.getMethod(),
                PmsEnumsInterface.PurchaseRefundEnum.financeBankFlowingWaterQuery.getContentType(),
                body);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(),"流水查询结果为空");
        return res_list.getJSONObject(0);
    }

    /**
     * 查询采购退款单列表
     */
    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void purchaseRefundList(Map<String, Object> params) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.query.getUrl());
        HttpResponseWrapper res = request.sendRequest(url,
                PmsEnumsInterface.PurchaseRefundEnum.query.getMethod(),
                PmsEnumsInterface.PurchaseRefundEnum.query.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("status"),"200");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "未查询到单据数据");
        boolean containsExpected = false;
        String bizId = TemVar.getCurrent().getPurchaseRefundId();
        for (int i = 0; i < res_list.size(); i++) {
            JSONObject item = res_list.getJSONObject(i);
            if (bizId.equals(item.getString("refundId"))) {
                containsExpected = true;
                break;
            }
        }
        Assert.assertTrue(containsExpected, "列表返回结果不包含指定的业务单号"+bizId);
    }

    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void refundDetail(Map<String, Object> params) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.refundDetail.getUrl());
        HttpResponseWrapper res = request.sendRequest(url,
                PmsEnumsInterface.PurchaseRefundEnum.refundDetail.getMethod(),
                PmsEnumsInterface.PurchaseRefundEnum.refundDetail.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("status") ,"200");
        Assert.assertEquals(result.getString("msg") ,"请求成功");

    }

    /**
     * 供应商退款核销单--列表查询
     */
    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void receivableList(Map<String, Object> params) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.receivableList.getUrl());
        HttpResponseWrapper res = request.sendRequest(url,
                PmsEnumsInterface.PurchaseRefundEnum.receivableList.getMethod(),
                PmsEnumsInterface.PurchaseRefundEnum.receivableList.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("status"),"200");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "未查询到单据数据");
        boolean containsExpected = false;
        String bizId = TemVar.getCurrent().getFinanceReceiptId();
        for (int i = 0; i < res_list.size(); i++) {
            JSONObject item = res_list.getJSONObject(i);
            if (bizId.equals(item.getString("id"))) {
                containsExpected = true;
                break;
            }
        }
        Assert.assertTrue(containsExpected, "列表返回结果不包含指定的业务单号"+bizId);
    }

    /**
     * 供应商退款核销单--详情
     */
    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void receivableDetail(Map<String, Object> params) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.receivableDetail.getUrl());
        HttpResponseWrapper res = request.sendRequest(url,
                PmsEnumsInterface.PurchaseRefundEnum.receivableDetail.getMethod(),
                PmsEnumsInterface.PurchaseRefundEnum.receivableDetail.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("status") ,"200");
        Assert.assertEquals(result.getString("msg") ,"请求成功");

    }

    /**
     * 供应商退款核销单--拒绝
     */
    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void receivableReject(Map<String, Object> params) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.receivableReject.getUrl());
        HttpResponseWrapper res = request.sendRequest(url,
                PmsEnumsInterface.PurchaseRefundEnum.receivableReject.getMethod(),
                PmsEnumsInterface.PurchaseRefundEnum.receivableReject.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("status"),"200");
    }

    /**
     * 供应商退款核销单--通过
     */
    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void receivableConfirm(Map<String, Object> params) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseRefundEnum.receivableConfirm.getUrl());
        HttpResponseWrapper res = request.sendRequest(url,
                PmsEnumsInterface.PurchaseRefundEnum.receivableConfirm.getMethod(),
                PmsEnumsInterface.PurchaseRefundEnum.receivableConfirm.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("status"),"200");
    }

}
