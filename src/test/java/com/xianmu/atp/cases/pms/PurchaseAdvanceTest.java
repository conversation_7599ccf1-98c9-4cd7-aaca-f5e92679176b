package com.xianmu.atp.cases.pms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.step.PurchaseAdvanceStep;
import com.xianmu.atp.enums.FlowConfigEnums;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import com.xianmu.atp.generic.common.FlowRequest;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.upload.QiNiuUpload;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.*;

import javax.annotation.Resource;
import java.util.HashMap;

@Slf4j
@Epic("PMS")
@Feature("预付单流程")
@Owner("大鹏")
public class PurchaseAdvanceTest extends BaseTest {

    @Resource
    private Request request;

    @Resource
    private QiNiuUpload qiNiuUpload;
    @Resource
    private FlowRequest flowRequest;
    //全局变量
    @Resource
    private EnvVariable envVar;

    //预付单操作步骤封装
    @Resource
    private PurchaseAdvanceStep purchaseAdvanceStep;

    @BeforeMethod(description = "初始化操作--登录")
    public void login(){
        String URL = request.urlBuild(xmAdminDomain, LoginEnumsInterface.xianmuLogin.getUrl());
        HashMap<String, String> loginBody = new HashMap<>();
        loginBody.put("username", envVar.getAdminUserName());
        loginBody.put("password", envVar.getAdminpassword());
        HttpResponseWrapper loginRes= request.sendRequest(URL,
                LoginEnumsInterface.xianmuLogin.getMethod(),
                LoginEnumsInterface.xianmuLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        request.setToken(token);
    }



    @AfterMethod(description = "临时变量")
    public void tear_down(){
        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
        TemVar.remove();
    }
    //获取用例生成的临时变量
    public TemVar temVar(){
        return TemVar.getCurrent();
    }



    @Story("预付单--不绑定采购单号")
    @Test(description = "预付单新增>预付付款审批拒绝")
    public void purchaseAdvance_case02(){
        //预付单新建-未关联采购单
        purchaseAdvanceStep.addPurchaseAdvance(null);
        //查询预付单列表查询--付款审核中的预付单
        HashMap<String, Object> purchaseAdvance_list_body = new HashMap<>();
        purchaseAdvance_list_body.put("status",4);
        purchaseAdvance_list_body.put("pageIndex",1);
        purchaseAdvance_list_body.put("pageSize",10);
        purchaseAdvance_list_body.put("id", temVar().getPurchaseAdvanceId());
        purchaseAdvanceStep.queryPurchaseAdvanceRequest(purchaseAdvance_list_body);
        //查询付款单列表查询--待审批\预付单类型、来源单号的付款单
        HashMap<String, Object> payment_list_body = new HashMap<>();
        payment_list_body.put("status",3);
        payment_list_body.put("type",1);
        payment_list_body.put("additionalId", temVar().getPurchaseAdvanceId());
        purchaseAdvanceStep.queryFinancePaymentList(payment_list_body);
        flowRequest.approveFlow(temVar().getPaymentId(),
                FlowConfigEnums.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL.getProcessType(),
                false);
        //验证付款单单据状态=已驳回
        payment_list_body.put("status",0);
        purchaseAdvanceStep.queryFinancePaymentList(payment_list_body);
    }

    @Story("预付单--不绑定采购单号")
    @Test(description = "预付单新增>审批通过>上传凭证")
    public void purchaseAdvance_case03(){
        //预付单新建-未关联采购单
        purchaseAdvanceStep.addPurchaseAdvance(null);
        //查询预付单列表查询--付款审核中的预付单
        HashMap<String, Object> purchaseAdvance_list_body = new HashMap<>();
        purchaseAdvance_list_body.put("status",4);
        purchaseAdvance_list_body.put("pageIndex",1);
        purchaseAdvance_list_body.put("pageSize",10);
        purchaseAdvance_list_body.put("id", temVar().getPurchaseAdvanceId());
        purchaseAdvanceStep.queryPurchaseAdvanceRequest(purchaseAdvance_list_body);
        //查询付款单列表查询--待审批\预付单类型、来源单号的付款单
        HashMap<String, Object> payment_list_body = new HashMap<>();
        payment_list_body.put("status",3);
        payment_list_body.put("type",1);
        payment_list_body.put("additionalId", temVar().getPurchaseAdvanceId());
        purchaseAdvanceStep.queryFinancePaymentList(payment_list_body);
        flowRequest.approveFlow(temVar().getPaymentId(),
                FlowConfigEnums.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL.getProcessType(),
                true);
        //验证付款单单据状态=待付款
        payment_list_body.put("status",1);
        purchaseAdvanceStep.queryFinancePaymentList(payment_list_body);
        //付款单上传凭证
        qiNiuUpload.uploadQiNiuYun();
        //设置打款人
        purchaseAdvanceStep.saveSettlementConfig();
        purchaseAdvanceStep.uploadPaymentVoucher(new HashMap<String, Object>(){{
            put("id", temVar().getPaymentId());
            put("paymentVoucher",picPath);
        }});
        //验证付款单单据状态=已付款
        payment_list_body.put("status",2);
        purchaseAdvanceStep.queryFinancePaymentList(payment_list_body);
        //查询预付单记录列表查询--采购预付单记录，验证变动记录正确
        purchaseAdvanceStep.queryAdvanceReocrdList(new HashMap<String, Object>(){{
            put("billId", temVar().getPurchaseAdvanceId());
            put("pageIndex",1);
            put("pageSize",10);
            put("supplierId",envVar.getSupplierId());
            put("type",10);
        }});

    }

}


