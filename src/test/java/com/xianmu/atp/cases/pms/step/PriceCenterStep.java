package com.xianmu.atp.cases.pms.step;

import cn.hutool.log.Log;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.FlowRequest;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.opentest4j.AssertionFailedError;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class PriceCenterStep{

    @Resource
    private Request request;

    //场景变量
    @Resource
    private EnvVariable envVar;

    /**
     * 价格中心佣金--列表接口
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public  void queryPage(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterCommissionEnum.queryPage.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PriceCenterCommissionEnum.queryPage.getMethod(),
                PmsEnumsInterface.PriceCenterCommissionEnum.queryPage.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        if(Objects.nonNull(res_list) && !res_list.isEmpty()){
            TemVar.getCurrent().setPriceCenterId(res_list.getJSONObject(0).getString("id"));
        }
    }

    /**
     * 价格中心佣金--新增报价单
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void add(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterCommissionEnum.add.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PriceCenterCommissionEnum.add.getMethod(),
                PmsEnumsInterface.PriceCenterCommissionEnum.add.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     * 价格中心佣金--作废报价单
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void delete(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterCommissionEnum.delete.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PriceCenterCommissionEnum.delete.getMethod(),
                PmsEnumsInterface.PriceCenterCommissionEnum.delete.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     * 步骤1: 新增报价单
     * 接口: POST /pms-service/price-center/upsert/add
     * 参数: 基于数据库日志上下文的真实参数
     * 复用现有工具类和环境变量
     */
    @Step("步骤1: 新增报价单")
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void addPriceOffer(Map<String, Object> requestBody) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterEnum.add.getUrl());



        // 使用Request工具类发送请求，添加超时设置
        HttpResponseWrapper response = request.sendRequest(url, PmsEnumsInterface.PriceCenterEnum.add.getMethod(),
                PmsEnumsInterface.PriceCenterEnum.add.getContentType(), requestBody);

        // 验证响应状态码
        Assert.assertEquals(response.getStatusCode(), 200, "新增报价单接口状态码验证失败");

        // 验证响应体结构
        JSONObject result = JSON.parseObject(response.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "新增报价单接口返回状态验证失败");
        Assert.assertNotNull(result.get("data"), "新增报价单返回数据不能为空");

    }

    /**
     * 步骤2: 查询报价单分页列表
     * 接口: POST /pms-service/price-center/query/page
     * 参数: 基于数据库日志上下文的真实参数
     * 复用现有工具类和TemVar管理
     */
    @Step("步骤2: 查询报价单分页列表")
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class, AssertionFailedError.class})
    public void queryPriceOfferPage(Map<String, Object> requestBody,boolean resFlag) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterEnum.queryPage.getUrl());

        HttpResponseWrapper response = request.sendRequest(url, PmsEnumsInterface.PriceCenterEnum.queryPage.getMethod(),
                PmsEnumsInterface.PriceCenterEnum.queryPage.getContentType(), requestBody);

        // 验证响应状态码
        Assert.assertEquals(response.getStatusCode(), 200, "查询报价单分页列表接口状态码验证失败");

        // 验证响应体结构
        JSONObject result = JSON.parseObject(response.getBody());

        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200) ,"查询报价单分页列表接口返回状态验证失败");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        //基于result判断是否需要断言lsit数据
        if (Objects.isNull(res_list) || res_list.isEmpty()){
            if (resFlag) {
                Assert.fail("查询结果为空");
            }
        }else {
            TemVar.getCurrent().setPriceCenterConfigId(String.valueOf(result.getJSONObject("data").getJSONArray("list").getJSONObject(0).getLong("id")));
        }

    }

    /**
     * 步骤3: 根据报价单ID查询成本明细
     * 接口: POST /pms-service/price-center/query/cost-detail/with-offerId
     * 参数: 基于数据库日志上下文的真实参数
     * 复用现有工具类和TemVar管理
     */
    @Step("步骤3: 根据报价单ID查询成本明细")
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryCostDetailWithOfferId(String priceCenterConfigId) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterEnum.queryWithId.getUrl());

        // 基于数据库日志上下文构建请求参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("id", Long.parseLong(priceCenterConfigId));

        HttpResponseWrapper response = request.sendRequest(url, PmsEnumsInterface.PriceCenterEnum.queryWithId.getMethod(),
                PmsEnumsInterface.PriceCenterEnum.queryWithId.getContentType(), requestBody);

        // 验证响应状态码
        Assert.assertEquals(response.getStatusCode(), 200, "根据报价单ID查询成本明细接口状态码验证失败");

        // 验证响应体结构
        JSONObject result = JSON.parseObject(response.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "根据报价单ID查询成本明细接口返回状态验证失败");
        Assert.assertNotNull(result.getJSONObject("data"), "成本明细数据不能为空");
    }

    /**
     * 步骤4: 根据SKU查询成本明细
     * 接口: POST /pms-service/price-center/query/cost-detail/with-sku
     * 参数: 基于数据库日志上下文的真实参数
     * 复用现有工具类和TemVar管理
     */
    @Step("步骤4: 根据SKU查询成本明细")
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryCostDetailWithSku(Map<String, Object> requestBody) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterEnum.queryWithSku.getUrl());



        HttpResponseWrapper response = request.sendRequest(url, PmsEnumsInterface.PriceCenterEnum.queryWithSku.getMethod(),
                PmsEnumsInterface.PriceCenterEnum.queryWithSku.getContentType(), requestBody);

        // 验证响应状态码
        Assert.assertEquals(response.getStatusCode(), 200, "根据SKU查询成本明细接口状态码验证失败");

        // 验证响应体结构
        JSONObject result = JSON.parseObject(response.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "根据SKU查询成本明细接口返回状态验证失败");
        Assert.assertNotNull(result.getJSONObject("data"), "成本明细数据不能为空");

    }

    /**
     * 步骤5: 查询所有类型的生效价格
     * 接口: POST /pms-service/price-center/query/all-type-price
     * 参数: 基于数据库日志上下文的真实参数
     * 复用现有工具类和TemVar管理
     */
    @Step("步骤5: 查询所有类型的生效价格")
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryAllTypePrice(Map<String, Object> requestBody) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterEnum.queryAllPrice.getUrl());



        HttpResponseWrapper response = request.sendRequest(url, PmsEnumsInterface.PriceCenterEnum.queryAllPrice.getMethod(),
                PmsEnumsInterface.PriceCenterEnum.queryAllPrice.getContentType(), requestBody);

        // 验证响应状态码
        Assert.assertEquals(response.getStatusCode(), 200, "查询所有类型生效价格接口状态码验证失败");

        // 验证响应体结构
        JSONObject result = JSON.parseObject(response.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "查询所有类型生效价格接口返回状态验证失败");
        Assert.assertNotNull(result.getJSONArray("data"), "价格数据不能为空");

    }

    /**
     * 步骤6: 作废报价单
     * 接口: POST /pms-service/price-center/upsert/invalid
     * 参数: 基于数据库日志上下文的真实参数
     * 复用现有工具类和TemVar管理
     */
    @Step("步骤6: 作废报价单")
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void invalidPriceOffer(String priceCenterConfigId) {
        String url = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PriceCenterEnum.invalid.getUrl());


        // 基于数据库日志上下文构建请求参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("offerDetailId", Long.parseLong(priceCenterConfigId));

        HttpResponseWrapper response = request.sendRequest(url, PmsEnumsInterface.PriceCenterEnum.invalid.getMethod(),
                PmsEnumsInterface.PriceCenterEnum.invalid.getContentType(), requestBody);

        // 验证响应状态码
        Assert.assertEquals(response.getStatusCode(), 200, "作废报价单接口状态码验证失败");

        // 验证响应体结构
        JSONObject result = JSON.parseObject(response.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "作废报价单接口返回状态验证失败");


        log.info("作废报价单成功，报价单ID: {}", priceCenterConfigId);

    }

}
