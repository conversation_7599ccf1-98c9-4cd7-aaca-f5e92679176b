package com.xianmu.atp.cases.pms.aiCase;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.step.PurchaseAdvanceStep;
import com.xianmu.atp.cases.pms.aiCase.step.PurchaseReconciliationStep;
import com.xianmu.atp.dal.dao.common.BaseInfoService;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import com.xianmu.atp.enums.FlowConfigEnums;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import com.xianmu.atp.generic.common.FlowRequest;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.generic.common.UtilRequest;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 预付对账流程测试用例
 * Author: AI Test Engineer
 * 包含完整的预付对账流程验证，从采购单创建到对账单审批
 * 测试类型: 功能测试
 */
@Slf4j
@Epic("PMS")
@Feature("预付对账流程")
@Owner("大鹏")
public class PurchaseReconciliationTest extends BaseTest {

    @Resource
    private EnvVariable envVar;

    @Resource
    private PurchaseReconciliationStep reconciliationStep;
    
    @Resource
    private PurchaseAdvanceStep purchaseAdvanceStep;
    @Resource
    private BaseInfoService baseInfoService;

    @Resource
    private UtilRequest utilRequest;
    @Resource
    private Request request;

    private  String testSku; // 使用EnvVariable中定义的SKU
    private  Integer supplierId; // 使用EnvVariable中定义的供应商ID
    private  String warehouseNo; // 使用EnvVariable中定义的仓库编号

    private Supplier supplierInfo;
    private SkuInfo skuInfo;

    private WarehouseStorageCenter warehouseInfo;

    @Resource
    private FlowRequest flowRequest;

    @BeforeClass
    public void init() {
        log.info("预付对账流程测试用例--初始化");
        // 登录初始化
        String URL = request.urlBuild(xmAdminDomain, LoginEnumsInterface.xianmuLogin.getUrl());
        HashMap<String, String> loginBody = new HashMap<>();
        loginBody.put("username", envVar.getAdminUserName());
        loginBody.put("password", envVar.getAdminpassword());
        HttpResponseWrapper loginRes= request.sendRequest(URL,
                LoginEnumsInterface.xianmuLogin.getMethod(),
                LoginEnumsInterface.xianmuLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        request.setToken(token);
        testSku = envVar.getSku(); // 使用EnvVariable中定义的SKU
        supplierId = envVar.getSupplierId(); // 使用EnvVariable中定义的供应商ID
        warehouseNo = envVar.getWarehouseNo(); // 使用EnvVariable中定义的仓库编号
        log.info("使用域名: {}", envVar.getXmAdminDomain());
        log.info("使用测试SKU: {}", testSku);
        log.info("使用供应商ID: {}", supplierId);
        log.info("使用仓库编号: {}", warehouseNo);
        skuInfo =baseInfoService.getProductInfo(envVar.getSku());
        supplierInfo = baseInfoService.selectById(envVar.getSupplierId());
        warehouseInfo = baseInfoService.getWarehouseInfo(envVar.getWarehouseNo());
    }
    public TemVar temVar(){
        return TemVar.getCurrent();
    }


    @AfterMethod
    public void teatDown() {
        // 每个测试方法执行前清理临时变量
        Allure.addAttachment("用例执行--临时变量", temVar().toString());
        TemVar.remove();
    }

    /**
     * 用例ID: TC_API_RECONCILIATION_COMPLETE_FLOW_001
     * 用例名称: 预付对账完整流程功能验证
     * 用例级别: P0(核心)
     * 测试类型: 功能测试
     * 前置条件: 用户已登录且有对账权限，存在有效的供应商和SKU数据
     * 测试步骤:
     * 1. 调用/quantity-service/pms/xmPurchaseCreate接口创建采购单
     * 2. 调用预付单相关接口创建预付单并审批付款
     * 3. 调用/pms-service/po/queryPayment接口查询采购单付款信息
     * 4. 调用/pms-service/financeAccountStatement/purchase/reconciliation/selectWarehousingOrderBySupplier/page接口查询入库单明细
     * 5. 调用/pms-service/financeAccountStatement/purchase/reconciliation/addReconciliation接口创建对账单
     * 预期结果:
     * - 所有接口返回状态码200
     * - 采购单创建成功并返回有效采购单号
     * - 付款信息查询返回正确的金额数据
     * - 入库单明细查询返回完整的数据结构
     * - 对账单创建成功并返回对账单ID
     */
    @Story("预付对账流程")
    @Test(description = "预付对账作废流程1:发起>撤销申请")
    public void TC_API_RECONCILIATION_COMPLETE_FLOW_001() {
        // 步骤1: 创建采购单并入库
        int quantity = 10;
        BigDecimal advancePrice=new BigDecimal(quantity);//预付金额

        Map<String, Object> purchaseBody = new HashMap<>();
        purchaseBody.put("supplierNo", supplierId);
        purchaseBody.put("warehouseNo", warehouseNo);
        purchaseBody.put("quantity", quantity);
        purchaseBody.put("skuList", Lists.newArrayList(testSku));
        purchaseBody.put("flag", 3);
        purchaseBody.put("envFlag", env);
        String purchaseNo = utilRequest.createPurchaseOrder(purchaseBody);

        // 步骤2: 绑定预付余额- 标准业务流程验证
        purchaseAdvanceStep.checkBySupplier(purchaseNo);
        Map<String, Object> advancePaymentBindBody = new HashMap<>();
        advancePaymentBindBody.put("purchaseNo", purchaseNo);
        advancePaymentBindBody.put("supplierId", supplierId);
        advancePaymentBindBody.put("amount", advancePrice.toString());
        purchaseAdvanceStep.advancePaymentBind(advancePaymentBindBody);
        //查询预付单记录列表查询--采购预付单记录，验证变动记录正确
        purchaseAdvanceStep.queryAdvanceReocrdList(new HashMap<String, Object>(){{
            put("billId", temVar().getPurchaseAdvanceId());
            put("pageIndex",1);
            put("pageSize",10);
            put("supplierId",envVar.getSupplierId());
            put("type",10);
        }});

        Map<String, Object> checkAmount= new HashMap<>();
        checkAmount.put("advanceAmount", advancePrice.toString());
        // 步骤3: 查询采购单付款信息 - 金额数据验证
        reconciliationStep.queryPurchasePayment(new HashMap<String, Object>(){{
                put("purchaseNo", purchaseNo);
        }},checkAmount);

        // 步骤4: 查询入库单明细 - 业务逻辑验证
        JSONArray detailList= reconciliationStep.queryInAndOut(new HashMap<String, Object>(){{
                put("supplierId", supplierId);
                put("purchaseNo", purchaseNo);
                put("skipStoreOnly", 0);
                put("pageIndex", 1);
                put("pageSize", 50);
            }});

        //步骤5: 添加入库单明细完整校验
        JSONArray financeAccountStatementDetailVOS = new JSONArray();
        Map<String, Object> verificationDetailData = new HashMap<>();
        verificationDetailData.put("purchaseNo", purchaseNo);
        verificationDetailData.put("sku", testSku);
        verificationDetailData.put("stockTaskProcessDetailId", detailList.getJSONObject(0).getIntValue("stockTaskProcessDetailId"));
        financeAccountStatementDetailVOS.add(verificationDetailData);
        Map<String, Object> verificationData = new HashMap<>();
        verificationData.put("financeAccountStatementDetailVOS", financeAccountStatementDetailVOS);
        reconciliationStep.verificationPurchase(verificationData);

        //步骤6: 查询供应商入库单和退货单检验
        Map<String, Object> checkInAndOutData = new HashMap<>();
        checkInAndOutData.put("supplierId", supplierId);
        checkInAndOutData.put("purchaseNoList",Lists.newArrayList(purchaseNo) );
        reconciliationStep.checkInAndOut(checkInAndOutData);

        //步骤7: 核销流水计算
        JSONArray reduceDetailList = new JSONArray();
        Map<String, Object> reduceDetail = detailList.getJSONObject(0);
        reduceDetail.put("purchaseNo",purchaseNo );
        reduceDetail.put("amount", advancePrice);
        reduceDetail.put("adjustAmount", 0);
        reduceDetailList.add(reduceDetail);
        Map<String, Object> reduceData = new HashMap<>();
        reduceData.put("dataList", reduceDetailList);
        reconciliationStep.reduce(reduceData,advancePrice);

        //步骤8: 获取供应商账户信息
        Map<String, Object> accountDetailData = new HashMap<>();
        accountDetailData.put("id",supplierId);
        JSONObject accountDetail = reconciliationStep.accountDetail(accountDetailData);

        //步骤9: 获取审批人信息
        Map<String, Object> auditInformationData = new HashMap<>();
        auditInformationData.put("supplierId",supplierId );
        reconciliationStep.auditInformation(auditInformationData);

        // 步骤10: 创建对账单
        Map<String, Object> addReconciliationBody = new HashMap<>();
        addReconciliationBody.put("writeOffAmount", advancePrice);
        addReconciliationBody.put("totalBillAmount", advancePrice);
        addReconciliationBody.put("estimateAmount", advancePrice);
        addReconciliationBody.put("taxNumber", supplierInfo.getTaxNumber());
        addReconciliationBody.put("supplierName", supplierInfo.getName());
        addReconciliationBody.put("supplierId", supplierId);
        addReconciliationBody.put("supplierAccountId", accountDetail.getLongValue("id"));
        addReconciliationBody.put("payType", accountDetail.getIntValue("payType"));
        addReconciliationBody.put("dataList", reduceDetailList);
        addReconciliationBody.put("remark", "自动化预付对账"+ System.currentTimeMillis());
        reconciliationStep.addReconciliation(addReconciliationBody);

        // 步骤11: 校验对账单数据-处于账单审核中\审批流待审批
        reconciliationStep.reconciliationCheck(1, temVar().getReconciliationId(),
                FlowConfigEnums.PURCHASE_STATEMENT_APPROVAL.getProcessType(),
                1);

        // 步骤12: 撤销申请
        reconciliationStep.reconciliationAdvanceCancel(new HashMap<String, Object>(){{
            put("id", temVar().getReconciliationId());
        }});
        // 步骤13: 校验对账单数据-作废、审批流撤销
        reconciliationStep.reconciliationCheck(9, temVar().getReconciliationId(),
                FlowConfigEnums.PURCHASE_STATEMENT_APPROVAL.getProcessType(),4);

    }

    @Story("预付对账流程")
    @Test(description = "预付对账作废流程2:发起>审批通过>作废")
    public void TC_API_RECONCILIATION_COMPLETE_FLOW_002() {
        // 步骤1: 创建采购单
        int quantity = 10;
        BigDecimal advancePrice=new BigDecimal(quantity);//预付金额

        Map<String, Object> purchaseBody = new HashMap<>();
        purchaseBody.put("supplierNo", supplierId);
        purchaseBody.put("warehouseNo", warehouseNo);
        purchaseBody.put("quantity", quantity);
        purchaseBody.put("skuList", Lists.newArrayList(testSku));
        purchaseBody.put("flag", 3);
        purchaseBody.put("envFlag", env);
        String purchaseNo = utilRequest.createPurchaseOrder(purchaseBody);

        // 步骤2: 绑定预付余额- 标准业务流程验证
        purchaseAdvanceStep.checkBySupplier(purchaseNo);
        Map<String, Object> advancePaymentBindBody = new HashMap<>();
        advancePaymentBindBody.put("purchaseNo", purchaseNo);
        advancePaymentBindBody.put("supplierId", supplierId);
        advancePaymentBindBody.put("amount", advancePrice.toString());
        purchaseAdvanceStep.advancePaymentBind(advancePaymentBindBody);
        //查询预付单记录列表查询--采购预付单记录，验证变动记录正确
        purchaseAdvanceStep.queryAdvanceReocrdList(new HashMap<String, Object>(){{
            put("billId", temVar().getPurchaseAdvanceId());
            put("pageIndex",1);
            put("pageSize",10);
            put("supplierId",envVar.getSupplierId());
            put("type",10);
        }});

        Map<String, Object> checkAmount= new HashMap<>();
        checkAmount.put("advanceAmount", advancePrice.toString());
        // 步骤3: 查询采购单付款信息 - 金额数据验证
        reconciliationStep.queryPurchasePayment(new HashMap<String, Object>(){{
            put("purchaseNo", purchaseNo);
        }},checkAmount);

        // 步骤4: 查询入库单明细 - 业务逻辑验证
        JSONArray detailList= reconciliationStep.queryInAndOut(new HashMap<String, Object>(){{
            put("supplierId", supplierId);
            put("purchaseNo", purchaseNo);
            put("skipStoreOnly", 0);
            put("pageIndex", 1);
            put("pageSize", 50);
        }});

        //步骤5: 添加入库单明细完整校验
        JSONArray financeAccountStatementDetailVOS = new JSONArray();
        Map<String, Object> verificationDetailData = new HashMap<>();
        verificationDetailData.put("purchaseNo", purchaseNo);
        verificationDetailData.put("sku", testSku);
        verificationDetailData.put("stockTaskProcessDetailId", detailList.getJSONObject(0).getIntValue("stockTaskProcessDetailId"));
        financeAccountStatementDetailVOS.add(verificationDetailData);
        Map<String, Object> verificationData = new HashMap<>();
        verificationData.put("financeAccountStatementDetailVOS", financeAccountStatementDetailVOS);
        reconciliationStep.verificationPurchase(verificationData);

        //步骤6: 查询供应商入库单和退货单检验
        Map<String, Object> checkInAndOutData = new HashMap<>();
        checkInAndOutData.put("supplierId", supplierId);
        checkInAndOutData.put("purchaseNoList",Lists.newArrayList(purchaseNo) );
        reconciliationStep.checkInAndOut(checkInAndOutData);

        //步骤7: 核销流水计算
        JSONArray reduceDetailList = new JSONArray();
        Map<String, Object> reduceDetail = detailList.getJSONObject(0);
        reduceDetail.put("purchaseNo",purchaseNo );
        reduceDetail.put("amount", advancePrice);
        reduceDetail.put("adjustAmount", 0);
        reduceDetailList.add(reduceDetail);
        Map<String, Object> reduceData = new HashMap<>();
        reduceData.put("dataList", reduceDetailList);
        reconciliationStep.reduce(reduceData,advancePrice);

        //步骤8: 获取供应商账户信息
        Map<String, Object> accountDetailData = new HashMap<>();
        accountDetailData.put("id",supplierId );
        JSONObject accountDetail = reconciliationStep.accountDetail(accountDetailData);

        //步骤9: 获取审批人信息
        Map<String, Object> auditInformationData = new HashMap<>();
        auditInformationData.put("supplierId",supplierId );
        reconciliationStep.auditInformation(auditInformationData);

        // 步骤10: 创建对账单
        Map<String, Object> addReconciliationBody = new HashMap<>();
        addReconciliationBody.put("writeOffAmount", advancePrice);
        addReconciliationBody.put("totalBillAmount", advancePrice);
        addReconciliationBody.put("estimateAmount", advancePrice);
        addReconciliationBody.put("taxNumber", supplierInfo.getTaxNumber());
        addReconciliationBody.put("supplierName", supplierInfo.getName());
        addReconciliationBody.put("supplierId", supplierId);
        addReconciliationBody.put("supplierAccountId", accountDetail.getLongValue("id"));
        addReconciliationBody.put("payType", accountDetail.getIntValue("payType"));
        addReconciliationBody.put("dataList", reduceDetailList);
        addReconciliationBody.put("remark", "自动化预付对账"+ System.currentTimeMillis());
        reconciliationStep.addReconciliation(addReconciliationBody);

        // 步骤11: 校验对账单数据-处于账单审核中
        reconciliationStep.reconciliationCheck(1, temVar().getReconciliationId(),
                FlowConfigEnums.PURCHASE_STATEMENT_APPROVAL.getProcessType(),1);
        // 步骤12: 账单审核通过
        flowRequest.approveFlow(temVar().getReconciliationId(), FlowConfigEnums.PURCHASE_STATEMENT_APPROVAL.getProcessType(), true);

        // 步骤13: 校验对账单数据-待开票
        reconciliationStep.reconciliationCheck(6, temVar().getReconciliationId(),
                FlowConfigEnums.PURCHASE_STATEMENT_APPROVAL.getProcessType(),2);

    }

}