package com.xianmu.atp.cases.pms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.goods.step.GoodsStep;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.step.PopStep;
import com.xianmu.atp.cases.pms.step.PriceCenterStep;
import com.xianmu.atp.cases.pms.step.SupplierStep;
import com.xianmu.atp.cases.pms.step.SupplyListStep;
import com.xianmu.atp.dal.dao.common.BaseInfoService;
import com.xianmu.atp.dal.dao.pms.InventorySupplierWarehouseStoreService;
import com.xianmu.atp.dal.model.*;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.generic.common.UtilRequest;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Epic("PMS")
@Feature("POP流程")
@Owner("大鹏")
public class PopTest  extends BaseTest {
    @Resource
    private Request request;

    @Resource
    private UtilRequest utilRequest;

    //全局变量
    @Resource
    private EnvVariable envVar;

    //操作步骤封装
    @Resource
    private PopStep popStep;

    @Resource
    private GoodsStep goodsStep;

    @Resource
    private PriceCenterStep priceCenterStep;
    @Resource
    private SupplierStep supplierStep;

    @Resource
    private SupplyListStep supplyListStep;

    //数据库
    @Resource
    private InventorySupplierWarehouseStoreService inventorySupplierWarehouseStoreService;

    @Resource
    private BaseInfoService baseInfoService;


    private Supplier supplierInfo;
    private SkuInfo skuInfo;

    private WarehouseStorageCenter warehouseInfo;


    @BeforeMethod(description = "初始化操作--登录")
    public void login(){
        String URL = request.urlBuild(xmAdminDomain, LoginEnumsInterface.xianmuLogin.getUrl());
        HashMap<String, String> loginBody = new HashMap<>();
        loginBody.put("username", envVar.getAdminUserName());
        loginBody.put("password", envVar.getAdminpassword());
        HttpResponseWrapper loginRes= request.sendRequest(URL,
                LoginEnumsInterface.xianmuLogin.getMethod(),
                LoginEnumsInterface.xianmuLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        request.setToken(token);
    }

    @BeforeMethod(description = "初始化操作--获取商品、供应商、仓库等基础数据")
    public void getBaseInfo(){
        skuInfo =baseInfoService.getProductInfo(envVar.getPopSku());
        supplierInfo = baseInfoService.selectById(envVar.getSupplierId());
        warehouseInfo = baseInfoService.getWarehouseInfo(envVar.getPopWarehouse());

    }


    //每个用例执行后打印和移除临时变量
    @AfterMethod(description = "临时变量")
    public void tear_down(){
        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
        TemVar.remove();
    }
    //获取用例生成的临时变量
    public TemVar temVar(){
        return TemVar.getCurrent();
    }

    @Story("POP流程")
    @Test(description = "pop商品上新>发布>供货目录操作(删除、新增、编辑)")
    public void popTest01(){

        //设置商品上新参数
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        HashMap<String, Object> publishData = new HashMap<>();
        publishData.put("pdName", "自动上新测试POP品"+ sdf.format(new Date()));
        publishData.put("categoryId", skuInfo.getCategoryId());
        publishData.put("externalSkuCode", null);
        publishData.put("level", "B级");
        publishData.put("isDomestic", 1);
        publishData.put("origin", "测试产地");
        publishData.put("unit", "盒");
        publishData.put("fruitSize", "5");
        publishData.put("length", "11");
        publishData.put("width", "12");
        publishData.put("high", "13");
        publishData.put("weightNum", 6);
        publishData.put("netWeightNum", 5.5);
        publishData.put("weightLow", "11");
        publishData.put("weightHeight", "13");
        publishData.put("buyerName", "大鹏");
        publishData.put("buyerId", 3);
        publishData.put("defaultBuyerId", 3);
//        publishData.put("supplierName", "自动化测试企业吴");
        publishData.put("supplierId", envVar.getSupplierId());
        publishData.put("afterSaleRuleDetail", "测试");
        publishData.put("selectSpecifications", "毛重");
        publishData.put("quoteType", "1");
        publishData.put("netWeightNumUnit", "KG");
        publishData.put("specification", "毛重11-13斤");
        publishData.put("picturePath", envVar.getMainPictureUrl());
        publishData.put("detailPictureList", Lists.newArrayList(
                envVar.getDetailPictureUrl()
        ));
        publishData.put("videoUrl", envVar.getVedioUrl());

        // 商品上新-新增商品
        String pdId= popStep.publishPopProduct(publishData);//

        JSONObject productInfo= goodsStep.queryProductInfo(new HashMap<String, Object>(){{
            put("pdid", pdId);
        }});// 获取商品详情
        JSONObject inventoryDetailVOS= productInfo.getJSONArray("inventoryDetailVOS").getJSONObject(0);
        productInfo.remove("outdated");
        productInfo.remove("inventoryDetailVOS");
        productInfo.put("saleValueList",inventoryDetailVOS.getJSONArray("saleValueList"));
        productInfo.put("createType",0);
        productInfo.put("commentPic","");
        productInfo.put("commentInfo","买手上新提交");
        productInfo.put("auditFlag", true);
        for (String key : productInfo.getJSONObject("createRemark").keySet()) {
            productInfo.put(key, productInfo.getJSONObject("createRemark").get(key));
        }
        JSONArray saleValueList= inventoryDetailVOS.getJSONArray("saleValueList");
        for (int i = 0; i < saleValueList.size(); i++) {
            inventoryDetailVOS.put(saleValueList.getJSONObject(i).getString("productsPropertyId"),
                    saleValueList.getJSONObject(i).getString("productsPropertyValue"));
        }
        for (String key : inventoryDetailVOS.getJSONObject("createRemark").keySet()) {
            inventoryDetailVOS.put(key, inventoryDetailVOS.getJSONObject("createRemark").get(key));
        }
        productInfo.put("skuList", Lists.newArrayList(inventoryDetailVOS));
        String spuNo= productInfo.getString("pdNo");
        String pdName= productInfo.getString("pdName");
        goodsStep.upsetInfo(productInfo);//商品上新--保存发布

        String supplyListId= supplyListStep.queryPage(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("defaultSupplier",1);
            put("spu",spuNo);
            put("supplierIds",Lists.newArrayList(envVar.getSupplierId()));
            put("warehouseNos",Lists.newArrayList(envVar.getPopWarehouse()));
        }});//查询供货目录数据
        supplierStep.detail(new HashMap<String, Object>(){{
            put("id",envVar.getSupplierId());
        }});//获取供应商详情
        supplyListStep.upsertUpdate(new HashMap<String, Object>(){{
            put("supplyListId",Integer.valueOf(supplyListId));
            put("channelType",1);
            put("defaultSupplier",1);
            put("advanceDay",2);
            put("fixedTimeList",Lists.newArrayList());
            put("orderModel",1);
        }});
        supplyListStep.upsertSupplier(new HashMap<String, Object>(){{
            put("supplyListId",Integer.valueOf(supplyListId));
            put("supplierId",envVar.getSupplierIdbak());
        }});
        supplyListStep.delete(new HashMap<String, Object>(){{
            put("supplyListId",supplyListId);
        }},null);

        supplyListStep.add(new HashMap<String, Object>(){{
            put("advanceDay",1);
            put("channelType",1);
            put("defaultSupplier",0);
            put("fixedTimeList",Lists.newArrayList());
            put("orderModel",1);
            put("pdName",pdName);
            put("spu",spuNo);
            put("supplierId",envVar.getSupplierId());
            put("supplierName",supplierInfo.getName());
            put("warehouseName",warehouseInfo.getWarehouseName());
            put("warehouseNo",Integer.valueOf(envVar.getPopWarehouse()));
        }});

        String supplyListId2=supplyListStep.queryPage(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("defaultSupplier",1);
            put("spu",spuNo);
            put("supplierIds",Lists.newArrayList(envVar.getSupplierId()));
            put("warehouseNos",Lists.newArrayList(envVar.getPopWarehouse()));
        }});//查询供货目录数据

        supplyListStep.delete(new HashMap<String, Object>(){{
            put("supplyListId",supplyListId2);
        }},null);

        List<String> supplyListTitleList = new ArrayList<>(Arrays.asList(
                "SPU", "仓库名称", "供应商编号", "默认供应商(是；否)","渠道类型(源头直采；分销市场)","订货模式(不定期不定量；不定期定量；定期不定量；定期定量)",
                "定期时间(非必填，若为“定期”需填写；数字1-7代表周一-周日，如需多选，用“;”分隔，如1；2；3)","提前期"));
        List<Object> supplyListValueList = new ArrayList<>();
        supplyListValueList.add(new HashMap<String, Object>(){{
            put("1",spuNo);
            put("2",warehouseInfo.getWarehouseName());
            put("3",envVar.getSupplierId());
            put("4","是");
            put("5","源头直采");
            put("6","不定期不定量");
            put("8","3");
        }});
        JSONObject stockAddExcelInfo =utilRequest.excelCreate(new HashMap<String, Object>(){{
            put("fileName","供货目录导入-自动化测试.xlsx");
            put("sheetInfo",Lists.newArrayList(new HashMap<String, Object>(){{
                put("titleList",supplyListTitleList);
                put("valueList",supplyListValueList);
            }}));
        }});

        supplyListStep.stockPackupImport(new HashMap<String, Object>(){{
            put("url","test-app-temp:"+stockAddExcelInfo.getString("data"));
        }});//excel--库存提报
        utilRequest.getResult(new HashMap<String, Object>(){{
            put("resourceId",temVar().getExcelResId());
        }});//excel--导入结果

        String supplyListId3= supplyListStep.queryPage(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("defaultSupplier",1);
            put("spu",spuNo);
            put("supplierIds",Lists.newArrayList(envVar.getSupplierId()));
            put("warehouseNos",Lists.newArrayList(envVar.getPopWarehouse()));
        }});//查询供货目录数据
        supplyListStep.delete(new HashMap<String, Object>(){{
            put("supplyListId",supplyListId3);
        }},null); //删除供货目录数据
    }



    @Story("价格中心佣金")
    @Test(description = "新增,删除报价")
    public void popTest02(){
        //价格佣金查询配置--生效中
        Map<String, Object> queryData = new HashMap<>();
        queryData.put("pageIndex",1);
        queryData.put("pageSize",10);
        queryData.put("effectStatus", Lists.newArrayList("IN_EFFECT"));
        queryData.put("warehouseNo",envVar.getPopWarehouse());
        queryData.put("skuNo",envVar.getPopSku());
        queryData.put("supplierId",envVar.getSupplierId());
        priceCenterStep.queryPage(queryData);

        if(Objects.nonNull(temVar().getPriceCenterId())){
            //作废
            priceCenterStep.delete(new HashMap<String, Object>(){{
                put("id",temVar().getPriceCenterId());
            }});
        }
        priceCenterStep.add(new HashMap<String, Object>(){{
            put("warehouseNo", envVar.getPopWarehouse());
            put("sku", envVar.getPopSku());
            put("stepPriceInputList", Lists.newArrayList(
                    new HashMap<String, Object>() {{
                        put("price", 10);
                        put("quantity", 1);
                    }}
            ));
            put("supplierId", envVar.getSupplierId());
            put("effectStartDate", LocalDate.now().toString());
            put("effectEndDate", LocalDate.now().toString());
        }});
        priceCenterStep.queryPage(queryData);
        priceCenterStep.delete(new HashMap<String, Object>(){{
            put("id",temVar().getPriceCenterId());
        }});
    }

    @Story("POP流程")
    @Test(description = "pop商品-提报价格、库存、提报记录")
    public void popTest03(){
        priceCenterStep.queryPage(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("effectStatus", Lists.newArrayList("IN_EFFECT"));
            put("warehouseNo",envVar.getPopWarehouse());
            put("skuNo",envVar.getPopSku());
            put("supplierId",envVar.getSupplierId());
        }});
        if (Objects.isNull(temVar().getPriceCenterId())){
            priceCenterStep.add(new HashMap<String, Object>(){{
                put("warehouseNo", envVar.getPopWarehouse());
                put("sku", envVar.getPopSku());
                put("stepPriceInputList", Lists.newArrayList(
                        new HashMap<String, Object>() {{
                            put("price", 10);
                            put("quantity", 1);
                        }}
                ));
                put("supplierId", envVar.getSupplierId());
                put("effectStartDate", LocalDate.now().toString());
                put("effectEndDate", LocalDate.now().toString());
            }});
        }
        //查询商品信息
        popStep.categoryIndex(new HashMap<String, Object>(){{
            put("supplierId",envVar.getSupplierId());
        }});
        popStep.skuIndex(new HashMap<String, Object>(){{
            put("categoryIdList",Lists.newArrayList(-1));
            put("supplierId",envVar.getSupplierId());
            put("sku",envVar.getPopSku());
            put("pageIndex",1);
            put("pageSize",50);
            put("orderFlag",0);
        }});
        popStep.priceUpsert(new HashMap<String, Object>(){{
            put("supplierId",envVar.getSupplierId());
            put("sku",envVar.getPopSku());
            put("warehouseNo",envVar.getPopWarehouse());
            put("adjustWeightPrice",String.valueOf(temVar().getSupplierWeightPrice().add(BigDecimal.valueOf(0.1))));
            put("originalWeightPrice",temVar().getSupplierWeightPrice());
            put("originalPrice",temVar().getSupplierPrice());
            put("quoteType",1);
        }});//价格提报
        JSONObject stockChangeRecord1= popStep.stockChangeRecord(new HashMap<String, Object>(){{
            put("typeList",Lists.newArrayList(5));
            put("supplierId",envVar.getSupplierId());
            put("pageIndex",1);
            put("pageSize",10);
            put("sku",envVar.getPopSku());
            put("warehouseNo",envVar.getPopWarehouse());
        }});//查看售价提报记录
        Assert.assertEquals(stockChangeRecord1.getBigDecimal("adjustWeightPrice"),
                temVar().getSupplierWeightPrice().add(BigDecimal.valueOf(0.1)),
                "价格提报记录异常");

        //查询供应商库存
        InventorySupplierWarehouseStore stockInfo= inventorySupplierWarehouseStoreService.selectBySupplierIdAndWarehouseNoAndSkuCode(envVar.getSupplierId(),
                Integer.valueOf(envVar.getPopWarehouse()),
                envVar.getPopSku());
        popStep.stockUpsert(new HashMap<String, Object>(){{
            put("supplierId",envVar.getSupplierId());
            put("sku",envVar.getPopSku());
            put("warehouseNo",envVar.getPopWarehouse());
            put("adjustTotalSaleStock",String.valueOf(stockInfo.getQuantity()+1));
            put("originalTotalSaleStock",stockInfo.getQuantity().toString());
        }});//库存提报

        JSONObject stockChangeRecord2= popStep.stockChangeRecord(new HashMap<String, Object>(){{
            put("typeList",Lists.newArrayList(2,6,7));
            put("supplierId",envVar.getSupplierId());
            put("pageIndex",1);
            put("pageSize",10);
            put("sku",envVar.getPopSku());
            put("warehouseNo",envVar.getPopWarehouse());
        }});//查看库存提报记录
        Assert.assertEquals(stockChangeRecord2.getString("adjustTotalSaleStock"),
                String.valueOf(stockInfo.getQuantity()+1),
                "库存提报记录异常");
    }



}

