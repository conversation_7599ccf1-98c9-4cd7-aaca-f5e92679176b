package com.xianmu.atp.cases.pms.aiCase.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.dal.mapper.PurchasesMapper;
import com.xianmu.atp.dal.mapper.StockTaskStorageMapper;
import com.xianmu.atp.dal.model.Purchases;
import com.xianmu.atp.dal.model.StockTaskStorage;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import com.xianmu.atp.enums.api.pms.SaasPurchaseEnumsInterface;
import com.xianmu.atp.generic.common.DateUtil;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * SaaS采购操作步骤封装类
 * 封装所有采购相关的API请求操作步骤
 * 
 * <AUTHOR> Test Engineer
 */
@Slf4j
@Component
public class SaasPurchaseStep {

    @Resource
    private Request request;

    @Resource
    private EnvVariable envVar;

    @Resource
    private PurchasesMapper purchasesMapper;

    @Resource
    private StockTaskStorageMapper stockTaskStorageMapper;

    // ==================== 采购单管理相关操作 ====================

    /**
     * 采购单列表查询接口V2
     * @param params 查询参数
     * @param assertFlag 断言参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryPurchasePageV2(Map<String, Object> params, boolean assertFlag  ) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.queryPageV2.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.queryPageV2.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.queryPageV2.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        JSONObject data = result.getJSONObject("data");
        JSONArray list = data.getJSONArray("list");
        if (assertFlag){
            Assert.assertNotNull(list, "采购单列表查询结果不符合预期,不应为空");
            Assert.assertFalse(list.isEmpty(), "结果不符合预期,不应为空");
        }else {
            Assert.assertTrue(list == null || list.isEmpty(), "采购单列表查询结果不符合预期,应为空");
        }
    }

    /**
     * 采购单新增接口
     * @param params 采购单信息参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void savePurchase(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.save.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.save.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.save.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        
    }

    //数据库查询采购单信息
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public Purchases queryPurchaseByRemark(String remark,Integer warehouseNo,Integer tenantId) {
        LambdaQueryWrapper<Purchases> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Purchases::getRemark, remark);
        queryWrapper.eq(Purchases::getAreaNo, warehouseNo);
        queryWrapper.eq(Purchases::getTenantId, tenantId);
        Purchases purchase = purchasesMapper.selectOne(queryWrapper);
        Assert.assertNotNull(purchase, "数据库查询采购单信息失败");
        return purchase;
    }
    //数据库查询采购单列表信息
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public List<Purchases> queryPurchaseListByRemark(String remark , Integer tenantId) {
        LambdaQueryWrapper<Purchases> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Purchases::getRemark, remark);
        queryWrapper.eq(Purchases::getTenantId, tenantId);
        List<Purchases> purchasesList = purchasesMapper.selectList(queryWrapper);
        Assert.assertNotNull(purchasesList, "数据库查询采购单信息失败");
        return purchasesList;
    }

    //数据库查询入库任务信息
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public StockTaskStorage queryTaskByPurchaseNo(String sourceId) {
        LambdaQueryWrapper<StockTaskStorage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockTaskStorage::getSourceId, sourceId);
        StockTaskStorage taskInfo = stockTaskStorageMapper.selectOne(queryWrapper);
        Assert.assertNotNull(taskInfo, "数据库查询出入库任务信息失败");
        return taskInfo;
    }

    /**
     * 采购单编辑更新接口
     * @param params 采购单更新参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void updatePurchase(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.update.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.update.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.update.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));

    }

    /**
     * 采购单删除接口
     * @param params 删除参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void deletePurchase(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.delete.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.delete.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.delete.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
    }

    /**
     * 采购单批量新增接口
     * @param params 批量新增参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void saveBatchPurchase(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.saveBatch.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.saveBatch.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.saveBatch.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));

    }

    /**
     * 采购单再来一单校验接口
     * @param params 批量新增参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void copyCheck(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.copyCheck.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.copyCheck.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.copyCheck.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));

    }


    /**
     * 采购单详情接口
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public JSONObject getPurchaseDetail(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.detail.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.detail.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.detail.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "采购单详情数据不能为空");
        return data;
    }

    /**
     * 采购单出入库明细列表查询
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryPurchaseInOutDetail(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.queryInOutDetail.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.queryInOutDetail.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.queryInOutDetail.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        JSONObject data = result.getJSONObject("data");
        JSONArray list = data.getJSONArray("list");
        Assert.assertNotNull(list, "结果不符合预期,不应为空");
        Assert.assertFalse(list.isEmpty(), "结果不符合预期,不应为空");
    }

    /**
     * 采购单出入库明细汇总数据
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void inOutStatistics(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.inOutStatistics.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.inOutStatistics.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.inOutStatistics.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "结果不符合预期,不应为空");
    }

    /**
     * 列表采购单导出
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void exportV2(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.exportV2.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.exportV2.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.exportV2.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "结果不符合预期,不能为空");
        String fileID = data.getString("resId");
        Assert.assertNotNull(fileID, "结果不符合预期,不能为空");

    }
    /**
     * 详情采购单导出
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void exportByPurchaseNo(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseEnum.exportByPurchaseNo.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.exportByPurchaseNo.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseEnum.exportByPurchaseNo.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
    }

    // ==================== 供应商关系查询相关操作 ====================

    /**
     * 查询允供供应商列表接口
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void querySkuSupplyList(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SupplyRelationQueryEnum.querySkuSupplyList.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SupplyRelationQueryEnum.querySkuSupplyList.getMethod(),
                SaasPurchaseEnumsInterface.SupplyRelationQueryEnum.querySkuSupplyList.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
    }

    // ==================== 预约单管理相关操作 ====================

    /**
     * 新增预约单接口
     * @param params 查询参数
     * @return 预约单id
     *
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String addArrange(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasArrangeEnum.addArrange.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasArrangeEnum.addArrange.getMethod(),
                SaasPurchaseEnumsInterface.SaasArrangeEnum.addArrange.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));

        String data = result.getString("data");
        Assert.assertNotNull(data, "预约单ID不能为空");
        return data;
    }

    /**
     * 预约单列表查询接口
     * @param params 查询参数
     * @return 预约单列表
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public JSONArray listArrangePurchasesNo(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasArrangeEnum.listArrange.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasArrangeEnum.listArrange.getMethod(),
                SaasPurchaseEnumsInterface.SaasArrangeEnum.listArrange.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        
        JSONArray data = result.getJSONArray("data");
        Assert.assertNotNull(data, "预约单列表数据不能为空");
        Assert.assertFalse(data.isEmpty(), "预约单列表数据不能为空");
        return data;
    }

    /**
     * 预约单详情接口
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void getArrangeDetailByStockArrangeId(Map<String, Object> params, Integer assertState) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasArrangeEnum.detailStockArrangeId.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasArrangeEnum.detailStockArrangeId.getMethod(),
                SaasPurchaseEnumsInterface.SaasArrangeEnum.detailStockArrangeId.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "预约单详情数据不能为空");
        Assert.assertEquals(data.getInteger("state"), assertState, "预约单状态不符合预期");
    }

    /**
     * 查询可预约SKU列表
     * @param params 查询参数
     * @return JSONArray 可预约SKU列表
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public JSONArray querySkuArrange(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasArrangeEnum.querySkuArrange.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasArrangeEnum.querySkuArrange.getMethod(),
                SaasPurchaseEnumsInterface.SaasArrangeEnum.querySkuArrange.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        JSONObject data = result.getJSONObject("data");
        JSONArray list = data.getJSONArray("purchasesPlanResultVOS");
        Assert.assertNotNull(list, "结果不符合预期,不应为空");
        Assert.assertFalse(list.isEmpty(), "结果不符合预期,不应为空");
        return list;
    }

    /**
     * 预约单撤销
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void closeArrange(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasArrangeEnum.closeArrange.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasArrangeEnum.closeArrange.getMethod(),
                SaasPurchaseEnumsInterface.SaasArrangeEnum.closeArrange.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
    }

    /**
     * 预约单备注更新
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void updateArrangeRemark(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasArrangeEnum.updateArrangeRemark.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasArrangeEnum.updateArrangeRemark.getMethod(),
                SaasPurchaseEnumsInterface.SaasArrangeEnum.updateArrangeRemark.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
    }

    /**
     * 预约单更新
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void updateArrange(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasArrangeEnum.updateArrange.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasArrangeEnum.updateArrange.getMethod(),
                SaasPurchaseEnumsInterface.SaasArrangeEnum.updateArrange.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
    }

    // ==================== 仓库管理相关操作 ====================
    /**
     * 查询证件信息接口
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryBatchStandardBatch(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.WarehouseEnum.queryBatchStandardBatch.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.WarehouseEnum.queryBatchStandardBatch.getMethod(),
                SaasPurchaseEnumsInterface.WarehouseEnum.queryBatchStandardBatch.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
    }

    /**
     * 批量查询仓库产能接口
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryWarehouseCapacityBatch(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.WarehouseEnum.queryWarehouseCapacityBatch.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.WarehouseEnum.queryWarehouseCapacityBatch.getMethod(),
                SaasPurchaseEnumsInterface.WarehouseEnum.queryWarehouseCapacityBatch.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        
    }

    /**
     * 关闭入库任务
     * @param params 关闭参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void closeTask(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.WarehouseEnum.closeTask.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.WarehouseEnum.closeTask.getMethod(),
                SaasPurchaseEnumsInterface.WarehouseEnum.closeTask.getContentType(),
                params);

        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");

        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));

    }

    // ==================== 采购退货单管理相关操作 ====================

    /**
     * 采购退货单列表查询
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryPurchaseBackPage(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasPurchaseEnumsInterface.SaasPurchaseBackEnum.queryPage.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasPurchaseEnumsInterface.SaasPurchaseBackEnum.queryPage.getMethod(),
                SaasPurchaseEnumsInterface.SaasPurchaseBackEnum.queryPage.getContentType(),
                params);
        
        // HTTP响应断言
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        
        // 业务状态码断言
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败: " + result.getString("msg"));
        
    }

    //--------------工具栏方法-------------

    //构造采购单新增参数
    public Map<String, Object> buildPurchaseParmams(Map<String, Object> skuInfo, WarehouseStorageCenter warehouseInfo,
                                                    Supplier supplierInfo,String remark,
                                                    Integer state,Integer num ,boolean isArrange) {
        Map<String, Object> purchaseParams = new HashMap<>();
        String currentDate = DateUtil.getCurrentDate();
        String tomorrow = DateUtil.getOffsetDate(1);
        // 基本采购信息
        purchaseParams.put("purchaseId", null);
        purchaseParams.put("purchaseNo", null);
        purchaseParams.put("areaNo", warehouseInfo.getWarehouseNo());
        if (isArrange){
            purchaseParams.put("arrangeTime", tomorrow);
        }
        purchaseParams.put("purchaseTime", currentDate);
        purchaseParams.put("purchasesType", 0);
        purchaseParams.put("receiver", warehouseInfo.getPersonContact());
        purchaseParams.put("state", state);
        purchaseParams.put("remark", remark);

        // 构建采购计划结果列表
        Map<String, Object> purchasePlan = new HashMap<>();
        purchasePlan.put("sku", skuInfo.get("sku"));
        purchasePlan.put("title", skuInfo.get("productName"));
        purchasePlan.put("quantity", num.toString());
        purchasePlan.put("specification", skuInfo.get("weight"));
        purchasePlan.put("specificationUnit", skuInfo.get("unit"));
        purchasePlan.put("supplierId", supplierInfo.getId());
        purchasePlan.put("supplier", supplierInfo.getName());
        purchasePlan.put("warehouseNo", warehouseInfo.getWarehouseNo());
        purchasePlan.put("price", num);
        purchasePlan.put("taxRate", 0.1);
        purchasePlan.put("productionDate", currentDate);

        // 将采购计划包装成列表
        purchaseParams.put("purchasesPlanResultVOS", Collections.singletonList(purchasePlan));

        return purchaseParams;
    }

    //构造多批次采购单新增参数
    public Map<String, Object> buildBatchPurchaseParmams(Map<String, Object> skuInfo, List<WarehouseStorageCenter> warehouseList,
                                                         List<Supplier> supplierInfo, String remark,
                                                         Integer num, boolean isArrange) {
        Map<String, Object> purchaseParams = new HashMap<>();
        String currentDate = DateUtil.getCurrentDate();
        String tomorrow = DateUtil.getOffsetDate(1);

        // 基本采购信息
        purchaseParams.put("remark", remark);
        purchaseParams.put("purchaseTime", currentDate);

        // 构建仓库输入列表
        List<Map<String, Object>> warehouseInputList = new ArrayList<>();
        for (WarehouseStorageCenter warehouse : warehouseList) {
            Map<String, Object> warehouseInput = new HashMap<>();
            warehouseInput.put("warehouseNo", warehouse.getWarehouseNo());
            warehouseInput.put("receiver", warehouse.getPersonContact());
            if (isArrange) {
                // 根据索引设置不同的预约时间
                warehouseInput.put("arrangeTime", tomorrow);
            }
            warehouseInput.put("phone", warehouse.getPhone());
            warehouseInputList.add(warehouseInput);


        }
        purchaseParams.put("warehouseInputList", warehouseInputList);

        // 构建采购计划列表
        List<Map<String, Object>> purchasePlanInputList = new ArrayList<>();
        // 根据supplierInfo长度和warehouseList长度的关系确定使用的供应商
        Supplier supplierToUse;

        for (int i = 0; i < warehouseList.size(); i++) {
            WarehouseStorageCenter warehouse = warehouseList.get(i);
            // 根据规则选择供应商
            if (supplierInfo.size() == 1) {
                // 当supplierInfo的长度等于1时，均使用同一个Supplier对象内容
                supplierToUse = supplierInfo.get(0);
            } else if (supplierInfo.size() == warehouseList.size()) {
                // 当supplierInfo的长度等于warehouseList长度相同时，一一对应
                supplierToUse = supplierInfo.get(i);
            } else if (supplierInfo.size() > warehouseList.size()) {
                // 当supplierInfo的长度大于warehouseList长度时，使用第一个Supplier对象
                supplierToUse = supplierInfo.get(0);
            } else {
                // 其他情况默认使用第一个供应商（作为兜底逻辑）
                supplierToUse = supplierInfo.get(0);
            }

            Map<String, Object> purchasePlan = new HashMap<>();
            purchasePlan.put("sku", skuInfo.get("sku"));
            purchasePlan.put("title", skuInfo.get("productName"));
            purchasePlan.put("quantity", num);
            purchasePlan.put("specification", skuInfo.get("weight"));
            purchasePlan.put("specificationUnit", skuInfo.get("unit"));
            purchasePlan.put("supplierId", supplierToUse.getId());
            purchasePlan.put("supplier", supplierToUse.getName());
            purchasePlan.put("warehouseNo", warehouse.getWarehouseNo());
            purchasePlan.put("price", String.format("%.2f", num.floatValue()));
            purchasePlan.put("taxRate", 0.1 + i * 0.1); // 不同仓库税率递增
            purchasePlan.put("latestArrivalDate", DateUtil.getOffsetDate(3)); // 到货日期统一为3天后

            purchasePlanInputList.add(purchasePlan);
        }
        purchaseParams.put("purchasePlanInputList", purchasePlanInputList);

        return purchaseParams;
    }



}