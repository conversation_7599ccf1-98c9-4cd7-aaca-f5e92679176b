package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.dal.model.SrmSupplierOfferDetailStepPrice;
import com.xianmu.atp.enums.api.common.CommonEnumsInterface;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.test.context.event.annotation.AfterTestMethod;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SrmPcStep{
    @Resource
    private Request request;

    //场景变量
    @Resource
    private EnvVariable envVar;
    //数据库操作


    /**
     * SRM-代销业务-库存管理--列表
     */
    @Retry(attempts =2, delay = 3000, onExceptions = {AssertionError.class})
    public void stockPackupQuery(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.stockPackupQuery.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.stockPackupQuery.getMethod(),
                PmsEnumsInterface.SrmPcEnum.stockPackupQuery.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "列表数据为空");
        Map<String, BigDecimal> srmStockInfo = new HashMap<>();
        srmStockInfo.put("totalSaleStock",res_list.getJSONObject(0).getJSONArray("skuList").
                getJSONObject(0).getBigDecimal("totalSaleStock"));
        srmStockInfo.put("soldOutStock",res_list.getJSONObject(0).getJSONArray("skuList").
                getJSONObject(0).getBigDecimal("soldOutStock"));
        srmStockInfo.put("remainSaleStock",res_list.getJSONObject(0).getJSONArray("skuList").
                getJSONObject(0).getBigDecimal("remainSaleStock"));
        TemVar.getCurrent().setSrmStockInfo(srmStockInfo);//设置当前售卖总库存
    }

    /**
     * SRM-代销业务-库存管理--库存调整导入
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void stockPackupImport(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.stockPackupImport.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.stockPackupImport.getMethod(),
                PmsEnumsInterface.SrmPcEnum.stockPackupImport.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        TemVar.getCurrent().setExcelResId(result.getJSONObject("data").getString("resId"));//设置导入结果id
    }

    /**
     * SRM-代销业务-库存管理--库存调整导入
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void stockChangeImport(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.stockChangeImport.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.stockChangeImport.getMethod(),
                PmsEnumsInterface.SrmPcEnum.stockChangeImport.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        TemVar.getCurrent().setExcelResId(result.getJSONObject("data").getString("resId"));//设置导入结果id
    }

    /**
     * SRM-代销业务-库存管理--导出
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void stockPackupExport(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.stockPackupExport.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.stockPackupExport.getMethod(),
                PmsEnumsInterface.SrmPcEnum.stockPackupExport.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        TemVar.getCurrent().setExcelResId(result.getJSONObject("data").getString("resId"));//设置导入结果id
    }

    /**
     * SRM-代销业务-库存管理--导出供应商供货商品列表
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void supplySkuListExport() {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.supplySkuListExport.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.supplySkuListExport.getMethod(),
                PmsEnumsInterface.SrmPcEnum.supplySkuListExport.getContentType(),
                null);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        TemVar.getCurrent().setExcelResId(result.getJSONObject("data").getString("resId"));//设置导入结果id
    }
    /**
     * SRM-代销业务-库存管理--更新供货编码
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void relationUpsert(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.relationUpsert.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.relationUpsert.getMethod(),
                PmsEnumsInterface.SrmPcEnum.relationUpsert.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     *   SRM-代销业务-库存管理--更新生产日期
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void productDateUpsert(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.productDateUpsert.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.productDateUpsert.getMethod(),
                PmsEnumsInterface.SrmPcEnum.productDateUpsert.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     *   SRM-代销业务-库存管理--库存调整
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void stockPackupUpsert(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.stockPackupUpsert.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.stockPackupUpsert.getMethod(),
                PmsEnumsInterface.SrmPcEnum.stockPackupUpsert.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        TemVar.getCurrent().setSrmStockChangeId(result.getString("data"));//设置变更记录单据ID
    }

    /**
     *  SRM-代销业务-库存变更记录--列表
     */
    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void stockChangeQuery(Map<String, Object> params,Integer flag) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.stockChangeQuery.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.stockChangeQuery.getMethod(),
                PmsEnumsInterface.SrmPcEnum.stockChangeQuery.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        //flag=1时，要求列表有数据返回，其他时无要求，有返回则设置id
        if (flag==1){
            Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "列表数据为空");
        }else {
            if (Objects.isNull(res_list) || res_list.isEmpty()){
                TemVar.getCurrent().setSrmStockChangeRecordID(null);
            }else {
                TemVar.getCurrent().setSrmStockChangeRecordID(res_list.getJSONObject(0).getInteger("recordId"));
            }
        }
    }


    /**
     *  SRM-代销业务-供货价调整--新增供货价
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void priceAdd(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.priceAdd.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.priceAdd.getMethod(),
                PmsEnumsInterface.SrmPcEnum.priceAdd.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     *  SRM-代销业务-供货价调整--供货价审批列表查询
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void priceAduitQuery(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.priceAduitQuery.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.priceAduitQuery.getMethod(),
                PmsEnumsInterface.SrmPcEnum.priceAduitQuery.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "列表数据为空");
        boolean containsExpected = false;
        for (int i = 0; i < res_list.size(); i++) {
            JSONObject res_list_item = res_list.getJSONObject(i);
            if (res_list_item.getInteger("id").equals(TemVar.getCurrent().getSrmSupplierOfferDetailAuditRecordID())) {
                containsExpected = true;
                break;
            }
        }
        Assert.assertTrue(containsExpected,"未查询到指定的审核记录");
    }
    /**
     *  SRM-代销业务-供货价调整--查询当前报价列表
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public JSONArray priceQuery(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.priceQuery.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.priceQuery.getMethod(),
                PmsEnumsInterface.SrmPcEnum.priceQuery.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        return result.getJSONObject("data").getJSONArray("list");
    }

    /**
     *  SRM-代销业务-供货价调整--供货价审批撤销
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void priceTerminate(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.priceTerminate.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.priceTerminate.getMethod(),
                PmsEnumsInterface.SrmPcEnum.priceTerminate.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     *  SRM-代销业务-订单管理--列表
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void packupPoQuerg(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SrmPcEnum.packupPoQuerg.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SrmPcEnum.packupPoQuerg.getMethod(),
                PmsEnumsInterface.SrmPcEnum.packupPoQuerg.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }


    //计算新增需要的供货价
    public BigDecimal calculatePrice(JSONArray priceList, SrmSupplierOfferDetailStepPrice stepDetailPriceInfo) {
        if (priceList.isEmpty()){
            if (Objects.nonNull(stepDetailPriceInfo.getPrice())){
                return stepDetailPriceInfo.getPrice().add(BigDecimal.valueOf(0.1));
            }else {
                return BigDecimal.valueOf(1);
            }
        }else {
            return priceList.getJSONObject(0).getBigDecimal("quotedPrice").add(BigDecimal.valueOf(0.1));
        }
    }
}
