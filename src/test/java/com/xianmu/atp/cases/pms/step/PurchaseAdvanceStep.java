package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.aiCase.enums.ReconciliationEnumsInterface;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.input.purchaseAdvanceInput;
import com.xianmu.atp.dal.dao.pms.SupplierDomainService;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.SupplierAccount;
import com.xianmu.atp.enums.api.bms.FmsEnumsInterface;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class PurchaseAdvanceStep  {

    @Resource
    private Request request;
    //场景变量
    @Resource
    private EnvVariable envVar;
    //数据库操作
    @Resource
    private SupplierDomainService supplierDomainService;



    /**
     * 根据采购单号新增预付单
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void addPurchaseAdvance(String purchaseNo) {
        //获取供应商ID
        Integer supplierId = envVar.getSupplierId();
        String timestampStr = String.valueOf(System.currentTimeMillis());
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseAdvanceEnum.add.getUrl());
        purchaseAdvanceInput purchaseAdvanceBody = new purchaseAdvanceInput();
        //获取供应商信息
        Supplier supplierInfo = supplierDomainService.selectById(supplierId);
        SupplierAccount supplierAccountInfo = supplierDomainService.getSupplierAccountList(supplierId);
        Assert.assertNotNull(supplierInfo, "前置检查失败:供应商id" + supplierId + "查询信息为空");
        Assert.assertNotNull(supplierAccountInfo, "前置检查失败:供应商id" + supplierId + "查询账户信息为空");
        if (purchaseNo != null && !purchaseNo.isEmpty()) {
            JSONObject PaymentInfo = queryPayment(purchaseNo);
            HashMap<String, Object> purchaseVo = new HashMap<>();
            purchaseVo.put("purchaseNo", purchaseNo);
            purchaseVo.put("price", PaymentInfo.getFloat("actualPrice"));
            purchaseVo.put("paymentAmount", PaymentInfo.getFloat("actualPrice"));
            purchaseVo.put("advanceAmount", PaymentInfo.getFloat("actualPrice") - PaymentInfo.getFloat("advanceWriteOff"));
            purchaseAdvanceBody.addParams(supplierId,
                    supplierInfo.getName(),
                    supplierAccountInfo.getPayType(),
                    BigDecimal.valueOf(PaymentInfo.getFloat("actualPrice") - PaymentInfo.getFloat("advanceWriteOff")),
                    2,
                    supplierAccountInfo.getId(),
                    "自动化-预付单新建-关联采购单"+timestampStr);
            purchaseAdvanceBody.setPurchasesVOList(Collections.singletonList(purchaseVo));
        } else {
            purchaseAdvanceBody.addParams(supplierId,
                    supplierInfo.getName(),
                    supplierAccountInfo.getPayType(),
                    BigDecimal.valueOf(10),
                    2,
                    supplierAccountInfo.getId(),
                    "自动化-预付单新建-未关联采购单"+timestampStr);
        }
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseAdvanceEnum.add.getMethod(),
                PmsEnumsInterface.PurchaseAdvanceEnum.add.getContentType(),
                purchaseAdvanceBody.getNonEmptyParams());
        Assert.assertNotNull(res.getBody(),"'新增预付单失败'");
        if (res.getStatusCode() == 200) {
            JSONObject result = JSON.parseObject(res.getBody());
            Assert.assertEquals(result.getIntValue("status"), 200,"'新增预付单失败'");
            log.info("设置预付单ID：{}", result.getString("data"));
            TemVar.getCurrent().setPurchaseAdvanceId(result.getString("data")); //环境变量--设置预付单id

        }
    }

    /**
     * 根据采购单号查询采购单付款信息
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public JSONObject queryPayment(String purchaseNo) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.queryPayment.getUrl());
        HashMap<String, Object> body = new HashMap<>();
        body.put("purchaseNo",purchaseNo);

        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.queryPayment.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.queryPayment.getContentType(),
                body);
        JSONObject result = JSON.parseObject(res.getBody());
        JSONArray res_list = result.getJSONObject("data").getJSONArray("purchaseSupplierPayments");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(),"查询结果为空");
        return res_list.getJSONObject(0);

    }

    /**
     * 查询预付单列表
     */
    @Retry(attempts =5, delay = 2000, onExceptions = {AssertionError.class})
    public void queryPurchaseAdvanceRequest(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseAdvanceEnum.query.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseAdvanceEnum.query.getMethod(),
                PmsEnumsInterface.PurchaseAdvanceEnum.query.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "未查询到正确的预付单单据");
    }

    /**
     * 查询付款单列表
     */
    @Retry(attempts = 5,delay = 2000, onExceptions = {AssertionError.class})
    public void queryFinancePaymentList(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, FmsEnumsInterface.PaymentEnums.payment_list.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                FmsEnumsInterface.PaymentEnums.payment_list.getMethod(),
                FmsEnumsInterface.PaymentEnums.payment_list.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "未查询到正确的付款单单据");
        TemVar.getCurrent().setPaymentId(res_list.getJSONObject(0).getString("id"));//设置付款单id

    }

    /**
     * 设置打款人
     */
    @Retry(attempts = 5,delay = 2000, onExceptions = {AssertionError.class})
    public void saveSettlementConfig() {
        String URL = request.urlBuild(envVar.xmAdminDomain, FmsEnumsInterface.PaymentEnums.saveSettlementConfig.getUrl());
        HashMap<String, Object> params = new HashMap<>();
        params.put("id",13);
        params.put("payer",envVar.getAdminUserName());
        HttpResponseWrapper res = request.sendRequest(URL,
                FmsEnumsInterface.PaymentEnums.saveSettlementConfig.getMethod(),
                FmsEnumsInterface.PaymentEnums.saveSettlementConfig.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
    }

    /**
     * 查询预付变动记录列表
     */
    @Retry(attempts = 5,delay = 3000, onExceptions = {AssertionError.class})
    public void queryAdvanceReocrdList(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseAdvanceEnum.advanceRecordQuery.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseAdvanceEnum.advanceRecordQuery.getMethod(),
                PmsEnumsInterface.PurchaseAdvanceEnum.advanceRecordQuery.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertNotNull(res_list,"预付变动记录为空");
    }


    //付款单上传凭证
    public void uploadPaymentVoucher(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, FmsEnumsInterface.PaymentEnums.upload_payment_voucher.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                FmsEnumsInterface.PaymentEnums.upload_payment_voucher.getMethod(),
                FmsEnumsInterface.PaymentEnums.upload_payment_voucher.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    //校验是否处于预付中
    public void checkBySupplier(String purchaseNo) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseAdvanceEnum.checkBySupplier.getUrl());
        Map<String, Object> params = new HashMap<>();
        params.put("purchaseNo", purchaseNo);
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseAdvanceEnum.checkBySupplier.getMethod(),
                PmsEnumsInterface.PurchaseAdvanceEnum.checkBySupplier.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    //绑定预付余额
    @Retry( delay = 1000, onExceptions = {AssertionError.class})
    public void advancePaymentBind(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.PurchaseAdvanceEnum.advancePaymentBind.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.PurchaseAdvanceEnum.advancePaymentBind.getMethod(),
                PmsEnumsInterface.PurchaseAdvanceEnum.advancePaymentBind.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        Assert.assertTrue(result.getBoolean("data"),"绑定预付余额失败");
    }

}


