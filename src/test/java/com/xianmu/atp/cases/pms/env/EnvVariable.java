package com.xianmu.atp.cases.pms.env;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * EnvVariable 类用于存储用例执行所需的环境变量。
 */
@Data
@Slf4j
@Component
public class EnvVariable {

    @Value("${app.xm_admin_domain}")// 使用正确的占位符语法
    public String xmAdminDomain;
    @Value("${app.saas_manage_domain}")// 使用正确的占位符语法
    public String saasManageDomain;

    @Value("${app.srm_domain}")// 使用正确的占位符语法
    public String srmDomain;

    @Value("${app.pic-path}")
    public String picPath;

    @Value("${app.environment}")
    public String env;
    /**
     * 供应商ID。
     */
    private final Integer supplierId= 5374;
    private final Integer supplierIdbak= 46;//备用供应商
    //仓库
    private final String warehouseNo= "126";

    private final String mid= "1478";


    /**
     * 鲜沐后台登录的用户名密码
     */
    private final String adminUserName= "<EMAIL>";

    private final String adminpassword= "123456";

    //SRM-PC后台登录的用户名密码
    private final String srmPhone= "15757100003";

    private final String srmPassWord= "5658vpD6";

    //定时任务相关
    private final String pmsGroup= "pms-service";

    private static final Map<String, Map<String, String>> ENV_TO_JOB_ID_MAP = new HashMap<>();

    static {
        // 初始化环境与 jobId 的映射关系
        Map<String, String> devJobs = new HashMap<>();
        devJobs.put("dontPackupJobId", "24990403");//供应商未提报商品库存调整处理
        devJobs.put("PackupDataHandleTask", "9381587"); // 供应商次日库存提报清理
        devJobs.put("UndonePackupSupplierMsgNoticeTask", "9381585");//供应商库存提报整点提醒

        Map<String, String> qaJobs = new HashMap<>();
        qaJobs.put("dontPackupJobId", "9381677");//供应商未提报商品库存调整处理
        qaJobs.put("PackupDataHandleTask", "9381678"); // 供应商次日库存提报清理
        qaJobs.put("UndonePackupSupplierMsgNoticeTask", "9381676");//供应商库存提报整点提醒

        ENV_TO_JOB_ID_MAP.put("dev", devJobs);
        ENV_TO_JOB_ID_MAP.put("qa", qaJobs);
    }
    public String getJobId(String env, String jobIdKey) {
        if (env == null || env.isEmpty()) {
            throw new IllegalArgumentException("环境变量env不能为空");
        }

        Map<String, String> jobs = ENV_TO_JOB_ID_MAP.get(env);
        if (jobs == null) {
            throw new IllegalArgumentException("无效环境变量env: " + env);
        }

        String jobId = jobs.get(jobIdKey);
        if (jobId == null) {
            throw new IllegalArgumentException("无效的jobIdKey: " + jobIdKey + " 在环境变量env: " + env);
        }

        return jobId;
    }
    private final String sku= "2225603800611";//测试经销品

    //图片视频地址
    private final String mainPictureUrl= "https://azure.summerfarm.net/pop/goods/测试主图.jpg";
    private final String detailPictureUrl= "https://azure.summerfarm.net/pop/goods/测试详情图.jpg";
    private final String vedioUrl= "https://azure.summerfarm.net/pop/goods/测试视频.mp4";
    //popsku
    private final String popSku= "2215826551600";

    //pop库存仓
    private final String popWarehouse= "479";

    //全品类代销不如仓SKU
    private final String sellWithoutWarehouseSku= "**********612";
    private final String sellWithoutWarehouseSPU= "**********";

    private final String sellWithoutWarehouseSku2= "**********000";
    //开启精细化仓库
    private final Integer openWarehouseNo= 373;

}
