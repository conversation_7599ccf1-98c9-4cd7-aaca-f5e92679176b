package com.xianmu.atp.cases.pms.aiCase.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.aiCase.enums.ReconciliationEnumsInterface;
import com.xianmu.atp.dal.dao.common.DingdingProcessFlowService;
import com.xianmu.atp.dal.mapper.FinanceAccountStatementMapper;
import com.xianmu.atp.dal.model.DingdingProcessFlow;
import com.xianmu.atp.dal.model.FinanceAccountStatement;
import com.xianmu.atp.enums.api.testTool.TestTool;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class PurchaseReconciliationStep {

    @Resource
    private Request request;
    
    //场景变量
    @Resource
    private EnvVariable envVar;

    @Resource
    private FinanceAccountStatementMapper financeAccountStatementMapper;

    @Resource
    private DingdingProcessFlowService dingdingProcessFlowService;
    


    
    /**
     * 查询采购单付款信息（支持自定义请求体）
     * @param requestBody 自定义请求体参数
     */
    @Retry( onExceptions = {AssertionError.class})
    public void queryPurchasePayment(HashMap<String, Object> requestBody, Map<String, Object> checkParams) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.queryPayment.getUrl());
        
        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.queryPayment.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.queryPayment.getContentType(),
                requestBody);
        
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");
        
        // 验证数据结构
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "返回数据不能为空");
        
        // 验证付款信息字段
        if (data.containsKey("purchaseSupplierPayments") && !data.getJSONArray("purchaseSupplierPayments").isEmpty()) {
            JSONObject paymentInfo = data.getJSONArray("purchaseSupplierPayments").getJSONObject(0);
            if (checkParams != null && !checkParams.isEmpty()) {
                for (Map.Entry<String, Object> entry : checkParams.entrySet()) {
                    String key = entry.getKey();
                    Object expectedValue = entry.getValue();
                    Assert.assertTrue(paymentInfo.containsKey(key), "付款信息缺少字段: " + key);

                    Double actualValue = paymentInfo.getDoubleValue(key);
                    if (expectedValue instanceof Number) {
                        // 数值类型比较
                        Assert.assertEquals(actualValue, expectedValue, "付款信息中金额字段 [" + key + "] 数值不符合预期");
                    }
                }
            }
        }
    }

    /**
     * 步骤4: 通过/pms-service/financeAccountStatement/purchase/reconciliation/selectWarehousingOrderBySupplier/page接口根据采购单号查询入库单明细数据
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为0
     * - 分页数据结构正确
     * - 入库单明细字段完整性
     * @param requestBody 自定义请求体参数
     */
    @Retry(attempts = 5, delay = 2000, onExceptions = {AssertionError.class})
    public JSONArray queryInAndOut(HashMap<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.queryInAndOut.getUrl());
        
        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.queryInAndOut.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.queryInAndOut.getContentType(),
                requestBody);
        
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");
        
        // 验证数据结构
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "返回数据不能为空");
        
        // 验证分页数据
        Assert.assertTrue(data.containsKey("list"), "缺少列表数据");

        // 验证入库单明细数据
        JSONArray list = data.getJSONArray("list");
        if (list != null && !list.isEmpty()) {
            return list;
        }
        return null;
    }

    /**
     * 通过/pms-service/financeAccountStatement/purchase/reconciliation/addReconciliation接口根据入库单明细数据发起对账单
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为0
     * - 返回有效的对账单ID
     * - 对账单ID格式正确
     * @param requestBody 自定义请求体参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void addReconciliation(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.addReconciliation.getUrl());
        
        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.addReconciliation.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.addReconciliation.getContentType(),
                requestBody);
        
        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");
        
        // 验证数据结构
        String data = result.getString("data");
        Assert.assertNotNull(data, "返回数据不能为空");
        
        // 保存对账单ID到临时变量
        TemVar.getCurrent().setReconciliationId(data);

        log.info("创建对账单成功，请求参数: {}, 对账单ID: {}", JSON.toJSONString(requestBody), data);
    }

    /**
     * 添加入库单明细完整校验
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为200
     * @param requestBody 自定义请求体参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void verificationPurchase(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.verificationPurchase.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.verificationPurchase.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.verificationPurchase.getContentType(),
                requestBody);

        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");
    }

    /**
     * 查询供应商入库单和退货单检验
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为200
     * @param requestBody 自定义请求体参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void checkInAndOut(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.checkInAndOut.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.checkInAndOut.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.checkInAndOut.getContentType(),
                requestBody);

        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");
    }

    /**
     * 核销流水计算
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为200
     * @param requestBody 自定义请求体参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void reduce(Map<String, Object> requestBody, BigDecimal amount) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.reduce.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.reduce.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.reduce.getContentType(),
                requestBody);

        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");

        // 验证数据结构
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "返回数据不能为空");
        Assert.assertNotNull(data.getBigDecimal("estimateAmount"), "暂估总成本不能为空");
        Assert.assertNotNull(data.getBigDecimal("totalBillAmount"), "总成本不能为空");
        Assert.assertNotNull(data.getBigDecimal("writeOffAmount"), "预付核销金额不能为空");
        Assert.assertEquals(data.getBigDecimal("writeOffAmount"), amount, "预付核销金额验证失败");
    }

    /**
     * 获取供应商账户信息
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为200
     * @param requestBody 自定义请求体参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public JSONObject accountDetail(Map<String, Object> requestBody ) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.accountDetail.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.accountDetail.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.accountDetail.getContentType(),
                requestBody);

        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");

        // 验证数据结构
        JSONArray data = result.getJSONArray("data");
        Assert.assertNotNull(data, "返回数据不能为空");
        return data.getJSONObject(0);
    }

    /**
     * 对账单发起人的审批信息
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为200
     * @param requestBody 自定义请求体参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void auditInformation(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.auditInformation.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.auditInformation.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.auditInformation.getContentType(),
                requestBody);

        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");
    }

    /**
     * 对账单数据验证
     * 断言验证：
     * - 校验对账单状态符合预期
     * - 对账单审批流状态符合预期
     * @param checkStatus 状态
     * @param reconciliationId 对账单id
     */
    @Retry(attempts=5,  delay = 3000, onExceptions = {AssertionError.class})
    public void reconciliationCheck(Integer checkStatus, String reconciliationId,Integer bizType,Integer flowStatus) {
        FinanceAccountStatement res = financeAccountStatementMapper.selectById(reconciliationId);
        Assert.assertNotNull(res, "对账单不存在");
        Assert.assertEquals(res.getStatus(), checkStatus, "对账单状态不符合预期"+ checkStatus);
        DingdingProcessFlow dingdingProcessFlowInfo = dingdingProcessFlowService.selectOneByBizIdAndBizType(Long.valueOf(reconciliationId), bizType);
        Assert.assertEquals(dingdingProcessFlowInfo.getProcessStatus(), flowStatus, "对账单审批流不符合预期"+ checkStatus);
    }

    /**
     * 对账单撤销申请&关闭账单
     * 断言验证：
     * - HTTP状态码为200
     * - 业务状态码为200
     * @param requestBody 自定义请求体参数
     */
    @Retry( delay = 2000, onExceptions = {AssertionError.class})
    public void reconciliationAdvanceCancel(Map<String, Object> requestBody) {
        String URL = request.urlBuild(envVar.xmAdminDomain, ReconciliationEnumsInterface.ReconciliationEnum.reconciliationAdvanceCancel.getUrl());

        HttpResponseWrapper res = request.sendRequest(URL,
                ReconciliationEnumsInterface.ReconciliationEnum.reconciliationAdvanceCancel.getMethod(),
                ReconciliationEnumsInterface.ReconciliationEnum.reconciliationAdvanceCancel.getContentType(),
                requestBody);

        Assert.assertEquals(res.getStatusCode(), 200, "HTTP状态码验证失败");
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "业务状态码验证失败");
    }


}