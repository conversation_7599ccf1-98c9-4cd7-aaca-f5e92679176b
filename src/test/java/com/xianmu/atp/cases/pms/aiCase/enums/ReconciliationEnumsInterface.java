package com.xianmu.atp.cases.pms.aiCase.enums;

import lombok.Getter;

/**
 * 预付对账流程相关接口枚举
 */
public interface ReconciliationEnumsInterface {

    /**
     * 预付对账流程相关接口
     */
    @Getter
    enum ReconciliationEnum {
        // 查询采购单付款信息
        queryPayment("/pms-service/po/queryPayment", "POST", "application/json", "查询采购单付款信息"),
        
        // 查询供应商入库单和退货单
        queryInAndOut("/pms-service/financeAccountStatement/purchase/reconciliation/selectWarehousingOrderBySupplier/page", "POST", "application/json", "查询供应商入库单和退货单"),
        //添加入库单明细完整校验
        verificationPurchase("/pms-service/financeAccountStatement/purchase/verification", "POST", "application/json", "添加入库单明细完整校验"),
        // 对账单检查
        checkInAndOut("/pms-service/financeAccountStatement/purchase/reconciliation/selectWarehousingOrderBySupplier/check",
                "POST", "application/json", "查询供应商入库单和退货单检验"),
        // 对账单检查
        reduce("/pms-service/financeAccountStatement/purchase/reduce",
                "POST", "application/json", "核销流水计算"),
        // 获取供应商账户信息
        accountDetail("/pms-service/supplier/query/account-detail-list",
                "POST", "application/json", "获取供应商账户信息"),
        //对账单发起人的审批信息
        auditInformation("/pms-service/financeAccountStatement/auditInformation", "POST", "application/json", "对账发起人信息"),
        // 添加对账单
        addReconciliation("/pms-service/financeAccountStatement/purchase/reconciliation/addReconciliation", "POST", "application/json", "添加对账单"),

        // 对账单列表
        reconciliationList("/pms-service/financeAccountStatement/purchase/reconciliation/list", "POST", "application/json", "对账单列表"),

        // 对账单详情
        reconciliationDetail("/pms-service/financeAccountStatement/purchase/reconciliation/detail", "POST", "application/json", "对账单详情"),
        

        // 对账单调整明细
        reconciliationAdjustDetail("/pms-service/financeAccountStatement/purchase/reconciliation/adjustDetail", "POST", "application/json", "对账单调整明细"),
        
        // 对账单导出
        reconciliationExport("/pms-service/financeAccountStatement/purchase/reconciliation/export", "POST", "application/json", "对账单导出"),
        
        // 复制对账单
        reconciliationCopy("/pms-service/financeAccountStatement/purchase/reconciliation/copy", "POST", "application/json", "复制对账单"),
        
        // 撤销申请&关闭账单
        reconciliationAdvanceCancel("/pms-service/financeAccountStatement/advance/cancel", "POST", "multipart/form-data", "对账单撤回申请"),
        
        // 对账单审批信息
        reconciliationApprovalInfo("/pms-service/financeAccountStatement/purchase/reconciliation/approvalInfo", "POST", "application/json", "对账单审批信息"),
        
        // 对账单操作记录
        reconciliationOperationRecord("/pms-service/financeAccountStatement/purchase/reconciliation/operationRecord", "POST", "application/json", "对账单操作记录"),
        
        // 采购确认
        reconciliationPurchaseConfirm("/pms-service/financeAccountStatement/purchase/reconciliation/purchaseConfirm", "POST", "application/json", "采购确认"),
        
        // SKU校验
        reconciliationSkuCheck("/pms-service/financeAccountStatement/purchase/reconciliation/skuCheck", "POST", "application/json", "SKU校验"),
        
        // 核销流水计算
        reconciliationWriteOffCalculation("/pms-service/financeAccountStatement/purchase/reconciliation/writeOffCalculation", "POST", "application/json", "核销流水计算");

        private final String url;
        private final String method;
        private final String contentType;
        private final String description;

        ReconciliationEnum(String url, String method, String contentType, String description) {
            this.url = url;
            this.method = method;
            this.contentType = contentType;
            this.description = description;
        }
    }
}