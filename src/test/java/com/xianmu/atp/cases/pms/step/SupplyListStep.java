package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.enums.api.common.CommonEnumsInterface;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SupplyListStep{
    @Resource
    private Request request;
    @Resource
    private EnvVariable envVar;

    /**
     * 供货目录--列表接口
     */
    @Retry(attempts =4, delay = 2000, onExceptions = {AssertionError.class})
    public String queryPage(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SupplyListEnum.queryPage.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplyListEnum.queryPage.getMethod(),
                PmsEnumsInterface.SupplyListEnum.queryPage.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "列表数据为空");
        return res_list.getJSONObject(0).getString("id");
    }

    /**
     * 供货目录--新增
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void add(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SupplyListEnum.add.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplyListEnum.add.getMethod(),
                PmsEnumsInterface.SupplyListEnum.add.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     * 供货目录--更新
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void upsertUpdate(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SupplyListEnum.upsertUpdate.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplyListEnum.upsertUpdate.getMethod(),
                PmsEnumsInterface.SupplyListEnum.upsertUpdate.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     * 供货目录--根据供货目录三要素（或四要素）查询列表
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void queryList(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SupplyListEnum.queryList.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplyListEnum.queryList.getMethod(),
                PmsEnumsInterface.SupplyListEnum.queryList.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        JSONArray res_list = result.getJSONObject("data").getJSONArray("list");
        Assert.assertFalse(Objects.isNull(res_list) || res_list.isEmpty(), "列表数据为空");
    }

    /**
     * 供货目录--变更供应商
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void upsertSupplier(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SupplyListEnum.upsertSupplier.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplyListEnum.upsertSupplier.getMethod(),
                PmsEnumsInterface.SupplyListEnum.upsertSupplier.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }

    /**
     * 供货目录--删除
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void delete(Map<String, Object> params,String expectMsg) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SupplyListEnum.delete.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplyListEnum.delete.getMethod(),
                PmsEnumsInterface.SupplyListEnum.delete.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        if (Objects.isNull(expectMsg)){
            Assert.assertEquals(result.getString("msg"),"请求成功");
        }else {
            Assert.assertTrue(result.getString("msg").contains(expectMsg),"删除供货目录断言失败");
        }
    }

    /**
     * 供货目录--导入
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void stockPackupImport(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, PmsEnumsInterface.SupplyListEnum.supplyListiImport.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplyListEnum.supplyListiImport.getMethod(),
                PmsEnumsInterface.SupplyListEnum.supplyListiImport.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        TemVar.getCurrent().setExcelResId(result.getString("data"));//设置导入结果id
    }

}
