package com.xianmu.atp.cases.pms.aiCase;

import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.step.DirectPurchaseStep;
import com.xianmu.atp.cases.pms.step.PurchaseAdvanceStep;
import com.xianmu.atp.dal.dao.PurchasesDao;
import com.xianmu.atp.dal.dao.common.BaseInfoService;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.generic.common.UtilRequest;
import io.qameta.allure.Allure;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.*;

/**
 * 直发采购接口测试用例
 * 测试直发采购完整业务流程
 * 
 * <AUTHOR>
 */
@Slf4j
@Epic("PMS")
@Feature("直发采购")
@Owner("大鹏")
public class DirectPurchaseApiTest extends BaseTest {

    @Resource
    private Request request;

    @Resource
    private EnvVariable envVar;

    @Resource
    private DirectPurchaseStep directPurchaseStep;
    @Resource
    private BaseInfoService baseInfoService;
    @Resource
    private UtilRequest utilRequest;
    @Resource
    private PurchasesDao purchasesDao;
    @Resource
    private PurchaseAdvanceStep purchaseAdvanceStep;


    private SkuInfo skuInfo;
    private Supplier supplierInfo;


    // 复用EnvVariable中的测试数据
    private  String testSku; // 使用EnvVariable中定义的SKU
    private  Integer supplierId; // 使用EnvVariable中定义的供应商ID


    @BeforeClass
    public void setUp() {
        // 模拟登录获取token
        String token = request.xmAdminLogin();
        request.setToken( token);
        testSku = envVar.getSku(); // 使用EnvVariable中定义的SKU
        supplierId = envVar.getSupplierId(); // 使用EnvVariable中定义的供应商ID

    }
    @BeforeClass(description = "初始化操作--获取基础数据")
    public void getBaseInfo(){
        skuInfo =baseInfoService.getProductInfo(envVar.getSku());
        supplierInfo = baseInfoService.selectById(envVar.getSupplierId());

    }

    @AfterMethod(description = "清理临时变量")
    public void tearDown() {
        // 使用TemVar的清理机制
        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
        TemVar.remove(); // 清理ThreadLocal变量
    }

    /**
     * TC_API_DIRECT_PURCHASE_WORKFLOW_001
     * 直发采购完整业务流程测试
     * 测试步骤：
     * 1. 新建直发采购单
     * 2. 查询采购单详情验证状态
     * 3. 查询付款金额数据验证
     * 4. 绑定预付金额
     * 5. 一键发货
     * 6. 批量获取阶梯价格
     * 7. 解绑预付金额
     */
    @Test(description = "直发采购完整业务流程测试:下单、导入、一键发货、出入库校验、绑定解绑预付")
    public void TC_API_DIRECT_PURCHASE_WORKFLOW_001() {

        Map<String, Object> userInfo = utilRequest.getPersonalInfo();
        String remark = "自动化直发采购"+System.currentTimeMillis();

        Map<String, Object> merchantDetail = utilRequest.getMerchantDetail(envVar.getMid());
        //  新建直发采购单
        Map<String, Object> orderParams = directPurchaseStep.buildPurchaseParams(
                skuInfo, testSku,userInfo, supplierId,merchantDetail,remark);
        directPurchaseStep.placeDirectPurchaseOrder(orderParams);
        String purchaseNo = purchasesDao.getPurchasesNo( remark);

        //  查询采购单详情验证状态
        directPurchaseStep.queryPurchaseOrderDetail(purchaseNo);

        //  查询当前采购单及供应商的付款信息
        Map<String, Object> paymentParams1 = new HashMap<>();
        paymentParams1.put("purchaseNo", purchaseNo);
        paymentParams1.put("supplierId", supplierId);
        JSONObject paymentData1 = directPurchaseStep.queryPaymentBySupplier(paymentParams1);
        //  绑定预付金额
        Map<String, Object> bindParams = new HashMap<>();
        bindParams.put("purchaseNo", purchaseNo);
        bindParams.put("supplierId", supplierId);
        bindParams.put("advanceAmount", paymentData1.getBigDecimal("actualPrice"));

        directPurchaseStep.bindAdvancePayment(bindParams);
        // 查询预付金额记录
        Map<String, Object> queryAdvanceRecordParams = new HashMap<>();
        queryAdvanceRecordParams.put("billId", purchaseNo);
        queryAdvanceRecordParams.put("pageIndex", 1);
        queryAdvanceRecordParams.put("pageSize", 10);
        queryAdvanceRecordParams.put("supplierId", supplierId);
        queryAdvanceRecordParams.put("type", 20);
        purchaseAdvanceStep.queryAdvanceReocrdList(queryAdvanceRecordParams);


        //  一键发货
        Map<String, Object> deliveryParams = new HashMap<>();
        deliveryParams.put("purchaseNo", purchaseNo);
        directPurchaseStep.oneClickDelivery(deliveryParams);
        //查询对应直发订单
        String directOrderNo= directPurchaseStep.queryDirectOrderDetail(purchaseNo);
        //查询直发采购对应的出入库任务完结
        directPurchaseStep.queryStockTask(directOrderNo,2);
        directPurchaseStep.queryStockTask(purchaseNo,2);

        // 批量获取阶梯价格
        Map<String, Object> stepPricesParams = new HashMap<>();
        Map<String, Object> priceQueryBean = new HashMap<>();
        priceQueryBean.put("sku", testSku);
        priceQueryBean.put("supplierId", supplierId);
        priceQueryBean.put("warehouseNo",1);
        stepPricesParams.put("skuParamList", Lists.newArrayList(priceQueryBean));
        directPurchaseStep.getStepPriceBatch(stepPricesParams);
        //  查询当前付款金额数据验证
        Map<String, Object> paymentParams2 = new HashMap<>();
        paymentParams2.put("purchaseNo", purchaseNo);
        paymentParams2.put("supplierId", supplierId);
        JSONObject paymentData2 = directPurchaseStep.queryPaymentBySupplier(paymentParams2);

        //  解绑预付金额
        Map<String, Object> unbindParams = new HashMap<>();
        unbindParams.put("purchaseNo", purchaseNo);
        unbindParams.put("supplierId", supplierId);
        unbindParams.put("unboundAmount", paymentData2.getBigDecimal("advanceAmount").subtract(paymentData2.getBigDecimal("advanceWriteOff")));

        directPurchaseStep.unbindAdvancePayment(unbindParams);
        // 查询解绑预付金额记录
        queryAdvanceRecordParams.put("type", 21);

        purchaseAdvanceStep.queryAdvanceReocrdList(queryAdvanceRecordParams);
        //导出详情
        Map<String, Object> exportParams = new HashMap<>();
        exportParams.put("purchaseNo", purchaseNo);
        directPurchaseStep.exportDetail(exportParams);
    }

//    @Test(description = "直发采购完整业务流程测试:导入")
//    public void TC_API_DIRECT_PURCHASE_WORKFLOW_002() {
//        List<String> purchasesTitleList = new ArrayList<>(Arrays.asList(
//                "sku(必填)", "商品名称", "数量(必填)", "总价(必填)","供应商(必填)"));
//        List<Object> purchasesValueList = new ArrayList<>();
//        purchasesValueList.add(new HashMap<String, Object>(){{
//            put("1",skuInfo.getPdNo());
//            put("2",skuInfo.getPdName());
//            put("3",10);
//            put("4",100);
//            put("5",supplierInfo.getName());
//        }});
//        JSONObject purchasesAddExcelInfo =utilRequest.excelCreate(new HashMap<String, Object>(){{
//            put("fileName","直发采购导入-自动化测试.xlsx");
//            put("sheetInfo", Lists.newArrayList(new HashMap<String, Object>(){{
//                put("titleList",purchasesTitleList);
//                put("valueList",purchasesValueList);
//            }}));
//        }});
//
//        directPurchaseStep.uploadV2(new HashMap<String, Object>(){{
//            put("file","test-app-temp:"+purchasesAddExcelInfo.getString("data"));
//            put("warehouseNo",envVar.getPopWarehouse());
//        }});//excel--上传
//    }
}