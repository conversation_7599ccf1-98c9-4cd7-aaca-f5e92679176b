package com.xianmu.atp.cases.pms.input;

import com.xianmu.atp.generic.common.BaseInput;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 预付单实体类
 */
@Data
public class purchaseAdvanceInput extends BaseInput {

        /**
         * 主键id，也为预付单号
         */
        private Long id;
        /**
         * 是否关联采购单 1、关联 2、不关联
         */
        private Integer type;
        /**
         * 付款金额
         */
        private BigDecimal totalAmount;
        /**
         * 供应商id
         */
        private Integer supplierId;
        /**
         * 供应商名称
         */
        private String supplierName;
        /**
         * 支付方式 1.银行卡 2.现金
         */
        private Integer payType;
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        /**
         * 1 生鲜 2 品牌
         */
        private Integer pdType;
        /**
         * 备注
         */
        private String remark;
        /**
         * 预付单状态 1、预付审核 4、付款审核中 5、待付款 6、已付款 7、作废
         */
        private Integer status;

        /**
         * 供应商账户id
         */
        private Integer supplierAccountId;

        /**
         * 是否有关联的对账单 0 是 1 否
         */
        private Integer state;

        /**
         * 预付单临时备注
         */
        private String temporaryRemark;

        /**
         * 当前状态处理人
         */
        private String currentProcessor;

        /**
         * 作废原因：0：预付审核失败 1：撤回申请 2：：付款审核失败
         */
        private Integer deleteReason;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;

        private List<Map<String, Object>> purchasesVOList;



        public void  addParams(Integer supplierId, String supplierName, Integer payType, BigDecimal totalAmount, Integer type, Integer supplierAccountId, String remark) {
                this.supplierId = supplierId;
                this.supplierName = supplierName;
                this.payType = payType;
                this.totalAmount = totalAmount;
                this.type = type;
                this.supplierAccountId = supplierAccountId;
                this.remark = remark;

        }




}
