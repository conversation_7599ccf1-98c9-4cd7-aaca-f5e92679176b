package com.xianmu.atp.cases.pms.aiCase;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.aiCase.env.SaasEnvVariable;
import com.xianmu.atp.cases.pms.aiCase.step.SaasSupplierStep;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.generic.common.UtilRequest;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.boot.test.context.SpringBootTest;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * SaaS供应商测试用例执行层
 * 
 * 测试用例覆盖：
 * 1. 供应商完整业务流程测试
 * 2. 供应商导入流程测试
 * 
 * <AUTHOR> Test Engineer
 */
@Slf4j
@Epic("Saas-PMS")
@Feature("saas供应商")
@Owner("大鹏")
public class SaasSupplierTest extends BaseTest {

    @Resource
    private Request request;

    @Resource
    private SaasEnvVariable saasSupplierEnvVar;

    @Resource
    private SaasSupplierStep saasSupplierStep;
    @Resource
    private UtilRequest utilRequest;

    private JSONObject skuInfo;


    /**
     * 测试初始化
     * 执行SaaS登录并设置token信息
     */
    @BeforeClass
    public void setUp() {
        log.info("=== 开始执行SaaS供应商测试用例初始化 ===");
        
        // 调用saasLogin进行登录并设置token
        String token = request.saasLogin(saasSupplierEnvVar.getUsername(), saasSupplierEnvVar.getPassword());
        Assert.assertNotNull(token, "SaaS登录失败，token为空");
        // 设置全局token
        request.setToken(token);
        //初始化saas货品信息
        Map<String, Object> saasSkuQuerParams = new HashMap<>();
        saasSkuQuerParams.put("saasSkuId", Long.valueOf(saasSupplierEnvVar.getSaasSkuId()));
        saasSkuQuerParams.put("pageIndex", 1);
        saasSkuQuerParams.put("pageSize", 10);
        saasSkuQuerParams.put("createTypeList", Lists.newArrayList(0,2,3) );

        JSONArray skuList = utilRequest.selectLikeBySkuOrName( saasSkuQuerParams);
        Assert.assertNotNull(skuList, "查询货品列表结果为空");
        skuInfo= skuList.getJSONObject(0);
    }


    /**
     * 测试用例01: 供应商完整业务流程测试
     * 测试步骤：
     * 1. 新增供应商，断言成功
     * 2. 通过新增数据查询供应商列表，断言返回结果包含新增数据
     * 3. 根据环境变量中的货品数据，添加合作货品
     * 4. 查询合作货品列表，断言返回结果包含新增数据
     * 5. 获取商品税率
     * 6. 获取价格有效期
     * 7. 新增采购价格
     * 8. 编辑采购价格
     * 9. 导出采购价格
     * 10. 编辑供应商状态=停止合作
     * 11. 导出该供应商数据
     * 12. 删除合作货品数据
     */
    @Test(description = "供应商完整业务流程测试1")
    public void testSupplierCompleteFlow() {
        log.info("=== 开始执行测试用例01: 供应商完整业务流程测试 ===");

        BigDecimal supplierDeadlineNum1 = saasSupplierStep.pendingMatter();//查询临期供应商

        // 步骤1: 新增供应商，断言成功
        // 使用当前时间精确到秒作为自动化唯一性标识
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String currentTimeMillis = java.time.LocalDateTime.now().format(formatter1);
        // 使用当前日期作为startDate，格式化为 yyyy-MM-dd
        DateTimeFormatter formatter2 = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currentDate = java.time.LocalDate.now().format(formatter2);

        Map<String, Object> supplierParams = saasSupplierStep.buildSupplierParams(picPath,currentDate,currentTimeMillis);
        String supplierId = saasSupplierStep.saveOrUpdateSupplier(supplierParams);

        // 步骤2: 通过新增数据查询供应商列表，断言返回结果包含新增数据
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("supplierId", supplierId);
        queryParams.put("customerSupplierId", currentTimeMillis);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("contractExpireFlag", 1);//临期供应商
        queryParams.put("status", 0);//状态=启用
        saasSupplierStep.querySupplierPage(queryParams);

        BigDecimal supplierDeadlineNum2 = saasSupplierStep.pendingMatter();//查询临期供应商
        Assert.assertTrue(supplierDeadlineNum2.compareTo(supplierDeadlineNum1) > 0, "临期供应商数据查询结果不符合预期");


        // 步骤3: 根据环境变量中的货品数据，添加合作货品
        log.info("步骤3: 添加合作货品");
        Map<String, Object> relationParams = new HashMap<>();
        relationParams.put("supplierId", supplierId);
        relationParams.put("skuList", Collections.singletonList(skuInfo.getString("sku")));
        saasSupplierStep.upsertSupplyRelation(relationParams);

        //查询添加货品后的供应商数据
        queryParams.put("saasSkuId", saasSupplierEnvVar.getSaasSkuId());
        saasSupplierStep.querySupplierPage(queryParams);



        // 步骤4: 查询合作货品列表，断言返回结果包含新增数据
        Map<String, Object> relationQueryParams = new HashMap<>();
        relationQueryParams.put("supplierId", supplierId);
        relationQueryParams.put("pageIndex", 1);
        relationQueryParams.put("pageSize", 10);
        String relationId= saasSupplierStep.querySupplyRelationPage(relationQueryParams, saasSupplierEnvVar.getSaasSkuId());

        // 步骤5: 删除合作货品数据
        Map<String, Object> relationDelParams = new HashMap<>();
        relationDelParams.put("supplierId", supplierId);
        relationDelParams.put("idList", Collections.singletonList(relationId));
        saasSupplierStep.batchDeleteSupplyRelation(relationDelParams);

        // 步骤6: 获取商品税率
        Map<String, Object> taxParams = new HashMap<>();
        Map<String, Object> skuParams = new HashMap<>();
        skuParams.put("saasSkuId", Long.valueOf(saasSupplierEnvVar.getSaasSkuId()));
        skuParams.put("sku", skuInfo.getString("sku"));
        taxParams.put("supplierId", saasSupplierEnvVar.getSaasSupplierId());
        taxParams.put("skuList", Collections.singletonList(skuParams));
        saasSupplierStep.getTaxRate(taxParams);

        // 步骤7: 获取价格有效期
        Map<String, Object> timeParams = new HashMap<>();
        timeParams.put("skuList", Collections.singletonList(skuInfo.getString("sku")));
        timeParams.put("supplierId", saasSupplierEnvVar.getSaasSupplierId());

        saasSupplierStep.getPriceTimeList(timeParams);


        // 步骤8: 新增采购价格
        Map<String, Object> priceParams = new HashMap<>();
        Map<String, Object> skuData = new HashMap<>();
        skuData.put("price", 10);
        skuData.put("saasSkuId", Long.valueOf(saasSupplierEnvVar.getSaasSkuId()));
        skuData.put("sku", skuInfo.getString("sku"));
        skuData.put("taxRate", 0.1);
        skuData.put("startTime", currentDate+" 00:00:00");
        skuData.put("endTime", currentDate+" 23:59:59");
        priceParams.put("skuList", Collections.singletonList(skuData));
        priceParams.put("supplierId", supplierId);
        saasSupplierStep.savePurchasePrice(priceParams);

        // 步骤9: 查询采购价格
        Map<String, Object> queryPriceParams = new HashMap<>();
        queryPriceParams.put("supplierId", supplierId);
        queryPriceParams.put("pageIndex", 1);
        queryPriceParams.put("pageSize", 10);
        queryPriceParams.put("saasSkuId", saasSupplierEnvVar.getSaasSkuId());
        queryPriceParams.put("queryDate", currentDate);

        String priceDetailId = saasSupplierStep.queryPurchasePriceList(queryPriceParams);
        // 步骤10: 编辑采购价格
        skuData.put("detailId", priceDetailId);
        skuData.put("price", 20);
        saasSupplierStep.updatePurchasePrice(skuData);

        // 步骤11: 导出采购价格
        Map<String, Object> exportPriceParams = new HashMap<>();
        exportPriceParams.put("supplierId", supplierId);
        exportPriceParams.put("queryDate", currentDate);
        exportPriceParams.put("saasSkuId", saasSupplierEnvVar.getSaasSkuId());
        exportPriceParams.put("paramJson",exportPriceParams.toString());
        String resId= saasSupplierStep.exportPurchasePrice(exportPriceParams);
        Map<String, Object> exportResultData = new HashMap<>();
        exportResultData.put("resourceId", resId);
        utilRequest.getResult(exportResultData);

        // 步骤12: 查询供应商详情数据
        Map<String, Object> supplierDetailParams = new HashMap<>();
        supplierDetailParams.put("supplierId", supplierId);
        JSONObject supplierDetail = saasSupplierStep.getSupplierDetail(supplierDetailParams);

        // 步骤12: 编辑供应商状态=停止合作
        supplierDetail.put("status", 1);
        supplierDetail.put("id", supplierId);
        supplierDetail.put("supplierContract", supplierDetail.getJSONObject("lastContract"));
        saasSupplierStep.saveOrUpdateSupplier(supplierDetail);

        // 步骤13:查询对应供应商列表
        queryParams.remove("saasSkuId");
        queryParams.remove("contractExpireFlag");
        queryParams.put("status", 1);
        saasSupplierStep.querySupplierPage(queryParams);

        // 步骤14: 导出该供应商数据
        queryParams.remove("pageIndex");
        queryParams.remove("pageSize");
        queryParams.put("params", queryParams.toString());
        String exportFileId = saasSupplierStep.exportSupplier(queryParams);
        //步骤125:获取导出结果
        exportResultData.put("resourceId", exportFileId);
        utilRequest.getResult(exportResultData);

    }

    /**
     * 测试用例02: 供应商导入流程测试
     */
    @Test(priority = 2, description = "供应商导入流程--导入新增、导入更新")
    public void testSupplierImportFlow() {

        // 步骤1: 创建Excel并导入供应商
        // 使用当前时间精确到秒作为自动化唯一性标识
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String currentTimeMillis = java.time.LocalDateTime.now().format(formatter1);
        // 使用当前日期作为startDate，格式化为 yyyy-MM-dd
        DateTimeFormatter formatter2 = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currentDate = java.time.LocalDate.now().format(formatter2);
        // 创建供应商导入Excel数据
        String[] supplierTitleArray = {"系统编码","自定义编码","供应商名称(必填)", "供应商类型(必填)[个人、企业(生产商)、企业(经销商)]",
                "统一社会信用代码(必填)", "合作开始时间(必填)[2025-01-01]", "合作结束时间(必填)[2025-01-01]","供应商状态(必填)[启用、停用]",
        "供应货品id(英文逗号分割)"};
        Map<String, Object> supplierValueMap = new HashMap<>();
        supplierValueMap.put("3", "自动导入生成"+currentTimeMillis);
        supplierValueMap.put("2", currentTimeMillis);
        supplierValueMap.put("4","个人");
        supplierValueMap.put("5", "3310"+currentTimeMillis);
        supplierValueMap.put("6", currentDate);
        supplierValueMap.put("7", currentDate);
        supplierValueMap.put("8", "启用");
        supplierValueMap.put("9", saasSupplierEnvVar.getSaasSkuId());

        // 创建Excel文件
        Map<String, Object> excelParams = new HashMap<>();
        Map<String, Object> sheetInfo = new HashMap<>();
        sheetInfo.put("titleList", supplierTitleArray);
        sheetInfo.put("valueList", Collections.singletonList(supplierValueMap));
        excelParams.put("fileName", "供应商导入-自动化测试.xlsx");
        excelParams.put("sheetInfo", Collections.singletonList(sheetInfo));

        JSONObject supplierExcelInfo = utilRequest.excelCreate(excelParams);

        // 使用导入接口上传
        Map<String, Object> importParams = new HashMap<>();
        importParams.put("fileOssUrl", "test-app-temp:" + supplierExcelInfo.getString("data"));
        importParams.put("url", "test-app-temp:" + supplierExcelInfo.getString("data"));
        importParams.put("source", "saas");
        importParams.put("sourceFileName", "供应商导入-自动化测试.xlsx");
        importParams.put("bizType", 310);

        String fileID = utilRequest.importExcel(importParams);

        // 步骤2: 获取供应商导入结果
        Map<String, Object> getResultParams = new HashMap<>();
        getResultParams.put("resourceId", fileID);
        utilRequest.getResult(getResultParams);

        // 步骤3: 通过新增数据查询供应商列表，断言返回结果包含新增数据
        Map<String, Object> queryParams = new HashMap<>();
//        queryParams.put("supplierId", supplierId);
        queryParams.put("customerSupplierId", currentTimeMillis);
        queryParams.put("pageIndex", 1);
        queryParams.put("pageSize", 10);
        queryParams.put("contractExpireFlag", 1);//临期供应商
        queryParams.put("status", 0);//状态=启用
        queryParams.put("saasSkuId", saasSupplierEnvVar.getSaasSkuId());
        String supplierId= saasSupplierStep.querySupplierPage(queryParams);

        // 步骤4: 导入更新供应商数据--停止合作
        supplierValueMap.put("1", supplierId);
        supplierValueMap.put("8", "停用");
        supplierValueMap.remove("9");
        sheetInfo.put("titleList", supplierTitleArray);
        sheetInfo.put("valueList", Collections.singletonList(supplierValueMap));
        excelParams.put("fileName", "供应商导入-自动化测试.xlsx");
        excelParams.put("sheetInfo", Collections.singletonList(sheetInfo));

        JSONObject supplierExcelInfo1 = utilRequest.excelCreate(excelParams);

        // 使用导入接口上传
        importParams.put("fileOssUrl", "test-app-temp:" + supplierExcelInfo1.getString("data"));
        importParams.put("url", "test-app-temp:" + supplierExcelInfo1.getString("data"));
        importParams.put("bizType", 310);

        String fileID1 = utilRequest.importExcel(importParams);

        // 步骤5: 获取供应商导入结果
        getResultParams.put("resourceId", fileID1);
        utilRequest.getResult(getResultParams);

        // 步骤6: 通过更新数据查询供应商列表，断言返回结果包含更新数据
        queryParams.put("supplierId", supplierId);
        queryParams.put("status", 1);
        queryParams.remove("contractExpireFlag");
        saasSupplierStep.querySupplierPage(queryParams);

    }

}