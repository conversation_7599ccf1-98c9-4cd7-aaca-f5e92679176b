package com.xianmu.atp.cases.pms.aiCase.env;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * SaaS供应商测试用例环境变量类
 * 用于存储测试执行所需的环境变量和测试数据
 * 
 * <AUTHOR> Test Engineer
 */
@Data
@Component
public class SaasEnvVariable {

    /**
     * 帆台商家后台账户密码
     */
    private String username = "13429645111" ;

    private String password = "Hello1234" ;

    private final Integer tenantID= 24514;
    private final String saasSkuId= "130487";//saas自营货品
    private final Integer saasSupplierId= 5721;//saas自动化专用供应商
    private final Integer saasSupplierId2 = 5783;//saas自动化专用供应商2
    /**
     * saas自动化专用自营仓
     */
    private final Integer ownWarehouseNo = 500;//saas自动化专用库存仓
    private final Integer ownWarehouseNo2 = 502;//saas自动化专用库存仓2
}