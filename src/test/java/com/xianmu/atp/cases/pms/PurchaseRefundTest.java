package com.xianmu.atp.cases.pms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.step.PurchaseAdvanceStep;
import com.xianmu.atp.cases.pms.step.PurchaseRefundStep;
import com.xianmu.atp.dal.dao.fms.FinanceReceiptService;
import com.xianmu.atp.dal.model.FinanceReceipt;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;

@Slf4j
@Epic("PMS")
@Feature("采购单退款流程")
@Owner("大鹏")
public class PurchaseRefundTest extends BaseTest {
    @Resource
    private Request request;

    //全局变量
    @Resource
    private EnvVariable envVar;

    //操作步骤封装
    @Resource
    private PurchaseRefundStep purchaseRefundStep;

    @Resource
    private PurchaseAdvanceStep purchaseAdvanceStep;

    @Resource
    private FinanceReceiptService financeReceiptService;


    @BeforeMethod(description = "初始化操作--登录")
    public void login(){
        String URL = request.urlBuild(xmAdminDomain, LoginEnumsInterface.xianmuLogin.getUrl());
        HashMap<String, String> loginBody = new HashMap<>();
        loginBody.put("username", envVar.getAdminUserName());
        loginBody.put("password", envVar.getAdminpassword());
        HttpResponseWrapper loginRes= request.sendRequest(URL,
                LoginEnumsInterface.xianmuLogin.getMethod(),
                LoginEnumsInterface.xianmuLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        request.setToken(token);
    }

    @AfterMethod(description = "通用等待配置")
    public void fixedSleep() throws InterruptedException {
        Thread.sleep(500);
    }

    //每个用例执行后打印和移除临时变量
//    @AfterMethod(description = "场景变量",alwaysRun= true)
//    public void tmpVar(){
//        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
//    }

    @AfterMethod(description = "临时变量")
    public void tear_down(){
        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
        TemVar.remove();
    }
    //获取用例生成的临时变量
    public TemVar temVar(){
        return TemVar.getCurrent();
    }

    @Story("采购退款单")
    @Test(description = "预付款退款新增>审批拒绝")
    public void purchaseRedund_case01(){
        //采购退款单新建-预付款退款
        purchaseRefundStep.addPurchaseRefund();
        //查询退款单列表查询--待审核的退款单
        purchaseRefundStep.purchaseRefundList(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("status",1);
            put("supplierId", envVar.getSupplierId());
        }});
        //查询预付单记录列表查询--退款核销单创建的记录，验证变动记录正确
        purchaseAdvanceStep.queryAdvanceReocrdList(new HashMap<String, Object>(){{
            put("billId", temVar().getPurchaseRefundId());
            put("pageIndex",1);
            put("pageSize",10);
            put("supplierId",envVar.getSupplierId());
            put("type",40);
        }});
        //查询退款核销单号数据
        FinanceReceipt financeReceiptInfo= financeReceiptService.selectAllByFinanceOrderId(
                Integer.valueOf(temVar().getPurchaseRefundId()));
        //根据核销单号、待审核、供应商ID查询退款核销单列表数据
        purchaseRefundStep.receivableList(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("payType",2);
            put("adminId",envVar.getSupplierId());
            put("writeOffStatus",0);
        }});
        //查看退款单详情
        purchaseRefundStep.refundDetail(new HashMap<String, Object>(){{
            put("id", temVar().getPurchaseRefundId());
        }});
        //查看退款核销单详情
        purchaseRefundStep.receivableDetail(new HashMap<String, Object>(){{
            put("id",financeReceiptInfo.getId());
        }});
        //退款单审批拒绝
        purchaseRefundStep.receivableReject(new HashMap<String, Object>(){{
            put("id",financeReceiptInfo.getId());
        }});
        //查询预付单记录列表查询--退款核销单审批拒绝的记录，验证变动记录正确
        purchaseAdvanceStep.queryAdvanceReocrdList(new HashMap<String, Object>(){{
            put("billId", temVar().getPurchaseRefundId());
            put("pageIndex",1);
            put("pageSize",10);
            put("supplierId",envVar.getSupplierId());
            put("type",41);
        }});

    }

    @Story("采购退款单")
    @Test(description = "预付款退款新增>审批通过")
    public void purchaseRedund_case02(){
        //采购退款单新建-预付款退款
        purchaseRefundStep.addPurchaseRefund();
        //查询退款单列表查询--待审核的退款单
        purchaseRefundStep.purchaseRefundList(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("status",1);
            put("supplierId", envVar.getSupplierId());
        }});
        //查询预付单记录列表查询--退款核销单创建的记录，验证变动记录正确
        purchaseAdvanceStep.queryAdvanceReocrdList(new HashMap<String, Object>(){{
            put("billId", temVar().getPurchaseRefundId());
            put("pageIndex",1);
            put("pageSize",10);
            put("supplierId",envVar.getSupplierId());
            put("type",40);
        }});
        //查询退款核销单号数据
        FinanceReceipt financeReceiptInfo= financeReceiptService.selectAllByFinanceOrderId(
                Integer.valueOf(temVar().getPurchaseRefundId()));
        //根据核销单号、待审核、供应商ID查询退款核销单列表数据
        purchaseRefundStep.receivableList(new HashMap<String, Object>(){{

            put("pageIndex",1);
            put("pageSize",10);
            put("payType",2);
            put("adminId",envVar.getSupplierId());
            put("writeOffStatus",0);
        }});
        //查看退款单详情
        purchaseRefundStep.refundDetail(new HashMap<String, Object>(){{
            put("id", temVar().getPurchaseRefundId());
        }});
        //查看退款核销单详情
        purchaseRefundStep.receivableDetail(new HashMap<String, Object>(){{
            put("id",financeReceiptInfo.getId());
        }});

        //退款单审批通过
        purchaseRefundStep.receivableConfirm(new HashMap<String, Object>(){{
            put("id",financeReceiptInfo.getId());
        }});
        //根据核销单号、已核销、供应商ID查询退款核销单列表数据
        purchaseRefundStep.receivableList(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("payType",2);
            put("adminId",envVar.getSupplierId());
            put("writeOffStatus",1);
        }});

        //查询退款单列表查询--已退款的退款单
        purchaseRefundStep.purchaseRefundList(new HashMap<String, Object>(){{
            put("pageIndex",1);
            put("pageSize",10);
            put("status",2);
            put("supplierId", envVar.getSupplierId());
        }});
    }

}
