package com.xianmu.atp.cases.pms.aiCase;

import cn.hutool.core.lang.Console;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sun.org.apache.bcel.internal.generic.NEW;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.cases.pms.step.PriceCenterStep;
import com.xianmu.atp.dal.dao.common.BaseInfoService;
import com.xianmu.atp.dal.model.SkuInfo;
import com.xianmu.atp.dal.model.Supplier;
import com.xianmu.atp.dal.model.WarehouseStorageCenter;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Arrays;
import org.assertj.core.util.Lists;
import org.opentest4j.AssertionFailedError;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 价格中心接口测试用例
 * 
 * <AUTHOR> Test Engineer
 * @description 基于API文档和数据库日志上下文生成的价格中心接口测试用例
 * 测试流程：新增报价 -> 查询报价 -> 查看成本明细 -> 查询生效价格 -> 作废报价
 * 复用项目现有工具类：Request、EnvVariable、TemVar、PriceCenterStep
 */
@Slf4j
@Epic("PMS")
@Feature("价格中心")
@Owner("大鹏")
public class PriceCenterApiTest extends BaseTest {

    @Resource
    private Request request;
    
    @Resource
    private EnvVariable envVar;

    @Resource
    private BaseInfoService baseInfoService;

    @Resource
    private PriceCenterStep priceCenterStep;


    private Supplier supplierInfo;
    private SkuInfo skuInfo;

    private WarehouseStorageCenter warehouseInfo;

    // 复用EnvVariable中的测试数据
    private  String testSku; // 使用EnvVariable中定义的SKU
    private  Integer supplierId; // 使用EnvVariable中定义的供应商ID
    private  String warehouseNo; // 使用EnvVariable中定义的仓库编号
    private  BigDecimal testPrice;


    @BeforeClass(description = "初始化操作--登录获取token")
    public void setup() {
        String URL = request.urlBuild(xmAdminDomain, LoginEnumsInterface.xianmuLogin.getUrl());
        HashMap<String, String> loginBody = new HashMap<>();
        loginBody.put("username", envVar.getAdminUserName());
        loginBody.put("password", envVar.getAdminpassword());
        HttpResponseWrapper loginRes= request.sendRequest(URL,
                LoginEnumsInterface.xianmuLogin.getMethod(),
                LoginEnumsInterface.xianmuLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        request.setToken(token);
        testSku = envVar.getSku(); // 使用EnvVariable中定义的SKU
        supplierId = envVar.getSupplierId(); // 使用EnvVariable中定义的供应商ID
        warehouseNo = envVar.getWarehouseNo(); // 使用EnvVariable中定义的仓库编号
        testPrice = new BigDecimal("10.50");
        log.info("价格中心测试用例初始化完成");
        log.info("使用域名: {}", envVar.getXmAdminDomain());
        log.info("使用测试SKU: {}", testSku);
        log.info("使用供应商ID: {}", supplierId);
        log.info("使用仓库编号: {}", warehouseNo);
    }

    @BeforeMethod(description = "初始化操作--获取商品、供应商、仓库等基础数据")
    public void getBaseInfo(){
        skuInfo =baseInfoService.getProductInfo(envVar.getSku());
        supplierInfo = baseInfoService.selectById(envVar.getSupplierId());
        warehouseInfo = baseInfoService.getWarehouseInfo(envVar.getWarehouseNo());

    }
    public TemVar temVar(){
        return TemVar.getCurrent();
    }

    @AfterMethod(description = "清理临时变量")
    public void tearDown() {
        // 使用TemVar的清理机制
        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
        TemVar.remove(); // 清理ThreadLocal变量
    }

    /**
     * 用例ID: TC_API_PRICE_CENTER_WORKFLOW_001
     * 用例名称: 价格中心完整业务流程测试
     * 优先级: P0
     * 测试类型: 功能测试
     * 前置条件: 用户已登录且有权限操作价格中心
     * 测试步骤:
     * 1. 通过/pms-service/price-center/upsert/add接口新增报价
     * 2. 通过/pms-service/price-center/query/page接口查询刚刚新增的报价
     * 3. 通过/pms-service/price-center/query/cost-detail/with-offerId查看该报价单对应的成本明细数据
     * 4. 通过/pms-service/price-center/query/cost-detail/with-sku查看SKU对应的成本明细数据
     * 5. 通过/pms-service/price-center/query/all-type-price接口查询刚刚生效的报价价格
     * 6. 最后通过/pms-service/price-center/upsert/invalid接口作废该报价单
     * 预期结果: 所有接口调用成功，数据一致性验证通过
     */
    @Story("价格中心业务流程")
    @Test(description = "价格中心完整业务流程测试：新增->查询->成本明细->价格查询->作废")
    public void testPriceCenterCompleteWorkflow() {

        Map<String, Object> queryBody = new HashMap<>();
        queryBody.put("startDate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        queryBody.put("endDate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        queryBody.put("orderBy", true);
        queryBody.put("pageSize", 10);
        queryBody.put("pageIndex", 1);
        queryBody.put("supplierId", supplierId);
        queryBody.put("sku", testSku);
        queryBody.put("statusList", Lists.newArrayList(1));//生效中状态

        //前置校验--若已经有价格，先作废
        priceCenterStep.queryPriceOfferPage(queryBody,false);
        if (Objects.nonNull(temVar().getPriceCenterConfigId())) {
            Allure.addAttachment("前置操作-已经有价格，先作废","报价单ID"+temVar().getPriceCenterConfigId());
            priceCenterStep.invalidPriceOffer(temVar().getPriceCenterConfigId());
        }
        // 基于数据库日志上下文和EnvVariable构建请求参数
        Map<String, Object> addData = new HashMap<>();
        addData.put("offerType", 1);
        addData.put("channelType", 1);
        addData.put("name", "吴鹏程1");
        addData.put("stepPriceParams", new Object[]{});
        addData.put("supplierId", supplierId);
        addData.put("supplierName", supplierInfo.getName());
        addData.put("pdName", skuInfo.getPdName());
        addData.put("sku", testSku);
        addData.put("warehouseNo", Integer.parseInt(warehouseNo));
        addData.put("warehouseName", warehouseInfo.getWarehouseName());
        addData.put("price", testPrice);
        addData.put("pdId", skuInfo.getPdId());
        addData.put("weight", skuInfo.getWeight());

        // 成本明细 - 基于日志上下文
        Map<String, Object> costDetailInput = new HashMap<>();
        costDetailInput.put("materialCost", 1);
        costDetailInput.put("depletionExpense", 1);
        addData.put("costDetailInput", costDetailInput);

        // 时间设置：当天的开始时间和结束时间
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = LocalDateTime.of(today, LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.of(today, LocalTime.MAX);

        String startDate = startOfDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endDate = endOfDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        addData.put("startDate", startDate);
        addData.put("endDate", endDate);
        // 步骤1: 新增报价单
        priceCenterStep.addPriceOffer(addData);
        // 步骤2: 查询报价单分页列表
        priceCenterStep.queryPriceOfferPage(queryBody, true);
        String priceCenterConfigId = temVar().getPriceCenterConfigId();

        // 步骤3: 查询成本明细（根据报价单ID）
        priceCenterStep.queryCostDetailWithOfferId(priceCenterConfigId);

        // 基于数据库日志上下文构建请求参数
        Map<String, Object> queryCostBody = new HashMap<>();
        queryCostBody.put("sku", testSku);
        queryCostBody.put("warehouseNo", Integer.parseInt(warehouseNo));
        queryCostBody.put("supplierId", supplierId);

        // 步骤4: 查询成本明细（根据SKU）
        priceCenterStep.queryCostDetailWithSku(queryCostBody);

        // 基于数据库日志上下文构建请求参数
        Map<String, Object> queryAllTypePriceBody = new HashMap<>();
        queryAllTypePriceBody.put("warehouseNo", Integer.parseInt(warehouseNo));

        // 价格查询列表
        Map<String, Object> priceQueryBean = new HashMap<>();
        priceQueryBean.put("sku", testSku);
        priceQueryBean.put("supplierId", supplierId);

        queryAllTypePriceBody.put("priceQueryBeanList", new Object[]{priceQueryBean});
        // 步骤5: 查询所有类型的生效价格
        priceCenterStep.queryAllTypePrice(queryAllTypePriceBody);

        // 步骤6: 作废报价单
        priceCenterStep.invalidPriceOffer(priceCenterConfigId);

        log.info("价格中心完整业务流程测试执行完成");
    }


 }