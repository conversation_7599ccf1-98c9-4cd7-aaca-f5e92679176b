package com.xianmu.atp.cases.pms.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SupplierStep {
    @Resource
    private Request request;

    @Resource
    private EnvVariable envVar;

    /**
     * 供应商--详情
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void detail(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.srmDomain, PmsEnumsInterface.SupplierEnum.detail.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                PmsEnumsInterface.SupplierEnum.detail.getMethod(),
                PmsEnumsInterface.SupplierEnum.detail.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
    }
}
