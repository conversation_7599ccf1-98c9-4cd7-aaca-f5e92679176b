package com.xianmu.atp.cases.pms.env;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

//临时变量
@Data
public class TemVar {
    // 使用ThreadLocal存储线程独立的TemVar实例
    private static final ThreadLocal<TemVar> threadLocal = ThreadLocal.withInitial(TemVar::new);

    // 私有化构造器
    private TemVar() {}

    // 获取当前线程的实例
    public static TemVar getCurrent() {
        return threadLocal.get();
    }

    // 清除当前线程的实例
    public static void remove() {
        threadLocal.remove();
    }

    //预付单ID

    private String purchaseAdvanceId;

    //付款单ID
    private String paymentId;

    //采购退款单ID
    private String purchaseRefundId;

    //供应商退款单审核单ID
    private String financeReceiptId;

    //POP价格佣金配置
    private String priceCenterId;

    private BigDecimal supplierPrice;//POP品单件售价
    private BigDecimal supplierWeightPrice;//POP品单斤价格

    private String  srmStockChangeId;//srmPC端代销库存调整单ID
    private Map<String, BigDecimal> srmStockInfo;//srmPC端--代销品库存信息
    private Integer srmSupplierOfferDetailAuditRecordID;//srmPC端--供货价审批记录ID
    private Integer srmStockChangeRecordID;//srmPC端--库存变更记录ID
    private String excelResId;//导入&导出结果id

    private Integer supplyListId;//供货目录配置ID

    //价格中心报价单id
    private String priceCenterConfigId;

    // 预付对账流程相关变量
    //采购单号
    private String purchaseNo;
    
    //入库单明细数据
    private JSONArray warehousingOrderData;
    
    //对账单ID
    private String reconciliationId;


}
