package com.xianmu.atp.cases.pms;

import cn.hutool.core.lang.Console;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.cases.pms.env.TemVar;

import com.xianmu.atp.cases.pms.step.SrmPcStep;
import com.xianmu.atp.dal.dao.SkuInfoDao;
import com.xianmu.atp.dal.dao.WarehouseStorageCenterDao;
import com.xianmu.atp.dal.dao.common.DingdingProcessFlowService;
import com.xianmu.atp.dal.dao.pms.SrmStockChangeRecordService;
import com.xianmu.atp.dal.dao.pms.SrmSupplierOfferDetailAuditRecordService;
import com.xianmu.atp.dal.dao.pms.SrmSupplierOfferDetailStepPriceService;
import com.xianmu.atp.dal.model.*;
import com.xianmu.atp.enums.FlowConfigEnums;
import com.xianmu.atp.enums.api.common.LoginEnumsInterface;
import com.xianmu.atp.generic.common.*;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Epic("PMS")
@Feature("SRMpc")
@Owner("大鹏")
public class SrmPcTest extends BaseTest {


    //全局变量
    @Resource
    private EnvVariable envVar;

    //操作步骤封装
    @Resource
    private SrmPcStep srmPcStep;

    //相关请求工具
    @Resource
    private Request request;
    @Resource
    private FlowRequest flowRequest;
    @Resource
    private UtilRequest utilRequest;
    @Resource
    private ExecuteJob executeJob;

    //数据库
    @Resource
    private SrmSupplierOfferDetailAuditRecordService srmSupplierOfferDetailAuditRecordService;

    @Resource
    private SrmStockChangeRecordService srmStockChangeRecordService ;
    @Resource
    private SrmSupplierOfferDetailStepPriceService srmSupplierOfferDetailStepPriceService;

    @Resource
    private DingdingProcessFlowService dingdingProcessFlowService;

    @Resource
    private SkuInfoDao skuInfoDao;
    @Resource
    private WarehouseStorageCenterDao warehouseStorageCenterDao;

    @BeforeClass(description = "初始化操作--srm登录")
    public void login(){
        String URL = request.urlBuild(srmDomain, LoginEnumsInterface.srmPcLogin.getUrl());
        HashMap<String, Object> loginBody = new HashMap<>();
        loginBody.put("phone", envVar.getSrmPhone());
        loginBody.put("password", envVar.getSrmPassWord());
        loginBody.put("origin",0);
        loginBody.put("tenantId",1);
        loginBody.put("type","PHONE_PWD");
        HttpResponseWrapper loginRes= request.sendRequest(URL,
                LoginEnumsInterface.srmPcLogin.getMethod(),
                LoginEnumsInterface.srmPcLogin.getContentType(),
                loginBody);
        Assert.assertEquals(loginRes.getStatusCode(),200);
        JSONObject result = JSON.parseObject(loginRes.getBody());
        Assert.assertEquals(result.getString("code"),"SUCCESS");
        String token = result.getJSONObject("data").getString("token");
        request.setToken(token);
    }


    //每个用例执行后打印和移除临时变量
    @AfterMethod(description = "临时变量")
    public void tear_down(){
        Allure.addAttachment("用例执行--临时变量", TemVar.getCurrent().toString());
        TemVar.remove();
    }
    //获取用例生成的临时变量
    public TemVar temVar(){
        return TemVar.getCurrent();
    }

    @Story("SRM-PC")
    @Test(description = "更新供货编码、生产日期、库存调整、定时任务")
    public void srmTest01(){

        String customSku = "customSku"+System.currentTimeMillis();

        srmPcStep.relationUpsert(new HashMap<String, Object>(){{
            put("customSku",customSku);
            put("sku", envVar.getSellWithoutWarehouseSku());
        }});  //更新供货编码
        srmPcStep.productDateUpsert(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("productDate",LocalDate.now().toString());
            put("warehouseNo",envVar.getOpenWarehouseNo());
        }});//更新批次最早生产日期

        srmPcStep.stockPackupQuery(new HashMap<String, Object>(){{
            put("customSku",customSku);
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("warehouseNoList",Lists.newArrayList(envVar.getOpenWarehouseNo()));
            put("pageIndex",1);
            put("pageSize",10);
            put("todayPackupStatus",0);
        }});//库存列表查询

        srmPcStep.stockPackupUpsert(new HashMap<String, Object>(){{
            put("adjustTotalSaleStock",temVar().getSrmStockInfo().get("totalSaleStock").add(BigDecimal.ONE));
            put("remainSaleStock",temVar().getSrmStockInfo().get("remainSaleStock"));
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("soldOutStock",temVar().getSrmStockInfo().get("soldOutStock"));
            put("spu",envVar.getSellWithoutWarehouseSPU());
            put("totalSaleStock",temVar().getSrmStockInfo().get("totalSaleStock"));
            put("warehouseNo",envVar.getOpenWarehouseNo());
        }});//库存调整

        srmPcStep.stockChangeQuery(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("status",2);
            put("type",2);
            put("pageIndex",1);
            put("pageSize",10);
        }},1);//查询库存变更记录--类型=库存调整、已完成的数据

        srmPcStep.stockChangeQuery(new HashMap<String, Object>(){{
            put("endTime",LocalDate.now().toString());
            put("startTime",LocalDate.now().toString());
            put("type",3);
            put("pageIndex",1);
            put("pageSize",10);
        }},2);//查询库存变更记录--类型=未提报库存的数据
        //预删除未提报库存清理的数据
        if (temVar().getSrmStockChangeRecordID()!=null){
            srmStockChangeRecordService.deleteById(temVar().getSrmStockChangeRecordID());
        }

        String res = executeJob.RunExecuteJob(env,
                envVar.getPmsGroup(),
                Long.valueOf(envVar.getJobId(env, "dontPackupJobId")));//执行定时任务--未提报库存清理
        Console.log("定时任务执行结果:"+res);
        srmPcStep.stockChangeQuery(new HashMap<String, Object>(){{
            put("endTime",LocalDate.now().toString());
            put("startTime",LocalDate.now().toString());
            put("type",3);
            put("pageIndex",1);
            put("pageSize",10);
        }},1);//查询库存变更记录--类型=未提报库存的数据
    }

    @Story("SRM-PC")
    @Test(description = "供货价流程--新增、撤销、通过")
    public void srmTest02(){

        //查询申请记录--0审核中1审核通过2审核拒绝3撤销 4自动通过
        SrmSupplierOfferDetailAuditRecord recordInfo= srmSupplierOfferDetailAuditRecordService.selectBySupplierIdAndWarehouseNoAndSkuAndStatus(
                envVar.getSupplierId(),envVar.getOpenWarehouseNo(),envVar.getSellWithoutWarehouseSku(),0);
        if (Objects.nonNull(recordInfo)){
            srmPcStep.priceTerminate(new HashMap<String, Object>(){{
                put("id",recordInfo.getId().intValue());
            }});
        }
        //查询当前生效中供货价
        JSONArray priceList = srmPcStep.priceQuery(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("warehouseNo",envVar.getOpenWarehouseNo());
            put("priceStatusList",Lists.newArrayList(1));
            put("pageIndex",1);
            put("pageSize",10);
        }});

        //计算所需的供货价
        SrmSupplierOfferDetailStepPrice stepDetailPriceInfo = srmSupplierOfferDetailStepPriceService.selectStepDetailPrice(
                envVar.getSupplierId(),envVar.getOpenWarehouseNo(),envVar.getSellWithoutWarehouseSku(),2);
        BigDecimal price= srmPcStep.calculatePrice(priceList,stepDetailPriceInfo);

        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        srmPcStep.priceAdd(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("startDate",LocalDate.now().plusDays(1).toString());
            put("endDate",LocalDate.now().plusDays(1).toString());
            put("warehouseNo",envVar.getOpenWarehouseNo());
            put("price",price);
            put("rateText","100%");
        }});//新增供货价
        //查询申请记录--0审核中1审核通过2审核拒绝3撤销 4自动通过
        SrmSupplierOfferDetailAuditRecord recordInfo1= srmSupplierOfferDetailAuditRecordService.selectBySupplierIdAndWarehouseNoAndSkuAndCreateTime(
                envVar.getSupplierId(),envVar.getOpenWarehouseNo(),envVar.getSellWithoutWarehouseSku(),now);
        temVar().setSrmSupplierOfferDetailAuditRecordID(recordInfo1.getId().intValue());
        srmPcStep.priceAduitQuery(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("warehouseNoList",Lists.newArrayList(envVar.getOpenWarehouseNo()));
            put("pageIndex",1);
            put("pageSize",10);
            put("auditStatusList", Lists.newArrayList(0));
        }});
        srmPcStep.priceTerminate(new HashMap<String, Object>(){{
            put("id",recordInfo1.getId().intValue());
        }});

        SrmSupplierOfferDetailAuditRecord recordInfo2= srmSupplierOfferDetailAuditRecordService.
                selectByRecordId(recordInfo1.getId().intValue());
        Assert.assertEquals(recordInfo2.getResult(), Integer.valueOf(3) ,"供货价申请撤销后状态不符合预期");


        String now1 = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        srmPcStep.priceAdd(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("startDate",LocalDate.now().plusDays(1).toString());
            put("endDate",LocalDate.now().plusDays(1).toString());
            put("warehouseNo",envVar.getOpenWarehouseNo());
            put("price",price);
            put("rateText","100%");
        }});//新增供货价
        //查询申请记录--0审核中1审核通过2审核拒绝3撤销 4自动通过
        SrmSupplierOfferDetailAuditRecord recordInfo3= srmSupplierOfferDetailAuditRecordService.selectBySupplierIdAndWarehouseNoAndSkuAndCreateTime(
                envVar.getSupplierId(),envVar.getOpenWarehouseNo(),envVar.getSellWithoutWarehouseSku(),now1);
        temVar().setSrmSupplierOfferDetailAuditRecordID(recordInfo3.getId().intValue());
        srmPcStep.priceAduitQuery(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku());
            put("warehouseNoList",Lists.newArrayList(envVar.getOpenWarehouseNo()));
            put("pageIndex",1);
            put("pageSize",10);
            put("auditStatusList", Lists.newArrayList(0));
        }});

        flowRequest.approveFlow(recordInfo3.getId().toString(),
                FlowConfigEnums.SUPPLIER_PRICE_AUDIT.getProcessType(), true);

        dingdingProcessFlowService.selectOneByBizIdAndBizTypeAndProcessStatus(recordInfo3.getId(),
                FlowConfigEnums.SUPPLIER_PRICE_AUDIT.getProcessType(), 2);

    }

    @Story("SRM-PC")
    @Test(description = "库存管理excel操作--库存提报、库存调整、定时任务")
    public void srmTest03(){
        srmPcStep.stockPackupQuery(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku2());
            put("warehouseNoList",Lists.newArrayList(envVar.getOpenWarehouseNo()));
            put("pageIndex",1);
            put("pageSize",10);
        }});//库存列表查询

        SkuInfo skuInfo = skuInfoDao.getProduct(envVar.getSellWithoutWarehouseSku2());
        String warehouseName= warehouseStorageCenterDao.getWarehouseName(String.valueOf(envVar.getOpenWarehouseNo()));
        List<String> stockAddTitleList = new ArrayList<>(Arrays.asList(
                "鲜沐仓库(必填)", "商品名称(非必填)", "商品编号(必填)", "商品规格(非必填)","规格编号(必填)","供货商供货编码(非必填)",
                "批次最早生产日期(非必填；格式：YYYY-MM-DD)","今日库存提报值(必填)"));
        List<Object> stockAddvalueList = new ArrayList<>();

        String stockAddNum = temVar().getSrmStockInfo().get("totalSaleStock").add(BigDecimal.valueOf(1)).toString();//待提报库存值
        stockAddvalueList.add(new HashMap<String, Object>(){{
            put("1",warehouseName);
            put("3",skuInfo.getPdNo());
            put("5",envVar.getSellWithoutWarehouseSku2());
            put("7",LocalDate.now().toString());
            put("8",stockAddNum);
        }});
        JSONObject stockAddExcelInfo =utilRequest.excelCreate(new HashMap<String, Object>(){{
            put("fileName","srm代销不如仓库存提报-自动化测试.xlsx");
            put("sheetInfo",Lists.newArrayList(new HashMap<String, Object>(){{
                put("titleList",stockAddTitleList);
                put("valueList",stockAddvalueList);
            }}));
        }});
        srmPcStep.stockPackupImport(new HashMap<String, Object>(){{
            put("url","test-app-temp:"+stockAddExcelInfo.getString("data"));
        }});//excel--库存提报
        utilRequest.getResult(new HashMap<String, Object>(){{
            put("resourceId",temVar().getExcelResId());
        }});//excel--导入结果

        srmPcStep.stockPackupQuery(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku2());
            put("warehouseNoList",Lists.newArrayList(envVar.getOpenWarehouseNo()));
            put("pageIndex",1);
            put("pageSize",10);
            put("todayPackupStatus",1);
        }});//库存列表查询
        Assert.assertEquals(temVar().getSrmStockInfo().get("totalSaleStock").toString(), stockAddNum,"库存提报后实际库存不符合预期");


        List<String> stockUpdateTitleList = new ArrayList<>(Arrays.asList(
                "鲜沐仓库(必填)", "商品名称(非必填)", "商品编号(必填)", "商品规格(非必填)","规格编号(必填)","供货商供货编码(非必填)",
                "调整后售卖总库存(必填)"));
        List<Object> stockUpdateValueList = new ArrayList<>();

        String stockUpdateNum = temVar().getSrmStockInfo().get("totalSaleStock").add(BigDecimal.valueOf(1)).toString();//待提报库存值
        stockUpdateValueList.add(new HashMap<String, Object>(){{
            put("1",warehouseName);
            put("3",skuInfo.getPdNo());
            put("5",envVar.getSellWithoutWarehouseSku2());
            put("7",stockUpdateNum);
        }});
        JSONObject excelInfo =utilRequest.excelCreate(new HashMap<String, Object>(){{
            put("fileName","srm代销不如仓库存调整-自动化测试.xlsx");
            put("sheetInfo",Lists.newArrayList(new HashMap<String, Object>(){{
                put("titleList",stockUpdateTitleList);
                put("valueList",stockUpdateValueList);
            }}));
        }});
        srmPcStep.stockChangeImport(new HashMap<String, Object>(){{
            put("url","test-app-temp:"+excelInfo.getString("data"));
        }});//excel--库存调整
        utilRequest.getResult(new HashMap<String, Object>(){{
            put("resourceId",temVar().getExcelResId());
        }});//excel--导入结果

        srmPcStep.stockPackupQuery(new HashMap<String, Object>(){{
            put("sku",envVar.getSellWithoutWarehouseSku2());
            put("warehouseNoList",Lists.newArrayList(envVar.getOpenWarehouseNo()));
            put("pageIndex",1);
            put("pageSize",10);
            put("todayPackupStatus",1);
        }});//库存列表查询

        Assert.assertEquals(temVar().getSrmStockInfo().get("totalSaleStock").toString(), stockUpdateNum,"库存调整后实际库存不符合预期");


        srmPcStep.stockChangeQuery(new HashMap<String, Object>(){{
            put("endTime",LocalDate.now().toString());
            put("startTime",LocalDate.now().toString());
            put("type",4);
            put("pageIndex",1);
            put("pageSize",10);
        }},2);//查询库存变更记录--类型=次日提货库存扣减的数据
        //预删除次日提报库存清理的数据
        if (temVar().getSrmStockChangeRecordID()!=null){
            srmStockChangeRecordService.deleteById(temVar().getSrmStockChangeRecordID());
        }
        executeJob.RunExecuteJob(env,
                envVar.getPmsGroup(),
                Long.valueOf(envVar.getJobId(env, "PackupDataHandleTask")));//执行定时任务--次日提货库存扣减清理
        srmPcStep.stockChangeQuery(new HashMap<String, Object>(){{
            put("endTime",LocalDate.now().toString());
            put("startTime",LocalDate.now().toString());
            put("type",4);
            put("pageIndex",1);
            put("pageSize",10);
        }},1);//查询库存变更记录--类型=次日提货库存扣减、已完成的数据

    }
    

    @Story("SRM-PC")
    @Test(description = "导出、供货库存数据下载、定时任务(未提报提醒)")
    public void srmTest04(){
        srmPcStep.stockPackupExport(new HashMap<String, Object>(){{
            put("sku", envVar.getSellWithoutWarehouseSku());
            put("params",new HashMap<String, Object>(){{
                put("规格编号",envVar.getSellWithoutWarehouseSku());
            }});
        }});//excel--导出；
        utilRequest.getResult(new HashMap<String, Object>(){{
            put("resourceId",temVar().getExcelResId());
        }});//excel--导出结果

        //导出供应商供货商品列表
        srmPcStep.supplySkuListExport();
        utilRequest.getResult(new HashMap<String, Object>(){{
            put("resourceId",temVar().getExcelResId());
        }});//excel--导出结果

        //执行定时任务--发送未提报提醒
        executeJob.RunExecuteJob(env, envVar.getPmsGroup(), Long.valueOf(envVar.getJobId(env, "UndonePackupSupplierMsgNoticeTask")));
    }


}

