package com.xianmu.atp.cases.pms.aiCase.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.enums.api.pms.SaasSupplierEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SaaS供应商操作步骤封装类
 * 封装所有供应商相关的API请求操作步骤
 * 
 * <AUTHOR> Test Engineer
 */
@Slf4j
@Component
public class SaasSupplierStep {

    @Resource
    private Request request;

    @Resource
    private EnvVariable envVar;

    // ==================== 供应商管理相关操作 ====================

    /**
     * 新增或编辑供应商
     * @param params 供应商信息参数
     * @return 供应商ID
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String saveOrUpdateSupplier(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SaasSupplierEnum.saveOrUpdate.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SaasSupplierEnum.saveOrUpdate.getMethod(),
                SaasSupplierEnumsInterface.SaasSupplierEnum.saveOrUpdate.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200);
        
        JSONObject result = JSON.parseObject(res.getBody());
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data,"供应商新增失败");
        
        // 返回供应商ID
        String supplierId = data.getString("supplierId");
        Assert.assertNotNull(supplierId, "供应商ID为空,供应商新增失败");
        return supplierId;
    }

    /**
     * 查询供应商详情
     * @param params 供应商详情参数
     * @return 供应商详情信息
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public JSONObject getSupplierDetail(Map<String, Object> params) {

        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SaasSupplierEnum.detail.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SaasSupplierEnum.detail.getMethod(),
                SaasSupplierEnumsInterface.SaasSupplierEnum.detail.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200, "查询供应商详情失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getInteger("status"), Integer.valueOf(200), "查询供应商详情失败");
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "供应商详情数据为空");
        return data;
    }

    /**
     * 查询供应商列表
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String querySupplierPage(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SaasSupplierEnum.queryPage.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SaasSupplierEnum.queryPage.getMethod(),
                SaasSupplierEnumsInterface.SaasSupplierEnum.queryPage.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200);
        
        JSONObject result = JSON.parseObject(res.getBody());
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "接口返回数据结构异常");
        JSONArray dataList = data.getJSONArray("list");
        Assert.assertNotNull(dataList, "供应商列表不应为空");
        Assert.assertFalse(dataList.isEmpty(), "结果不符合预期,不应为空");

        return dataList.getJSONObject(0).getString("supplierId");
    }

    /**
     * 导出供应商数据
     * @param params 导出参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String exportSupplier(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SaasSupplierEnum.export.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SaasSupplierEnum.export.getMethod(),
                SaasSupplierEnumsInterface.SaasSupplierEnum.export.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200, "导出供应商失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "导出供应商失败");
        String fileID = result.getString("data");
        Assert.assertNotNull(fileID, "导出供应商失败");
        return fileID;

    }

    // ==================== 供应商关系管理相关操作 ====================

    /**
     * 添加或修改供应商关系（合作货品）
     * @param params 供应商关系参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void upsertSupplyRelation(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SupplyRelationEnum.upsert.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SupplyRelationEnum.upsert.getMethod(),
                SaasSupplierEnumsInterface.SupplyRelationEnum.upsert.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200,"添加合作货品失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200,"添加合作货品失败");
    }

    /**
     * 批量删除供应商关系（合作货品）
     * @param params 自定义参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void batchDeleteSupplyRelation(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SupplyRelationEnum.batchDel.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SupplyRelationEnum.batchDel.getMethod(),
                SaasSupplierEnumsInterface.SupplyRelationEnum.batchDel.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200,"删除合作货品接口状态码验证失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200,"删除合作货品接口业务状态码验证失败");
    }

    /**
     * 查询供应商关系列表（合作货品列表）
     * @param params 查询参数
     * @param skuID 货品ID
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String querySupplyRelationPage(Map<String, Object> params, String skuID) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SupplyRelationEnum.queryPage.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SupplyRelationEnum.queryPage.getMethod(),
                SaasSupplierEnumsInterface.SupplyRelationEnum.queryPage.getContentType(),
                params);
        
        log.info("查询合作货品列表响应: {}", res.getBody());
        Assert.assertEquals(res.getStatusCode(), 200,"查询合作货品列表接口状态码验证失败");
        
        JSONObject result = JSON.parseObject(res.getBody());

        JSONObject data = result.getJSONObject("data");
        JSONArray relationList = data.getJSONArray("list");
        Assert.assertNotNull(relationList, "合作货品列表为空");
        Assert.assertFalse(relationList.isEmpty(), "结果不符合预期,不应为空");
        for (int i = 0; i < relationList.size(); i++) {
            JSONObject relation = relationList.getJSONObject(i);
            if (skuID.equals(relation.getString("saasSkuId"))) {
                return relation.getString("id");
            }
        }
        return null;
    }

    // ==================== 采购价格管理相关操作 ====================

    /**
     * 获取商品税率
     * @param params 参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void getTaxRate(Map<String, Object> params) {

        
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.PurchasesPriceEnum.getTaxRate.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.PurchasesPriceEnum.getTaxRate.getMethod(),
                SaasSupplierEnumsInterface.PurchasesPriceEnum.getTaxRate.getContentType(),
                params);
        Assert.assertEquals(res.getStatusCode(), 200, "获取商品税率失败");

        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "获取商品税率失败");

    }

    /**
     * 获取价格有效期
     * @param params 查询参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void getPriceTimeList(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.PurchasesPriceEnum.getTimeList.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.PurchasesPriceEnum.getTimeList.getMethod(),
                SaasSupplierEnumsInterface.PurchasesPriceEnum.getTimeList.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200, "获取价格有效期失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "获取价格有效期失败");

    }

    /**
     * 新增采购价格
     * @param params 采购价格参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void savePurchasePrice(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.PurchasesPriceEnum.save.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.PurchasesPriceEnum.save.getMethod(),
                SaasSupplierEnumsInterface.PurchasesPriceEnum.save.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200 , "新增采购价格失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "新增采购价格失败");

    }

    /**
     * 编辑采购价格
     * @param params 采购价格参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public void updatePurchasePrice(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.PurchasesPriceEnum.update.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.PurchasesPriceEnum.update.getMethod(),
                SaasSupplierEnumsInterface.PurchasesPriceEnum.update.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200, "编辑采购价格失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "编辑采购价格失败");
    }

    /**
     * 查询采购价格列表
     * @param params 查询参数
     * @return 采购价格列表
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String queryPurchasePriceList(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.PurchasesPriceEnum.list.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.PurchasesPriceEnum.list.getMethod(),
                SaasSupplierEnumsInterface.PurchasesPriceEnum.list.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200,"查询采购价格失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "查询采购价格失败");
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "查询采购价格失败");
        JSONArray records = data.getJSONArray("list");
        Assert.assertNotNull(records, "查询采购价格列表为空");
        Assert.assertFalse(records.isEmpty(), "结果不符合预期,不应为空");

        return records.getJSONObject(0).getString("detailId");
    }

    /**
     * 导出采购价格
     * @param params 导出参数
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public String exportPurchasePrice(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.PurchasesPriceEnum.export.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.PurchasesPriceEnum.export.getMethod(),
                SaasSupplierEnumsInterface.PurchasesPriceEnum.export.getContentType(),
                params);
        
        Assert.assertEquals(res.getStatusCode(), 200, "导出采购价格失败");
        
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200, "导出采购价格失败");
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "导出采购价格失败");
        String resId = data.getString("resId");
        Assert.assertNotNull(resId, "导出采购价格失败");
        return resId;

    }

    // ==================== 供应商导入相关操作 ====================

    /**
     * 查询工作台待办事项--
     * @return 临期供应商数量
     */
    @Retry(attempts = 2, delay = 2000, onExceptions = {AssertionError.class})
    public BigDecimal pendingMatter() {
        String URL = request.urlBuild(envVar.saasManageDomain, SaasSupplierEnumsInterface.SaasSupplierEnum.pendingMatter.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                SaasSupplierEnumsInterface.SaasSupplierEnum.pendingMatter.getMethod(),
                SaasSupplierEnumsInterface.SaasSupplierEnum.pendingMatter.getContentType(),
                null);

        Assert.assertEquals(res.getStatusCode(), 200,"查询工作台待办事项请求状态码验证失败");

        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getIntValue("status"), 200,"查询工作台待办事项接口业务状态码验证失败");
        JSONObject data = result.getJSONObject("data");
        Assert.assertNotNull(data, "查询工作台待办事项接口返回数据为空");
        return data.getBigDecimal("supplierDeadlineNum");
    }

    // ==================== 工具方法 ====================
    /**
     * 构建供应商基础参数
     * @return 供应商参数Map
     */
    public Map<String, Object> buildSupplierParams(String url, String currentDate,String currentTimeFlag) {
        Map<String, Object> params = new HashMap<>();

        // 基础供应商信息
        params.put("supplierName", "自动化生成供应商"+currentTimeFlag);
        params.put("customerSupplierId",currentTimeFlag);
        params.put("taxNum", "3301"+currentTimeFlag);
        params.put("categoryArray", null);
        params.put("status", 0);
        params.put("supplierType", 1);
        // 根据样本数据添加缺失的参数结构

        // 账户信息列表
        Map<String, Object> account = new HashMap<>();
        account.put("payType", 1);
        account.put("accountBank", "测试银行");
        account.put("accountAscription", "测试归属");
        account.put("account", "***********23456");
        params.put("accountList", new Map[]{account});

        // 文件列表
        Object[] dateList = new Object[]{currentDate, null};
        Map<String, Object> file = new HashMap<>();
        file.put("fileType", 5);
        file.put("status", false);
        file.put("startDate", currentDate);
        file.put("endDate", currentDate);
        file.put("date", dateList);
        file.put("fileUrl", url);
        params.put("fileList", new Map[]{file});

        // 联系人列表
        Map<String, Object> contact = new HashMap<>();
        contact.put("phone", "***********");
        contact.put("name", "测试");
        contact.put("position", "默认职位");
        params.put("connectList", new Map[]{contact});
        // 其他字段
        params.put("skuList", new Object[]{});
        Map<String, Object> supplierContract = new HashMap<>();
        supplierContract.put("startDate", currentDate);
        supplierContract.put("endDate", currentDate);
        params.put("supplierContract", supplierContract);

        return params;
    }


}