package com.xianmu.atp.cases.goods.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.cases.pms.env.EnvVariable;
import com.xianmu.atp.enums.api.goods.GoodsEnumsInterface;
import com.xianmu.atp.enums.api.pms.PmsEnumsInterface;
import com.xianmu.atp.generic.common.HttpResponseWrapper;
import com.xianmu.atp.generic.common.Request;
import com.xianmu.atp.util.retry.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.testng.Assert;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class GoodsStep{
    @Resource
    private Request request;
    @Resource
    private EnvVariable envVar;


    /**
     * 货品--获取商品详情
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public JSONObject queryProductInfo(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, GoodsEnumsInterface.GoodsEnum.queryProductInfo.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                GoodsEnumsInterface.GoodsEnum.queryProductInfo.getMethod(),
                GoodsEnumsInterface.GoodsEnum.queryProductInfo.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());
        Assert.assertEquals(result.getString("msg"),"请求成功");
        return result.getJSONObject("data");
    }

    /**
     * 货品--商品上新保存发布
     */
    @Retry(attempts =2, delay = 2000, onExceptions = {AssertionError.class})
    public void upsetInfo(Map<String, Object> params) {
        String URL = request.urlBuild(envVar.xmAdminDomain, GoodsEnumsInterface.GoodsEnum.upsetInfo.getUrl());
        HttpResponseWrapper res = request.sendRequest(URL,
                GoodsEnumsInterface.GoodsEnum.upsetInfo.getMethod(),
                GoodsEnumsInterface.GoodsEnum.upsetInfo.getContentType(),
                params);
        JSONObject result = JSON.parseObject(res.getBody());

        Assert.assertEquals(result.getString("msg"),"请求成功");
    }
}
