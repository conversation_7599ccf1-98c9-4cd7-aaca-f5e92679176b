package com.xianmu.atp.cases.demo;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class JdbcTempDemo extends BaseTest {

    //这个必须要加，用来获取数据库配置信息
    @Autowired
    private DynamicDataSourceProperties properties;


    @Test()
    public void zylDemo01() throws Exception {
        //初始化为saas数据库
        JdbcTemplate saasJdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.cosfodb);
        //返回list类型
        List<Map<String, Object>> orders =saasJdbcTemplate.queryForList("select * from ofcdb.fulfillment_order limit 3");
        for (Map<String, Object> order : orders){
            String orderNo = order.get("fulfillment_no").toString();
            System.out.println("fulfillment_no:"+orderNo);
        }
        //获取第一行返回值
        System.out.println(orders.get(0).get("fulfillment_no").toString());


        //初始化成鲜沐数据库
        //自行完成sql拼接
        //插入数据
        JdbcTemplate xianmuJdbcTemplate =  SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String sql="INSERT INTO `xianmudb`.`merchant_coupon`(`id`, `m_id`, `coupon_id`, `vaild_date`, `sender`, `used`, `add_time`, `view`, `order_no`, `receive_type`, `start_time`, `update_time`, `send_id`)\n" +
                "VALUES (NULL, 350887, 19880, '2080-04-23 23:59:59', NULL, 0, '2023-10-26 14:40:06', NULL, NULL, 5, '2023-10-26 14:40:06', NULL, NULL)\n" +
                ";";
        xianmuJdbcTemplate.execute(sql);

        //更新数据
        String sql2="update merchant_coupon set used=1 where id=1647790";
        xianmuJdbcTemplate.update(sql2); //用execute也行

    }
}
