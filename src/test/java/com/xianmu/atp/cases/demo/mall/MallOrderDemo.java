package com.xianmu.atp.cases.demo.mall;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.dal.dao.OfcOrderDao;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.SkuEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import javax.annotation.Resource;

@Slf4j
public class MallOrderDemo extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private OfcOrderDao ofcOrderDao;
    @Test()
    public void zylCase01() throws InterruptedException {
        //支持增加多个sku，需要先维护好对应的sku、地址、用户
        MallOrder order = new MallOrder();
        order.addEnv(EnvEnum.qa);
        order.addAddress(AddressEnum.mallAutoAddress);
        order.addSku(SkuEnum.skuForSpuStartNum,6);
        //order.addSku(SkuEnum.skuForAllCategory,1);  这里是因为这个sku失效了
        order.send(false);
        JdbcTemplate saasJdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.cosfodb);
        Assert.isTrue(SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,"select * from ofcdb.fulfillment_order where out_order_no='"+order.getOrderNo()+"'", "time", order.getOrderNo(), 5),"text");
        //System.out.println(ofcOrderDao.getOrderNo("0124XXJU461220154878"));
    }

    @Test
    public void zylCase02(){
        //只能用一个商品下单
        //String orderNo =MallOrderUtil.send("qa", MallUserEnum.mallAutoUser.getPhone(), AddressEnum.mallAutoAddress.getContactId(), SkuEnum.skuForSpuStartNum.getSku(), 6);
        String orderNo = MallOrderUtil.send("qa","13732254809",351940,"308354112240",5);
        System.out.println(orderNo);
    }
}
