package com.xianmu.atp.cases.demo.dubbo;


import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.rpc.service.GenericService;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import java.util.Map;


/**
 * 类<code>Doc</code>用于：
 * dubbo泛化调用demo
 * <AUTHOR>
 * @ClassName DemoGeneric
 * @date 2025-01-20
 */
@Slf4j
public class DemoGeneric extends BaseTest {

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Test
    public void test03() {

        Map<String, Object> paramMap = JsonUtil.toMap("{\"contactId\":350801,\"merchantId\":350927,\"orderTime\":\"2025-03-04 18:27:06\",\"source\":\"XM_MALL\"}");
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("dev")
                .interfaceName("net.summerfarm.mall.client.provider.OrderProvider")
                .methodName("addOrder")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AddOrderReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @Test
    public void test02() {
        Map<String, Object> parameters = JsonUtil.toMap("");
        //数据初始化
       ATPRequestDto atpRequestDto = ATPRequestDto.builder()
               .env("qa")
               .interfaceName("net.xianmu.authentication.client.provider.AuthUserProvider")
               .methodName("shiroUser")
               .paramTypes(new String[]{"java.lang.String"})
               .params(new Object[]{parameters})
               .build();
       //调用获取结果
       ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
       log.info("调用结果：{}", atpDto);
       //assert TODO: 2025/断言
    }


}
