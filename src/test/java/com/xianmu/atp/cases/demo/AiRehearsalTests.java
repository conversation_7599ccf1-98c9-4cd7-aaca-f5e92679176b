package com.xianmu.atp.cases.demo;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

/**
 * AI演练接口测试用例 - 拜访任务场景化测试
 * 基于API文档和数据库日志上下文生成的全面测试用例
 * 
 * <AUTHOR> Test Engineer
 * @date 2025-01-08
 */
@Epic("crm")
@Feature("拜访任务场景化测试")
@Owner("AI Test Engineer")
@Slf4j
public class AiRehearsalTests extends BaseTest {

    private final String BASE_URL = "https://" + EnvEnum.qa.getName() + "h5.summerfarm.net";
    private final String VISIT_PLAN_INSERT_URL = BASE_URL + "/crm-service/visitPlan/insert";
    private final String FOLLOW_UP_RECORD_URL = BASE_URL + "/crm-service/follow-up-record";
    private final String TOKEN = getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), EnvEnum.qa.getName());
    
    private Long createdVisitPlanId; // 存储创建的拜访计划ID，用于后续测试
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @BeforeClass
    public void setUp() {
        log.info("初始化拜访任务场景化测试环境");
    }

    // ==================== 正向场景测试 ====================

    @Test(priority = 1, retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_VisitPlan_Scenario_001: 完整拜访任务场景 - 创建拜访计划并执行拜访"
    )
    @Story("完整拜访任务场景")
    public void TC_API_VisitPlan_Scenario_001_CompleteVisitFlow() {
        /**
         * **用例ID**: TC_API_VisitPlan_Scenario_001
         * **用例名称**: 完整拜访任务场景 - 创建拜访计划并执行拜访
         * **优先级**: P0
         * **前置条件**: 用户已登录，存在有效的客户和联系人信息
         * **测试步骤**:
         * 1. 创建拜访计划 - 调用/crm-service/visitPlan/insert接口
         * 2. 执行拜访任务 - 调用/crm-service/follow-up-record接口
         * **预期结果**:
         * - 拜访计划创建成功，返回状态码200
         * - 拜访记录创建成功，返回状态码200
         * - 整个拜访流程完整执行
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_VisitPlan_Scenario_001_CompleteVisitFlow");
        
        // 步骤1: 创建拜访计划
        JSONObject visitPlanRequest = new JSONObject();
        visitPlanRequest.put("expectedTime", dateFormat.format(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000))); // 明天
        visitPlanRequest.put("status", 0); // 待拜访
        visitPlanRequest.put("mId", 12345L); // 客户ID
        visitPlanRequest.put("adminId", 100); // 拜访销售ID
        visitPlanRequest.put("expectedContent", "客户维护拜访，了解近期经营情况");
        visitPlanRequest.put("contactId", 1001); // 联系人地址ID
        visitPlanRequest.put("creator", "testUser");
        visitPlanRequest.put("type", 0); // 拜访类型：拜访
        visitPlanRequest.put("source", 1); // 来源
        visitPlanRequest.put("areaNo", 40); // 区域编号
        visitPlanRequest.put("updater", "testUser");

        HttpResponse visitPlanResponse = HttpRequest.post(VISIT_PLAN_INSERT_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(visitPlanRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(visitPlanResponse.isOk(), "创建拜访计划失败，响应状态码非200");
        JSONObject visitPlanResponseJson = JSONObject.parseObject(visitPlanResponse.body());
        Assert.assertEquals(visitPlanResponseJson.getInteger("status"), Integer.valueOf(0), "创建拜访计划业务状态码不为0");
        log.info("拜访计划创建成功: {}", visitPlanResponseJson.toJSONString());

        // 步骤2: 执行拜访记录
        JSONObject followUpRequest = new JSONObject();
        followUpRequest.put("mId", 12345L); // 商户ID
        followUpRequest.put("adminId", 100); // 跟进人
        followUpRequest.put("adminName", "测试销售");
        followUpRequest.put("creator", "testUser");
        followUpRequest.put("mLifecycle", 1); // 用户生命周期
        followUpRequest.put("mTag", "重点客户");
        followUpRequest.put("followUpWay", "电话拜访");
        followUpRequest.put("status", 1); // 已跟进
        followUpRequest.put("priority", 1); // 优先级
        followUpRequest.put("condition", "客户反馈良好，有进货意向");
        followUpRequest.put("contactId", 1001);
        followUpRequest.put("nextFollowTime", dateFormat.format(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000))); // 下周
        followUpRequest.put("expectedContent", "跟进进货需求");
        followUpRequest.put("visitObjective", 2); // 客户维护
        followUpRequest.put("visitType", 0); // 普通拜访
        followUpRequest.put("operateStatus", 0); // 正常经营
        followUpRequest.put("location", "北京市朝阳区");
        followUpRequest.put("feedback", "客户满意度较高");
        followUpRequest.put("province", "北京市");
        followUpRequest.put("city", "北京市");
        followUpRequest.put("area", "朝阳区");
        followUpRequest.put("areaNo", 40);
        followUpRequest.put("mname", "测试商户");
        followUpRequest.put("mcontact", "张经理");
        followUpRequest.put("phone", "13800138000");
        followUpRequest.put("address", "朝阳区测试街道123号");
        followUpRequest.put("visitToHome", 1); // 上门拜访

        HttpResponse followUpResponse = HttpRequest.post(FOLLOW_UP_RECORD_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(followUpRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(followUpResponse.isOk(), "创建拜访记录失败，响应状态码非200");
        JSONObject followUpResponseJson = JSONObject.parseObject(followUpResponse.body());
        Assert.assertNotNull(followUpResponseJson.getString("code"), "拜访记录响应code为空");
        log.info("拜访记录创建成功: {}", followUpResponseJson.toJSONString());
        
        log.info("用例 TC_API_VisitPlan_Scenario_001_CompleteVisitFlow 执行成功");
    }

    @Test(priority = 2, retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_VisitPlan_Scenario_002: 陪访场景 - 创建陪访计划并执行"
    )
    @Story("陪访场景")
    public void TC_API_VisitPlan_Scenario_002_EscortVisitFlow() {
        /**
         * **用例ID**: TC_API_VisitPlan_Scenario_002
         * **用例名称**: 陪访场景 - 创建陪访计划并执行
         * **优先级**: P1
         * **前置条件**: 用户已登录，存在有效的客户和陪访人员信息
         * **测试步骤**:
         * 1. 创建陪访计划 - 包含陪访人员信息
         * 2. 执行陪访记录 - 记录陪访结果
         * **预期结果**:
         * - 陪访计划创建成功
         * - 陪访记录创建成功，包含陪访人员信息
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_VisitPlan_Scenario_002_EscortVisitFlow");
        
        // 步骤1: 创建陪访计划
        JSONObject visitPlanRequest = new JSONObject();
        visitPlanRequest.put("expectedTime", dateFormat.format(new Date(System.currentTimeMillis() + 2 * 24 * 60 * 60 * 1000))); // 后天
        visitPlanRequest.put("status", 0); // 待拜访
        visitPlanRequest.put("mId", 12346L); // 客户ID
        visitPlanRequest.put("adminId", 101); // 拜访销售ID
        visitPlanRequest.put("expectedContent", "新客户陪访，介绍产品和服务");
        visitPlanRequest.put("contactId", 1002); // 联系人地址ID
        visitPlanRequest.put("creator", "testUser");
        visitPlanRequest.put("type", 2); // 拜访类型：陪访
        visitPlanRequest.put("source", 1); // 来源
        visitPlanRequest.put("areaNo", 41); // 区域编号
        visitPlanRequest.put("escortAdminIdList", Arrays.asList(102, 103)); // 陪访人员ID列表
        visitPlanRequest.put("updater", "testUser");

        HttpResponse visitPlanResponse = HttpRequest.post(VISIT_PLAN_INSERT_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(visitPlanRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(visitPlanResponse.isOk(), "创建陪访计划失败，响应状态码非200");
        JSONObject visitPlanResponseJson = JSONObject.parseObject(visitPlanResponse.body());
        Assert.assertEquals(visitPlanResponseJson.getInteger("status"), Integer.valueOf(0), "创建陪访计划业务状态码不为0");
        log.info("陪访计划创建成功: {}", visitPlanResponseJson.toJSONString());

        // 步骤2: 执行陪访记录
        JSONObject followUpRequest = new JSONObject();
        followUpRequest.put("mId", 12346L); // 商户ID
        followUpRequest.put("adminId", 101); // 跟进人
        followUpRequest.put("adminName", "主拜访销售");
        followUpRequest.put("creator", "testUser");
        followUpRequest.put("mLifecycle", 0); // 新客户
        followUpRequest.put("mTag", "潜在客户");
        followUpRequest.put("followUpWay", "上门拜访");
        followUpRequest.put("status", 1); // 已跟进
        followUpRequest.put("priority", 2); // 高优先级
        followUpRequest.put("condition", "陪访成功，客户有合作意向");
        followUpRequest.put("contactId", 1002);
        followUpRequest.put("nextFollowTime", dateFormat.format(new Date(System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000))); // 3天后
        followUpRequest.put("expectedContent", "跟进合作细节");
        followUpRequest.put("visitObjective", 0); // 拉新
        followUpRequest.put("visitType", 1); // 陪访
        followUpRequest.put("escortAdminId", 102); // 陪访人ID
        followUpRequest.put("escortAdminIdList", Arrays.asList(102, 103)); // 陪访人员列表
        followUpRequest.put("operateStatus", 0); // 正常经营
        followUpRequest.put("location", "上海市浦东新区");
        followUpRequest.put("feedback", "陪访效果良好，客户认可产品");
        followUpRequest.put("province", "上海市");
        followUpRequest.put("city", "上海市");
        followUpRequest.put("area", "浦东新区");
        followUpRequest.put("areaNo", 41);
        followUpRequest.put("mname", "新客户商户");
        followUpRequest.put("mcontact", "李总");
        followUpRequest.put("phone", "13900139000");
        followUpRequest.put("address", "浦东新区测试路456号");
        followUpRequest.put("visitToHome", 1); // 上门拜访
        followUpRequest.put("escortAdminName", "陪访经理");

        HttpResponse followUpResponse = HttpRequest.post(FOLLOW_UP_RECORD_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(followUpRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(followUpResponse.isOk(), "创建陪访记录失败，响应状态码非200");
        JSONObject followUpResponseJson = JSONObject.parseObject(followUpResponse.body());
        Assert.assertNotNull(followUpResponseJson.getString("code"), "陪访记录响应code为空");
        log.info("陪访记录创建成功: {}", followUpResponseJson.toJSONString());
        
        log.info("用例 TC_API_VisitPlan_Scenario_002_EscortVisitFlow 执行成功");
    }

    // ==================== 异常场景测试 ====================

    @Test(priority = 3, retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_VisitPlan_Scenario_003: 异常场景 - 创建拜访计划失败后的处理"
    )
    @Story("异常场景处理")
    public void TC_API_VisitPlan_Scenario_003_CreateVisitPlanFailure() {
        /**
         * **用例ID**: TC_API_VisitPlan_Scenario_003
         * **用例名称**: 异常场景 - 创建拜访计划失败后的处理
         * **优先级**: P1
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 使用无效参数创建拜访计划
         * 2. 验证错误处理机制
         * 3. 确保不会创建后续的拜访记录
         * **预期结果**:
         * - 拜访计划创建失败，返回相应错误信息
         * - 系统能正确处理异常情况
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_VisitPlan_Scenario_003_CreateVisitPlanFailure");
        
        // 步骤1: 使用缺少必填参数的请求创建拜访计划
        JSONObject visitPlanRequest = new JSONObject();
        visitPlanRequest.put("expectedTime", dateFormat.format(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000)));
        visitPlanRequest.put("status", 0);
        visitPlanRequest.put("mId", -1L); // 无效的客户ID
        visitPlanRequest.put("adminId", 100);
        visitPlanRequest.put("expectedContent", "测试异常场景");
        visitPlanRequest.put("contactId", -1); // 无效的联系人ID
        visitPlanRequest.put("creator", "testUser");
        // 缺少必填的type参数

        HttpResponse visitPlanResponse = HttpRequest.post(VISIT_PLAN_INSERT_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(visitPlanRequest.toJSONString())
                .timeout(3000)
                .execute();

        // 验证错误处理
        if (visitPlanResponse.isOk()) {
            JSONObject visitPlanResponseJson = JSONObject.parseObject(visitPlanResponse.body());
            Assert.assertNotEquals(visitPlanResponseJson.getInteger("status"), Integer.valueOf(0), "应该返回业务错误状态码");
            Assert.assertNotNull(visitPlanResponseJson.getString("msg"), "应该返回错误信息");
            log.info("拜访计划创建失败，错误信息: {}", visitPlanResponseJson.getString("msg"));
        } else {
            log.info("拜访计划创建失败，HTTP状态码: {}", visitPlanResponse.getStatus());
            Assert.assertTrue(visitPlanResponse.getStatus() >= 400, "应该返回4xx或5xx错误状态码");
        }
        
        log.info("用例 TC_API_VisitPlan_Scenario_003_CreateVisitPlanFailure 执行成功");
    }

    @Test(priority = 4, retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_VisitPlan_Scenario_004: 异常场景 - 无效token访问"
    )
    @Story("权限异常场景")
    public void TC_API_VisitPlan_Scenario_004_InvalidTokenAccess() {
        /**
         * **用例ID**: TC_API_VisitPlan_Scenario_004
         * **用例名称**: 异常场景 - 无效token访问拜访接口
         * **优先级**: P0
         * **前置条件**: 无
         * **测试步骤**:
         * 1. 使用无效token创建拜访计划
         * 2. 使用无效token创建拜访记录
         * **预期结果**:
         * - 两个接口都应该返回401或403错误
         * - 返回相应的权限错误信息
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_VisitPlan_Scenario_004_InvalidTokenAccess");
        
        String invalidToken = "invalid_token_12345";
        
        // 步骤1: 使用无效token创建拜访计划
        JSONObject visitPlanRequest = new JSONObject();
        visitPlanRequest.put("expectedTime", dateFormat.format(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000)));
        visitPlanRequest.put("status", 0);
        visitPlanRequest.put("mId", 12345L);
        visitPlanRequest.put("adminId", 100);
        visitPlanRequest.put("expectedContent", "权限测试");
        visitPlanRequest.put("contactId", 1001);
        visitPlanRequest.put("creator", "testUser");
        visitPlanRequest.put("type", 0);

        HttpResponse visitPlanResponse = HttpRequest.post(VISIT_PLAN_INSERT_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", invalidToken)
                .body(visitPlanRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertFalse(visitPlanResponse.isOk(), "使用无效token应该访问失败");
        Assert.assertTrue(visitPlanResponse.getStatus() == 401 || visitPlanResponse.getStatus() == 403, 
                "应该返回401或403权限错误");
        log.info("拜访计划接口权限验证通过，状态码: {}", visitPlanResponse.getStatus());

        // 步骤2: 使用无效token创建拜访记录
        JSONObject followUpRequest = new JSONObject();
        followUpRequest.put("mId", 12345L);
        followUpRequest.put("adminId", 100);
        followUpRequest.put("adminName", "测试销售");
        followUpRequest.put("creator", "testUser");
        followUpRequest.put("status", 1);
        followUpRequest.put("condition", "权限测试");

        HttpResponse followUpResponse = HttpRequest.post(FOLLOW_UP_RECORD_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", invalidToken)
                .body(followUpRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertFalse(followUpResponse.isOk(), "使用无效token应该访问失败");
        Assert.assertTrue(followUpResponse.getStatus() == 401 || followUpResponse.getStatus() == 403, 
                "应该返回401或403权限错误");
        log.info("拜访记录接口权限验证通过，状态码: {}", followUpResponse.getStatus());
        
        log.info("用例 TC_API_VisitPlan_Scenario_004_InvalidTokenAccess 执行成功");
    }

    // ==================== 逆向场景测试 ====================

    @Test(priority = 5, retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_VisitPlan_Scenario_005: 逆向场景 - 先创建拜访记录再创建计划"
    )
    @Story("逆向场景")
    public void TC_API_VisitPlan_Scenario_005_ReverseOrderFlow() {
        /**
         * **用例ID**: TC_API_VisitPlan_Scenario_005
         * **用例名称**: 逆向场景 - 先创建拜访记录再创建计划
         * **优先级**: P2
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 先创建拜访记录（无关联的拜访计划）
         * 2. 再创建拜访计划
         * 3. 验证系统对逆向操作的处理
         * **预期结果**:
         * - 拜访记录可以独立创建成功
         * - 拜访计划也可以独立创建成功
         * - 系统能正确处理逆向操作
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_VisitPlan_Scenario_005_ReverseOrderFlow");
        
        // 步骤1: 先创建拜访记录
        JSONObject followUpRequest = new JSONObject();
        followUpRequest.put("mId", 12347L); // 商户ID
        followUpRequest.put("adminId", 104); // 跟进人
        followUpRequest.put("adminName", "逆向测试销售");
        followUpRequest.put("creator", "testUser");
        followUpRequest.put("mLifecycle", 2); // 用户生命周期
        followUpRequest.put("mTag", "测试客户");
        followUpRequest.put("followUpWay", "电话联系");
        followUpRequest.put("status", 1); // 已跟进
        followUpRequest.put("priority", 1); // 优先级
        followUpRequest.put("condition", "逆向场景测试，先记录后计划");
        followUpRequest.put("contactId", 1003);
        followUpRequest.put("nextFollowTime", dateFormat.format(new Date(System.currentTimeMillis() + 5 * 24 * 60 * 60 * 1000))); // 5天后
        followUpRequest.put("expectedContent", "后续跟进");
        followUpRequest.put("visitObjective", 2); // 客户维护
        followUpRequest.put("visitType", 0); // 普通拜访
        followUpRequest.put("operateStatus", 0); // 正常经营
        followUpRequest.put("location", "广州市天河区");
        followUpRequest.put("feedback", "逆向测试反馈");
        followUpRequest.put("province", "广东省");
        followUpRequest.put("city", "广州市");
        followUpRequest.put("area", "天河区");
        followUpRequest.put("areaNo", 42);
        followUpRequest.put("mname", "逆向测试商户");
        followUpRequest.put("mcontact", "王经理");
        followUpRequest.put("phone", "13700137000");
        followUpRequest.put("address", "天河区测试大道789号");
        followUpRequest.put("visitToHome", 0); // 非上门拜访

        HttpResponse followUpResponse = HttpRequest.post(FOLLOW_UP_RECORD_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(followUpRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(followUpResponse.isOk(), "逆向场景-创建拜访记录失败，响应状态码非200");
        JSONObject followUpResponseJson = JSONObject.parseObject(followUpResponse.body());
        Assert.assertNotNull(followUpResponseJson.getString("code"), "拜访记录响应code为空");
        log.info("逆向场景-拜访记录创建成功: {}", followUpResponseJson.toJSONString());

        // 步骤2: 再创建拜访计划
        JSONObject visitPlanRequest = new JSONObject();
        visitPlanRequest.put("expectedTime", dateFormat.format(new Date(System.currentTimeMillis() + 6 * 24 * 60 * 60 * 1000))); // 6天后
        visitPlanRequest.put("status", 0); // 待拜访
        visitPlanRequest.put("mId", 12347L); // 同一个客户ID
        visitPlanRequest.put("adminId", 104); // 同一个销售ID
        visitPlanRequest.put("expectedContent", "逆向场景后续拜访计划");
        visitPlanRequest.put("contactId", 1003); // 同一个联系人ID
        visitPlanRequest.put("creator", "testUser");
        visitPlanRequest.put("type", 0); // 拜访类型：拜访
        visitPlanRequest.put("source", 1); // 来源
        visitPlanRequest.put("areaNo", 42); // 区域编号
        visitPlanRequest.put("updater", "testUser");

        HttpResponse visitPlanResponse = HttpRequest.post(VISIT_PLAN_INSERT_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(visitPlanRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(visitPlanResponse.isOk(), "逆向场景-创建拜访计划失败，响应状态码非200");
        JSONObject visitPlanResponseJson = JSONObject.parseObject(visitPlanResponse.body());
        Assert.assertEquals(visitPlanResponseJson.getInteger("status"), Integer.valueOf(0), "逆向场景-创建拜访计划业务状态码不为0");
        log.info("逆向场景-拜访计划创建成功: {}", visitPlanResponseJson.toJSONString());
        
        log.info("用例 TC_API_VisitPlan_Scenario_005_ReverseOrderFlow 执行成功");
    }

    @Test(priority = 6, retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            },
            description = "TC_API_VisitPlan_Scenario_006: 边界场景 - 大量数据和特殊字符处理"
    )
    @Story("边界场景")
    public void TC_API_VisitPlan_Scenario_006_BoundaryDataFlow() {
        /**
         * **用例ID**: TC_API_VisitPlan_Scenario_006
         * **用例名称**: 边界场景 - 大量数据和特殊字符处理
         * **优先级**: P2
         * **前置条件**: 用户已登录
         * **测试步骤**:
         * 1. 创建包含特殊字符和长文本的拜访计划
         * 2. 创建包含特殊字符和长文本的拜访记录
         * **预期结果**:
         * - 系统能正确处理特殊字符
         * - 系统能正确处理长文本内容
         * - 接口响应正常
         * **实际结果**: [执行时填写]
         * **测试结论**: [Pass/Fail]
         */
        log.info("执行用例: TC_API_VisitPlan_Scenario_006_BoundaryDataFlow");
        
        StringBuilder longTextBuilder = new StringBuilder();
        String baseText = "这是一个非常长的文本内容，用于测试系统对长文本的处理能力。";
        for (int i = 0; i < 10; i++) {
            longTextBuilder.append(baseText);
        }
        String longText = longTextBuilder.toString();
        String specialChars = "特殊字符测试：!@#$%^&*()_+-=[]{}|;':\",./<>?`~中文测试αβγδε日本語한국어";
        
        // 步骤1: 创建包含特殊数据的拜访计划
        JSONObject visitPlanRequest = new JSONObject();
        visitPlanRequest.put("expectedTime", dateFormat.format(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000)));
        visitPlanRequest.put("status", 0);
        visitPlanRequest.put("mId", 12348L);
        visitPlanRequest.put("adminId", 105);
        visitPlanRequest.put("expectedContent", longText + specialChars);
        visitPlanRequest.put("contactId", 1004);
        visitPlanRequest.put("creator", "边界测试用户" + specialChars);
        visitPlanRequest.put("type", 0);
        visitPlanRequest.put("source", 1);
        visitPlanRequest.put("areaNo", 43);
        visitPlanRequest.put("updater", "边界测试用户");

        HttpResponse visitPlanResponse = HttpRequest.post(VISIT_PLAN_INSERT_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(visitPlanRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(visitPlanResponse.isOk(), "边界场景-创建拜访计划失败，响应状态码非200");
        JSONObject visitPlanResponseJson = JSONObject.parseObject(visitPlanResponse.body());
        Assert.assertEquals(visitPlanResponseJson.getInteger("status"), Integer.valueOf(0), "边界场景-创建拜访计划业务状态码不为0");
        log.info("边界场景-拜访计划创建成功");

        // 步骤2: 创建包含特殊数据的拜访记录
        JSONObject followUpRequest = new JSONObject();
        followUpRequest.put("mId", 12348L);
        followUpRequest.put("adminId", 105);
        followUpRequest.put("adminName", "边界测试销售" + specialChars);
        followUpRequest.put("creator", "边界测试用户");
        followUpRequest.put("mLifecycle", 1);
        followUpRequest.put("mTag", "边界测试标签" + specialChars);
        followUpRequest.put("followUpWay", "特殊方式" + specialChars);
        followUpRequest.put("status", 1);
        followUpRequest.put("priority", 1);
        followUpRequest.put("condition", longText + specialChars);
        followUpRequest.put("contactId", 1004);
        followUpRequest.put("nextFollowTime", dateFormat.format(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000)));
        followUpRequest.put("expectedContent", "边界测试期望内容" + specialChars);
        followUpRequest.put("visitObjective", 2);
        followUpRequest.put("visitType", 0);
        followUpRequest.put("operateStatus", 0);
        followUpRequest.put("location", "边界测试位置" + specialChars);
        followUpRequest.put("feedback", longText + "边界测试反馈" + specialChars);
        followUpRequest.put("province", "测试省份");
        followUpRequest.put("city", "测试城市");
        followUpRequest.put("area", "测试区域");
        followUpRequest.put("areaNo", 43);
        followUpRequest.put("mname", "边界测试商户" + specialChars);
        followUpRequest.put("mcontact", "边界测试联系人" + specialChars);
        followUpRequest.put("phone", "13600136000");
        followUpRequest.put("address", "边界测试地址" + specialChars + longText);
        followUpRequest.put("visitToHome", 1);

        HttpResponse followUpResponse = HttpRequest.post(FOLLOW_UP_RECORD_URL)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", TOKEN)
                .body(followUpRequest.toJSONString())
                .timeout(3000)
                .execute();

        Assert.assertTrue(followUpResponse.isOk(), "边界场景-创建拜访记录失败，响应状态码非200");
        JSONObject followUpResponseJson = JSONObject.parseObject(followUpResponse.body());
        Assert.assertNotNull(followUpResponseJson.getString("code"), "边界场景-拜访记录响应code为空");
        log.info("边界场景-拜访记录创建成功");
        
        log.info("用例 TC_API_VisitPlan_Scenario_006_BoundaryDataFlow 执行成功");
    }
}
