package com.xianmu.atp.cases.demo.redis;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPRedisRequestDto;
import com.xianmu.atp.util.redis.DefaultRedisConfig;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
public class RedisTests extends BaseTest {

    @Resource
    private DefaultRedisConfig defaultRedisConfig;

    @Test
    public void testSet() throws Exception {
        ATPRedisRequestDto dto = ATPRedisRequestDto.builder()
                .dbIndex("5")
                .operation("set")
                .queryContent("测试123")
                .queryKey("test123")
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res = defaultRedisConfig.getRedisResult(dto);
        log.info(res);
    }

    @Test
    public void testGet() throws Exception {
        // get操作时queryContent是查询key
        // set操作时queryContent是插入的value
        ATPRedisRequestDto dto = ATPRedisRequestDto.builder()
                .operation("get")
                .queryContent("test123")
                .timeout(1)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res = defaultRedisConfig.getRedisResult(dto);
        log.info(res);
    }
}
