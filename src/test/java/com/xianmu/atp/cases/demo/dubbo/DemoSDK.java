package com.xianmu.atp.cases.demo.dubbo;

import cn.hutool.core.lang.Console;
import com.xianmu.atp.BaseTest;
import lombok.extern.slf4j.Slf4j;



/**
 * 类<code>Doc</code>用于：
 * dubbo实例调用
 * <AUTHOR>
 * @ClassName DemoSDK
 * @date 2025-01-20
 */
@Slf4j
public class DemoSDK extends BaseTest {

//    @DubboReference
//    private AuthUserProvider authUserProvider;
//
//    @Test
//    public void test01(){
//
//        DubboResponse<Boolean> response = authUserProvider.checkPhonePassword(SystemOriginEnum.COSFO_MANAGE, "18969052048", "mock");
//        if (!response.isSuccess()) {
//            if (("BIZ-" + LoginLockEnum.TEMPORARY_LOCK.name).equals(response.getCode())) {
//                Console.log("test");
//            }
//            log.info("校验用户信息失败，用户或密码错误");
//        }
//    }


}
