package com.xianmu.atp.cases.demo;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.zaxxer.hikari.HikariDataSource;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

/**
 * 常购商品管理接口测试用例
 * 
 * 本测试类基于真实API文档和数据库日志生成，覆盖常购商品管理的完整业务流程
 * 包括添加、删除、置顶、取消置顶、查询等11个接口的正向、异常和边界测试场景
 * 
 * 接口列表：
 * 1. /frequent-sku-pool/update/add - 添加常购商品
 * 2. /frequent-sku-pool/update/delete - 删除常购商品  
 * 3. /frequent-sku-pool/update/top - 置顶常购商品
 * 4. /frequent-sku-pool/update/cancelTop - 取消置顶常购商品
 * 5. /frequent-sku-pool/query/page - 常购清单列表
 * 6. /frequent-sku-pool/query/recommend - 常购清单弹窗推荐
 * 7. /frequent-sku-pool/query/category - 常购清单列表类目信息
 * 8. /frequent-sku-pool/query/config - 常购清单消息提醒查询
 * 9. /frequent-sku-pool/update/config - 常购清单消息提醒修改
 * 10. /frequent-sku-pool/query/business_format_list - 业态榜单top20查询
 * 11. /frequent-sku-pool/query/need_guide - 是否需要引导查询
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Epic("常购商品")
@Feature("常购商品管理")
@Owner("ATP自动生成")
@Slf4j
public class AiRehearsal extends BaseTest {
    
    @Autowired
    private DynamicDataSourceProperties properties;
    
    // 接口URL常量 - 基于API文档的11个接口
    private static final String BASE_URL_TEMPLATE = "https://%sh5.summerfarm.net";
    private static final String ADD_FREQUENT_SKU_URL = "/frequent-sku-pool/update/add";
    private static final String DELETE_FREQUENT_SKU_URL = "/frequent-sku-pool/update/delete";
    private static final String TOP_FREQUENT_SKU_URL = "/frequent-sku-pool/update/top";
    private static final String CANCEL_TOP_FREQUENT_SKU_URL = "/frequent-sku-pool/update/cancelTop";
    private static final String QUERY_PAGE_URL = "/frequent-sku-pool/query/page";
    private static final String QUERY_RECOMMEND_URL = "/frequent-sku-pool/query/recommend";
    private static final String QUERY_CATEGORY_URL = "/frequent-sku-pool/query/category";
    private static final String QUERY_CONFIG_URL = "/frequent-sku-pool/query/config";
    private static final String UPDATE_CONFIG_URL = "/frequent-sku-pool/update/config";
    private static final String QUERY_BUSINESS_FORMAT_LIST_URL = "/frequent-sku-pool/query/business_format_list";
    private static final String QUERY_NEED_GUIDE_URL = "/frequent-sku-pool/query/need_guide";
    
    // 测试数据常量 - 基于数据库日志的真实数据
    private static final String TEST_SKU_1 = "************"; // 来自日志的真实SKU
    private static final String TEST_SKU_2 = "************";
    private static final String TEST_SKU_3 = "************";
    private static final String NON_EXISTENT_SKU = "************";
    private static final String INVALID_SKU = "";
    private static final String LONG_SKU = "123456789012345678901234567890123456789012345678901234567890";
    
    // 数据来源常量 - 基于API文档定义
    private static final int SOURCE_RECOMMEND_POPUP = 1;
    private static final int SOURCE_PRODUCT_LIST = 2;
    private static final int SOURCE_PRODUCT_DETAIL = 3; // 日志中的真实值
    private static final int SOURCE_CART = 4;
    private static final int SOURCE_PAY_SUCCESS = 5;
    private static final int INVALID_SOURCE = 99;
    
    // 分页参数 - 基于日志数据
    private static final int DEFAULT_PAGE_INDEX = 1;
    private static final int DEFAULT_PAGE_SIZE = 50;
    private static final int MAX_PAGE_SIZE = 100;
    private static final int INVALID_PAGE_INDEX = 0;
    private static final int INVALID_PAGE_SIZE = -1;
    
    // 配置类型常量
    private static final int CONFIG_TYPE_MESSAGE_REMIND = 1;
    private static final int CONFIG_STATUS_ENABLE = 1;
    private static final int CONFIG_STATUS_DISABLE = 0;
    
    // 业态ID
    private static final String TEST_BUSINESS_FORMAT_ID = "1";
    
    private String baseUrl;
    private String token;
    
    @BeforeMethod
    public void setup() {
        String env = EnvEnum.qa.getName();
        baseUrl = String.format(BASE_URL_TEMPLATE, env);
        token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), env);
        log.info("测试环境初始化完成 - 环境: {}, BaseURL: {}", env, baseUrl);
    }

    // ==================== 添加常购商品接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_ADD_001
     * 正向测试：添加单个SKU到常购商品列表
     * 优先级：P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("添加常购商品")
    @Severity(SeverityLevel.CRITICAL)
    @Description("验证通过商品详情页添加单个SKU到常购商品列表的功能")
    public void testAddSingleSkuFromProductDetail() throws InterruptedException {
        // 准备测试数据 - 基于日志的真实请求格式
        String url = baseUrl + ADD_FREQUENT_SKU_URL;
        String requestBody = String.format("{\"skuList\":[\"%s\"],\"source\":%d}", 
                TEST_SKU_1, SOURCE_PRODUCT_DETAIL);
        
        log.info("开始测试添加单个SKU - SKU: {}, 来源: 商品详情页", TEST_SKU_1);
        
        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        // 验证响应
        log.info("添加单个SKU响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(200), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getLong("serverTime"), "服务器时间不应为空");
        
        // 数据库验证
        HikariDataSource ds = SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        
        String checkSql = String.format("SELECT COUNT(1) as count FROM frequent_sku_pool WHERE sku = '%s' AND mid = %d", 
                TEST_SKU_1, MallUserEnum.mallAutoUser.getMid());
        
        boolean skuExists = SqlExcutorUtil.checkSqlValue(jdbcTemplate, checkSql, "count", "1", 3);
        ds.close();
        
        Assert.assertTrue(skuExists, "数据库中应存在添加的SKU记录");
        log.info("测试完成 - SKU {} 已成功添加到常购商品列表", TEST_SKU_1);
    }
    
    /**
     * TC_API_FREQUENT_SKU_ADD_002
     * 正向测试：添加多个SKU到常购商品列表
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("添加常购商品")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证通过购物车批量添加多个SKU到常购商品列表的功能")
    public void testAddMultipleSkusFromCart() throws InterruptedException {
        // 准备测试数据
        String url = baseUrl + ADD_FREQUENT_SKU_URL;
        String requestBody = String.format("{\"skuList\":[\"%s\",\"%s\",\"%s\"],\"source\":%d}", 
                TEST_SKU_1, TEST_SKU_2, TEST_SKU_3, SOURCE_CART);
        
        log.info("开始测试添加多个SKU - SKUs: [{}, {}, {}], 来源: 购物车", 
                TEST_SKU_1, TEST_SKU_2, TEST_SKU_3);
        
        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        // 验证响应
        log.info("添加多个SKU响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        
        // 数据库验证
        HikariDataSource ds = SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        
        String checkSql = String.format("SELECT COUNT(1) as count FROM frequent_sku_pool WHERE sku IN ('%s', '%s', '%s') AND mid = %d", 
                TEST_SKU_1, TEST_SKU_2, TEST_SKU_3, MallUserEnum.mallAutoUser.getMid());
        
        boolean skusExist = SqlExcutorUtil.checkSqlValue(jdbcTemplate, checkSql, "count", "3", 3);
        ds.close();
        
        Assert.assertTrue(skusExist, "数据库中应存在添加的3个SKU记录");
        log.info("测试完成 - 3个SKU已成功添加到常购商品列表");
    }
    
    /**
     * TC_API_FREQUENT_SKU_ADD_003
     * 异常测试：添加空SKU列表
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("添加常购商品")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证添加空SKU列表时的错误处理")
    public void testAddEmptySkuList() {
        // 准备测试数据
        String url = baseUrl + ADD_FREQUENT_SKU_URL;
        String requestBody = String.format("{\"skuList\":[],\"source\":%d}", SOURCE_PRODUCT_DETAIL);
        
        log.info("开始测试添加空SKU列表");
        
        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        // 验证响应
        log.info("添加空SKU列表响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertNotEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为非0(失败)");
        Assert.assertNotNull(responseJson.getString("msg"), "错误消息不应为空");
        
        log.info("测试完成 - 空SKU列表正确返回错误: {}", responseJson.getString("msg"));
    }

    // ==================== 删除常购商品接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_DELETE_001
     * 正向测试：删除单个常购商品
     * 优先级：P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("删除常购商品")
    @Severity(SeverityLevel.CRITICAL)
    @Description("验证删除单个常购商品的功能")
    public void testDeleteSingleFrequentSku() throws InterruptedException {
        // 先添加一个SKU，确保有数据可删除
        String addUrl = baseUrl + ADD_FREQUENT_SKU_URL;
        String addRequestBody = String.format("{\"skuList\":[\"%s\"],\"source\":%d}", 
                TEST_SKU_1, SOURCE_PRODUCT_DETAIL);
        
        HttpRequest.post(addUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(addRequestBody)
                .execute();
        
        Thread.sleep(1000); // 等待数据写入
        
        // 准备删除测试数据 - 基于日志的真实请求格式
        String url = baseUrl + DELETE_FREQUENT_SKU_URL;
        String requestBody = String.format("{\"sku\":\"%s\"}", TEST_SKU_1);
        
        log.info("开始测试删除单个常购商品 - SKU: {}", TEST_SKU_1);
        
        // 发送删除请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        // 验证响应
        log.info("删除单个SKU响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getLong("serverTime"), "服务器时间不应为空");
        
        // 数据库验证 - 确认SKU已被删除
        HikariDataSource ds = SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        
        String checkSql = String.format("SELECT COUNT(1) as count FROM frequent_sku_pool WHERE sku = '%s' AND mid = %d", 
                TEST_SKU_1, MallUserEnum.mallAutoUser.getMid());
        
        boolean skuDeleted = SqlExcutorUtil.checkSqlValue(jdbcTemplate, checkSql, "count", "0", 3);
        ds.close();
        
        Assert.assertTrue(skuDeleted, "数据库中应不存在已删除的SKU记录");
        log.info("测试完成 - SKU {} 已成功从常购商品列表中删除", TEST_SKU_1);
    }
    
    /**
     * TC_API_FREQUENT_SKU_DELETE_002
     * 异常测试：删除不存在的SKU
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("删除常购商品")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证删除不存在的SKU时的处理")
    public void testDeleteNonExistentSku() {
        // 准备测试数据 - 使用一个不存在的SKU
        String url = baseUrl + DELETE_FREQUENT_SKU_URL;
        String requestBody = String.format("{\"sku\":\"%s\"}", NON_EXISTENT_SKU);
        
        log.info("开始测试删除不存在的SKU - SKU: {}", NON_EXISTENT_SKU);
        
        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        // 验证响应
        log.info("删除不存在SKU响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertNotNull(responseJson.getInteger("status"), "业务状态码不应为空");
        Assert.assertNotNull(responseJson.getString("msg"), "响应消息不应为空");
        
        log.info("测试完成 - 删除不存在SKU处理结果: {}", responseJson.getString("msg"));
    }

    // ==================== 置顶常购商品接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_TOP_001
     * 正向测试：置顶常购商品
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("置顶常购商品")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证置顶常购商品的功能")
    public void testTopFrequentSku() throws InterruptedException {
        // 先添加一个SKU
        String addUrl = baseUrl + ADD_FREQUENT_SKU_URL;
        String addRequestBody = String.format("{\"skuList\":[\"%s\"],\"source\":%d}", 
                TEST_SKU_1, SOURCE_PRODUCT_DETAIL);
        
        HttpRequest.post(addUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(addRequestBody)
                .execute();
        
        Thread.sleep(1000);
        
        // 置顶操作
        String url = baseUrl + TOP_FREQUENT_SKU_URL;
        String requestBody = String.format("{\"sku\":\"%s\"}", TEST_SKU_1);
        
        log.info("开始测试置顶常购商品 - SKU: {}", TEST_SKU_1);
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("置顶SKU响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        
        log.info("测试完成 - SKU {} 置顶操作成功", TEST_SKU_1);
    }
    
    /**
     * TC_API_FREQUENT_SKU_CANCEL_TOP_001
     * 正向测试：取消置顶常购商品
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("取消置顶常购商品")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证取消置顶常购商品的功能")
    public void testCancelTopFrequentSku() throws InterruptedException {
        // 先添加并置顶一个SKU
        String addUrl = baseUrl + ADD_FREQUENT_SKU_URL;
        String addRequestBody = String.format("{\"skuList\":[\"%s\"],\"source\":%d}", 
                TEST_SKU_1, SOURCE_PRODUCT_DETAIL);
        
        HttpRequest.post(addUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(addRequestBody)
                .execute();
        
        Thread.sleep(500);
        
        String topUrl = baseUrl + TOP_FREQUENT_SKU_URL;
        String topRequestBody = String.format("{\"sku\":\"%s\"}", TEST_SKU_1);
        
        HttpRequest.post(topUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(topRequestBody)
                .execute();
        
        Thread.sleep(500);
        
        // 取消置顶操作
        String url = baseUrl + CANCEL_TOP_FREQUENT_SKU_URL;
        String requestBody = String.format("{\"sku\":\"%s\"}", TEST_SKU_1);
        
        log.info("开始测试取消置顶常购商品 - SKU: {}", TEST_SKU_1);
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("取消置顶SKU响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        
        log.info("测试完成 - SKU {} 取消置顶操作成功", TEST_SKU_1);
    }

    // ==================== 常购清单列表查询接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_QUERY_PAGE_001
     * 正向测试：查询常购清单列表
     * 优先级：P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询常购清单列表")
    @Severity(SeverityLevel.CRITICAL)
    @Description("验证查询常购清单列表的功能")
    public void testQueryFrequentSkuPage() {
        // 准备测试数据 - 基于日志的真实请求格式
        String url = baseUrl + QUERY_PAGE_URL;
        String requestBody = String.format("{\"pageIndex\":%d,\"pageSize\":%d,\"firstCategoryIdList\":[]}", 
                DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE);
        
        log.info("开始测试查询常购清单列表 - 页码: {}, 页大小: {}", DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE);
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("查询列表响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getJSONObject("data"), "数据对象不应为空");
        
        JSONObject data = responseJson.getJSONObject("data");
        Assert.assertNotNull(data.getJSONArray("list"), "列表数据不应为空");
        Assert.assertNotNull(data.getInteger("totalCount"), "总数不应为空");
        
        log.info("测试完成 - 常购清单列表查询成功，总数: {}", data.getInteger("totalCount"));
    }
    
    /**
     * TC_API_FREQUENT_SKU_QUERY_PAGE_002
     * 边界测试：查询最大页大小
     * 优先级：P2
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询常购清单列表")
    @Severity(SeverityLevel.MINOR)
    @Description("验证查询最大页大小时的处理")
    public void testQueryFrequentSkuPageWithMaxSize() {
        String url = baseUrl + QUERY_PAGE_URL;
        String requestBody = String.format("{\"pageIndex\":%d,\"pageSize\":%d,\"firstCategoryIdList\":[]}", 
                DEFAULT_PAGE_INDEX, MAX_PAGE_SIZE);
        
        log.info("开始测试查询最大页大小 - 页大小: {}", MAX_PAGE_SIZE);
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("最大页大小查询响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertNotNull(responseJson.getInteger("status"), "业务状态码不应为空");
        
        log.info("测试完成 - 最大页大小查询处理结果: {}", responseJson.getString("msg"));
    }

    // ==================== 常购清单推荐接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_QUERY_RECOMMEND_001
     * 正向测试：查询常购清单弹窗推荐
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询常购清单推荐")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证查询常购清单弹窗推荐的功能")
    public void testQueryFrequentSkuRecommend() {
        String url = baseUrl + QUERY_RECOMMEND_URL;
        String requestBody = "{}"; // 空请求体
        
        log.info("开始测试查询常购清单弹窗推荐");
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("推荐查询响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getJSONObject("data"), "数据对象不应为空");
        
        log.info("测试完成 - 常购清单推荐查询成功");
    }

    // ==================== 常购清单类目信息接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_QUERY_CATEGORY_001
     * 正向测试：查询常购清单类目信息
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询常购清单类目")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证查询常购清单类目信息的功能")
    public void testQueryFrequentSkuCategory() {
        String url = baseUrl + QUERY_CATEGORY_URL;
        String requestBody = "{}"; // 空请求体
        
        log.info("开始测试查询常购清单类目信息");
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("类目查询响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getJSONArray("data"), "类目数据不应为空");
        
        log.info("测试完成 - 常购清单类目信息查询成功");
    }

    // ==================== 消息提醒配置接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_QUERY_CONFIG_001
     * 正向测试：查询消息提醒配置
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询消息提醒配置")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证查询消息提醒配置的功能")
    public void testQueryMessageConfig() {
        String url = baseUrl + QUERY_CONFIG_URL;
        String requestBody = String.format("{\"configType\":%d}", CONFIG_TYPE_MESSAGE_REMIND);
        
        log.info("开始测试查询消息提醒配置 - 配置类型: {}", CONFIG_TYPE_MESSAGE_REMIND);
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("配置查询响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getJSONObject("data"), "配置数据不应为空");
        
        log.info("测试完成 - 消息提醒配置查询成功");
    }
    
    /**
     * TC_API_FREQUENT_SKU_UPDATE_CONFIG_001
     * 正向测试：修改消息提醒配置
     * 优先级：P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("修改消息提醒配置")
    @Severity(SeverityLevel.NORMAL)
    @Description("验证修改消息提醒配置的功能")
    public void testUpdateMessageConfig() {
        String url = baseUrl + UPDATE_CONFIG_URL;
        String requestBody = String.format("{\"configType\":%d,\"status\":%d}", 
                CONFIG_TYPE_MESSAGE_REMIND, CONFIG_STATUS_ENABLE);
        
        log.info("开始测试修改消息提醒配置 - 配置类型: {}, 状态: {}", 
                CONFIG_TYPE_MESSAGE_REMIND, CONFIG_STATUS_ENABLE);
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("配置修改响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        
        log.info("测试完成 - 消息提醒配置修改成功");
    }

    // ==================== 业态榜单查询接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_QUERY_BUSINESS_FORMAT_001
     * 正向测试：查询业态榜单top20
     * 优先级：P2
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询业态榜单")
    @Severity(SeverityLevel.MINOR)
    @Description("验证查询业态榜单top20的功能")
    public void testQueryBusinessFormatList() {
        String url = baseUrl + QUERY_BUSINESS_FORMAT_LIST_URL;
        String requestBody = String.format("{\"businessFormatId\":\"%s\"}", TEST_BUSINESS_FORMAT_ID);
        
        log.info("开始测试查询业态榜单 - 业态ID: {}", TEST_BUSINESS_FORMAT_ID);
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("业态榜单查询响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getJSONArray("data"), "榜单数据不应为空");
        
        log.info("测试完成 - 业态榜单查询成功");
    }

    // ==================== 引导查询接口测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_QUERY_NEED_GUIDE_001
     * 正向测试：查询是否需要引导
     * 优先级：P2
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询是否需要引导")
    @Severity(SeverityLevel.MINOR)
    @Description("验证查询是否需要引导的功能")
    public void testQueryNeedGuide() {
        String url = baseUrl + QUERY_NEED_GUIDE_URL;
        String requestBody = "{\"completeTheGuidance\":false}"; // 基于API文档的参数
        
        log.info("开始测试查询是否需要引导");
        
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();
        
        log.info("引导查询响应: {}", response.body());
        Assert.assertEquals(response.getStatus(), 200, "HTTP状态码应为200");
        
        JSONObject responseJson = JSONObject.parseObject(response.body());
        Assert.assertEquals(responseJson.getInteger("status"), Integer.valueOf(0), "业务状态码应为0");
        Assert.assertNotNull(responseJson.getJSONObject("data"), "引导数据不应为空");
        
        log.info("测试完成 - 引导查询成功");
    }

    // ==================== 综合测试用例 ====================
    
    /**
     * TC_API_FREQUENT_SKU_INTEGRATION_001
     * 综合测试：完整的常购商品业务流程
     * 优先级：P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("常购商品综合流程")
    @Severity(SeverityLevel.CRITICAL)
    @Description("验证常购商品的完整业务流程：添加->置顶->查询->取消置顶->删除")
    public void testCompleteFrequentSkuWorkflow() throws InterruptedException {
        log.info("开始测试完整的常购商品业务流程");
        
        // 第一步：添加常购商品
        String addUrl = baseUrl + ADD_FREQUENT_SKU_URL;
        String addRequestBody = String.format("{\"skuList\":[\"%s\"],\"source\":%d}", 
                TEST_SKU_1, SOURCE_PRODUCT_DETAIL);
        
        log.info("步骤1: 添加常购商品 - SKU: {}", TEST_SKU_1);
        
        HttpResponse addResponse = HttpRequest.post(addUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(addRequestBody)
                .execute();
        
        Assert.assertEquals(addResponse.getStatus(), 200, "添加请求HTTP状态码应为200");
        JSONObject addResponseJson = JSONObject.parseObject(addResponse.body());
        Assert.assertEquals(addResponseJson.getInteger("status"), Integer.valueOf(0), "添加操作应成功");
        
        Thread.sleep(1000);
        
        // 第二步：置顶商品
        log.info("步骤2: 置顶商品 - SKU: {}", TEST_SKU_1);
        
        String topUrl = baseUrl + TOP_FREQUENT_SKU_URL;
        String topRequestBody = String.format("{\"sku\":\"%s\"}", TEST_SKU_1);
        
        HttpResponse topResponse = HttpRequest.post(topUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(topRequestBody)
                .execute();
        
        Assert.assertEquals(topResponse.getStatus(), 200, "置顶请求HTTP状态码应为200");
        
        Thread.sleep(1000);
        
        // 第三步：查询列表验证
        log.info("步骤3: 查询列表验证");
        
        String queryUrl = baseUrl + QUERY_PAGE_URL;
        String queryRequestBody = String.format("{\"pageIndex\":%d,\"pageSize\":%d,\"firstCategoryIdList\":[]}", 
                DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE);
        
        HttpResponse queryResponse = HttpRequest.post(queryUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(queryRequestBody)
                .execute();
        
        Assert.assertEquals(queryResponse.getStatus(), 200, "查询请求HTTP状态码应为200");
        JSONObject queryResponseJson = JSONObject.parseObject(queryResponse.body());
        Assert.assertEquals(queryResponseJson.getInteger("status"), Integer.valueOf(0), "查询操作应成功");
        
        // 第四步：取消置顶
        log.info("步骤4: 取消置顶 - SKU: {}", TEST_SKU_1);
        
        String cancelTopUrl = baseUrl + CANCEL_TOP_FREQUENT_SKU_URL;
        String cancelTopRequestBody = String.format("{\"sku\":\"%s\"}", TEST_SKU_1);
        
        HttpResponse cancelTopResponse = HttpRequest.post(cancelTopUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(cancelTopRequestBody)
                .execute();
        
        Assert.assertEquals(cancelTopResponse.getStatus(), 200, "取消置顶请求HTTP状态码应为200");
        
        Thread.sleep(1000);
        
        // 第五步：删除商品
        log.info("步骤5: 删除商品 - SKU: {}", TEST_SKU_1);
        
        String deleteUrl = baseUrl + DELETE_FREQUENT_SKU_URL;
        String deleteRequestBody = String.format("{\"sku\":\"%s\"}", TEST_SKU_1);
        
        HttpResponse deleteResponse = HttpRequest.post(deleteUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(deleteRequestBody)
                .execute();
        
        Assert.assertEquals(deleteResponse.getStatus(), 200, "删除请求HTTP状态码应为200");
        JSONObject deleteResponseJson = JSONObject.parseObject(deleteResponse.body());
        Assert.assertEquals(deleteResponseJson.getInteger("status"), Integer.valueOf(0), "删除操作应成功");
        
        log.info("测试完成 - 完整业务流程验证通过");
    }
    
    // ==================== 数据清理方法 ====================
    
    /**
     * 清理测试数据
     * 在测试类执行完毕后清理所有测试产生的数据
     */
    @AfterClass
    public void cleanup() {
        log.info("开始清理测试数据");
        
        try {
            HikariDataSource ds = SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
            JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
            
            // 清理所有测试SKU
            String[] testSkus = {TEST_SKU_1, TEST_SKU_2, TEST_SKU_3, NON_EXISTENT_SKU};
            
            for (String sku : testSkus) {
                String deleteSql = String.format("DELETE FROM frequent_sku_pool WHERE sku = '%s' AND mid = %d", 
                        sku, MallUserEnum.mallAutoUser.getMid());
                
                try {
                    int deletedRows = jdbcTemplate.update(deleteSql);
                    if (deletedRows > 0) {
                        log.info("清理测试数据 - 删除SKU: {}, 影响行数: {}", sku, deletedRows);
                    }
                } catch (Exception e) {
                    log.warn("清理SKU {} 时发生异常: {}", sku, e.getMessage());
                }
            }
            
            ds.close();
            log.info("测试数据清理完成");
            
        } catch (Exception e) {
            log.error("清理测试数据时发生异常", e);
        }
    }

}
