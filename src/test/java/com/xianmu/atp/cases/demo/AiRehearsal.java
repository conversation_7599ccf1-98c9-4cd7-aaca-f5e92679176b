package com.xianmu.atp.cases.demo;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @description 测试类，用于测试抽奖活动相关的接口。
 */
@Epic("demo")
@Feature("AI彩排")
@Owner("zyl")
@Slf4j
public class AiRehearsal extends BaseTest {
    /**
     * @description 测试抽奖活动的基本功能。
     * 验证用户能够正常参与抽奖活动并获得预期结果。
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("抽奖活动")
    @Description("TC_API_MALL_LOTTERY_001: 用户正常参与抽奖活动")
    public void testLuckyDraw() {
        String env = EnvEnum.qa.getName();
        String url = "https://" + env + "h5.summerfarm.net/luck/draw/activity/query/info";
        String token = MallUserEnum.mallAutoUser2.getToken();

        // 查询红包雨信息
        String body1 = "{\"type\":1}";
        HttpResponse response1 = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body1)
                .execute();
        JSONObject jsonObject1 = JSONObject.parseObject(response1.body());
        Assert.isTrue(jsonObject1.getJSONObject("data").get("id").equals(144), "未返回红包雨");

        // 查询抽奖活动信息
        String body2 = "{\"type\":2}";
        HttpResponse response2 = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body2)
                .execute();
        JSONObject jsonObject2 = JSONObject.parseObject(response2.body());
        Assert.isTrue(jsonObject2.getJSONObject("data").get("id").equals(145), "未返回抽奖活动");

        // 抽红包雨
        String drawUrl = "https://" + env + "h5.summerfarm.net/luck/draw/activity/upsert/start";
        String body3 = "{\"activityId\":144}";
        HttpResponse response3 = HttpRequest.post(drawUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body3)
                .execute();
        Assert.isTrue(response3.getStatus() == 200, "抽红包雨失败");

        // 抽奖活动
        String body6 = "{\"activityId\":145}";
        HttpResponse response6 = HttpRequest.post(drawUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body6)
                .execute();
        Assert.isTrue(response6.getStatus() == 200, "抽奖失败");

        // 查询奖项详情
        String prizeUrl = "https://" + env + "h5.summerfarm.net/luck/draw/activity/query/prize";
        String body5 = "{\"id\":145,\"type\":2}";
        HttpResponse response5 = HttpRequest.post(prizeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body5)
                .execute();
        JSONObject jsonObject5 = JSONObject.parseObject(response5.body());
        Assert.isTrue(jsonObject5.getJSONObject("data").containsKey("prizeList"), "未返回奖项列表");
    }
}