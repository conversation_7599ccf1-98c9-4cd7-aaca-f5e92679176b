package com.xianmu.atp.cases.tms;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.dal.dao.DeliveryNoteTemplateDao;
import com.xianmu.atp.dal.dao.DeliveryNoteTemplateBelongDao;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;


@Slf4j
@Epic("TMS")
@Feature("配送单模版")
@Owner("xianmu")

public class DeliveryNoteTemplateTest extends BaseTest {

    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    private String deliveryNoteTemplateId;

    @Value("张小虎")
    private String scopeBusinessName;

    @Autowired
    private DeliveryNoteTemplateDao deliveryNoteTemplateDao;

    @Autowired
    private DeliveryNoteTemplateBelongDao deliveryNoteTemplateBelongDao;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendPostRequest(String url, String json) {
        return HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }


    @Test(description = "新增配送单模版")
    public void case027() {
        deliveryNoteTemplateDao.deletebelongBusinessName(scopeBusinessName);
        deliveryNoteTemplateBelongDao.deleteScopeBusinessName(scopeBusinessName);
        String helpUrl = domain + "/tms-new/tmsDeliveryNoteTemplate/upsert/insert";
        String json = "{\n" +
                "\"deliveryNoteName\": \"自动化测试\",\n" +
                "\"appSource\": 2,\n" +
                "\"belongSaveCommandInputList\": [{\n" +
                "\"tenantId\": 1,\n" +
                "\"scopeType\": 1,\n" +
                "\"scopeBusinessId\": 11494,\n" +
                "\"scopeBusinessName\": \"张小虎\",\n" +
                "\"appSource\": 2\n" +
                "}],\n" +
                "\"scopeType\": 1,\n" +
                "\"showPriceTemplateOssUrl\": \"https://devossperm.summerfarm.net/front_file/fe-manage/test/oss/excel-file-4bb43341e2a74ef4ada6b8cdd7125a39.xlsx\",\n" +
                "\"noShowPriceTemplateOssUrl\": \"https://devossperm.summerfarm.net/front_file/fe-manage/test/oss/excel-file-8c83d2fc42a24227b107e3849e0678b7.xlsx\",\n" +
                "\"frontPageStr\": \"[{\\\"dataSource\\\":[{\\\"title\\\":\\\"配送单标题\\\",\\\"show\\\":true,\\\"disabled\\\":true,\\\"content\\\":\\\"鲜沐农场\\\",\\\"key\\\":\\\"deliveryNoteName\\\"},{\\\"title\\\":\\\"品牌名称\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"content\\\":false,\\\"key\\\":\\\"brandName\\\"},{\\\"title\\\":\\\"发货时间\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"deliveryTime\\\",\\\"content\\\":false},{\\\"title\\\":\\\"订单编号\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"orderNo\\\",\\\"content\\\":false},{\\\"title\\\":\\\"订单备注\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"orderRemark\\\",\\\"content\\\":false},{\\\"title\\\":\\\"地址备注\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"sendRemark\\\",\\\"content\\\":false}]}]\", \n" +
                "\"showPriceFlag\": 0,\n" +
                "\"useState\": 0\n" +
                "}";

        assertResponse(sendPostRequest(helpUrl, json));

    }

    @Test(description = "配送单模版列表",dependsOnMethods = "case027")
    public void case028() {
        String helpUrl = domain + "/tms-new/tmsDeliveryNoteTemplate/query/page";
        String json = "{\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 10,\n" +
                "}";

        HttpResponse response = sendPostRequest(helpUrl, json);
        assertResponse(response);
        JSONObject jsonResponse = JSON.parseObject(response.body());
        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("list");
        if (dataList != null && !dataList.isEmpty()) {
            deliveryNoteTemplateId = dataList.getJSONObject(0).getString("id");
            log.info("第一个配送单模版的 ID: {}", deliveryNoteTemplateId);
        } else {
            log.warn("返回的数据列表为空");
        }

    }

    @Test(description = "停用配送单模版", dependsOnMethods = "case028")
    public void case029() {
        String helpUrl = domain + "/tms-new/tmsDeliveryNoteTemplate/upsert/use-state";
        String json = "{\n" +
                "\t\"id\": \"" + deliveryNoteTemplateId + "\",\n" + // 使用 performanceReviewTaskId
                "\t\"useState\": 1\n" +
                "}";

        assertResponse(sendPostRequest(helpUrl, json));
    }

    @Test(description = "启用配送单模版", dependsOnMethods = "case029")
    public void case030() {
        String helpUrl = domain + "/tms-new/tmsDeliveryNoteTemplate/upsert/use-state";
        String json = "{\n" +
                "\t\"id\": \"" + deliveryNoteTemplateId + "\",\n" + // 使用 performanceReviewTaskId
                "\t\"useState\": 0\n" +
                "}";
        assertResponse(sendPostRequest(helpUrl, json));
    }

    @Test(description = "编辑配送单模版", dependsOnMethods = "case030")
    public void case031() {
        String helpUrl = domain + "/tms-new/tmsDeliveryNoteTemplate/upsert/update";
        String json = "{\n" +
                "\"deliveryNoteName\": \"自动化测试2\",\n" +
                "\"appSource\": 2,\n" +
                "\"scopeType\": 1,\n" +
                "\"showPriceTemplateOssUrl\": \"https://devossperm.summerfarm.net/front_file/fe-manage/test/oss/excel-file-825dfaea7f2443c297f25d7acfcb6e2b.xlsx\",\n" +
                "\"noShowPriceTemplateOssUrl\": \"https://devossperm.summerfarm.net/front_file/fe-manage/test/oss/excel-file-b142b34f78954ac5954eae617412b740.xlsx\",\n" +
                "\"frontPageStr\": \"[{\\\"dataSource\\\":[{\\\"title\\\":\\\"配送单标题\\\",\\\"show\\\":true,\\\"disabled\\\":true,\\\"content\\\":\\\"鲜沐农场\\\",\\\"key\\\":\\\"deliveryNoteName\\\"},{\\\"title\\\":\\\"品牌名称\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"content\\\":false,\\\"key\\\":\\\"brandName\\\"},{\\\"title\\\":\\\"发货时间\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"deliveryTime\\\",\\\"content\\\":false},{\\\"title\\\":\\\"订单编号\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"orderNo\\\",\\\"content\\\":false},{\\\"title\\\":\\\"订单备注\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"orderRemark\\\",\\\"content\\\":false},{\\\"title\\\":\\\"地址备注\\\",\\\"show\\\":true,\\\"disabled\\\":false,\\\"key\\\":\\\"sendRemark\\\",\\\"content\\\":false}]}]\", \n" +
                "\"showPriceFlag\": 0,\n" +
                "\"useState\": 0,\n" +
                "\t\"id\": \"" + deliveryNoteTemplateId + "\",\n" +
                "\"belongUpdateCommandInput\": [\n" +
                "{\n" +
                "\"tenantId\": 1,\n" +
                "\"scopeType\": 1,\n" +
                "\"scopeBusinessId\": 11494,\n" +
                "\"scopeBusinessName\": \"张小虎\",\n" +
                "\"appSource\": 2\n" +
                "}\n" +
                "]\n" +
                "}";
        assertResponse(sendPostRequest(helpUrl, json));

        deliveryNoteTemplateDao.deleteDeliveryNoteTemplate(deliveryNoteTemplateId);
        deliveryNoteTemplateBelongDao.deleteDeliveryNoteTemplateBelong(deliveryNoteTemplateId);

    }

}
