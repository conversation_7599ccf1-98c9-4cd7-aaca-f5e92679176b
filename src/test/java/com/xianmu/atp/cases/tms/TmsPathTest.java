package com.xianmu.atp.cases.tms;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.*;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Epic("TMS")
@Feature("干线路由")
@Owner("xianmu")

public class TmsPathTest extends BaseTest{
    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    @Value("295-306")
    private String pathCode;

    @Value("谱尼测试集团股份有限公司")
    private String carrierName;

    @Autowired
    private TmsPathDao tmsPathDao;

    @Autowired
    private TmsPathQuotationDao tmsPathQuotationDao;

    @Autowired
    private TmsPathSectionDao tmsPathSectionDao;

    @Autowired
    private TmsPathCarDao tmsPathCarDao;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "新增干线路由")
    public void case074() {
        Long tmspathid = tmsPathDao.getTmsPathId(pathCode);
        // 先删除相关数据
        if (tmspathid != null) {
            tmsPathCarDao.delcetTmsPathCar(tmspathid);
            tmsPathQuotationDao.delcetTmsPathQuotation(tmspathid);
            tmsPathSectionDao.delcetTmsPathSection(tmspathid);
            tmsPathDao.delcetTmsPath(pathCode);
        } else {
            log.warn("No driver found with phone: {}; Skipping deletion.", pathCode);
        }
        String helpUrl = domain + "/tms-new/trunk-path/upsert/new/save";
        // 构建 sectionList
        List<Map<String, Object>> sectionList = new ArrayList<>();
        Map<String, Object> section1 = new HashMap<>();
        section1.put("siteId", 295);
        section1.put("siteName", "嘉兴库存仓");
        section1.put("type", 2);
        section1.put("sequence", 1);
        section1.put("planSpan", 1);
        section1.put("loadHour", 1);
        sectionList.add(section1);

        Map<String, Object> section2 = new HashMap<>();
        section2.put("siteId", 306);
        section2.put("siteName", "河北总仓");
        section2.put("type", 2);
        section2.put("sequence", 2);
        sectionList.add(section2);

        // 构建 quotationList
        List<Map<String, Object>> quotationList = new ArrayList<>();
        Map<String, Object> quotation = new HashMap<>();
        quotation.put("carrierId", 50);
        quotation.put("carrierName", carrierName);
        quotation.put("carType", 4);
        quotation.put("storage", 0);
        quotation.put("quotationFee", 100);
        quotation.put("sequence", 1);
        quotationList.add(quotation);

        // 构建 carList
        List<Map<String, Object>> carList = new ArrayList<>();
        Map<String, Object> car = new HashMap<>();
        car.put("deliveryBatchType", 0);
        car.put("shiftType", 0);
        car.put("periodType", 0);
        car.put("intervalDays", 1);
        car.put("startTime", "2025-01-14");
        car.put("carryType", -1);
        car.put("carryTime", "13:48");
        car.put("sequence", 1);
        car.put("dispatchType", 1);
        carList.add(car);

        // 构建最终的 params Map
        Map<String, Object> params = new HashMap<>();
        params.put("region", "张小虎自动化路由");
        params.put("sectionList", sectionList);
        params.put("quotationList", quotationList);
        params.put("carList", carList);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "停用干线路由", dependsOnMethods = "case074")
    public void case075() {
        Long newtmspathid = tmsPathDao.getTmsPathId(pathCode);
        String helpUrl = domain + "/tms-new/trunk-path/upsert/new/disable?pathId=" + newtmspathid;
        assertResponse(sendRequest(helpUrl, null, true));
    }

    @Test(description = "启用干线路由", dependsOnMethods = "case075")
    public void case076() {
        Long newtmspathid = tmsPathDao.getTmsPathId(pathCode);
        String helpUrl = domain + "/tms-new/trunk-path/upsert/new/enable?pathId=" + newtmspathid;
        assertResponse(sendRequest(helpUrl, null, true));
    }

    @Test(description = "查询干线路由详情", dependsOnMethods = "case076")
    public void case077() {
        Long newtmspathid = tmsPathDao.getTmsPathId(pathCode);
        String helpUrl = domain + "/tms-new/trunk-path/query/new/detail?pathId=" + newtmspathid;
        assertResponse(sendRequest(helpUrl, null, true));
    }

    @Test(description = "查询站点信息", dependsOnMethods = "case077")
    public void case078() {
        String helpUrl = domain + "/tms-new/site/query/site-search";
        Map<String, Object> params = new HashMap<>();
        params.put("type", 1);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "查询干线承运商数据", dependsOnMethods = "case078")
    public void case079() {
        String helpUrl = domain + "/tms-new/carrier/query/searchTrunk?name=" + carrierName;
        assertResponse(sendRequest(helpUrl, null, true));
    }

    @Test(description = "编辑干线路由", dependsOnMethods = "case079")
    public void case080() {
        Long newtmspathid = tmsPathDao.getTmsPathId(pathCode);
        Long tmspathcarid =tmsPathCarDao.getTmsPathCarId(newtmspathid);
        String helpUrl = domain + "/tms-new/trunk-path/upsert/new/update";
        // 构建 carList
        List<Map<String, Object>> carList = new ArrayList<>();
        Map<String, Object> car = new HashMap<>();
        car.put("carryTime", "13:48");
        car.put("carryType", -1);
        car.put("deliveryBatchType", 0);
        car.put("dispatchType", 1);
        car.put("id", tmspathcarid);
        car.put("intervalDays", 1);
        car.put("periodType", 0);
        car.put("shiftType", 0);
        car.put("startTime", "2025-01-14");
        car.put("sequence", 1);
        carList.add(car);

        // 构建 quotationList
        List<Map<String, Object>> quotationList = new ArrayList<>();
        Map<String, Object> quotation = new HashMap<>();
        quotation.put("carType", 4);
        quotation.put("carTypeDesc", "4.2米");
        quotation.put("carrierId", 50);
        quotation.put("carrierName", "谱尼测试集团股份有限公司");
        quotation.put("quotationFee", 100);
        quotation.put("storage", 0);
        quotation.put("storageDesc", "常温");
        quotation.put("sequence", 1);
        quotationList.add(quotation);

        // 构建 sectionList
        List<Map<String, Object>> sectionList = new ArrayList<>();
        Map<String, Object> section1 = new HashMap<>();
        section1.put("loadHour", 1);
        section1.put("planSpan", 1);
        section1.put("sequence", 1);
        section1.put("siteId", 295);
        section1.put("siteName", "嘉兴库存仓");
        section1.put("siteType", 2);
        sectionList.add(section1);

        Map<String, Object> section2 = new HashMap<>();
        section2.put("sequence", 2);
        section2.put("siteId", 306);
        section2.put("siteName", "河北总仓");
        section2.put("siteType", 2);
        sectionList.add(section2);

        // 构建最终的 params Map
        Map<String, Object> params = new HashMap<>();
        params.put("carList", carList);
        params.put("id", newtmspathid);
        params.put("quotationList", quotationList);
        params.put("region", "张小虎自动化路由");
        params.put("sectionList", sectionList);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "查询干线路由列表", dependsOnMethods = "case080")
    public void case081() {
        String helpUrl = domain + "/tms-new/trunk-path/query/path/list";
        Map<String, Object> params = new HashMap<>();
        params.put("pageIndex", 1);
        params.put("pageSize", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }
}
