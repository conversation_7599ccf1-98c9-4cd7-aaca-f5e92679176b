package com.xianmu.atp.cases.tms;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.DriverDao;
import com.xianmu.atp.dal.dao.DriverAccountDao;
import com.xianmu.atp.dal.dao.DriverCarCarrierMappingDao;
import com.xianmu.atp.dal.dao.AuthUserBaseDao;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Epic("TMS")
@Feature("司机管理")
@Owner("xianmu")

public class DriverTest extends BaseTest {
    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    private String cityCarrierName;

    private int cityMappingId;

    @Value("***********")
    private String phone;

    @Autowired
    private DriverDao driverDao;

    @Autowired
    private DriverAccountDao driverAccountDao;

    @Autowired
    private DriverCarCarrierMappingDao driverCarCarrierMappingDao;

    @Autowired
    private AuthUserBaseDao authUserBaseDao;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "新增司机")
    public void case060() {
        authUserBaseDao.delcetAuthUserBase(phone);
        Long tmsdriverid = driverDao.getDriver(phone);
        // 先删除相关数据
        if (tmsdriverid != null) {
            driverAccountDao.delcetDriverAccount(tmsdriverid);
            driverCarCarrierMappingDao.delcetDriverCarCarrierMapping(tmsdriverid);
            driverDao.delcetDriver(phone);
        } else {
            log.warn("No driver found with phone: {}; Skipping deletion.", phone);
        }
        String helpUrl = domain + "/tms-new/driver/save";
        Map<String, Object> params = new HashMap<>();
        params.put("idCardFrontPic", "test/9j2nmhuw6q5nqlmet.jpg"); // 身份证正面照片路径
        params.put("driverPics", "test/siwdbeo5kpnqlpbh.jpg");      // 驾驶员照片路径
        params.put("idCardBehindPic", "test/fju39hnrh7nqlk2c.jpg"); // 身份证背面照片路径
        params.put("name", "自动化测试司机");                           // 驾驶员姓名
        params.put("password", "123456");                             // 驾驶员密码
        params.put("cooperationCycle", 1);                            // 合作周期
        params.put("idCard", "211111111111111111");                   // 身份证号
        params.put("phone", phone);                           // 驾驶员电话
        params.put("status", 0);                                      // 驾驶员状态
        params.put("businessType", 2);                                // 业务类型
        params.put("cityStoreNo", 7);                                // 城市门店编号
        params.put("cityCarrierId", 50);                             // 城市承运人 ID
        params.put("cityCarId", 9075);                               // 城市车辆 ID

        // 车辆详细信息对象
        Map<String, Object> cityCarDto = new HashMap<>();
        cityCarDto.put("adminId", 336);                               // 管理员 ID
        cityCarDto.put("adminName", "kimer");                        // 管理员姓名
        cityCarDto.put("carNumber", "琼A12345");                    // 车牌号
        cityCarDto.put("carStorageEnum", "normal");                  // 车辆存储状态
        cityCarDto.put("createTime", "2024-12-30 10:40:30");        // 创建时间
        cityCarDto.put("id", 9075);                                   // 车辆 ID
        cityCarDto.put("quantity", 1);                                // 数量
        cityCarDto.put("status", 0);                                  // 车辆状态
        cityCarDto.put("storage", 0);                                 // 存储
        cityCarDto.put("storageDesc", "常温");                        // 存储描述
        cityCarDto.put("type", 0);                                    // 车辆类型
        cityCarDto.put("typeDesc", "小面包车");                      // 车辆类型描述
        cityCarDto.put("updateTime", "2024-12-30 10:40:30");        // 更新时间
        cityCarDto.put("volume", 2.5);                                // 车辆容积
        cityCarDto.put("weight", 1);                                  // 车辆重量
        cityCarDto.put("_checked", true);                             // 是否已检查

        params.put("cityCarDto", cityCarDto);                        // 将车辆详细信息对象添加到 params 中

        // 驾驶员账户详细信息对象
        Map<String, Object> driverAccountDTO = new HashMap<>();
        driverAccountDTO.put("payType", 2);                          // 支付类型
        driverAccountDTO.put("accountName", "测试");                 // 账户名称
        driverAccountDTO.put("accountBank", "");                     // 账户银行
        driverAccountDTO.put("account", "111");                      // 账户
        driverAccountDTO.put("accountAscription", "");               // 账户归属

        params.put("driverAccountDTO", driverAccountDTO);            // 将驾驶员账户详细信息对象添加到 params 中

        // 现在 params 包含了所有需要的键值对
        System.out.println(params);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "查询司机列表", dependsOnMethods = "case060")
    public void case061() {
        String helpUrl = String.format(domain + "/tms-new/driver/list?status=0&pageIndex=1&pageSize=10");
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "查询司机详情", dependsOnMethods = "case061")
    public void case062() {
        Long newtmsdriverid = driverDao.getDriver(phone);
        String helpUrl = String.format(domain + "/tms-new/driver/detail/%d",newtmsdriverid);
        HttpResponse response = sendRequest(helpUrl, null, false);
        assertResponse(response);
        try {
            JSONObject jsonResponse = JSON.parseObject(response.body());
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data != null) {
                cityCarrierName = data.getString("cityCarrierName");
                cityMappingId = data.getInteger("cityMappingId");
                log.info("City Carrier Name: {}", cityCarrierName);
                log.info("City Mapping ID: {}", cityMappingId);
            } else {
                log.warn("No data found in the response.");
            }
        } catch (Exception e) {
            log.error("Failed to parse driver details: {}", e.getMessage());
            Assert.fail("Failed to retrieve driver details");
        }
    }

    @Test(description = "查询绑定承运商信息", dependsOnMethods = "case062")
    public void case063() {
        String helpUrl = String.format(domain + "/tms-new/carrier/query/all-name");
        Map<String, Object> params = new HashMap<>();
        params.put("carrierName", cityCarrierName);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json , true));
    }

    @Test(description = "删除绑定车辆", dependsOnMethods = "case063")
    public void case064() {
        String helpUrl = String.format(domain + "/tms-new/driver/deleteCar/%d",cityMappingId);
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "更新司机信息", dependsOnMethods = "case064")
    public void case065() {
        Long newtmsdriverid = driverDao.getDriver(phone);
        String helpUrl = String.format(domain + "/tms-new/driver/edit");
        Map<String, Object> params = new HashMap<>();
        params.put("idCardFrontPic", "test/9j2nmhuw6q5nqlmet.jpg");
        params.put("driverPics", "test/siwdbeo5kpnqlpbh.jpg");
        params.put("idCardBehindPic", "test/fju39hnrh7nqlk2c.jpg");
        params.put("name", "自动化测试司机");
        params.put("password", "123456");
        params.put("cooperationCycle", 1);
        params.put("idCard", "211111111111111111");
        params.put("phone", phone);
        params.put("status", 0);
        params.put("businessType", 0);
        params.put("id", newtmsdriverid);
        params.put("cityMappingId", cityMappingId);

        // 创建 driverAccountDTO Map
        Map<String, Object> driverAccountDTO = new HashMap<>();
        driverAccountDTO.put("account", "111");
        driverAccountDTO.put("accountAscription", "");
        driverAccountDTO.put("accountBank", "");
        driverAccountDTO.put("accountName", "测试");
        driverAccountDTO.put("payType", 2);

        // 将 driverAccountDTO 添加到 params 中
        params.put("driverAccountDTO", driverAccountDTO);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json , true));
    }

    @Test(description = "导出司机信息", dependsOnMethods = "case065")
    public void case066() {
        String helpUrl = String.format(domain + "/tms-new/driver/export-async/driver-info");
        String json =  "{\"status\":0,\"params\":\"{\\\"状态\\\":0}\"}";
        assertResponse(sendRequest(helpUrl, json , true));
    }

}
