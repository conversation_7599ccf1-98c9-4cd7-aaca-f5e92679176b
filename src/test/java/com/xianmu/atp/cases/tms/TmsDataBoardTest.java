package com.xianmu.atp.cases.tms;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.*;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Epic("TMS")
@Feature("数据看板")
@Owner("xianmu")
public class TmsDataBoardTest extends BaseTest {
    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "查询看板列表")
    public void case072() {
        String helpUrl = domain + "/tms-new/tms-data/list";
        Map<String, Object> params = new HashMap<>();
        params.put("deliveryTime", "2025-01-01");
        params.put("deliveryEndTime", "2025-01-05");
        params.put("pageIndex", 1);
        params.put("pageSize", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "下载看板列表", dependsOnMethods = "case072")
    public void case073() {
        String helpUrl = domain + "/tms-new/tms-data/export-async/data-board";
        Map<String, Object> params = new HashMap<>();
        params.put("deliveryTime", "2025-01-01");
        params.put("deliveryEndTime", "2025-01-05");
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "获取城配仓权限")
    public void case07() {
        String helpUrl = domain + "/tms-new/tms-data/query/person-store";
        assertResponse(sendRequest(helpUrl, null, true));
    }
}
