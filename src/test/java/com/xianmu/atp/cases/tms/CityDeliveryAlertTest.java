package com.xianmu.atp.cases.tms;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.CompleteDeliveryAdCodeMappingDao;
import com.xianmu.atp.dal.dao.CompleteDeliveryDao;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Epic("TMS")
@Feature("完成配送提醒配置")
@Owner("xianmu")
public class CityDeliveryAlertTest extends BaseTest {

    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    @Value("218")
    private Integer storeNo;

    private int TmsCompleteDeliveryRuleItemId;

    @Autowired
    private CompleteDeliveryAdCodeMappingDao completeDeliveryAdCodeMappingDao;

    @Autowired
    private CompleteDeliveryDao completeDeliveryDao;

    private final String[] adCodes = {"330482", "330402", "330421"};

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "新增完成配送提醒")
    public void case041() {
        Integer completeDeliveryId = completeDeliveryAdCodeMappingDao.getCompleteDeliveryId(adCodes[0]);
        completeDeliveryDao.deleteCompleteDelivery(completeDeliveryId);
        completeDeliveryAdCodeMappingDao.deleteCompleteDeliveryAdCodeMapping(completeDeliveryId);
        String helpUrl = domain + "/tms-new/city-delivery-alert/upsert/add";
        Map<String, Object> params = new HashMap<>();
        params.put("completeDeliveryTime", "18:00");
        params.put("region", "华中");
        params.put("storeNo", 10);
        params.put("adCodes", Arrays.asList(adCodes));
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "查询配送列表", dependsOnMethods = "case041")
    public void case042() {
        String helpUrl = domain + "/tms-new/city-delivery-alert/query/page/1/10?regions=";
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "暂停完成配送提醒", dependsOnMethods = "case042")
    public void case043() {
        Integer completeDeliveryId = completeDeliveryAdCodeMappingDao.getCompleteDeliveryId(adCodes[0]);
        String helpUrl = String.format(domain + "/tms-new/city-delivery-alert/upsert/change-status?id=%d&status=%d", completeDeliveryId, 1);
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "启用完成配送提醒", dependsOnMethods = "case043")
    public void case044() {
        Integer completeDeliveryId = completeDeliveryAdCodeMappingDao.getCompleteDeliveryId(adCodes[0]);
        String helpUrl = String.format(domain + "/tms-new/city-delivery-alert/upsert/change-status?id=%d&status=%d", completeDeliveryId, 0);
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "查看完成配送提醒详情", dependsOnMethods = "case044")
    public void case045() {
        Integer completeDeliveryId = completeDeliveryAdCodeMappingDao.getCompleteDeliveryId(adCodes[0]);
        String helpUrl = String.format(domain + "/tms-new/city-delivery-alert/query/areas?storeNo=%d&id=%d&status=%d", 10, completeDeliveryId, 0);
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "编辑完成配送提醒", dependsOnMethods = "case045")
    public void case046() {
        Integer completeDeliveryId = completeDeliveryAdCodeMappingDao.getCompleteDeliveryId(adCodes[0]);
        String helpUrl = domain + "/tms-new/city-delivery-alert/upsert/edit";
        Map<String, Object> params = new HashMap<>();
        params.put("id", completeDeliveryId);
        params.put("storeNo", 10);
        params.put("adCodes", Arrays.asList(adCodes));
        params.put("region", "华中");
        params.put("city", "嘉兴市");
        params.put("completeDeliveryTime", "18:00");
        params.put("storeName", "嘉兴城配仓");
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
        completeDeliveryDao.deleteCompleteDelivery(completeDeliveryId);
        completeDeliveryAdCodeMappingDao.deleteCompleteDeliveryAdCodeMapping(completeDeliveryId);

    }

    @Test(description = "查询门店品牌规则配置列表")
    public void case047() {
        String helpUrl = domain + "/tms-new/city-delivery-alert/query/rule-group/page";
        Map<String, Object> params = new HashMap<>();
        params.put("pageIndex", 1);
        params.put("pageSize", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));

    }

    @Test(description = "新增特殊品牌的规则", dependsOnMethods = "case047")
    public void case048() {
        String helpUrl = domain + "/tms-new/city-delivery-alert/upsert/rule/add";
        Map<String, Object> params = new HashMap<>();
        params.put("storeNo", storeNo);
        params.put("ruleName", "测试");
        params.put("brandRuleObjectOssKey", "test-app-perm:front_file/fe-manage/test/oss/特殊品牌规则上传模版+(2)-e039f95d571c47d8ab3336bf6a73f22c (1)-edf3a1ed158541d5bf6616f2c7bcb981.xlsx");
        params.put("merchantRuleObjectOssKey", "");
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));

    }

    @Test(description = "新增特殊门店的规则", dependsOnMethods = "case048")
    public void case049() {
        String helpUrl = domain + "/tms-new/city-delivery-alert/upsert/rule/add";
        Map<String, Object> params = new HashMap<>();
        params.put("storeNo", storeNo);
        params.put("ruleName", "测试");
        params.put("brandRuleObjectOssKey", "");
        params.put("merchantRuleObjectOssKey", "test-app-perm:front_file/fe-manage/test/oss/特殊门店规则上传模版-bcba703d6d6d4f85af8656e6df8a4e22 (2)-5ef38b740d874cac869e3a1bf357457f.xlsx");
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));

    }

    @Test(description = "查询特殊配置详情", dependsOnMethods = "case049")
    public void case050() {
        String helpUrl = String.format(domain + "/tms-new/city-delivery-alert/query/rule-group/detail?pageIndex=1&pageSize=10&storeNo=218");
        HttpResponse response = sendRequest(helpUrl, null, true);
        assertResponse(response);

        JSONObject jsonResponse = JSON.parseObject(response.body());
        JSONArray dataList = jsonResponse.getJSONArray("data"); // 获取 "data" 数组

        if (dataList != null && !dataList.isEmpty()) {
            TmsCompleteDeliveryRuleItemId = Integer.parseInt(dataList.getJSONObject(0).getString("id")); // 提取第一个 ID
            log.info("第一个配置的 ID: {}", TmsCompleteDeliveryRuleItemId);
        } else {
            log.warn("返回的数据列表为空");
        }

    }

    @Test(description = "编辑特殊品牌的规则", dependsOnMethods = "case050")
    public void case051() {
        String helpUrl = domain + "/tms-new/city-delivery-alert/upsert/rule/edit";
        Map<String, Object> params = new HashMap<>();
        params.put("storeNo", storeNo);
        params.put("ruleName", "测试");
        params.put("brandRuleObjectOssKey", "test-app-perm:front_file/fe-manage/test/oss/特殊品牌规则上传模版+(2)-e039f95d571c47d8ab3336bf6a73f22c (1)-edf3a1ed158541d5bf6616f2c7bcb981.xlsx");
        params.put("id", TmsCompleteDeliveryRuleItemId);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));

    }

    @Test(description = "删除特殊品牌的规则", dependsOnMethods = "case051")
    public void case052() {
        String helpUrl = String.format(domain + "/tms-new/city-delivery-alert/upsert/rule/del?ruleId=%d",TmsCompleteDeliveryRuleItemId);
        assertResponse(sendRequest(helpUrl, null, true));

    }

    @Test(description = "删除城配仓维度的配置", dependsOnMethods = "case052")
    public void case053() {
        String helpUrl = String.format(domain + "/tms-new/city-delivery-alert/upsert/rule-group/remove?storeNo=%d",storeNo);
        assertResponse(sendRequest(helpUrl, null, true));

    }
}
