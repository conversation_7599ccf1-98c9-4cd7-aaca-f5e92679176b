package com.xianmu.atp.cases.tms;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Epic("TMS")
@Feature("履约审核")
@Owner("xianmu")
public class PerformanceReviewTest extends BaseTest {

    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private AreaStoreDao areaStoreDao;

    @Resource
    private LoginHttp login;

    private String performanceReviewTaskId;
    private List<String> performanceReviewDetailIds = new ArrayList<>();
    private List<String> performanceAppealIds = new ArrayList<>();
    private List<String> appealItemIds = new ArrayList<>();
    private String token;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendPostRequest(String url, String json) {
        return HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

//    @Test(description = "创建履约审核任务")
//    public void case010() {
//        String helpUrl = domain + "/tms-new/performance-review/upsert/create-performance-review-task";
//        String json = "{\n" +
//                "\t\"reviewTaskType\": 1,\n" +
//                "\t\"penaltyStandards\": 50,\n" +
//                "\t\"storeNos\": [2],\n" +
//                "\t\"name\": \"自动化测试\",\n" +
//                "\t\"beginDeliveryTime\": \"2024-09-26\",\n" +
//                "\t\"endDeliveryTime\": \"2024-09-29\",\n" +
//                "\t\"storeNames\": [\"上海城配仓\"]\n" +
//                "}";
//
//        assertResponse(sendPostRequest(helpUrl, json));
//    }
//
//    @Test(description = "查询履约审核任务", dependsOnMethods = "case010")
//    public void case011() {
//        String helpUrl = domain + "/tms-new/performance-review/query/performance-review-task";
//        String json = "{\n" +
//                "\t\"pageIndex\": 1,\n" +
//                "\t\"pageSize\": 10,\n" +
//                "\t\"name\": \"自动化测试\"\n" +
//                "}";
//
//        HttpResponse response = sendPostRequest(helpUrl, json);
//        assertResponse(response);
//
//        JSONObject jsonResponse = JSON.parseObject(response.body());
//        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("list");
//        if (dataList != null && !dataList.isEmpty()) {
//            performanceReviewTaskId = dataList.getJSONObject(0).getString("id");
//            log.info("第一个履约审核任务的 ID: {}", performanceReviewTaskId);
//        } else {
//            log.warn("返回的数据列表为空");
//        }
//    }
//
//    @Test(description = "查询履约审核任务详情", dependsOnMethods = "case011")
//    public void case012() {
//        if (performanceReviewTaskId == null) {
//            log.error("未能提取到任务 ID，无法进行详细信息查询");
//            Assert.fail("未能提取到任务 ID");
//        }
//
//        String helpUrl = domain + "/tms-new/performance-review/query/performance-review-task-detail?performanceReviewTaskId=" + performanceReviewTaskId;
//        assertResponse(sendPostRequest(helpUrl, ""));
//    }
//
//    @Test(description = "查询履约审核任务详情列表", dependsOnMethods = "case012")
//    public void case013() {
//        String helpUrl = domain + "/tms-new/performance-review/query/review-detail-city-sign-in";
//        String json = "{\n" +
//                "\t\"pageIndex\": 1,\n" +
//                "\t\"pageSize\": 10,\n" +
//                "\t\"performanceReviewTaskId\": " + performanceReviewTaskId + "\n" +
//                "}";
//
//        HttpResponse response = sendPostRequest(helpUrl, json);
//        assertResponse(response);
//
//        JSONObject jsonResponse = JSON.parseObject(response.body());
//        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("list");
//        if (dataList != null && !dataList.isEmpty()) {
//            for (int i = 0; i < dataList.size(); i++) {
//                String performanceReviewDetailId = dataList.getJSONObject(i).getString("performanceReviewDetailId");
//                performanceReviewDetailIds.add(performanceReviewDetailId);
//                log.info("履约审核任务的 ID {}: {}", i + 1, performanceReviewDetailId);
//            }
//        } else {
//            log.warn("返回的数据列表为空");
//        }
//    }
//
//    @Test(description = "履约审核不合格", dependsOnMethods = "case013")
//    public void case014() {
//        performReviewAudit(1);
//    }
//
//    @Test(description = "履约审核不合格2", dependsOnMethods = "case013")
//    public void case015() {
//        performReviewAudit(2);
//    }
//
//    @Test(description = "履约审核不合格3", dependsOnMethods = "case013")
//    public void case016() {
//        performReviewAudit(3);
//    }
//
//    private void performReviewAudit(int index) {
//        if (performanceReviewDetailIds.size() <= index) {
//            log.error("未能提取到足够的 performanceReviewDetailId，无法进行审核");
//            Assert.fail("未能提取到足够的 performanceReviewDetailId");
//        }
//
//        String helpUrl = domain + "/tms-new/performance-review/upsert/review-detail-audit";
//        String json = "[{\n" +
//                "\t\"performanceReviewDetailId\": " + performanceReviewDetailIds.get(index) + ",\n" + // 使用对应的 performanceReviewDetailId
//                "\t\"state\": 3,\n" + // 设置状态为 3，表示审核不合格
//                "\t\"sitePicReasons\": [2000],\n" + // 添加相关的原因 ID
//                "\t\"penaltyMoney\": 50\n" + // 设置罚款金额
//                "}]";
//
//        assertResponse(sendPostRequest(helpUrl, json));
//    }
//
//    @Test(description = "审核完成", dependsOnMethods = "case014")
//    public void case017() {
//        String helpUrl = domain + "/tms-new/performance-review/upsert/review-task-change-state";
//        String json = "{\n" +
//                "\t\"performanceReviewTaskId\": \"" + performanceReviewTaskId + "\",\n" + // 使用 performanceReviewTaskId
//                "\t\"state\": 1\n" + // 设置状态为 1
//                "}";
//
//        assertResponse(sendPostRequest(helpUrl, json));
//    }
//
//    @Test(description = "查询申诉任务列表", dependsOnMethods = "case017")
//    public void case018() {
//        String helpUrl = domain + "/tms-new/performance-appeal/query/page";
//        String json = "{\n" +
//                "\t\"pageIndex\": 1,\n" + // 设置页码
//                "\t\"pageSize\": 10,\n" + // 设置每页大小
//                "\t\"status\": null,\n" + // 状态为 null
//                "\t\"performanceReviewTaskId\": \"" + performanceReviewTaskId + "\",\n" +
//                "}";
//
//        assertResponse(sendPostRequest(helpUrl, json));
//
//        HttpResponse response = sendPostRequest(helpUrl, json);
//        assertResponse(response);
//
//        JSONObject jsonResponse = JSON.parseObject(response.body());
//        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("list");
//        if (dataList != null && !dataList.isEmpty()) {
//            for (int i = 0; i < dataList.size(); i++) {
//                String performanceAppealId = dataList.getJSONObject(i).getString("id");
//                performanceAppealIds.add(performanceAppealId);
//                log.info("履约审核任务的 ID {}: {}", i + 1, performanceAppealId);
//            }
//        } else {
//            log.warn("返回的数据列表为空");
//        }
//    }
//
//    @SneakyThrows
//    @Test(description = "查询申诉详情1", dependsOnMethods = "case018")
//    public void case019() {
//        Thread.sleep(1000);
//        String helpUrl = domain + "/tms-new/performance-appeal/query/detail";
//        String json = "{\n" +
//                "\t\"id\": \"" + performanceAppealIds.get(0) + "\",\n" +
//                "}";
//
//        assertResponse(sendPostRequest(helpUrl, json));
//    }
//
//    @Test(description = "关闭申诉任务", dependsOnMethods = "case019")
//    public void case020() {
//
//        String helpUrl = domain + "/tms-new/performance-appeal/upsert/batch-close-appeal";
//        String json = "{\n" +
//                "\t\"appealIds\": [" + performanceAppealIds.get(0) + "],\n" + // 设置要关闭的申诉 ID 列表
//                "\t\"closeReason\": \"测试\"\n" + // 设置关闭原因
//                "}";
//        assertResponse(sendPostRequest(helpUrl, json));
//
//    }
//
//    @Test(description = "查询申诉详情2", dependsOnMethods = "case018")
//    public void case021() {
//
//        String helpUrl = domain + "/tms-new/performance-appeal/query/detail";
//        String json = "{\n" +
//                "\t\"id\": \"" + performanceAppealIds.get(1) + "\",\n" +
//                "}";
//
//        assertResponse(sendPostRequest(helpUrl, json));
//
//        HttpResponse response = sendPostRequest(helpUrl, json);
//        assertResponse(response);
//        JSONObject jsonResponse = JSON.parseObject(response.body());
//        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("appealItemVOList");
//        if (dataList != null && !dataList.isEmpty()) {
//            String appealItemId = dataList.getJSONObject(0).getString("appealItemId");
//            appealItemIds.add(appealItemId);
//            log.info("第一个履约审核任务的 ID: {}", appealItemId);
//        } else {
//            log.warn("返回的数据列表为空");
//        }
//    }
//
//    @Test(description = "查询申诉详情3", dependsOnMethods = "case018")
//    public void case022() {
//
//        String helpUrl = domain + "/tms-new/performance-appeal/query/detail";
//        String json = "{\n" +
//                "\t\"id\": \"" + performanceAppealIds.get(2) + "\",\n" +
//                "}";
//
//        assertResponse(sendPostRequest(helpUrl, json));
//
//        HttpResponse response = sendPostRequest(helpUrl, json);
//        assertResponse(response);
//        JSONObject jsonResponse = JSON.parseObject(response.body());
//        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("appealItemVOList");
//        if (dataList != null && !dataList.isEmpty()) {
//            String appealItemId = dataList.getJSONObject(0).getString("appealItemId");
//            appealItemIds.add(appealItemId);
//            log.info("第一个履约审核任务的 ID: {}", appealItemId);
//        } else {
//            log.warn("返回的数据列表为空");
//        }
//    }
//
//    @Test(description = "提交申诉任务1", dependsOnMethods = "case021")
//    public void case023() {
//
//        String helpUrl = domain + "/tms-new/performance-appeal/upsert/submit-appeal";
//        String json = "{\n" +
//                "\t\"appealId\": \"" + performanceAppealIds.get(1) + "\",\n" +
//                "\t\"appealItemCommandInputList\": [{\n" +
//                "\t\t\"appealItemId\": \"" + appealItemIds.get(0) + "\",\n" +
//                "\t\t\"appealPic\": \"\",\n" +
//                "\t\t\"appealReason\": \"测试\"\n" +
//                "\t}]\n" +
//                "}";
//        assertResponse(sendPostRequest(helpUrl, json));
//
//    }
//
//    @Test(description = "提交申诉任务2", dependsOnMethods = "case022")
//    public void case024() {
//
//        String helpUrl = domain + "/tms-new/performance-appeal/upsert/submit-appeal";
//        String json = "{\n" +
//                "\t\"appealId\": \"" + performanceAppealIds.get(2) + "\",\n" +
//                "\t\"appealItemCommandInputList\": [{\n" +
//                "\t\t\"appealItemId\": \"" + appealItemIds.get(1) + "\",\n" +
//                "\t\t\"appealPic\": \"\",\n" +
//                "\t\t\"appealReason\": \"测试\"\n" +
//                "\t}]\n" +
//                "}";
//        assertResponse(sendPostRequest(helpUrl, json));
//
//    }
//
//    @Test(description = "申诉拒绝", dependsOnMethods = "case024")
//    public void case025() {
//
//        String helpUrl = domain + "/tms-new/performance-appeal/upsert/update-appeal";
//        String json = "{\n" +
//                "\t\"appealId\": \"" + performanceAppealIds.get(1) + "\",\n" +
//                "\t\"status\": 2,\n" +
//                "\t\"appealFailReason\": \"测试\"\n" +
//                "}";
//        assertResponse(sendPostRequest(helpUrl, json));
//
//    }
//
//    @Test(description = "申诉通过", dependsOnMethods = "case025")
//    public void case026() {
//
//        String helpUrl = domain + "/tms-new/performance-appeal/upsert/update-appeal";
//        String json = "{\n" +
//                "\t\"appealId\": \"" + performanceAppealIds.get(2) + "\",\n" +
//                "\t\"status\": 3,\n" +
//                "}";
//        assertResponse(sendPostRequest(helpUrl, json));
//
//    }

}