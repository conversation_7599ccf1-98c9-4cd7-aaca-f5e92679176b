package com.xianmu.atp.cases.tms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.json.JsonUtil;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.*;

import static org.testng.Assert.assertEquals;

@Slf4j
@Epic("TMS")
@Feature("dubbo")
@Owner("xianmu")
public class TmsDubboTest extends BaseTest {

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Value("qa")
    private String env;

    @Test(description = "标准化查询委托单详情")
    public void testQueryDistOrderDetail01() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"expectBeginTime\":\"2025-03-08T00:00:00\",\"outerContactId\":\"351997\",\"outerOrderId\":\"0125QPWT6W0306174378\",\"source\":200}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.standard.TmsDistOrderQueryStandardProvider")
                .methodName("queryDistOrderDetail")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "标准化查询委托单集合(仅查询委托单单表)")
    public void testQueryDistOrderSingleList15() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"distOrderQueryStandardReqs\":[{\"expectBeginTime\":\"2025-03-11T00:00:00\",\"outerContactId\":\"348521\",\"outerOrderId\":\"0425SETLLV0310175595\",\"source\":200}]}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.standard.TmsDistOrderQueryStandardProvider")
                .methodName("queryDistOrderSingleList")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.standard.DistOrderBatchQueryStandardReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "标准化查询委托单详情集合")
    public void testQueryDistOrderDetailList19() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"distOrderQueryStandardReqs\":[{\"expectBeginTime\":\"2025-03-11T00:00:00\",\"outerContactId\":\"348521\",\"outerOrderId\":\"0425SETLLV0310175595\",\"source\":200}]}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.standard.TmsDistOrderQueryStandardProvider")
                .methodName("queryDistOrderDetailList")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.standard.DistOrderBatchQueryStandardReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "标准化查询点位所有委托单集合")
    public void testQuerySiteDistOrderSingleList20() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"expectBeginTime\":\"2025-03-08T00:00:00\",\"outerContactId\":\"352042\",\"outerOrderId\":\"8586232752835461120\",\"source\":201}");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.standard.TmsDistOrderQueryStandardProvider")
                .methodName("querySiteDistOrderSingleList")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "委托单外部条码查询")
    public void testQueryDistOrderOnlyCode02() {
        // 构造请求参数

//        Map<String, Object> map = JsonUtil.toMap("{\"source\":200,\"orderNo\":\"0125NLM8WM0310151199\",\"expectBeginTime\":{\"dayOfYear\":70,\"dayOfWeek\":\"TUESDAY\",\"month\":\"MARCH\",\"dayOfMonth\":11,\"year\":2025,\"monthValue\":3,\"nano\":0,\"hour\":0,\"minute\":0,\"second\":0,\"chronology\":{\"calendarType\":\"iso8601\",\"id\":\"ISO\"}},\"contactId\":\"346691\"}");
        Map<String, Object> map = JsonUtil.toMap("{\"source\":200,\"orderNo\":\"0125NLM8WM0310151199\",\"deliveryTime\":\"2025-03-11T00:00:00\",\"contactId\":\"346691\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider")
                .methodName("queryDistOrderOnlyCode")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.DeliverySiteOnlyCodeQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "批量查询委托单详情")
    public void testQueryDistOrderList03() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"expectBeginTime\":\"2025-03-04T00:00:00\",\"needPagination\":true,\"outerContactIds\":[\"350315\"],\"outerOrderIds\":[\"01255F622S0304114811\"],\"pageIndex\":1,\"pageSize\":20,\"sources\":[200,203],\"startRow\":0}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider")
                .methodName("queryDistOrderList")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.DistOrderQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询委托单详情")
    public void testQueryDetail04() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"deliveryTime\":\"2025-03-07\",\"needPagination\":true,\"outerContactId\":\"350778\",\"outerOrderId\":\"0125AABATA0306141156\",\"pageIndex\":1,\"pageSize\":20,\"source\":200,\"startRow\":0}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider")
                .methodName("queryDetail")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.DistOrderQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询订单配送排线信息")
    public void testQueryOrderNoDeliveryInfo05() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"deliveryTime\":\"2025-03-09\",\"orderNos\":[\"PO25INRGQS0307153582\",\"PO257D89CU0307153987\"]}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider")
                .methodName("queryOrderNoDeliveryInfo")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.OrderNoDeliveryInfoReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询点位信息")
    public void testQueryDistSite06() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"needPagination\":true,\"pageIndex\":1,\"pageSize\":20,\"startRow\":0,\"type\":1}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.TmsDistSiteQueryProvider")
                .methodName("queryDistSite")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.DistSiteQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询指定地址")
    public void testQuerySpecifySiteForBms07() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"needPagination\":true,\"pageIndex\":1,\"pageSize\":20,\"startRow\":0}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.TmsDistSiteQueryProvider")
                .methodName("querySpecifySiteForBms")
                .paramTypes(new String[]{"net.summerfarm.tms.client.dist.req.DistSiteQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询自提、采购的配送送点位")
    public void testQueryPurchaseOwnSaleOutSiteForBms08() {
        // 构造请求参数
//        Map<String, Object> map = JsonUtil.toMap("");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.dist.provider.TmsDistSiteQueryProvider")
                .methodName("queryPurchaseOwnSaleOutSiteForBms")
                .paramTypes(new String[]{})
                .params(new Object[]{})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "wms出库任务查询调度单详情")
    public void testQueryDetail09() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"cancelFlag\":false,\"needPagination\":true,\"outerOrderId\":\"351854\",\"outerRequest\":true,\"pageIndex\":1,\"pageSize\":20,\"source\":102,\"startRow\":0}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.provider.dist.DistOrderQueryProvider")
                .methodName("queryDetail")
                .paramTypes(new String[]{"net.summerfarm.tms.query.dist.DistOrderQuery"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "是否需要自动提交委托单")
    public void testIsAutoSubmitDistOrder10() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"beginSite\":{\"address\":\"杭州市余杭区万达广\",\"area\":\"余杭区\",\"city\":\"杭州市\",\"completeAddress\":\"浙江省杭州市余杭区杭州市余杭区万达广\",\"contactPerson\":\"测试联系人1\",\"name\":\"自动化库存仓\",\"outBusinessNo\":\"126\",\"phone\":\"***********\",\"poi\":\"120.120287,30.330581\",\"province\":\"浙江省\",\"type\":2},\"endSite\":{\"address\":\"浙江省金华市金瓯路枫树小区\",\"area\":\"金东区\",\"city\":\"金华市\",\"completeAddress\":\"浙江省金华市金东区浙江省金华市金瓯路枫树小区\",\"contactPerson\":\"金华金华\",\"name\":\"金华城配\",\"outBusinessNo\":\"3\",\"phone\":\"***********\",\"poi\":\"119.685775,29.115374\",\"province\":\"浙江省\",\"type\":1},\"source\":102}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.provider.dist.DistOrderQueryProvider")
                .methodName("isAutoSubmitDistOrder")
                .paramTypes(new String[]{"net.summerfarm.tms.dist.dto.DistBlackConfigDTO"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "提交委托单")
    public void testSubmitDistOrder11() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"beginSite\":{\"address\":\"凌云路2435号\",\"area\":\"市中区\",\"city\":\"乐山市\",\"completeAddress\":\"四川省乐山市市中区凌云路2435号\",\"createTime\":\"2022-10-11T14:57:11\",\"fullAddress\":\"四川省乐山市市中区凌云路2435号\",\"intelligencePath\":0,\"name\":\"四川\",\"poi\":\"103.771968,29.544055\",\"province\":\"四川省\",\"type\":4},\"creator\":\"kimer\",\"creatorId\":\"336\",\"distOrderItemList\":[{\"outerItemId\":\"2183622027173\",\"outerItemName\":\"张测试鲜果\",\"outerItemType\":\"张测试三级\",\"quantity\":1,\"specification\":\"100斤*111mL/一级/1\",\"temperature\":1,\"type\":0,\"unit\":\"包\",\"volume\":0.0101,\"weight\":5.0}],\"distOrderMark\":{\"expectBeginTime\":\"2025-03-11T00:00:00\",\"outerOrderId\":\"52885\",\"source\":\"PURCHASE\"},\"endSite\":{\"address\":\"浙江省杭州市西湖区龙章路6号\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"completeAddress\":\"浙江省杭州市西湖区浙江省杭州市西湖区龙章路6号\",\"contactPerson\":\"李ceshi\",\"createTime\":\"2022-09-21T13:36:41\",\"fullAddress\":\"浙江省杭州市西湖区浙江省杭州市西湖区龙章路6号\",\"intelligencePath\":0,\"name\":\"杭州总仓库\",\"outBusinessNo\":\"1\",\"phone\":\"***********\",\"poi\":\"120.058591,30.279943\",\"province\":\"浙江省\",\"type\":2},\"expectBeginTime\":\"2025-03-11T00:00:00\",\"outerOrderId\":\"52885\",\"pickType\":0,\"source\":101,\"type\":0}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.provider.dist.DistOrderProvider")
                .methodName("submitDistOrder")
                .paramTypes(new String[]{"net.summerfarm.tms.dist.dto.DistOrderCommand"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "取消承运单", dependsOnMethods = "testSubmitDistOrder11")
    public void testCancelDistOrder12() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"distOrderMark\":{\"outerOrderId\":\"52885\",\"source\":\"PURCHASE\"},\"outerOrderId\":\"52885\",\"source\":101,\"updater\":\"kimer\",\"updaterId\":\"336\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.provider.dist.DistOrderProvider")
                .methodName("cancelDistOrder")
                .paramTypes(new String[]{"net.summerfarm.tms.dist.dto.DistOrderEndCommand"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "获取批次信息")
    public void testQueryBatch13() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"deliveryTime\":\"2025-03-08\",\"needPagination\":true,\"pageIndex\":1,\"pageSize\":20,\"startRow\":0,\"storeNo\":\"1\"}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.provider.delivery.DeliveryBatchQueryProvider")
                .methodName("queryBatch")
                .paramTypes(new String[]{"net.summerfarm.tms.query.delivery.DeliverySectionQuery"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询售后单配送点位信息")
    public void testQueryAfterSaleDeliveryForOfc14() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"afterSaleNo\":\"8588060625085857792\",\"source\":201}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.delivery.provider.TmsDeliveryOrderQueryProvider")
                .methodName("queryAfterSaleDeliveryForOfc")
                .paramTypes(new String[]{"net.summerfarm.tms.client.delivery.req.AfterSaleDeliveryQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询司机实时配送定位")
    public void testQueryDriverCurrentInfo16() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"deliveryBatchId\":399051,\"driverId\":343,\"type\":1}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.driver.provider.standard.TmsDriverQueryStandardProvider")
                .methodName("queryDriverCurrentInfo")
                .paramTypes(new String[]{"net.summerfarm.tms.client.driver.req.standard.DriverCurrentInfoStandardReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询调度单")
    public void testQueryTrunkDeliveryBatch17() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"deliveryBatchId\":396610}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.delivery.provider.TmsTrunkDeliveryQueryProvider")
                .methodName("queryTrunkDeliveryBatch")
                .paramTypes(new String[]{"net.summerfarm.tms.client.delivery.req.TrunkDeliveryBatchQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "查询干线详情信息")
    public void testQueryTrunkDeliveryDetail18() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"beginQueryDateTime\":\"2025-03-10T00:00:00\",\"endQueryDateTime\":\"2025-03-11T05:00:00\",\"status\":40}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.client.delivery.provider.TmsTrunkDeliveryQueryProvider")
                .methodName("queryTrunkDeliveryDetail")
                .paramTypes(new String[]{"net.summerfarm.tms.client.delivery.req.TrunkDeliveryDetailQueryReq"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }

    @Test(description = "获取路段详情信息")
    public void testQueryDeliverySection21() {
        // 构造请求参数
        Map<String, Object> map = JsonUtil.toMap("{\"batchId\":399008,\"finishDeliveryTime\":\"2025-03-12T05:00:44.650\",\"needPagination\":true,\"pageIndex\":1,\"pageSize\":20,\"startRow\":0,\"type\":0}");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env(env)
                .interfaceName("net.summerfarm.tms.provider.delivery.DeliveryBatchQueryProvider")
                .methodName("queryDeliverySection")
                .paramTypes(new String[]{"net.summerfarm.tms.query.delivery.DeliverySectionQuery"})
                .params(new Object[]{map})
                .build();

        log.info("调用请求参数：{}", atpRequestDto);
        // 调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        // 确保 atpDto.getDubboResponse() 返回的是 JSON 字符串
        String jsonResponse = atpDto.getDubboResponse().toString(); // 确保转换为字符串
        JSONObject responseObject = JSON.parseObject(jsonResponse);
        // 进行状态码断言，假设响应中有 status 字段
        int status = responseObject.getIntValue("status");
        assertEquals(status, 200, "状态码应该是 200");
    }


}