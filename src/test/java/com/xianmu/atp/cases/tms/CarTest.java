package com.xianmu.atp.cases.tms;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.CompleteDeliveryDao;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.dal.dao.CarDao;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Epic("TMS")
@Feature("车辆管理")
@Owner("xianmu")

public class CarTest extends BaseTest{

    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    @Value("京A12345")
    private String carNumber;

    @Autowired
    private CarDao carDao;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "新增车辆")
    public void case054() {
        carDao.delcetCar(carNumber);
        String helpUrl = domain + "/tms-new/car/save";
        Map<String, Object> params = new HashMap<>();
        params.put("carTypeNum", 0);
        params.put("carNumber", carNumber);
        params.put("type", 0);
        params.put("volume", 2.5);
        params.put("quantity", 100);
        params.put("status", 0);
        params.put("storage", 0);
        params.put("weight", 1);
        params.put("businessType", 1);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "查询车辆列表", dependsOnMethods = "case054")
    public void case055() {
        String helpUrl = String.format(domain + "/tms-new/car/list?status=%d&pageIndex=%d&pageSize=%d", 0, 1,10);
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "查看车辆详情", dependsOnMethods = "case055")
    public void case056() {
        long carId = carDao.getCarId(carNumber);
        String helpUrl = String.format(domain + "/tms-new/car/getDetail/%d", carId);
        assertResponse(sendRequest(helpUrl, null, false));
    }

    @Test(description = "车辆类型枚举", dependsOnMethods = "case056")
    public void case057() {
        long carId = carDao.getCarId(carNumber);
        String helpUrl = String.format(domain + "/tms-new/car/query/car-type");
        assertResponse(sendRequest(helpUrl, null, true));
    }

    @Test(description = "编辑车辆", dependsOnMethods = "case057")
    public void case058() {
        long carId = carDao.getCarId(carNumber);
        String helpUrl = domain + "/tms-new/car/edit";
        Map<String, Object> params = new HashMap<>();
        params.put("carNumber", carNumber);
        params.put("type", 0);
        params.put("volume", 2.5);
        params.put("quantity", 100);
        params.put("status", 0);
        params.put("storage", 0);
        params.put("weight", 1);
        params.put("id", carId);
        params.put("businessType", 1);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "导出车辆列表", dependsOnMethods = "case058")
    public void case059() {
        String helpUrl = domain + "/tms-new/car/export-async/car-info";
        String json =  "{\"status\":0,\"params\":\"{\\\"状态\\\":0}\"}";
        assertResponse(sendRequest(helpUrl, json, true));
    }
}
