package com.xianmu.atp.cases.tms;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.dal.dao.OutDistanceConfigDao;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;

@Slf4j
@Epic("TMS")
@Feature("签收围栏配置")
@Owner("xianmu")

public class OutDistanceConfigTest extends BaseTest {

    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    @Value("174")
    private Integer storeNo;

    private String outDistanceConfigId;

    @Autowired
    private OutDistanceConfigDao outDistanceConfigDao;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendPostRequest(String url, String json) {
        return HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        String body = response.body();
        System.out.println(body);
        JSONObject jsonResponse = JSON.parseObject(body);
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }


    @Test(description = "新增签收围栏配置")
    public void case033() {
        outDistanceConfigDao.deleteOutDistanceConfig(storeNo);
        String helpUrl = domain + "/tms-new/outDistance/upsert/save";
        String json = "{\n" +
                "\t\"outDistance\": 1,\n" +
                "\t\"storeNo\": " + storeNo + "\n" +
                "}";

        assertResponse(sendPostRequest(helpUrl, json));
    }

    @Test(description = "查询签收围栏配置列表",dependsOnMethods = "case033")
    public void case034() {
        String helpUrl = domain + "/tms-new/outDistance/query/page";
        String json = "{\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 10,\n" +
                "\t\"storeNo\": " + storeNo + "\n" +
                "}";

        HttpResponse response = sendPostRequest(helpUrl, json);
        assertResponse(response);
        JSONObject jsonResponse = JSON.parseObject(response.body());
        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("list");
        if (dataList != null && !dataList.isEmpty()) {
            outDistanceConfigId = dataList.getJSONObject(0).getString("id");
            log.info("第一个配送单模版的 ID: {}", outDistanceConfigId);
        } else {
            log.warn("返回的数据列表为空");
        }
    }

    @Test(description = "暂停签收围栏配置",dependsOnMethods = "case034")
    public void case035() {
        String helpUrl = domain + "/tms-new/outDistance/upsert/update";
        String json = "{\n" +
                "\t\"id\": " + outDistanceConfigId + ",\n" +
                "\t\"outDistance\": 1,\n" +
                "\t\"state\": 1\n" +
                "}";

        assertResponse(sendPostRequest(helpUrl, json));
    }

    @Test(description = "启用签收围栏配置",dependsOnMethods = "case035")
    public void case036() {
        String helpUrl = domain + "/tms-new/outDistance/upsert/update";
        String json = "{\n" +
                "\t\"id\": " + outDistanceConfigId + ",\n" +
                "\t\"outDistance\": 1,\n" +
                "\t\"state\": 0\n" +
                "}";

        assertResponse(sendPostRequest(helpUrl, json));
    }

    @Test(description = "编辑签收围栏配置",dependsOnMethods = "case036")
    public void case037() {
        String helpUrl = domain + "/tms-new/outDistance/upsert/update";
        String json = "{\n" +
                "\t\"id\": " + outDistanceConfigId + ",\n" +
                "\t\"outDistance\": 2,\n" +
                "\t\"state\": 0\n" +
                "}";

        assertResponse(sendPostRequest(helpUrl, json));
    }

}
