package com.xianmu.atp.cases.tms;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.*;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Epic("TMS")
@Feature("承运商管理")
@Owner("xianmu")

public class CarrierTest extends BaseTest {
    @Value("${app.manage-domain}")
    private String domain;

    @Resource
    private LoginHttp login;

    private String token;

    @Value("张小虎自动化承运商")
    private String cityCarrierName;

    @Autowired
    private CarrierDao carrierDao;

    @Autowired
    private CarrierAccountDao carrierAccountDao;

    @Autowired
    private CarrierInvoiceDao carrierInvoiceDao;

    @BeforeMethod
    public void setUp() {
        token = login.login();
        log.info("token: {}", token);
    }

    private HttpResponse sendRequest(String url, String json, boolean isPost) {
        HttpRequest request = isPost ? HttpRequest.post(url) : HttpRequest.get(url);
        return request.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
    }

    private void assertResponse(HttpResponse response) {
        log.info("响应: {}", response.body());
        JSONObject jsonResponse = JSON.parseObject(response.body());
        Assert.assertEquals(jsonResponse.getString("status"), "200");
    }

    private String createJson(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

    @Test(description = "新增承运商")
    public void case067() {
        Long carrierid = carrierDao.getCarrierId(cityCarrierName);
        // 先删除相关数据
        if (carrierid != null) {
            carrierAccountDao.delcetCarrierAccount(carrierid);
            carrierInvoiceDao.delcetCarrierInvoice(carrierid);
            carrierDao.delcetCarrier(cityCarrierName);
        } else {
            log.warn("No driver found with phone: {}; Skipping deletion.", cityCarrierName);
        }
        String helpUrl = domain + "/tms-new/carrier/upsert/save";
        Map<String, Object> params = new HashMap<>();
        params.put("id", "");
        params.put("carrierName", cityCarrierName);
        params.put("director", "测试");
        params.put("directorPhone", "***********");
        params.put("address", "西溪街道星晖商务中心");

        // 创建 carrierAccountList
        List<Map<String, Object>> carrierAccountList = new ArrayList<>();
        Map<String, Object> accountInfo = new HashMap<>();
        accountInfo.put("payType", 1);
        accountInfo.put("accountBank", "招商");
        accountInfo.put("account", "*********");
        accountInfo.put("accountName", "测试");
        accountInfo.put("accountAscription", "杭州");
        carrierAccountList.add(accountInfo);
        params.put("carrierAccountList", carrierAccountList);

        params.put("socialCreditCode", "913304025888000741");

        // 创建 carrierInvoice
        Map<String, Object> carrierInvoice = new HashMap<>();
        carrierInvoice.put("invoiceHead", "张小虎自动化承运商");
        carrierInvoice.put("taxNo", "913304025888000741");
        params.put("carrierInvoice", carrierInvoice);

        params.put("invoiceId", 56465);
        params.put("cooperationAgreement", "test/byfkvndpqsqur6obz.jpg");
        params.put("businessType", "1");

        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "查询承运商列表", dependsOnMethods = "case067")
    public void case068() {
        String helpUrl = domain + "/tms-new/carrier/query/page-list";
        Map<String, Object> params = new HashMap<>();
        params.put("pageIndex", 1);
        params.put("pageSize", 10);
        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "查询承运商详情", dependsOnMethods = "case068")
    public void case069() {
        Long newcarrierid = carrierDao.getCarrierId(cityCarrierName);
        String helpUrl = domain + "/tms-new/carrier/query/detail?id=" + newcarrierid;
        assertResponse(sendRequest(helpUrl, null, true));
    }

    @Test(description = "编辑承运商", dependsOnMethods = "case069")
    public void case070() {
       Long newcarrierid = carrierDao.getCarrierId(cityCarrierName);
       int carrieraccountid = carrierAccountDao.getCarrierAccountId(newcarrierid);
       Long carrierinvoiceid = carrierInvoiceDao.getCarrierInvoiceId(newcarrierid);

        String helpUrl = domain + "/tms-new/carrier/upsert/edit";
        Map<String, Object> params = new HashMap<>();
        params.put("address", "西溪街道星晖商务中心");
        params.put("businessType", "1");

        // 创建 carrierAccountList
        List<Map<String, Object>> carrierAccountList = new ArrayList<>();
        Map<String, Object> accountInfo = new HashMap<>();
        accountInfo.put("account", "*********");
        accountInfo.put("accountAscription", "杭州");
        accountInfo.put("accountBank", "招商");
        accountInfo.put("accountName", "测试");
        accountInfo.put("carrierId", newcarrierid);
        accountInfo.put("id", carrieraccountid);  // 假设这是已有的 ID
        accountInfo.put("payType", 1);
        carrierAccountList.add(accountInfo);
        params.put("carrierAccountList", carrierAccountList);

        // 创建 carrierInvoice
        Map<String, Object> carrierInvoice = new HashMap<>();
        carrierInvoice.put("invoiceHead", "张小虎自动化承运商");
        carrierInvoice.put("taxNo", "913304025888000741");
        carrierInvoice.put("carrierId", newcarrierid);
        carrierInvoice.put("id", carrierinvoiceid);  // 假设这是已有的 ID
        params.put("carrierInvoice", carrierInvoice);

        params.put("carrierName", "张小虎自动化承运商");
        params.put("cooperationAgreement", "test/byfkvndpqsqur6obz.jpg");
        params.put("director", "测试");
        params.put("directorPhone", "***********");
        params.put("id", newcarrierid);
        params.put("socialCreditCode", "913304025888000741");

        String json = createJson(params);
        assertResponse(sendRequest(helpUrl, json, true));
    }

    @Test(description = "导出承运商列表", dependsOnMethods = "case070")
    public void case071() {
        String helpUrl = domain + "/tms-new/carrier/export-async/carrier-info";
        String json =  "{\"params\":\"{}\"}";
        assertResponse(sendRequest(helpUrl, json, true));
    }
}
