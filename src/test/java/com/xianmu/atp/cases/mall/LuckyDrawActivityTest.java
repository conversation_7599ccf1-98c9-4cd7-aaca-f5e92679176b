package com.xianmu.atp.cases.mall;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

/**
 * 抽奖活动接口测试用例
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Epic("mall")
@Feature("抽奖活动接口测试")
@Owner("ATP")
@Slf4j
public class LuckyDrawActivityTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    private static final String BASE_URL_TEMPLATE = "https://%sh5.summerfarm.net";
    private static final String QUERY_INFO_URL = "/luck/draw/activity/query/info";
    private static final String QUERY_DETAIL_URL = "/luck/draw/activity/query/detail";
    private static final String START_DRAW_URL = "/luck/draw/activity/upsert/start";
    private static final String QUERY_PRIZE_URL = "/luck/draw/activity/query/prize";

    // 测试数据常量
    private static final int RED_PACKET_RAIN_TYPE = 1;  // 红包雨类型
    private static final int DAILY_LOTTERY_TYPE = 2;    // 每日抽奖类型
    private static final int RED_PACKET_ACTIVITY_ID = 144;  // 红包雨活动ID
    private static final int LOTTERY_ACTIVITY_ID = 145;     // 抽奖活动ID

    /**
     * TC_API_LUCK_DRAW_QUERY_INFO_001
     * 用例名称: 查询红包雨活动信息-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动类型")
    @Description("验证根据活动类型查询红包雨活动信息的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testQueryRedPacketRainInfo() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"type\":%d}", RED_PACKET_RAIN_TYPE);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询红包雨活动信息响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");
        Assert.notNull(jsonObject.getJSONObject("data"), "数据不能为空");
        
        JSONObject data = jsonObject.getJSONObject("data");
        Assert.equals(data.getInteger("id"), RED_PACKET_ACTIVITY_ID, "活动ID应为144");
        Assert.equals(data.getInteger("type"), RED_PACKET_RAIN_TYPE, "活动类型应为1");
        Assert.notNull(data.getString("name"), "活动名称不能为空");

        Allure.addAttachment("查询红包雨活动信息响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_INFO_002
     * 用例名称: 查询每日抽奖活动信息-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动类型")
    @Description("验证根据活动类型查询每日抽奖活动信息的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testQueryDailyLotteryInfo() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"type\":%d}", DAILY_LOTTERY_TYPE);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询每日抽奖活动信息响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");
        Assert.notNull(jsonObject.getJSONObject("data"), "数据不能为空");
        
        JSONObject data = jsonObject.getJSONObject("data");
        Assert.equals(data.getInteger("id"), LOTTERY_ACTIVITY_ID, "活动ID应为145");
        Assert.equals(data.getInteger("type"), DAILY_LOTTERY_TYPE, "活动类型应为2");
        Assert.notNull(data.getString("name"), "活动名称不能为空");

        Allure.addAttachment("查询每日抽奖活动信息响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_INFO_003
     * 用例名称: 查询活动信息-无效活动类型
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动类型")
    @Description("验证传入无效活动类型时的错误处理")
    @Severity(SeverityLevel.NORMAL)
    public void testQueryInfoWithInvalidType() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造无效请求参数
        String requestBody = "{\"type\":999}";

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询无效活动类型响应: {}", response.body());

        // 断言验证 - 应该返回错误或空数据
        Assert.notNull(jsonObject, "响应不能为空");
        // 根据实际业务逻辑调整断言条件
        if (jsonObject.getInteger("status") != 0) {
            Assert.notNull(jsonObject.getString("msg"), "错误信息不能为空");
        }

        Allure.addAttachment("查询无效活动类型响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_INFO_004
     * 用例名称: 查询活动信息-缺少必填参数
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动类型")
    @Description("验证缺少必填参数type时的错误处理")
    @Severity(SeverityLevel.NORMAL)
    public void testQueryInfoWithMissingType() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造缺少type参数的请求
        String requestBody = "{}";

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("缺少type参数响应: {}", response.body());

        // 断言验证 - 应该返回参数错误
        Assert.notNull(jsonObject, "响应不能为空");
        // 根据实际业务逻辑调整断言条件
        if (jsonObject.getInteger("status") != 0) {
            Assert.notNull(jsonObject.getString("msg"), "错误信息不能为空");
        }

        Allure.addAttachment("缺少type参数响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_INFO_005
     * 用例名称: 查询活动信息-边界值测试
     * 优先级: P2
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动类型")
    @Description("验证活动类型边界值的处理")
    @Severity(SeverityLevel.MINOR)
    public void testQueryInfoWithBoundaryValues() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 测试边界值
        int[] boundaryValues = {0, -1, Integer.MAX_VALUE, Integer.MIN_VALUE};
        
        for (int value : boundaryValues) {
            String requestBody = String.format("{\"type\":%d}", value);

            HttpResponse response = HttpRequest.post(url)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(requestBody)
                    .execute();

            JSONObject jsonObject = JSONObject.parseObject(response.body());
            log.info("边界值{}测试响应: {}", value, response.body());

            // 基本验证
            Assert.notNull(jsonObject, "响应不能为空");
            
            Allure.addAttachment("边界值" + value + "测试响应", response.body());
        }
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_DETAIL_001
     * 用例名称: 根据活动ID查询红包雨详情-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动ID")
    @Description("验证根据活动ID查询红包雨活动详情的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testQueryRedPacketRainDetail() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_DETAIL_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"id\":%d,\"type\":%d}", RED_PACKET_ACTIVITY_ID, RED_PACKET_RAIN_TYPE);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询红包雨活动详情响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");
        Assert.notNull(jsonObject.getJSONObject("data"), "数据不能为空");

        JSONObject data = jsonObject.getJSONObject("data");
        Assert.equals(data.getInteger("id"), RED_PACKET_ACTIVITY_ID, "活动ID应为144");
        Assert.equals(data.getInteger("type"), RED_PACKET_RAIN_TYPE, "活动类型应为1");
        Assert.notNull(data.getString("name"), "活动名称不能为空");
        Assert.notNull(data.getString("startTime"), "开始时间不能为空");
        Assert.notNull(data.getString("endTime"), "结束时间不能为空");

        Allure.addAttachment("查询红包雨活动详情响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_DETAIL_002
     * 用例名称: 根据活动ID查询每日抽奖详情-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动ID")
    @Description("验证根据活动ID查询每日抽奖活动详情的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testQueryDailyLotteryDetail() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_DETAIL_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"id\":%d,\"type\":%d}", LOTTERY_ACTIVITY_ID, DAILY_LOTTERY_TYPE);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询每日抽奖活动详情响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");
        Assert.notNull(jsonObject.getJSONObject("data"), "数据不能为空");

        JSONObject data = jsonObject.getJSONObject("data");
        Assert.equals(data.getInteger("id"), LOTTERY_ACTIVITY_ID, "活动ID应为145");
        Assert.equals(data.getInteger("type"), DAILY_LOTTERY_TYPE, "活动类型应为2");
        Assert.notNull(data.getString("name"), "活动名称不能为空");
        Assert.notNull(data.getString("startTime"), "开始时间不能为空");
        Assert.notNull(data.getString("endTime"), "结束时间不能为空");

        Allure.addAttachment("查询每日抽奖活动详情响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_DETAIL_003
     * 用例名称: 查询活动详情-不存在的活动ID
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动信息-根据活动ID")
    @Description("验证查询不存在的活动ID时的错误处理")
    @Severity(SeverityLevel.NORMAL)
    public void testQueryDetailWithNonExistentId() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_DETAIL_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造不存在的活动ID请求
        String requestBody = "{\"id\":99999,\"type\":1}";

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询不存在活动ID响应: {}", response.body());

        // 断言验证 - 应该返回错误或空数据
        Assert.notNull(jsonObject, "响应不能为空");
        // 根据实际业务逻辑调整断言条件
        if (jsonObject.getInteger("status") != 0) {
            Assert.notNull(jsonObject.getString("msg"), "错误信息不能为空");
        } else {
            // 如果状态码为0，数据应该为空或null
            Object data = jsonObject.get("data");
            Assert.isTrue(data == null || data.toString().equals("null"), "不存在的活动应返回空数据");
        }

        Allure.addAttachment("查询不存在活动ID响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_START_001
     * 用例名称: 开始红包雨抽奖-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("开始抽奖")
    @Description("验证开始红包雨抽奖的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testStartRedPacketRainDraw() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + START_DRAW_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"activityId\":%d}", RED_PACKET_ACTIVITY_ID);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("开始红包雨抽奖响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");

        // 验证抽奖结果
        if (jsonObject.getJSONObject("data") != null) {
            JSONObject data = jsonObject.getJSONObject("data");
            Assert.notNull(data.get("merchantCouponVOS"), "抽奖结果不能为空");
        }

        Allure.addAttachment("开始红包雨抽奖响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_START_002
     * 用例名称: 开始每日抽奖-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("开始抽奖")
    @Description("验证开始每日抽奖的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testStartDailyLotteryDraw() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + START_DRAW_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"activityId\":%d}", LOTTERY_ACTIVITY_ID);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("开始每日抽奖响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");

        // 验证抽奖结果
        if (jsonObject.getJSONObject("data") != null) {
            JSONObject data = jsonObject.getJSONObject("data");
            Assert.notNull(data.get("merchantCouponVOS"), "抽奖结果不能为空");
        }

        Allure.addAttachment("开始每日抽奖响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_START_003
     * 用例名称: 开始抽奖-不存在的活动ID
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("开始抽奖")
    @Description("验证使用不存在的活动ID开始抽奖时的错误处理")
    @Severity(SeverityLevel.NORMAL)
    public void testStartDrawWithNonExistentActivityId() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + START_DRAW_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造不存在的活动ID请求
        String requestBody = "{\"activityId\":99999}";

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("不存在活动ID抽奖响应: {}", response.body());

        // 断言验证 - 应该返回错误
        Assert.notNull(jsonObject, "响应不能为空");
        if (jsonObject.getInteger("status") != 0) {
            Assert.notNull(jsonObject.getString("msg"), "错误信息不能为空");
        }

        Allure.addAttachment("不存在活动ID抽奖响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_START_004
     * 用例名称: 开始抽奖-缺少必填参数
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("开始抽奖")
    @Description("验证缺少必填参数activityId时的错误处理")
    @Severity(SeverityLevel.NORMAL)
    public void testStartDrawWithMissingActivityId() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + START_DRAW_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造缺少activityId参数的请求
        String requestBody = "{}";

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("缺少activityId参数抽奖响应: {}", response.body());

        // 断言验证 - 应该返回参数错误
        Assert.notNull(jsonObject, "响应不能为空");
        if (jsonObject.getInteger("status") != 0) {
            Assert.notNull(jsonObject.getString("msg"), "错误信息不能为空");
        }

        Allure.addAttachment("缺少activityId参数抽奖响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_PRIZE_001
     * 用例名称: 查询红包雨抽奖记录-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动抽奖记录")
    @Description("验证查询红包雨抽奖记录的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testQueryRedPacketRainPrize() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_PRIZE_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"id\":%d,\"type\":%d}", RED_PACKET_ACTIVITY_ID, RED_PACKET_RAIN_TYPE);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询红包雨抽奖记录响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");
        Assert.notNull(jsonObject.getJSONObject("data"), "数据不能为空");

        JSONObject data = jsonObject.getJSONObject("data");
        Assert.equals(data.getInteger("id"), RED_PACKET_ACTIVITY_ID, "活动ID应为144");
        Assert.notNull(data.get("merchantCouponVOS"), "抽奖记录不能为空");

        Allure.addAttachment("查询红包雨抽奖记录响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_QUERY_PRIZE_002
     * 用例名称: 查询每日抽奖记录-正常场景
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("查询活动抽奖记录")
    @Description("验证查询每日抽奖记录的正常功能")
    @Severity(SeverityLevel.BLOCKER)
    public void testQueryDailyLotteryPrize() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_PRIZE_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        // 构造请求参数
        String requestBody = String.format("{\"id\":%d,\"type\":%d}", LOTTERY_ACTIVITY_ID, DAILY_LOTTERY_TYPE);

        // 发送请求
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 验证响应
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("查询每日抽奖记录响应: {}", response.body());

        // 断言验证
        Assert.notNull(jsonObject, "响应不能为空");
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");
        Assert.notNull(jsonObject.getJSONObject("data"), "数据不能为空");

        JSONObject data = jsonObject.getJSONObject("data");
        Assert.equals(data.getInteger("id"), LOTTERY_ACTIVITY_ID, "活动ID应为145");
        Assert.notNull(data.get("merchantCouponVOS"), "抽奖记录不能为空");

        Allure.addAttachment("查询每日抽奖记录响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_COMPREHENSIVE_001
     * 用例名称: 抽奖活动完整流程测试
     * 优先级: P0
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"5"})
            })
    @Story("抽奖活动完整流程")
    @Description("验证抽奖活动的完整业务流程：查询活动信息->查询活动详情->开始抽奖->查询抽奖记录")
    @Severity(SeverityLevel.BLOCKER)
    public void testLuckyDrawCompleteFlow() throws InterruptedException {
        String env = EnvEnum.qa.getName();
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);
        String baseUrl = String.format(BASE_URL_TEMPLATE, env);

        // 步骤1: 查询红包雨活动信息
        String queryInfoUrl = baseUrl + QUERY_INFO_URL;
        String queryInfoBody = String.format("{\"type\":%d}", RED_PACKET_RAIN_TYPE);

        HttpResponse infoResponse = HttpRequest.post(queryInfoUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(queryInfoBody)
                .execute();

        JSONObject infoJson = JSONObject.parseObject(infoResponse.body());
        Assert.equals(infoJson.getInteger("status"), 0, "查询活动信息失败");
        Assert.equals(infoJson.getJSONObject("data").getInteger("id"), RED_PACKET_ACTIVITY_ID, "活动ID不匹配");

        log.info("步骤1-查询活动信息成功");

        // 步骤2: 查询活动详情
        String queryDetailUrl = baseUrl + QUERY_DETAIL_URL;
        String queryDetailBody = String.format("{\"id\":%d,\"type\":%d}", RED_PACKET_ACTIVITY_ID, RED_PACKET_RAIN_TYPE);

        HttpResponse detailResponse = HttpRequest.post(queryDetailUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(queryDetailBody)
                .execute();

        JSONObject detailJson = JSONObject.parseObject(detailResponse.body());
        Assert.equals(detailJson.getInteger("status"), 0, "查询活动详情失败");
        Assert.equals(detailJson.getJSONObject("data").getInteger("id"), RED_PACKET_ACTIVITY_ID, "活动详情ID不匹配");

        log.info("步骤2-查询活动详情成功");

        // 步骤3: 开始抽奖
        String startDrawUrl = baseUrl + START_DRAW_URL;
        String startDrawBody = String.format("{\"activityId\":%d}", RED_PACKET_ACTIVITY_ID);

        HttpResponse drawResponse = HttpRequest.post(startDrawUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(startDrawBody)
                .execute();

        JSONObject drawJson = JSONObject.parseObject(drawResponse.body());
        Assert.equals(drawJson.getInteger("status"), 0, "开始抽奖失败");

        log.info("步骤3-开始抽奖成功");

        // 等待抽奖处理完成
        Thread.sleep(2000);

        // 步骤4: 查询抽奖记录
        String queryPrizeUrl = baseUrl + QUERY_PRIZE_URL;
        String queryPrizeBody = String.format("{\"id\":%d,\"type\":%d}", RED_PACKET_ACTIVITY_ID, RED_PACKET_RAIN_TYPE);

        HttpResponse prizeResponse = HttpRequest.post(queryPrizeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(queryPrizeBody)
                .execute();

        JSONObject prizeJson = JSONObject.parseObject(prizeResponse.body());
        Assert.equals(prizeJson.getInteger("status"), 0, "查询抽奖记录失败");

        log.info("步骤4-查询抽奖记录成功");

        // 添加所有响应到报告
        Allure.addAttachment("完整流程-查询活动信息", infoResponse.body());
        Allure.addAttachment("完整流程-查询活动详情", detailResponse.body());
        Allure.addAttachment("完整流程-开始抽奖", drawResponse.body());
        Allure.addAttachment("完整流程-查询抽奖记录", prizeResponse.body());

        log.info("抽奖活动完整流程测试通过");
    }

    /**
     * TC_API_LUCK_DRAW_DATABASE_001
     * 用例名称: 抽奖活动数据库验证测试
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"5"})
            })
    @Story("抽奖活动数据库验证")
    @Description("验证抽奖活动的数据库记录和数据一致性")
    @Severity(SeverityLevel.NORMAL)
    public void testLuckyDrawDatabaseValidation() throws InterruptedException {
        String env = EnvEnum.qa.getName();
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);
        String baseUrl = String.format(BASE_URL_TEMPLATE, env);

        // 获取数据库连接
        HikariDataSource ds = SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);

        try {
            // 清理测试数据
            String cleanupSql1 = "DELETE FROM merchant_coupon WHERE m_id = " + MallUserEnum.mallAutoUser2.getMid();
            String cleanupSql2 = "DELETE FROM lucky_draw_activity_record WHERE m_id = " + MallUserEnum.mallAutoUser2.getMid();
            String resetSql = "UPDATE lucky_draw_activity_equity_package SET surplus_quantity = 100000 WHERE activity_id IN (144, 145)";

            jdbcTemplate.execute(cleanupSql1);
            jdbcTemplate.execute(cleanupSql2);
            jdbcTemplate.execute(resetSql);

            log.info("数据库清理完成");

            // 执行抽奖操作
            String startDrawUrl = baseUrl + START_DRAW_URL;
            String startDrawBody = String.format("{\"activityId\":%d}", RED_PACKET_ACTIVITY_ID);

            HttpResponse drawResponse = HttpRequest.post(startDrawUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(startDrawBody)
                    .execute();

            JSONObject drawJson = JSONObject.parseObject(drawResponse.body());
            Assert.equals(drawJson.getInteger("status"), 0, "抽奖操作失败");

            // 等待数据库更新
            Thread.sleep(3000);

            // 验证抽奖记录
            String recordSql = "SELECT COUNT(1) as count FROM lucky_draw_activity_record WHERE m_id = " +
                              MallUserEnum.mallAutoUser2.getMid() + " AND activity_id = " + RED_PACKET_ACTIVITY_ID;

            boolean hasRecord = SqlExcutorUtil.checkSqlValue(jdbcTemplate, recordSql, "count", "1", 5);
            Assert.isTrue(hasRecord, "抽奖记录未正确写入数据库");

            log.info("抽奖记录数据库验证通过");

            // 验证优惠券发放
            String couponSql = "SELECT COUNT(1) as count FROM merchant_coupon WHERE m_id = " + MallUserEnum.mallAutoUser2.getMid();

            boolean hasCoupon = SqlExcutorUtil.checkSqlValue(jdbcTemplate, couponSql, "count", "1", 5);
            Assert.isTrue(hasCoupon, "优惠券未正确发放到数据库");

            log.info("优惠券发放数据库验证通过");

            Allure.addAttachment("数据库验证-抽奖响应", drawResponse.body());

        } finally {
            // 清理测试数据
            String cleanupSql1 = "DELETE FROM merchant_coupon WHERE m_id = " + MallUserEnum.mallAutoUser2.getMid();
            String cleanupSql2 = "DELETE FROM lucky_draw_activity_record WHERE m_id = " + MallUserEnum.mallAutoUser2.getMid();
            String resetSql = "UPDATE lucky_draw_activity_equity_package SET surplus_quantity = 100000 WHERE activity_id IN (144, 145)";

            jdbcTemplate.execute(cleanupSql1);
            jdbcTemplate.execute(cleanupSql2);
            jdbcTemplate.execute(resetSql);

            ds.close();
            log.info("测试数据清理完成");
        }
    }

    /**
     * TC_API_LUCK_DRAW_UNAUTHORIZED_001
     * 用例名称: 未授权访问测试
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("权限验证")
    @Description("验证未授权用户访问抽奖接口时的错误处理")
    @Severity(SeverityLevel.NORMAL)
    public void testUnauthorizedAccess() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;

        // 不传token的请求
        String requestBody = String.format("{\"type\":%d}", RED_PACKET_RAIN_TYPE);

        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(requestBody)
                .execute();

        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("未授权访问响应: {}", response.body());

        // 验证应该返回授权错误
        Assert.notNull(jsonObject, "响应不能为空");
        // 根据实际业务逻辑调整断言条件
        if (jsonObject.getInteger("status") != 0) {
            Assert.notNull(jsonObject.getString("msg"), "错误信息不能为空");
        }

        Allure.addAttachment("未授权访问响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_INVALID_TOKEN_001
     * 用例名称: 无效token测试
     * 优先级: P1
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("权限验证")
    @Description("验证使用无效token访问抽奖接口时的错误处理")
    @Severity(SeverityLevel.NORMAL)
    public void testInvalidToken() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;

        // 使用无效token
        String invalidToken = "invalid_token_12345";
        String requestBody = String.format("{\"type\":%d}", RED_PACKET_RAIN_TYPE);

        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", invalidToken)
                .body(requestBody)
                .execute();

        JSONObject jsonObject = JSONObject.parseObject(response.body());
        log.info("无效token访问响应: {}", response.body());

        // 验证应该返回token错误
        Assert.notNull(jsonObject, "响应不能为空");
        // 根据实际业务逻辑调整断言条件
        if (jsonObject.getInteger("status") != 0) {
            Assert.notNull(jsonObject.getString("msg"), "错误信息不能为空");
        }

        Allure.addAttachment("无效token访问响应", response.body());
    }

    /**
     * TC_API_LUCK_DRAW_PERFORMANCE_001
     * 用例名称: 接口性能测试
     * 优先级: P2
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("性能测试")
    @Description("验证抽奖接口的响应时间性能")
    @Severity(SeverityLevel.MINOR)
    public void testApiPerformance() {
        String env = EnvEnum.qa.getName();
        String url = String.format(BASE_URL_TEMPLATE, env) + QUERY_INFO_URL;
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);
        String requestBody = String.format("{\"type\":%d}", RED_PACKET_RAIN_TYPE);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(requestBody)
                .execute();

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;

        log.info("接口响应时间: {}ms", responseTime);

        // 验证响应时间应小于3秒
        Assert.isTrue(responseTime < 3000, "接口响应时间超过3秒: " + responseTime + "ms");

        // 验证响应正确性
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        Assert.equals(jsonObject.getInteger("status"), 0, "状态码应为0");

        Allure.addAttachment("性能测试响应时间", responseTime + "ms");
        Allure.addAttachment("性能测试响应内容", response.body());
    }
}
