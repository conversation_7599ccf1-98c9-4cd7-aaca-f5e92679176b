package com.xianmu.atp.cases.mall;
import cn.hutool.core.lang.Console;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.*;
import com.xianmu.atp.dal.model.MerchantCoupon;
import com.xianmu.atp.util.testng.XMListener;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.xianmu.atp.util.http.LoginHttp;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/**
 * 类<code>Doc</code>用于：TODO
 * 下单场景
 * <AUTHOR>
 * @ClassName Order
 * @date 2024-11-04
 */
@Epic("注解测试")
@Feature("下单场景")
@Owner("xianmu")
@Slf4j
public class OrderTest extends BaseTest {

    @Resource
    private MerchantDao merchantDao;

    @Resource
    private MerchantCouponDao merchantCouponDao;

    @Resource
    private ShoppingCartDao shoppingCartDao;

    @Resource
    private AreaStoreDao areaStoreDao;

    @Resource
    private AreaSkuDao areaSkuDao;

    @Resource
    private LoginHttp login;


    @Value("${app.environment}")
    private String env;

    @Value("${app.dev-domain}")
    private String devDomain;

    @Value("${app.merchantId}")
    private long merchantId;

    @Value("592811381576")
    private String salesAgencySku;

    @Value("308354112550")
    private String nonSalesAgencySku;

    @Value("342600")
    private String contactId;

    @Value("344065")
    private long mId;


    @Story("下单场景")
    @Owner("xianmu")
    @Test(description = "代下单")
    public void case05(){
        Map<String, String> map = new HashMap<>();
        map.put("test","test");
        String token = login.login();
        log.info("token:{}", token);
        String domain = "https://" + env + "admin.summerfarm.net";
        Allure.addAttachment("定位符：", token);
        String helpUrl = domain + "/help-order";
        String json = "{\n" +
                "  \"mcontact\": \"图谱唐2\",\n" +
                "  \"contactId\": 348521,\n" +
                "  \"helpType\": 3,\n" +
                "  \"mphone\": \"18611117293\",\n" +
                "  \"remark\": \"账期客户下单\",\n" +
                "  \"giftItems\": [],\n" +
                "  \"orderItems\": [\n" +
                "    {\n" +
                "      \"amount\": 5,\n" +
                "      \"searchInfo\": \"\",\n" +
                "      \"isEdit\": true,\n" +
                "      \"name\": \"\",\n" +
                "      \"baseSaleQuantity\": 1,\n" +
                "      \"weight\": \"2个*1KG/一级/可口\",\n" +
                "      \"pdName\": \"zt测试桃之夭夭\",\n" +
                "      \"baseSaleUnit\": 1,\n" +
                "      \"sku\": \"561568850421\",\n" +
                "      \"categoryId\": 561\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        HttpResponse response = HttpRequest.post(helpUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
        Reporter.log("log-test");
        log.info(JSON.parseObject(response.body()).getJSONObject("data").getString("orderNo"));
        Assert.assertTrue(JSON.parseObject(response.body()).getJSONObject("data").get("orderNo") != null, "下单失败");
    }

    @Story("代销场景")
    @Owner("xianmu")
    @Test(description = "代销不入仓&非代销不入仓有多张优惠券下单", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            })
    public void case06(){
        String mallToken = login.wechatLogin();
        String recommendUrl = devDomain + "/recommend/1/6";
        String cartUrl = devDomain + "/shopping/cart/upsert/insert";
        String preOrderUrl = devDomain + "/order/query/pre-order/v3";
        Map<String ,Object> map = new HashMap<>();
        map.put("type", 2);
        HttpResponse response = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .header("token", mallToken)
                .form(map)
                .execute();
//        Allure.addAttachment("response返回","application/json", response.body());
        /* 更新余额 */
        int res = merchantDao.updateAmountByMerchantId(merchantId, 10000);
        Console.log("te06:{}", res);
        try {
            /* 删除购物车商品 */
            int i = shoppingCartDao.deleteByAccountId(merchantId);
            /* 删除优惠券 */
            merchantCouponDao.deleteByCouponId(merchantId);
            /* 更新库存 */
            areaStoreDao.updateOnlineQuantity(salesAgencySku);
            areaStoreDao.updateOnlineQuantity(nonSalesAgencySku);
            Console.log("te066:{}", i);
        }catch (Exception e){
            e.printStackTrace();
        }
        /* 添加代销商品进购物车 */
        JSONObject body = JSON.parseObject("{\n" +
                "    \"quantity\": 2,\n" +
                "    \"parentSku\": \"\",\n" +
                "    \"contactId\": \"${contactId}\",\n" +
                "    \"bizId\": 0,\n" +
                "    \"sku\": \"${sku}\",\n" +
                "    \"productType\": 0\n" +
                "}");
        body.put("contactId", contactId);
        body.put("sku", salesAgencySku);
        HttpResponse salesAgencyResponse = HttpRequest.post(cartUrl)
             .header(Header.CONTENT_TYPE, "application/json")
             .header("token", mallToken)
             .body(body.toJSONString())
             .execute();
        /* 添加non代销商品进购物车 */
        body.put("sku", nonSalesAgencySku);
        HttpResponse nonSalesAgencyResponse = HttpRequest.post(cartUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", mallToken)
                .body(body.toJSONString())
                .execute();
        /* 发放优惠券 */
        MerchantCoupon merchantCoupon = new MerchantCoupon();
        merchantCoupon.setMId(mId);
        merchantCoupon.setCouponId(18631);
        Timestamp vaildTimestamp = Timestamp.valueOf("2088-04-23 23:59:59");
        Timestamp addTimestamp = new Timestamp(System.currentTimeMillis());
        merchantCoupon.setVaildDate(vaildTimestamp);
        merchantCoupon.setAddTime(addTimestamp);
        merchantCoupon.setUsed(0);
        merchantCoupon.setReceiveType(5);
        merchantCoupon.setStartTime(addTimestamp);
        merchantCouponDao.insertCoupon(merchantCoupon);
        merchantCoupon.setCouponId(18252);
        merchantCouponDao.insertCoupon(merchantCoupon);
        /* 下单 */
        areaSkuDao.updateAreaSku(salesAgencySku, 15);
        areaSkuDao.updateAreaSku(nonSalesAgencySku, 25);
        JSONObject preOrderBody = JSON.parseObject("{\n" +
                "    \"isTakePrice\": 0,\n" +
                "    \"contactId\": \"${contactId}\",\n" +
                "    \"orderItemList\": [\n" +
                "        {\n" +
                "            \"sku\": \"${sku}\",\n" +
                "            \"productType\": 0,\n" +
                "            \"quantity\": 2\n" +
                "        },\n" +
                "        {\n" +
                "            \"sku\": \"${sku2}\",\n" +
                "            \"productType\": 0,\n" +
                "            \"quantity\": 2\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        preOrderBody.put("contactId", contactId);
        JSONPath.set(preOrderBody, "$.orderItemList[0].sku", salesAgencySku);
        JSONPath.set(preOrderBody, "$.orderItemList[1].sku", nonSalesAgencySku);
        HttpResponse preOrderResponse = HttpRequest.post(preOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json").header("token", mallToken)
                .body(preOrderBody.toJSONString())
                .execute();
//        Assert.assertEquals(JSON.parseObject(preOrderResponse.body()).getJSONObject("data").getString("itemOriginalPrice").equals("80"), true);
    }
}
