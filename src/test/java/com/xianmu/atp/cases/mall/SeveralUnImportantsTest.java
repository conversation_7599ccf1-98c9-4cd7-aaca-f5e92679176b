package com.xianmu.atp.cases.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("一些不重要的接口合集")
@Owner("zyl")
@Slf4j
public class SeveralUnImportantsTest extends BaseTest {
    @Autowired
    private DynamicDataSourceProperties properties;

    //用户提交拜访记录相关评价，等方洲完成拜访计划的编写

    @Test
    @Story("年报接口")
    public void testAnnualReport() {
        String env= EnvEnum.qa.getName();
        int mId= MallUserEnum.mallAutoUser.getMid();
        String phone=MallUserEnum.mallAutoUser.getPhone();
        String annualReportUrl="https://"+env+"h5.summerfarm.net/annual/statement/query/detail/";
        annualReportUrl+=mId;
        String token= MallOrder.getXianmuMallUserToken(phone,env);
        HttpResponse annualReportResponse =   HttpRequest.post(annualReportUrl)
                .header("token", token)
                .execute();
        Assert.isTrue(annualReportResponse.isOk(),"年报接口报错");
    }

    @Test
    @Story("海报跳转详情")
    public void testBannerDetail(){

        String env= EnvEnum.qa.getName();
        int mId= MallUserEnum.mallAutoUser.getMid();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String bannerDetailUrl1="https://"+env+"h5.summerfarm.net/banner/query/detail?id="+66606;
        HttpResponse response1 = HttpRequest.post(bannerDetailUrl1)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();


        String bannerDetailUrl2="https://"+env+"h5.summerfarm.net/banner/query/detail?id="+66607;
        HttpResponse response2 = HttpRequest.post(bannerDetailUrl2)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();

        String bannerDetailUrl3="https://"+env+"h5.summerfarm.net/banner/query/detail?id="+66608;
        HttpResponse response3 = HttpRequest.post(bannerDetailUrl3)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .execute();

        String bannerDetailUrl4="https://"+env+"h5.summerfarm.net/banner/query/expired?pageNo=1&pageSize=10";
        HttpResponse response4 = HttpRequest.post(bannerDetailUrl4)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body("{\n" +
                        "    \"pageNo\": 1,\n" +
                        "    \"pageSize\": 10\n" +
                        "  }")
                .execute();

        Assert.isTrue(response1.isOk(),"海报跳转省心送接口报错");
        Assert.isTrue(response2.isOk(),"海报跳转抽奖接口报错");
        Assert.isTrue(response3.isOk(),"海报跳转临保品接口报错");
        Assert.isTrue(response4.isOk(),"临保品列表获取接口报错");
    }

    @Test
    @Story("APP就绪接口")
    public void testAppReady(){
        String env= EnvEnum.qa.getName();
        String appReadyUrl="https://"+env+"h5.summerfarm.net/appHealth/isAvailable";
        HttpResponse response3 = HttpRequest.get(appReadyUrl)
                .execute();
        Assert.isTrue(response3.isOk(),"准备就绪接口报错");
    }

    @Test
    @Story("BD信息请求接口")
    public void testBdInfo(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String bdInfoUrl="https://"+env+"h5.summerfarm.net/merchant/query/bd-info";
        HttpResponse response3 = HttpRequest.post(bdInfoUrl)
                .header("token", token)
                .execute();
        Assert.isTrue(response3.isOk(),"BD信息接口报错");
    }

    @Test
    @Story("首页通用推荐接口")
    public void testRecommend(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String recommendUrl="https://"+env+"h5.summerfarm.net/home/<USER>/commonly-recommended";
        HttpResponse response3 = HttpRequest.post(recommendUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body("{\"pageIndex\":1,\"pageSize\":6}")
                .execute();
        Assert.isTrue(response3.isOk(),"首页通用推荐接口");
    }

    @Test
    @Story("工商信息请求接口")
    public void testBusinessAll(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String businessInfoUrl="https://"+env+"h5.summerfarm.net/invoice-config/businessAll";
        HttpResponse response3 = HttpRequest.get(businessInfoUrl)
                .header("token", token)
                .execute();
        Assert.isTrue(response3.isOk(),"BD工商接口报错");
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("发票相关")
    public void testInvoice(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String createUrl = "https://qah5.summerfarm.net/invoice-config/businessSubmitted";
        Map<String, Object> body = new HashMap<>();
        body.put("defaultFlag", "0");
        body.put("businessLicenseAddress", "3d3d4720bc1bec5983d86281d864a5d4");
        body.put("openBank", "6");
        body.put("taxNumber", "91330106MA27XXN62W");
        body.put("mailAddress", "4");
        body.put("linkMethod", "3");
        body.put("companyEmail", "<EMAIL>");
        body.put("companyPhone", "2");
        body.put("merchantId", "350935");
        body.put("companyAddress", "1");
        body.put("companyReceiver", "7");
        body.put("openAccount", "5");
        body.put("invoiceTitle", "杭州鲜沐科技有限公司");
        String bodyStr = JSONObject.toJSONString(body);
        HttpResponse response = HttpRequest.post(createUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr)
                .execute();
        Assert.isTrue(response.isOk(),"创建发票报错");

        String listUrl = "https://qah5.summerfarm.net/invoice-config/list-merchant";
        HttpResponse response2 = HttpRequest.post(listUrl)
                .header("token", token)
                .execute();

        JSONObject jsonObject = JSONObject.parseObject(response2.body());
        int invoiceId=jsonObject.getJSONArray("data").getJSONObject(0).getInteger("id");

        String defaultUrl = "https://qah5.summerfarm.net/invoice-config/upsert/default-invoice";
        Map<String, Object> defaultBody = new HashMap<>();
        defaultBody.put("defaultFlag", "0");
        defaultBody.put("merchantId", "350935");
        defaultBody.put("adminId", "");
        defaultBody.put("id", invoiceId);
        String bodyStr2 = JSONObject.toJSONString(defaultBody);
        HttpResponse response3 = HttpRequest.post(defaultUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr2)
                .execute();


        String delUrl = "https://qah5.summerfarm.net/invoice-config/upsert/del-invoice";
        Map<String, Object> body4 = new HashMap<>();
        body4.put("id", invoiceId);
        String bodyStr4 = JSONObject.toJSONString(body4);
        HttpResponse response4 = HttpRequest.post(delUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr4)
                .execute();

        Assert.isTrue(response2.isOk(),"发票列表报错");
        Assert.isTrue(response3.isOk(),"设置默认发票报错");
        Assert.isTrue(response4.isOk(),"删除发票报错");

        String orderInvoiceHistoryUrl = "https://qah5.summerfarm.net/financial-invoice/invoiceQuery/1/10";
        HttpResponse response5 = HttpRequest.post(orderInvoiceHistoryUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body("{\"contactId\":"+ AddressEnum.mallAutoAddress.getContactId() +"}")
                .execute();
        Assert.isTrue(response5.isOk(),"已开发票订单列表报错");

    }

    @Test
    @Story("余额变动记录")
    public void testBalanceHistory(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String url = "https://"+env+"h5.summerfarm.net/recharge/recharge/record/1/10?type=";
        HttpResponse response = HttpRequest.get(url)
                .header("token", token)
                .execute();
        Assert.isTrue(response.isOk(),"余额变动记录报错");
    }

    @Test
    @Story("配送信息")
    public void testDFrame(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String dFrameUrl = "https://"+env+"h5.summerfarm.net/delivery-plan/query/time-frame";
        HttpResponse response = HttpRequest.post(dFrameUrl)
                .header("token", token)
                .header("Content-Type", "application/json")
                .body("{\"contactId\":"+AddressEnum.mallAutoAddress.getContactId()+"}")
                .execute();
        Assert.isTrue(response.isOk(),"配送信息报错");
    }

    @Test
    @Story("回访评价")
    public void testFeedBack(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);

        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String deleteSql1="update follow_up_record set feedback=null,add_time=now() where id=32";
        jdbcTemplate.execute(deleteSql1);

        String dFrameUrl = "https://"+env+"h5.summerfarm.net/follow-up-record/feedback";
        Map<String, Object> body = new HashMap<>();
        body.put("followRecordId", 32);
        body.put("feedBack", "已登门拜访");
        HttpResponse response = HttpRequest.post(dFrameUrl)
                .header("token", token)
                .header("Content-Type", "application/json")
                .body(JSONObject.toJSONString(body))
                .execute();
        Assert.isTrue(response.isOk(),"填写评价报错");
    }




}
