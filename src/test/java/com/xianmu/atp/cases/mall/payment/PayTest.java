package com.xianmu.atp.cases.mall.payment;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPRedisRequestDto;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.redis.DefaultRedisConfig;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.hutool.http.HttpRequest.*;

@Epic("mall")
@Feature("支付测试")
@Owner("zyl")
@Slf4j
public class PayTest extends BaseTest {

    @Value("23")
    private int h5DinRuleId;

    @Value("13")
    private int h5wxRuleId;

    @Value("3")
    private int h5CbdRuleId;

    @Value("1130")
    private int routingId;


    @Resource
    private DefaultRedisConfig defaultRedisConfig;

    @Autowired
    private DynamicDataSourceProperties properties;

    @Value("2985")
    private int timingRuleId;


    @Test
    public void payOldWxPayTest() throws InterruptedException {
        String env="qa";
        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);

        Map<String,Object> timingBodyMap = new HashMap<>();
        timingBodyMap.put("timingRuleId",timingRuleId);
        timingBodyMap.put("mphone", MallUserEnum.mallAutoUser.getPhone());
        timingBodyMap.put("contactId", AddressEnum.mallAutoAddress.getContactId());
        timingBodyMap.put("mcontact","dev");
        timingBodyMap.put("merchantCouponId","");
        timingBodyMap.put("quantity",6);
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/timing")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(timingBodyMap))
                .execute();
        JSONObject jsonObject= JSONObject.parseObject(response.body());
        String timingOrderNo=jsonObject.getString("data");

        String payUrl="https://"+env+"h5.summerfarm.net/wechatpay/JsapiPay";

        HttpResponse response2 = post(payUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .body("type=0&orderNo="+timingOrderNo)
                .execute();

        //Assert.isTrue(response2.body().contains("PARAM_ERROR"),"微信支付签名判断失败");

    }

    @Test
    public void payH5Test() throws InterruptedException {
        String env="qa";
        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);

        Thread.sleep(2000);

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);

        String token=MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        mallOrder.sendWithToken(false,token);

        String sql1="update payment_rule_routing set rule_id="+h5DinRuleId+" where id="+routingId;
        jdbcTemplate.execute(sql1);

        HttpResponse dinREsponse = payRequest(mallOrder.getMasterOrderNo(),1,0,token,env);
        //Assert.isTrue(dinREsponse.body().contains("智付请求响应异常"),"智付前置判断失败");

        mallOrder.sendWithToken(false,token);

        String sql2="update payment_rule_routing set rule_id="+h5wxRuleId+" where id="+routingId;
        jdbcTemplate.execute(sql2);
        HttpResponse wxResponse =  payRequest(mallOrder.getMasterOrderNo(),1,0,token,env);
        //Assert.isTrue(wxResponse.body().contains("PARAM_ERROR"),"微信前置判断失败");

        mallOrder.sendWithToken(false,token);

        String sql3="update payment_rule_routing set rule_id="+h5CbdRuleId+" where id="+routingId;
        jdbcTemplate.execute(sql3);
        HttpResponse cbdResponse =  payRequest(mallOrder.getMasterOrderNo(),1,0,token,env);

        //Assert.isTrue(cbdResponse.body().contains("qrCode"),"招行支付二维码获取失败");


        mallOrder.sendWithToken(false,token);
        HttpResponse cbdQRResponse =  payRequest(mallOrder.getMasterOrderNo(),1,2,token,env);
        //Assert.isTrue(cbdQRResponse.body().contains("招行支付配置异常"),"招行支付配置判断失败");
        //招行支付配置异常

        mallOrder.sendWithToken(false,token);
        HttpResponse xianmuCardResponse =  payRequest(mallOrder.getMasterOrderNo(),1,1,token,env);
        //Assert.isTrue(xianmuCardResponse.body().contains("请求成功"),"鲜沐卡支付失败");

        mallOrder.sendWithToken(false,token);
        HttpResponse billResponse =  payRequest(mallOrder.getMasterOrderNo(),1,3,token,env);
        //Assert.isTrue(billResponse.body().contains("不支持该付款方式"),"付款方式未填写判断失败");
        //不支持该付款方式

        mallOrder.sendWithToken(false,token);
        HttpResponse aliPayResponse =  payRequest(mallOrder.getMasterOrderNo(),1,4,token,env);
        //Assert.isTrue(aliPayResponse.body().contains("暂无可用的支付方式"),"空支付方式获取判断失败");
        //暂无可用的支付方式

    }

    public HttpResponse payRequest(String masterOrderNo,int bizType,int paymentMethod,String token,String env){
        String payUrl = "https://" + env + "h5.summerfarm.net/payment/union/pay";

        Map<String, Object> payRequestMap = new HashMap<>();
        // 业务类型（0、订单支付-省心送 1、主订单支付）
        payRequestMap.put("bizType", bizType);
        // 订单号
        payRequestMap.put("bizOrderNo",masterOrderNo);
        //商城用户所见的支付方式 例如：0、微信 1、鲜沐卡 2、付款码、3、账期 4、支付宝
        payRequestMap.put("paymentMethod", paymentMethod);

        HttpResponse response = post(payUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(JSONObject.toJSONString(payRequestMap))
                .execute();

        return response;

    }


    @Test
    public void statementTest() throws InterruptedException {
        String env="qa";
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String url="https://"+env+"h5.summerfarm.net/statement";
        String url1=url+"?date=********&accountId=10295&payType=微信支付";
        HttpResponse response1 = HttpRequest.get(url1)
                .header("token", token)
                .execute();
        //Assert.isTrue(response1.body().contains("微信配置异常"),"获取微信配置判断失败");

        String url2=url+"?date=********&accountId=10295&payType=招行支付";
        HttpResponse response2 = HttpRequest.get(url2)
                .header("token", token)
                .execute();
        //Assert.isTrue(response2.body().contains("招行支付配置异常"),"获取招行支付配置判断失败");


    }
}
