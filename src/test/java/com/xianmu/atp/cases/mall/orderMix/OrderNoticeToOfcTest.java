package com.xianmu.atp.cases.mall.orderMix;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.dal.dao.MerchantDao;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.http.LoginHttp;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.Reporter;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@Epic("mall")
@Feature("订单toOFC后置链路")
@Owner("zyl")
@Slf4j
public class OrderNoticeToOfcTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private LoginHttp login;
    @Story("同时下单自营和全品类，校验OFC是否生成数据且有2个配送日期")
    @Test()
    public void syncToOfcTest() throws InterruptedException {
        //支持增加多个sku，需要先维护好对应的sku、地址、用户
        MallOrder order = new MallOrder();
        order.addEnv(EnvEnum.qa);
        order.addAddress(AddressEnum.mallAutoAddress);
        order.addSku(SkuEnum.skuForSpuStartNum,6);
        order.addSku(SkuEnum.allCategory,6);
        //order.addSku(SkuEnum.skuForAllCategory,1);  这里是因为这个sku失效了
        order.send(true);
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.cosfodb);
        JdbcTemplate saasJdbcTemplate = new JdbcTemplate(ds);
        Assert.isTrue(SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,"select count(distinct fulfillment_time) as diffDeliveryCount from ofcdb.fulfillment_order where out_order_no in ('"+order.getOrderNo()+"','"+order.getSecondOrderNo()+"');", "diffDeliveryCount", "2", 20),"自营和全品类下单OFC校验失败");
        ds.close();
        //System.out.println(ofcOrderDao.getOrderNo("0124XXJU461220154878"));
    }

    @Resource
    private AreaStoreDao areaStoreDao;

    @Resource
    private MerchantDao merchantDao;


    @Test
    @Story("pop基础下单")
    public void testPopOrder() throws InterruptedException {
        areaStoreDao.setOnlineQuantity(SkuEnum.skuPop.getSku(), 479);
        merchantDao.updateAmountByMerchantId(MallUserEnum.popUser.getMid(), 1000000);
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.popUser);
        mallOrder.addAddress(AddressEnum.popAddress);
        mallOrder.addSku(SkuEnum.skuPop,2);
        mallOrder.send(true);

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.cosfodb);
        JdbcTemplate saasJdbcTemplate = new JdbcTemplate(ds);
        Assert.isTrue(SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,"select count(distinct fulfillment_time) as diffDeliveryCount from ofcdb.fulfillment_order where out_order_no in ('"+mallOrder.getOrderNo()+"');", "diffDeliveryCount", "1", 20),"pop下单OFC服务校验失败");
        ds.close();
    }


    @Story("代下单")
    @Test
    public void testHelpOrder() throws InterruptedException {
        Map<String, String> map = new HashMap<>();
        map.put("test","test");
        String token = login.login();
        String domain = "https://" + "qa" + "admin.summerfarm.net";
        Allure.addAttachment("定位符：", token);
        String helpUrl = domain + "/help-order";
        String json = "{\n" +
                "  \"mcontact\": \"图谱唐2\",\n" +
                "  \"contactId\": 348521,\n" +
                "  \"helpType\": 3,\n" +
                "  \"mphone\": \"18611117293\",\n" +
                "  \"remark\": \"账期客户下单\",\n" +
                "  \"giftItems\": [],\n" +
                "  \"orderItems\": [\n" +
                "    {\n" +
                "      \"amount\": 5,\n" +
                "      \"searchInfo\": \"\",\n" +
                "      \"isEdit\": true,\n" +
                "      \"name\": \"\",\n" +
                "      \"baseSaleQuantity\": 1,\n" +
                "      \"weight\": \"2个*1KG/一级/可口\",\n" +
                "      \"pdName\": \"zt测试桃之夭夭\",\n" +
                "      \"baseSaleUnit\": 1,\n" +
                "      \"sku\": \"561568850421\",\n" +
                "      \"categoryId\": 561\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        HttpResponse response = HttpRequest.post(helpUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(json)
                .execute();
        Reporter.log("log-test");
        log.info(JSON.parseObject(response.body()).getJSONObject("data").getString("orderNo"));
        org.testng.Assert.assertTrue(JSON.parseObject(response.body()).getJSONObject("data").get("orderNo") != null, "下单失败");
        String orderNo = JSON.parseObject(response.body()).getJSONObject("data").getString("orderNo");
        if(orderNo == null){
            orderNo="123";
        }

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.cosfodb);
        JdbcTemplate saasJdbcTemplate = new JdbcTemplate(ds);
        Assert.isTrue(SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,"select count(distinct fulfillment_time) as diffDeliveryCount from ofcdb.fulfillment_order where out_order_no in ('"+orderNo+"');", "diffDeliveryCount", "1", 20),"代下单OFC服务校验失败");
        ds.close();
    }
}
