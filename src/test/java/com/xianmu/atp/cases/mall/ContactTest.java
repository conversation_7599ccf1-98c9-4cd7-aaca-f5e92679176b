package com.xianmu.atp.cases.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.MerchantDao;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.JsonDealer;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Time;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Epic("mall")
@Feature("商城地址")
@Owner("zyl")
@Slf4j
public class ContactTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private MerchantDao merchantDao;

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("地址新增删除")
    public void testContact() throws InterruptedException {
        String env= EnvEnum.qa.getName();
        String address="万达广场二楼TEST";
        String addressRemark="{\"customRemark\":\"\",\"baseRemark\":[]}";
        String area="睢阳区";
        String areaPhone = "";
        String city="商丘市";
        String contact="自动化地址增加";
        String fixedPhone="";
        String houseNumber="123";
        String isChecked="0";
        String phone="12345678901";
        String poiNote="115.661298,34.390155";
        String province="河南";

        String body = "address=" + address +
                "&addressRemark=" + addressRemark +
                "&area=" + area +
                "&areaPhone=" + areaPhone +
                "&city=" + city +
                "&contact=" + contact +
                "&fixedPhone=" + fixedPhone +
                "&houseNumber=" + houseNumber +
                "&isChecked=" + isChecked +
                "&phone=" + phone +
                "&poiNote=" + poiNote +
                "&province=" + province;

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);

        //重复地址校验
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/InsertContact")
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body(body)
                .execute();
        boolean isRepeated= JsonDealer.findKeyInJson(JSONObject.parseObject(response.body()),"msg","收货地址信息重复，请修改后提交")==1;
        Assert.isTrue(isRepeated,"被添加了重复地址");

        //新增地址
        String body2 = "address=" + "新测试地址" +
                "&addressRemark=" + addressRemark +
                "&area=" + area +
                "&areaPhone=" + areaPhone +
                "&city=" + city +
                "&contact=" + contact +
                "&fixedPhone=" + fixedPhone +
                "&houseNumber=" + houseNumber +
                "&isChecked=" + isChecked +
                "&phone=" + phone +
                "&poiNote=" + poiNote +
                "&province=" + province;
        HttpResponse response2 = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/InsertContact")
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body(body2)
                .execute();
        Thread.sleep(1000);
        List<Map<String, Object>> newContact=jdbcTemplate.queryForList("select * from contact where m_id="+MallUserEnum.mallAutoUser.getMid()+" order by contact_id desc limit 1");

        String contactId=newContact.get(0).get("contact_id").toString();
        if(Integer.parseInt(newContact.get(0).get("contact_id").toString()) <=351837){
            Assert.isTrue(false,"新增地址失败");
        }
        //删除地址
        String body3="contactId="+contactId+"&status="+2;
        HttpResponse response3 = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/UpdateContact")
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body(body3)
                .execute();
        Thread.sleep(1000);
        List<Map<String, Object>> contactDetail=jdbcTemplate.queryForList("select * from contact where contact_id="+contactId);
        boolean isDeleted= contactDetail.get(0).get("status").toString().equals(String.valueOf(2));


        //清除垃圾数据
        jdbcTemplate.execute("delete from contact where contact_id=" +contactId);
        ds.close();
        Assert.isTrue(isDeleted,"地址未被删除");
        logger.info("本次新增后并被删除的地址为："+contactId);

    }

}
