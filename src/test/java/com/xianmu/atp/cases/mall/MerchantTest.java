package com.xianmu.atp.cases.mall;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

@Epic("mall")
@Feature("门店信息相关")
@Owner("zyl")
@Slf4j
public class MerchantTest extends BaseTest {

    @Test
    public void testBasicInfo() {
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), EnvEnum.qa.getName());
        String url = "https://qah5.summerfarm.net/merchant";
        HttpResponse response = HttpRequest.get(url)
                .execute();
        Assert.isTrue(response.isOk(),"/merchant接口报错");

        String url2 = "https://qah5.summerfarm.net/merchant/invite-info";

        HttpResponse response2 = HttpRequest.get(url2)
                .header("token", token)
                .execute();
        Assert.isTrue(response2.isOk(),"/invite-info接口报错");

        String url3 = "https://qah5.summerfarm.net/merchant/getFreeDay";

        HttpResponse response3 = HttpRequest.get(url3)
                .header("token", token)
                .execute();
        Assert.isTrue(response3.isOk(),"/getFreeDay接口报错");

    }


}
