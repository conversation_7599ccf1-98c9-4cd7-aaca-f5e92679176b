package com.xianmu.atp.cases.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AfterSaleOrderDao;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.AfterSaleHelper;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;

@Epic("mall")
@Feature("账期售后")
@Owner("zyl")
@Slf4j
public class AfterSaleBillTest extends BaseTest {

    @Resource
    private AreaStoreDao areaStoreDao;

    @Resource
    private AfterSaleOrderDao  afterSaleOrderDao;

    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private LoginHttp login;
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("大客户账期未到货售后")
    public void billAfterTest() throws InterruptedException {

        areaStoreDao.setOnlineQuantity(SkuEnum.skuBillAfter.getSku(), 1);//1：杭州仓

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.bigUser);
        mallOrder.addAddress(AddressEnum.bigUserAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,3);
        mallOrder.send(false);
        mallOrder.payByBill();
        String orderNo = mallOrder.getOrderNo();
        System.out.println(orderNo);
        String env = EnvEnum.qa.getName();
        //String orderNo ="01254PPNU00226112283";
        String atferSaleBody = "afterSaleUnit=" + "盒" +
                "&applyRemark=" + "" +
                "&deliveryed=" + 0 +
                "&isManage=" + "false" +
                "&mId=" + MallUserEnum.bigUser.getMid() +
                "&orderNo=" + orderNo +
                "&suitId=" + 0 +
                "&type=" + 0 +
                "&proofPic=" + "" +
                "&proofVideo=" + "" +
                "&handleType=" +3 +
                "&handleNum=" + 159.6 +
                "&quantity=" + 3 +
                "&refundType=" + "拍多/拍错/不想要" +
                "&sku=" + SkuEnum.skuBillAfter.getSku();

        String createAfterUrl= "https://"+env+"h5.summerfarm.net/after-sale";
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.bigUser.getPhone(), env);
        System.out.println(createAfterUrl);
        HttpResponse afterResponse =  HttpRequest.post(createAfterUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body(atferSaleBody)
                .execute();

        Thread.sleep(1000);
        boolean isStatusRight=afterSaleOrderDao.getAfterSaleOrder(orderNo).get(0).getStatus()==2;
        Assert.isTrue(isStatusRight,"账期大客户账期未到货售后失败");

    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("大客户账期已到货售后_录入账期")
    public void billBehandTest() throws InterruptedException {


        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.bigUser);
        mallOrder.addAddress(AddressEnum.bigUserAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,3);
        mallOrder.send(false);
        mallOrder.payByBill();
        String orderNo = mallOrder.getOrderNo();
        System.out.println(orderNo);
        String env = EnvEnum.qa.getName();

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        Thread.sleep(1000);
        AfterSaleHelper.setOrderReceivable(jdbcTemplate,orderNo);

        String atferSaleBody = "afterSaleUnit=" + "盒" +
                "&applyRemark=" + "autoTest" +
                "&deliveryed=" + 1 +
                "&isManage=" + "false" +
                "&mId=" + MallUserEnum.bigUser.getMid() +
                "&orderNo=" + orderNo +
                "&afterSaleType=" + "包装问题" +
                "&suitId=" + 0 +
                "&type=" + 0 +
                "&proofPic=" + "4493c0cbe51e4fdd37c0d9b36e5963e6" +
                "&proofVideo=" + "" +
                "&handleType=" +3 +
                "&handleNum=" + 53.2 +
                "&quantity=" + 1 +
                "&sku=" + SkuEnum.skuBillAfter.getSku();

        String createAfterUrl= "https://"+env+"h5.summerfarm.net/after-sale";
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.bigUser.getPhone(), env);
        System.out.println(createAfterUrl);
        HttpResponse afterResponse =  HttpRequest.post(createAfterUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body(atferSaleBody)
                .execute();

        Thread.sleep(1000);
        boolean isStatusRight=afterSaleOrderDao.getAfterSaleOrder(orderNo).get(0).getStatus()==0;
        Assert.isTrue(isStatusRight,"账期大客户账期已到货售后发起失败");


        String adminToken = login.login();
        AfterSaleHelper.accessRefundAfterSale(jdbcTemplate,adminToken,orderNo,3,1);

        Thread.sleep(1000);
        boolean isAuditStatusRight=afterSaleOrderDao.getAfterSaleOrder(orderNo).get(0).getStatus()==2;
        Assert.isTrue(isStatusRight,"账期大客户账期已到货售后审核失败");
        ds.close();
    }
}
