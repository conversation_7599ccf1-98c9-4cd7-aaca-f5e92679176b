package com.xianmu.atp.cases.mall;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

@Epic("mall")
@Feature("火脸预录入")
@Owner("zyl")
@Slf4j
public class FileFaceEntryTest extends BaseTest {


    //疑似有个绑定接口/bind
    @Test
    @Story("火脸认证接口")
    public void fileFaceEntryTest() {
        String env= EnvEnum.qa.getName();
        String url= "https://" +env +"h5.summerfarm.net/merchant/store/account/authorization/pre-entry";
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), env);
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        Assert.isTrue(jsonObject.get("status").equals(200), "接口请求失败");

    }

    @Test
    @Story("B2b信息接口")
    public void testB2bInfo(){
        String env="qa";
        String b2bUrl="https://"+env+"h5.summerfarm.net/merchant/b2b/query";
        String token=MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), env);
        HttpResponse response1 = HttpRequest.post(b2bUrl)
                .header("token", token)
                .execute();

        String b2bInfoUrl="https://"+env+"h5.summerfarm.net/merchant/b2b/query";
        HttpResponse response2 = HttpRequest.post(b2bInfoUrl)
                .header("token", token)
                .execute();

        String bpxUrl="https://"+env+"h5.summerfarm.net/merchant/b2b/upsert/update-status";
        HttpResponse response3 = HttpRequest.post(bpxUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body("{\"popupStatus\":0,\"coverStatus\":1}")
                .execute();

        com.baomidou.mybatisplus.core.toolkit.Assert.isTrue(response1.isOk(), "b2b配置信息获取失败");
        com.baomidou.mybatisplus.core.toolkit.Assert.isTrue(response2.isOk(), "b2b微信状态获取失败");
        com.baomidou.mybatisplus.core.toolkit.Assert.isTrue(response3.isOk(), "设置B2B信息弹窗状态失败");

        ;
    }
}
