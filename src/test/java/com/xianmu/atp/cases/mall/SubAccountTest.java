package com.xianmu.atp.cases.mall;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPRedisRequestDto;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.redis.DefaultRedisConfig;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Epic("mall")
@Feature("子账号相关")
@Owner("zyl")
@Slf4j
public class SubAccountTest extends BaseTest {

    @Resource
    private DefaultRedisConfig defaultRedisConfig;

    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private LoginHttp login;

    @Test
    public void submitSubAccountTest() throws InterruptedException {
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        String deletesql2="delete from merchant_sub_account where contact='自动化子账号'";
        jdbcTemplate.execute(deletesql2);

        String token="1-5026d495154343bea198c30c198130e6";
        String key="login:mall:1-5026d495154343bea198c30c198130e6";

        Map<String,Object> map=new HashMap<>();
        String subAccountPhone="***********";
        map.put("loginWay","wechat_gzh");
        map.put("openId","oZbI26ROn4gvNgLwwWCIDzDiuBer");
        map.put("phone",subAccountPhone);


        ATPRedisRequestDto dto = ATPRedisRequestDto.builder()
                .operation("set")
                .dbIndex("5")
                .queryContent( JSON.toJSONString(map))
                .queryKey(key)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res = defaultRedisConfig.getRedisResult(dto);
        log.info(res);


        String submitUrl = "https://qah5.summerfarm.net/sub-account/submit";
//        HttpResponse response4 = HttpRequest.post(submitUrl)
//                .header("token", token)
//                .contentType("application/x-www-form-urlencoded")
//                .body("contact=自动化子账号&mergeKey=b26kvs&phone="+subAccountPhone)
//                .execute();

        HttpResponse response5 = HttpRequest.post(submitUrl)
                .header("token", token)
                .contentType("application/x-www-form-urlencoded")
                .body("contact=自动化子账号&mergeKey=2IouTF&phone="+subAccountPhone)
                .execute();

        String mainAccountToken= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), "qa");

        String listUrl = "https://qah5.summerfarm.net/sub-account/account-list";
        HttpResponse response = HttpRequest.get(listUrl)
                .header("token", mainAccountToken)
                .execute();
        JSONObject json = JSONObject.parseObject(response.body());
        JSONArray jsonArray = json.getJSONObject("data").getJSONArray("auditAccount");
        int newAccountId=0;
        for(Object o:jsonArray){
            JSONObject jsonObject = (JSONObject) o;
            if(jsonObject.getString("phone").equals(subAccountPhone)){
                newAccountId=jsonObject.getInteger("accountId");
                break;
            }
        }

        String infourl = "https://qah5.summerfarm.net/sub-account/account-info?accountId="+newAccountId;
        HttpResponse response2 = HttpRequest.get(infourl)
                .header("token", mainAccountToken)
                .execute();

        String auditurl = "https://qah5.summerfarm.net/sub-account/audit";
        String bodyStr = "accountId="+newAccountId+
                "&auditFlag="+"1";

        HttpResponse response3 = HttpRequest.post(auditurl)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", mainAccountToken)
                .body(bodyStr)
                .execute();

        String delurl = "https://qah5.summerfarm.net/sub-account/del";
//        Map<String,Object> map2=new HashMap<>();
//        map2.put("accountId",newAccountId);


        Thread.sleep(2000);
        HttpResponse response4 = HttpRequest.post(delurl)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", mainAccountToken)
                .body("accountId="+newAccountId)
                .execute();
        Assert.isTrue(response3.isOk(),"新增审核子账户失败");
        Assert.isTrue(response4.isOk(),"删除子账户失败");

        String inviteCodeCheckUrl=  "https://qah5.summerfarm.net/sub-account/invalidMergeKey?mergeKey="+"2IouTF";
        HttpResponse response6= HttpRequest.get(inviteCodeCheckUrl)
                .header("token", mainAccountToken)
                .execute();
        ds.close();
        Assert.isTrue(response6.isOk(),"请求校验店铺信息失败");
    }
}
