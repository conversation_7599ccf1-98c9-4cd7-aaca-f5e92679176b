package com.xianmu.atp.cases.mall;


import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.contexts.MQTopicConstant;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.JsonDealer;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.mq.MQData;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.rocketmq.support.producer.SendResultDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("订单一些其他链路的接口")
@Owner("zyl")
@Slf4j
public class OrderExtraTest extends BaseCaseTest{

    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private MqProducer mqProducer;
    @Test
    @Story("取消订单")
    public void cancelOrder() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(false);
        String masterOrderNo=mallOrder.getMasterOrderNo();
        String env="qa";
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), env);
        String url = "https://"+env+"h5.summerfarm.net/order/query/merge?masterOrderNo="+masterOrderNo;

        String body="masterOrderNo="+masterOrderNo;
        HttpResponse response1 = HttpRequest.post(url)
                .header("token", token)
                .execute();

        String closeUrl = "https://"+env+"h5.summerfarm.net/order/close-order/v2?masterOrderNo="+masterOrderNo;

        HttpResponse response2 = HttpRequest.post(closeUrl)
                .header("token", token)
                .execute();

        Assert.isTrue(response1.isOk(),"获取子订单信息失败");
        Assert.isTrue(response2.isOk(),"v2取消订单失败");

        mallOrder.send(false);
        String orderNo2=mallOrder.getOrderNo();
        String cancelUrl="https://"+env+"h5.summerfarm.net/order/cancel/"+orderNo2;
        HttpResponse response3 = HttpRequest.post(cancelUrl)
                .header("token", token)
                .contentType("application/json")
                .execute();
        Assert.isTrue(response3.isOk(),"v1取消订单失败");
    }

    @Test
    @Story("订单收货")
    public void confirmOrder() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        String orderNo=mallOrder.getOrderNo();

        String env="qa";

        JdbcTemplate mallJdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String sql1="update orders set order_time=date_add(order_time,interval -2 day) where order_no=\""+orderNo+"\"";
        String sql2="update delivery_plan set status=6,delivery_time=date_add(delivery_time,interval -1 day) where order_no=\""+orderNo+"\"";
        String sql3="update tms_dist_order set state=40,expect_begin_time=date_add(expect_begin_time,interval -1 day) where outer_order_id=\""+orderNo+"\"";
        mallJdbcTemplate.execute("delete from merchant_coupon where m_id="+MallUserEnum.mallAutoUser.getMid()+" and coupon_id=20000");
        mallJdbcTemplate.execute("delete from coupon_receive_log where m_id="+MallUserEnum.mallAutoUser.getMid()+" and coupon_id=20000");


        mallJdbcTemplate.execute(sql1);
        mallJdbcTemplate.execute(sql2);
        mallJdbcTemplate.execute(sql3);

        //老的订单列表页
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), env);
        String url = "https://"+env+"h5.summerfarm.net/order/1/6";
        HttpResponse response = HttpRequest.get(url)
                .header("token", token)
                .execute();

        String confirmUrl = "https://qah5.summerfarm.net/order/UserConfirm";
        String bodyStr = "deliveryStatus="+"2"+
                "&orderNo="+orderNo;

        HttpResponse response2 = HttpRequest.post(confirmUrl)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token)
                .body(bodyStr)
                .execute();

        Assert.isTrue(response.isOk(),"老获取订单列表失败");
        Assert.isTrue(response2.isOk(),"订单收货失败");
        JSONObject jsonObject = JSON.parseObject(mallOrder.getOrderdetail());

        String deliveryId=jsonObject.getJSONObject("data").getJSONObject("orderVO").getString("deliveryPlanId") ;
        String judgeUrl="https://qah5.summerfarm.net/evaluation/upsert/rapid-evaluation";
        HttpResponse response5 = HttpRequest.post(judgeUrl)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token)
                .body("{\"deliveryPlanId\":"+deliveryId+"}")
                .execute();
        mallJdbcTemplate.execute("delete from merchant_coupon where m_id="+MallUserEnum.mallAutoUser.getMid()+" and coupon_id=20000");
        mallJdbcTemplate.execute("delete from coupon_receive_log where m_id="+MallUserEnum.mallAutoUser.getMid()+" and coupon_id=20000");

        Assert.isTrue(response5.isOk(),"评价失败");



    }


    @Test
    @Story("购买优惠卡")
    public void testDiscountBuy() throws InterruptedException {
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String createUrl = "https://qah5.summerfarm.net/discount-card/buy";
        HttpResponse response = HttpRequest.post(createUrl)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token)
                .body("discountCardId=97")
                .execute();

        String orderNo=JSONObject.parseObject(response.body()).getJSONObject("data").getString("orderNo");
        MallOrderUtil.payOrder(env,token,orderNo,1);
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        String checksql= "select count(1) as num from discount_card_to_merchant where m_id="+MallUserEnum.mallAutoUser.getMid()+" and discount_card_id=97";
        boolean success = SqlExcutorUtil.checkSqlValue(jdbcTemplate,checksql,"num","1",5);
        Assert.isTrue(success,"购买优惠卡失败");
        jdbcTemplate.execute("delete from discount_card_to_merchant where m_id="+MallUserEnum.mallAutoUser.getMid()+" and discount_card_id=97");
        ds.close();
    }

    @Test
    public void testOrderAutoClose() throws InterruptedException {
        String env = "qa";
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuForActivityLimit,2);
        mallOrder.send(false);
        String masterOrderNo = mallOrder.getMasterOrderNo();

        MQData mqData = new MQData();
        Map<String, Object> data = new HashMap<>();
        data.put("areaNo","29380");
        data.put("mId", MallUserEnum.mallAutoUser.getMid());
        data.put("orderNo",masterOrderNo);
        data.put("id","cancelorder"+masterOrderNo);
        data.put("openid","oRRzJ6S0qwsDnSWGI_8A8OihI3i4");
        data.put("runTime", Instant.now().plus(Duration.ofSeconds(2)).toEpochMilli());
        mqData.setData(data);
        mqData.setType("MASTER_ORDER_TIMEOUT_CLOSE");
        try {
            SendResultDTO dto = mqProducer.send("dev-mall-delay-list", null, JSON.toJSONString(data));
            log.info("返回消息{}", dto);
        } catch (Exception e){
            e.printStackTrace();
        }
        log.info("MQ消息发送："+JSON.toJSONString(mqData));
        org.testng.Assert.assertEquals(true, true);
    }
}
