package com.xianmu.atp.cases.mall;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.JsonDealer;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.generic.mall.SelftUsages.ProductInfo;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

@Epic("mall")
@Feature("自动领券")
@Owner("zyl")
@Slf4j
public class AutoReceiveCouponTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Story("预下单自动领券")
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void autoReceiveCouponTest() throws InterruptedException {
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        jdbcTemplate.execute("UPDATE xianmudb.coupon_sender_setup t\n" +
                "SET t.status = 1\n" +
                "WHERE t.id = 1288;\n");

        String env= EnvEnum.qa.getName();
        String preOrderSTr=MallOrderUtil.preOrder(env,MallUserEnum.mallAutoUser.getPhone(), AddressEnum.mallAutoAddress.getContactId(), SkuEnum.skuForActivityLimit.getSku(), 1);
        JSONObject jsonObject = JSONObject.parseObject(preOrderSTr);
        int proOrderShowReceiveable= JsonDealer.findKeyInJson(jsonObject,"couponSenderId","1288");

        String autoCouponUrl = "https://" + env+"h5.summerfarm.net/sender-setup/add/automatic-coupon";
        Map<String, Object> body = new HashMap<>();
        body.put("couponId", 19985);
        body.put("couponSenderId",1288);
        String token= getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse response = HttpRequest.post(autoCouponUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(body))
                .execute();


        boolean recieveSuccess = SqlExcutorUtil.checkSqlValue(jdbcTemplate,"select count(1) as num from merchant_coupon where m_id="+MallUserEnum.mallAutoUser.getMid()+" and coupon_id=19985", "num", "1", 5);

        //重置数据
        jdbcTemplate.execute("UPDATE xianmudb.coupon_sender_setup t\n" +
                "SET t.status = 3\n" +
                "WHERE t.id = 1288;\n");
        jdbcTemplate.execute("delete from merchant_coupon where m_id="+MallUserEnum.mallAutoUser.getMid()+" and coupon_id=19985");
        jdbcTemplate.execute("delete from coupon_receive_log where m_id="+MallUserEnum.mallAutoUser.getMid()+" and coupon_id=19985");

        ds.close();
        //Assert.isTrue(proOrderShowReceiveable==1,"预下单自动领券内容未返回");
        Assert.isTrue(recieveSuccess,"自动领券失败");

    }


    @Story("手动领券")
    @Test(dependsOnMethods = "autoReceiveCouponTest",retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void userRecieveTest() throws InterruptedException {
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        jdbcTemplate.execute("UPDATE xianmudb.coupon_sender_setup t\n" +
                "SET t.status = 1\n" +
                "WHERE t.id = 1288;\n");

        String env= EnvEnum.qa.getName();

        String autoCouponUrl = "https://" + env+"h5.summerfarm.net/sender-setup/coupon/receive";
        Map<String, Object> body = new HashMap<>();
        body.put("couponId", 19985);
        body.put("couponSenderId",1288);
        body.put("isFromSender",true);
        String token= getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(),env);
        HttpResponse response = HttpRequest.post(autoCouponUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(body))
                .execute();


        boolean recieveSuccess = SqlExcutorUtil.checkSqlValue(jdbcTemplate,"select count(1) as num from merchant_coupon where m_id="+MallUserEnum.mallAutoUser2.getMid()+" and coupon_id=19985", "num", "1", 5);

        //重置数据
        jdbcTemplate.execute("UPDATE xianmudb.coupon_sender_setup t\n" +
                "SET t.status = 3\n" +
                "WHERE t.id = 1288;\n");
        jdbcTemplate.execute("delete from merchant_coupon where m_id="+MallUserEnum.mallAutoUser2.getMid()+" and coupon_id=19985");
        jdbcTemplate.execute("delete from coupon_receive_log where m_id="+MallUserEnum.mallAutoUser2.getMid()+" and coupon_id=19985");

        ds.close();
        Assert.isTrue(recieveSuccess,"手动领券失败");

    }


    @Story("可领券列表")
    @Test(dependsOnMethods = "autoReceiveCouponTest",retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void receivableCouponListTest() throws InterruptedException {

        String env= EnvEnum.qa.getName();

        String autoCouponUrl = "https://" + env+"h5.summerfarm.net/sender-setup/coupon/list";
        String token= getXianmuMallUserToken("13744550987",env);
        HttpResponse response = HttpRequest.get(autoCouponUrl)
                .header("token", token)
                .execute();

        Assert.isTrue(response.body().contains("自动化领券配置"),"可领券列表返回错误");



    }
}
