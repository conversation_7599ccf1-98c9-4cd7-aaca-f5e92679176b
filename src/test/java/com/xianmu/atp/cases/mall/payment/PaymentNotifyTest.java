package com.xianmu.atp.cases.mall.payment;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("支付测试")
@Owner("zyl")
@Slf4j
public class PaymentNotifyTest extends BaseTest {

    @Value("147")
    private int fireFaceAccount;

    @Value("153")
    private int dingAccount;

    @Value("5")
    private int wxAccount;


    @Test
    @Story("火脸支付回调")
    public void testFireFaceNotify() throws InterruptedException {
        String env="qa";

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(false);
        String masterOrderNo = mallOrder.getMasterOrderNo();
        String formattedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        Map<String,Object> bodyMap = new HashMap<>();
        bodyMap.put("actualAmount",100);
        bodyMap.put("businessOrderNo",mallOrder.getMasterOrderNo());
        bodyMap.put("channelOrderNo","fireo"+mallOrder.getMasterOrderNo());
        bodyMap.put("merchantNo",MallUserEnum.mallAutoUser.getMid());
        bodyMap.put("orderNo", mallOrder.getOrderNo());
        bodyMap.put("orderStatus",2);
        bodyMap.put("payWay","wechat");
        bodyMap.put("remark", SkuEnum.skuBillAfter.getSkuName());
        bodyMap.put("payTime",formattedTime);
        bodyMap.put("topChannelOrderNo","99999999"+mallOrder.getMasterOrderNo());
        bodyMap.put("userId","oFQai6-9xyQrj5A_AoFMUy5Yv4o8");



        Map<String,Object> notifyMap = new HashMap<>();
        notifyMap.put("authCode","****************");
        notifyMap.put("code","200");
        notifyMap.put("message","请求成功");
        notifyMap.put("respBody",bodyMap);
        notifyMap.put("requestTime",formattedTime);

        notifyMap.put("resource", "api.hl.applet.b2b.preAppletB2b");
        notifyMap.put("sign","a04fbd9f0d215da4f517c71ef700c4c4");

        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/pay-notify/fire-face/"+fireFaceAccount)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(JSONObject.toJSONString(notifyMap))
                .execute();
        Thread.sleep(3000);
        //Assert.isTrue(mallOrder.getOrderStatus().equals("3"),"火脸回调失败");
    }

    @Test
    @Story("智付回调")
    public void testDinNotify() throws InterruptedException {


        String env="qa";

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(false);
        String masterOrderNo = mallOrder.getMasterOrderNo();


        Map<String, Object> jsonMap = new HashMap<>();


        String formattedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        // 将JSON中的键值对存入Map
        jsonMap.put("cashFee", 100);
        jsonMap.put("chargeFlag", "758");
        jsonMap.put("couponFee", 0);
        jsonMap.put("controlStatus", "FREEZE");
        jsonMap.put("outTransactionOrderId", "88888888"+mallOrder.getMasterOrderNo());
        jsonMap.put("channelNumber", "25031416450290505331283");
        jsonMap.put("subMerchantNo", "740482657");
        jsonMap.put("channelSettlementAmount", 0);
        jsonMap.put("orderFee", 0);
        jsonMap.put("orderStatus", "SUCCESS");
        jsonMap.put("paymentAmount", 100);
        jsonMap.put("paymentType", "WXPAY");
        jsonMap.put("payAmount", 100);
        jsonMap.put("wxTradeType", "JSAPI");
        jsonMap.put("merchantId", "S10000000009651");
        jsonMap.put("paymentMethods", "APPLET");
        jsonMap.put("subOpenId", "oFQai6-9xyQrj5A_AoFMUy5Yv4o8");
        jsonMap.put("currency", "CNY");
        jsonMap.put("orderCreditAmount", 0);
        jsonMap.put("orderDesc", "{\"companyAccountId\":"+dingAccount+"}"); // 注意：需要转义
        jsonMap.put("orderPayDate", formattedTime);
        jsonMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        jsonMap.put("orderNo", mallOrder.getMasterOrderNo());
        jsonMap.put("bankType", "OTHERS");
        jsonMap.put("openid", "ou3_21Xw23lv6GspyfK3mEAq-HB0");
        jsonMap.put("merchantFee", 0);
        jsonMap.put("merchantCreditAmount", 0);
        jsonMap.put("controlType", "CONTROL");
        jsonMap.put("feeAccountAmt", 0);
        jsonMap.put("appid", "wx674b60a859676717");
        jsonMap.put("receiverFee", 0);
        jsonMap.put("onlineCardType", "CFT");


        String notifyStr="msg"+"="+"接口调用成功"+"&"+
                "code"+"="+"0000"+"&"+
                "msg"+"="+"接口调用成功"+"&"+
                "data"+"="+JSONObject.toJSONString(jsonMap)+"&"+
                "merchantId"+"="+"接口调用成功"+"&"+
                "msg"+"="+"D10000000008630"+"&"+
                "sign"+"="+"mocksigntt6hRT/99i0L2Faxzc6nhLmp4cxbsp5MxBuFn/NBAiEAo0QFRcib8EC5NM1VipeRWaYU9wYtNRcJib5hHg8C9xI="+"&"+
                "signatureMethod"+"="+"SM3WITHSM2"+"&";


        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/pay-notify/din")
                .header(Header.CONTENT_TYPE,"application/x-www-form-urlencoded")
                .body(notifyStr)
                .execute();

        Thread.sleep(3000);

        //Assert.isTrue(mallOrder.getOrderStatus().equals("3"),"智付回调失败");
    }



    @Test
    @Story("招行回调")
    public void testCmbNotify() throws InterruptedException {


        String env="qa";

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(false);
        String masterOrderNo = mallOrder.getMasterOrderNo();


        Map<String, Object> jsonMap = new HashMap<>();


        String formattedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String formattedData = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String formattedSecond = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        // 将JSON中的键值对存入Map
        jsonMap.put("merId", "3089991074200IY");
        jsonMap.put("orderId", mallOrder.getMasterOrderNo());
        jsonMap.put("cmbOrderId", "100625030415545291322044");
        jsonMap.put("userId", "N003338026");
        jsonMap.put("txnAmt", "10000");//单位分
        jsonMap.put("currencyCode", "156");
        jsonMap.put("payType", "ZF");
        jsonMap.put("openId", "****************");
        jsonMap.put("txnTime", formattedTime);
        jsonMap.put("endDate", formattedData);
        jsonMap.put("endTime", formattedSecond);
        jsonMap.put("mchReserved", "107");
        jsonMap.put("dscAmt", "0");
        jsonMap.put("thirdOrderId", "682025030422001477921436938756");
        jsonMap.put("payBank", "ALIPAYACCOUNT");
        jsonMap.put("buyerLogonId", "156****8291");
        jsonMap.put("payChannel", "U");


        String notifyStr="encoding"+"="+"UTF-8"+"&"+
                "version"+"="+"0.0.1"+"&"+
                "biz_content"+"="+JSONObject.toJSONString(jsonMap)+"&"+
                "sign"+"="+"mocksigntt6hRT/99i0L2Faxzc6nhLmp4cxbsp5MxBuFn/NBAiEAo0QFRcib8EC5NM1VipeRWaYU9wYtNRcJib5hHg8C9xI="+"&"+
                "signMethod"+"="+"02";


        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/pay-notify/cmb/v2")
                .header(Header.CONTENT_TYPE,"application/x-www-form-urlencoded")
                .body(notifyStr)
                .execute();
        Assert.isTrue(mallOrder.getOrderStatus().equals("3"),"招行V2回调失败");

        mallOrder.send(false);

        jsonMap.put("orderId", mallOrder.getOrderNo());
        notifyStr="encoding"+"="+"UTF-8"+"&"+
                "version"+"="+"0.0.1"+"&"+
                "biz_content"+"="+JSONObject.toJSONString(jsonMap)+"&"+
                "sign"+"="+"mocksigntt6hRT/99i0L2Faxzc6nhLmp4cxbsp5MxBuFn/NBAiEAo0QFRcib8EC5NM1VipeRWaYU9wYtNRcJib5hHg8C9xI="+"&"+
                "signMethod"+"="+"02";


        //v1接口也还在用

        HttpResponse response2 = HttpRequest.post("https://"+env+"h5.summerfarm.net/pay-notify/cmb")
                .header(Header.CONTENT_TYPE,"application/x-www-form-urlencoded")
                .body(notifyStr)
                .execute();

        Thread.sleep(3000);
        //Assert.isTrue(mallOrder.getOrderStatus().equals("3"),"招行V1回调失败");
    }


//    @Test
//    @Story("微信回调")
//    //需要先生成支付单
//
//    public void testWxNotify() {
//
//
//        String env="qa";
//
//        MallOrder mallOrder = new MallOrder();
//        mallOrder.addEnv(EnvEnum.qa);
//        mallOrder.addUser(MallUserEnum.mallAutoUser);
//        mallOrder.addAddress(AddressEnum.mallAutoAddress);
//        mallOrder.addSku(SkuEnum.skuBillAfter,1);
//        mallOrder.send(false);
//
//        String formattedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
//
//        String notifyStr="<xml>\n" +
//                "    <appid><![CDATA[wx674b60a859676717]]></appid>\n" +
//                "    <attach><![CDATA[{\"companyAccountId\":"+wxAccount+"}]]></attach>\n" +
//                "    <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
//                "    <cash_fee><![CDATA["+100+"]]></cash_fee>\n" +
//                "    <fee_type><![CDATA[CNY]]></fee_type>\n" +
//                "    <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
//                "    <mch_id><![CDATA[**********]]></mch_id>\n" +
//                "    <nonce_str><![CDATA[903cc7be42d6fae3ae8c8be791ceeb74]]></nonce_str>\n" +
//                "    <openid><![CDATA[oFQai604EV-blhXH4Yk6jMskzshs]]></openid>\n" +
//                "    <out_trade_no><![CDATA["+mallOrder.getMasterOrderNo()+"]]></out_trade_no>\n" +
//                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" +
//                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" +
//                "    <sign><![CDATA[581F3BE745F24393E25CA693445E7A0D]]></sign>\n" +
//                "    <time_end><![CDATA["+formattedTime+"]]></time_end>\n" +
//                "    <total_fee>100</total_fee>\n" +
//                "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" +
//                "    <transaction_id><![CDATA[4200002523202503060336297960]]></transaction_id>\n" +
//                "</xml>";
//
//
//        HttpResponse response = HttpRequest.post("https://"+"dev"+"h5.summerfarm.net/pay-notify/weix/v2")
//                .header(Header.CONTENT_TYPE,"application/x-www-form-urlencoded")
//                .body(notifyStr)
//                .execute();
//    }
//
//


}
