package com.xianmu.atp.cases.mall.orderMix;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("运费测试")
@Owner("zyl")
@Slf4j
public class DelieveryFeeTest extends BaseTest {

    @Resource
    private AreaStoreDao areaStoreDao;

    @Autowired
    private DynamicDataSourceProperties properties;

    @Test
    @Story("单店运费测试-阶梯/单日收取")
    public void delievryFeeTest() {
        //单商品价格10，起送60，运费15，起送200，运费0
        areaStoreDao.setOnlineQuantity(SkuEnum.deliveryFeeSku.getSku(), 186);
        areaStoreDao.setOnlineQuantity(SkuEnum.deliveryFeeSku2.getSku(), 186);
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate JdbcTemplate = new JdbcTemplate(ds);
        JdbcTemplate.execute("delete  from delivery_plan where contact_id in (select contact.contact_id from contact where m_id="+MallUserEnum.deliveryFeeUser.getMid()+")");
        JdbcTemplate.execute("delete  from  orders  where m_id="+MallUserEnum.deliveryFeeUser.getMid());

        //不满起送
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.deliveryFeeUser.getPhone(),"qa");

        //满起送查看运费
        String orderResult1=MallOrderUtil.getOrderResult("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 5);
        String preOrderResult1=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 6);
        String orderNo1=MallOrderUtil.send("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 6);

        //同日配送不收运费
        String preOrderResult2=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 6);
        String orderNo2=MallOrderUtil.send("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 6);

        //同日配送有订单后忽略起送
        String orderResult2=MallOrderUtil.getOrderResult("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 5);

        String deliveryFee1=SqlExcutorUtil.getOnlyValue(JdbcTemplate, "select delivery_fee from orders where order_no='"+orderNo1+"'");
        String deliveryFee2=SqlExcutorUtil.getOnlyValue(JdbcTemplate, "select delivery_fee from orders where order_no='"+orderNo2+"'");

        JdbcTemplate.execute("delete  from delivery_plan where contact_id in (select contact.contact_id from contact where m_id="+MallUserEnum.deliveryFeeUser.getMid()+")");
        JdbcTemplate.execute("delete  from  orders  where m_id="+MallUserEnum.deliveryFeeUser.getMid());

        //全品类走另一个运费模板
        String orderResult3=MallOrderUtil.getOrderResult("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 1);
        String preOrderResult3=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 3);
        String orderNo3=MallOrderUtil.send("qa", token, AddressEnum.deliveryFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 6);
        String deliveryFee3=SqlExcutorUtil.getOnlyValue(JdbcTemplate, "select delivery_fee from orders where order_no='"+orderNo3+"'");




        ds.close();
        Assert.isTrue(orderResult1.contains("deliveryThresholdMinDifference\":10.00"),"起送运费测试失败");
        Assert.isTrue(preOrderResult1.contains("\"deliveryFee\":15.00"),"一阶梯运费测试失败");
        Assert.isTrue(preOrderResult2.contains("\"deliveryFee\":0.00"),"同配送日第二单不收运费失败");
        Assert.isTrue(orderResult2.contains("subOrderNos"),"同配送日下有订单后忽略起送下单测试失败");

        Assert.isTrue(deliveryFee1.equals("15.00"),"阶梯运费测试失败");
        Assert.isTrue(deliveryFee2.equals("0.00"),"同配送日有订单计算运费测试失败");

        Assert.isTrue(orderResult3.contains("\"deliveryThresholdMinDifference\":20.00"),"全品类起送运费测试失败");
        Assert.isTrue(preOrderResult3.contains("\"deliveryFee\":20.00"),"全品类收取不同运费测试失败");
        Assert.isTrue(deliveryFee3.equals("20.00"),"全品类订单运费测试失败");


    }


    @Test
    @Story("大客户运费测试-阶梯/单日收取/地址设置模板")
    public void delievryFeeBigUserTest() {
        //商品单价10，大客户配置起送70，运费30/35，特殊地址配置起送100，运费31/32
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate JdbcTemplate = new JdbcTemplate(ds);
        JdbcTemplate.execute("delete  from delivery_plan where contact_id in (select contact.contact_id from contact where m_id="+MallUserEnum.deliveryFeeBigUser.getMid()+")");
        JdbcTemplate.execute("delete  from  orders  where m_id="+MallUserEnum.deliveryFeeBigUser.getMid());


        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.deliveryFeeBigUser.getPhone(),"qa");

        //不满起送
        String orderResult1=MallOrderUtil.getOrderResult("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 5);

        //满起送查看运费
        String preOrderResult1=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 7);
        String orderNo1=MallOrderUtil.send("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 7);

        //同日配送不收运费
        String preOrderResult2=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 7);
        String orderNo2=MallOrderUtil.send("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 7);

        //同日配送有订单后忽略起送
        String orderResult2=MallOrderUtil.getOrderResult("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku.getSku(), 5);

        String deliveryFee1=SqlExcutorUtil.getOnlyValue(JdbcTemplate, "select delivery_fee from orders where order_no='"+orderNo1+"'");
        String deliveryFee2=SqlExcutorUtil.getOnlyValue(JdbcTemplate, "select delivery_fee from orders where order_no='"+orderNo2+"'");

        JdbcTemplate.execute("delete  from delivery_plan where contact_id in (select contact.contact_id from contact where m_id="+MallUserEnum.deliveryFeeBigUser.getMid()+")");
        JdbcTemplate.execute("delete  from  orders  where m_id="+MallUserEnum.deliveryFeeBigUser.getMid());

        //全品类走另一个运费模板
        String orderResult3=MallOrderUtil.getOrderResult("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 1);
        String preOrderResult3=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 3);
        String orderNo3=MallOrderUtil.send("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 7);
        String deliveryFee3=SqlExcutorUtil.getOnlyValue(JdbcTemplate, "select delivery_fee from orders where order_no='"+orderNo3+"'");

        //地址单独设置配送费测试优先级
        String orderResult4=MallOrderUtil.getOrderResult("qa", token, AddressEnum.deliveryUniqueBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 1);
        String orderNo4=MallOrderUtil.send("qa", token, AddressEnum.deliveryUniqueBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku2.getSku(), 10);
        String deliveryFee4=SqlExcutorUtil.getOnlyValue(JdbcTemplate, "select delivery_fee from orders where order_no='"+orderNo4+"'");


        ds.close();
        Assert.isTrue(orderResult1.contains("deliveryThresholdMinDifference\":20.00"),"起送运费测试失败");
        Assert.isTrue(preOrderResult1.contains("\"deliveryFee\":30.00"),"一阶梯运费测试失败");
        Assert.isTrue(preOrderResult2.contains("\"deliveryFee\":0"),"同配送日第二单不收运费失败");
        Assert.isTrue(orderResult2.contains("subOrderNos"),"同配送日下有订单后忽略起送下单测试失败");

        Assert.isTrue(deliveryFee1.equals("30.00"),"阶梯运费测试失败");
        Assert.isTrue(deliveryFee2.equals("0.00"),"同配送日有订单计算运费测试失败");

        Assert.isTrue(orderResult3.contains("\"deliveryThresholdMinDifference\":60.00"),"全品类起送运费测试失败");
        Assert.isTrue(preOrderResult3.contains("\"deliveryFee\":35.00"),"全品类收取不同运费测试失败");
        Assert.isTrue(deliveryFee3.equals("35.00"),"全品类订单运费测试失败");


        Assert.isTrue(orderResult4.contains("deliveryThresholdMinDifference\":90.00"),"地址设置模板起送运费测试失败");
        Assert.isTrue(deliveryFee4.equals("32.00"),"地址设置模板运费测试失败");

    }


    @Test(dependsOnMethods = "delievryFeeBigUserTest")
    @Story("运费售后相关")
    public void delievryFeeAfterSaleTest() throws InterruptedException {
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate JdbcTemplate = new JdbcTemplate(ds);
        JdbcTemplate.execute("delete  from delivery_plan where contact_id in (select contact.contact_id from contact where m_id="+MallUserEnum.deliveryFeeBigUser.getMid()+")");
        JdbcTemplate.execute("delete  from  orders  where m_id="+MallUserEnum.deliveryFeeBigUser.getMid());

        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.deliveryFeeBigUser.getPhone(),"qa");
        String orderNo2=MallOrderUtil.send("qa", token, AddressEnum.deliveryBigFeeAddress.getContactId(), SkuEnum.deliveryFeeSku3.getSku(), 7);

        MallOrder order=new MallOrder();
        order.addUser(MallUserEnum.deliveryFeeBigUser);
        order.addSku(SkuEnum.deliveryFeeSku3,3);
        order.addSku(SkuEnum.deliveryFeeSku,4);
        order.addAddress(AddressEnum.deliveryBigFeeAddress);
        order.addEnv(EnvEnum.qa);
        order.send(true);

        token= MallOrder.getXianmuMallUserToken(MallUserEnum.deliveryFeeBigUser.getPhone(),"qa");

        String checkDeliveryFeeUrl="https://qah5.summerfarm.net/after-sale/query/return-shipping";
        String afterSaleUrl="https://qah5.summerfarm.net/after-sale";

        Map<String, Object> map = new HashMap<>();
        map.put("orderNo", orderNo2);
        map.put("sku", SkuEnum.deliveryFeeSku3.getSku());
        map.put("quantity", 7);
        map.put("deliveryed", 0);
        map.put("suitId", 0);
        map.put("handleType", 2);
        //map.put("productType", 0);
        map.put("afterSaleUnit", "1");

        map.put("applyRemark", "");

        map.put("handleNum", 70);
        map.put("isManage", false);
        map.put("refundType", "%E6%8B%8D%E5%A4%9A%2F%E6%8B%8D%E9%94%99%2F%E4%B8%8D%E6%83%B3%E8%A6%81");
        map.put("type", 0);
        map.put("proofPic", "");
        map.put("proofVideo", "");

        StringBuilder queryString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (queryString.length() > 0) {
                queryString.append("&");
            }
            String key = entry.getKey();
            String value = entry.getValue().toString();
            queryString.append(key);
            queryString.append("=");
            queryString.append(value);
        }


        HttpResponse checkResponse = HttpRequest.post(checkDeliveryFeeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString( map))
                .execute();
        Assert.isTrue(checkResponse.body().contains("\"data\":0"),"不满起送额外不退运费提示判断测试失败");

        HttpResponse afterResponse = HttpRequest.post(afterSaleUrl)
                //.header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(queryString.toString())
                .execute();
        System.out.println(afterResponse.body());

        boolean isDeliveryFeeUnReturn= SqlExcutorUtil.checkSqlValue(JdbcTemplate, "select handle_num from after_sale_proof where after_sale_order_no =(select after_sale_order_no from after_sale_order where order_no='"+orderNo2+"');", "handle_num", "70.00", 5);
        ds.close();
        Assert.isTrue(isDeliveryFeeUnReturn,"仍有订单要配送不退运费测试失败");
    }
}
