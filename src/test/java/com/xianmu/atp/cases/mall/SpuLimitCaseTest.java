package com.xianmu.atp.cases.mall;

import cn.hutool.core.lang.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.generic.mall.SelftUsages.ProductInfo;
import com.xianmu.atp.generic.mall.SelftUsages.ShopCart;
import com.xianmu.atp.generic.mall.SelftUsages.SkuHelper;
import com.xianmu.atp.dal.dao.ShoppingCartDao;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.common.JsonDealer;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;

@Epic("mall")
@Feature("营销")
@Owner("zyl")
@Slf4j
public class SpuLimitCaseTest extends BaseTest {

    @Resource
    private ShoppingCartDao shoppingCartDao;

    @Resource
    private AreaStoreDao areaStoreDao;

    @Story("spu起购功能")
    @Test(description = "不满起购下单",retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void zylCase01() throws InterruptedException {
        areaStoreDao.setOnlineQuantity(SkuEnum.skuForSpuStartNum.getSku(),1);
        MallOrder order = new MallOrder();
        order.addEnv(EnvEnum.qa);
        order.addAddress(AddressEnum.mallAutoAddress);
        order.addSku(SkuEnum.skuForSpuStartNum);
        order.send(false);
        int judge = JsonDealer.findKeyInJson(order.getJsonResult(),"baseSaleQuantity", "5");
        Assert.isFalse(judge==2,"下单接口限购数量错误："+order.getStringResult());
        Assert.isFalse(judge==3,"下单接口未返回限购信息："+order.getStringResult());
    }

    @Story("spu起购功能")
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void zylCase02(){
        ProductInfo productInfo = new ProductInfo();
        productInfo.addUser(MallUserEnum.mallAutoUser);
        productInfo.addEnv(EnvEnum.qa);
        productInfo.addPdId(SkuEnum.skuForSpuStartNum);
        productInfo.send();
        SkuHelper.checkSkuExtraInfo(SkuEnum.skuForSpuStartNum,productInfo.getJsonResult());
    }

    @Story("spu起购功能")
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void zylCase03(){
        shoppingCartDao.deleteByAccountId(MallUserEnum.mallAutoUser.getMid());
        ShopCart shopCart =new ShopCart();
        shopCart.addEnv(EnvEnum.qa);
        shopCart.addUser(MallUserEnum.mallAutoUser);
        shopCart.addSku(SkuEnum.skuForSpuStartNum,1);
        SkuHelper.checkSkuExtraInfo(SkuEnum.skuForSpuStartNum,shopCart.getCartInfo());
    }


}
