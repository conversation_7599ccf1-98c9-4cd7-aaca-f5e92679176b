package com.xianmu.atp.cases.mall.orderMix;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;
@Epic("mall")
@Feature("订单价格相关")
@Owner("zyl")
@Slf4j
public class OrderPriceTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Value("2985")
    public int timingRuleId;

    @Test
    @Story("价格校验/优惠均摊")
    public void testOrderPirces(){
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser3.getPhone(),"qa");
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        String sql1 = "update area_store set online_quantity=10000 where area_no=1 and sku='"+SkuEnum.priceTestA.getSku()+"'";
        jdbcTemplate.execute(sql1);

        String sql2 = "update area_store set online_quantity=10000 where area_no=1 and sku='"+SkuEnum.priceTestB.getSku()+"'";
        jdbcTemplate.execute(sql2);

        String deleteCouponSql = "delete from merchant_coupon where m_id="+MallUserEnum.mallAutoUser3.getMid();
        jdbcTemplate.execute(deleteCouponSql);
        //不设任何阶梯价、没有优惠券
        //该方法不校验都传""
        checkSkuPrice(token, SkuEnum.priceTestA.getSku(), 5, AddressEnum.mallAutoAddress3.getContactId(), "","500.00","500.00","");



        String sql3 = "INSERT INTO `xianmudb`.`merchant_coupon`(`id`, `m_id`, `coupon_id`, `vaild_date`, `sender`, `used`, `add_time`, `view`, `order_no`, `receive_type`, `start_time`, `update_time`, `send_id`)\n" +
                "VALUES (NULL, "+MallUserEnum.mallAutoUser3.getMid()+", 20044, '2040-04-23 23:59:59', NULL, 0, '2023-10-26 14:40:06', NULL, NULL, 5, '2023-10-26 14:40:06', NULL, NULL)\n" +
                ";";
        jdbcTemplate.execute(sql3);
        String getCouponIdSql="select max(id) from merchant_coupon where m_id="+MallUserEnum.mallAutoUser3.getMid();
        String couponId=SqlExcutorUtil.getOnlyValue(jdbcTemplate,getCouponIdSql);
        //优惠券走的是缓存，到手价不能马上生效,不校验到手价了
        //优惠券价格满110-16
        checkSkuPrice(token, SkuEnum.priceTestB.getSku(), 5, AddressEnum.mallAutoAddress3.getContactId(), "","584.00","584.00",couponId);

        //阶梯价1-99  3-98   5-97
        //到手价按阶梯1  99算
        checkSkuPrice(token, SkuEnum.priceTestC.getSku(), 5, AddressEnum.mallAutoAddress3.getContactId(), "99.00","485.00","485.00","");

        ds.close();
    }

    @Test
    @Story("优惠券状态变更")
    public void testCouponStatus() throws InterruptedException {
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser3.getPhone(),"qa");
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);

        String deleteCouponSql = "delete from merchant_coupon where coupon_id =20062 and m_id="+MallUserEnum.mallAutoUser3.getMid();
        jdbcTemplate.execute(deleteCouponSql);


        String sql3 = "INSERT INTO `xianmudb`.`merchant_coupon`(`id`, `m_id`, `coupon_id`, `vaild_date`, `sender`, `used`, `add_time`, `view`, `order_no`, `receive_type`, `start_time`, `update_time`, `send_id`)\n" +
                "VALUES (NULL, "+MallUserEnum.mallAutoUser3.getMid()+", 20062, '2040-04-23 23:59:59', NULL, 0, '2023-10-26 14:40:06', NULL, NULL, 5, '2023-10-26 14:40:06', NULL, NULL)\n" +
                ";";
        jdbcTemplate.execute(sql3);

        String getCouponIdSql="select max(id) from merchant_coupon where coupon_id =20062 and m_id="+MallUserEnum.mallAutoUser3.getMid();
        String couponId=SqlExcutorUtil.getOnlyValue(jdbcTemplate,getCouponIdSql);
        getOrderPrice(token, SkuEnum.priceTestC.getSku(), 5, AddressEnum.mallAutoAddress3.getContactId(), couponId);

        String couponStatusSql="select max(used) as status from merchant_coupon where coupon_id =20062 and m_id="+MallUserEnum.mallAutoUser3.getMid();
        boolean couponStatusHasChanged= SqlExcutorUtil.checkSqlValue(jdbcTemplate,couponStatusSql,"status","1",5);

        Assert.isTrue(couponStatusHasChanged,"优惠券状态变更失败");
        ds.close();
    }

    @Test
    @Story("优惠券商品包含/排除")
    public void testCouponCoverage() throws InterruptedException {

        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser3.getPhone(),"qa");
        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);

        String deleteCouponSql = "delete from merchant_coupon where coupon_id between 20063 and 20066 and m_id="+MallUserEnum.mallAutoUser3.getMid();
        jdbcTemplate.execute(deleteCouponSql);

        //排除A
        //包含B
        //省心送不可用
        //仅省心送可用
        String sql3 = "INSERT INTO `xianmudb`.`merchant_coupon`(`id`, `m_id`, `coupon_id`, `vaild_date`, `sender`, `used`, `add_time`, `view`, `order_no`, `receive_type`, `start_time`, `update_time`, `send_id`)\n" +
                "VALUES (NULL, "+MallUserEnum.mallAutoUser3.getMid()+", 20063, '2040-04-23 23:59:59', NULL, 0, '2023-10-26 14:40:06', NULL, NULL, 5, '2023-10-26 14:40:06', NULL, NULL)\n" +
                ";";
        String sql4 = "INSERT INTO `xianmudb`.`merchant_coupon`(`id`, `m_id`, `coupon_id`, `vaild_date`, `sender`, `used`, `add_time`, `view`, `order_no`, `receive_type`, `start_time`, `update_time`, `send_id`)\n" +
                "VALUES (NULL, "+MallUserEnum.mallAutoUser3.getMid()+", 20064, '2040-04-23 23:59:59', NULL, 0, '2023-10-26 14:40:06', NULL, NULL, 5, '2023-10-26 14:40:06', NULL, NULL)\n" +
                ";";
        String sql5 = "INSERT INTO `xianmudb`.`merchant_coupon`(`id`, `m_id`, `coupon_id`, `vaild_date`, `sender`, `used`, `add_time`, `view`, `order_no`, `receive_type`, `start_time`, `update_time`, `send_id`)\n" +
                "VALUES (NULL, "+MallUserEnum.mallAutoUser3.getMid()+", 20065, '2040-04-23 23:59:59', NULL, 0, '2023-10-26 14:40:06', NULL, NULL, 5, '2023-10-26 14:40:06', NULL, NULL)\n" +
                ";";
        String sql6 = "INSERT INTO `xianmudb`.`merchant_coupon`(`id`, `m_id`, `coupon_id`, `vaild_date`, `sender`, `used`, `add_time`, `view`, `order_no`, `receive_type`, `start_time`, `update_time`, `send_id`)\n" +
                "VALUES (NULL, "+MallUserEnum.mallAutoUser3.getMid()+", 20066, '2040-04-23 23:59:59', NULL, 0, '2023-10-26 14:40:06', NULL, NULL, 5, '2023-10-26 14:40:06', NULL, NULL)\n" +
                ";";
        jdbcTemplate.execute(sql3);
        jdbcTemplate.execute(sql4);
        jdbcTemplate.execute(sql5);
        jdbcTemplate.execute(sql6);

        String COrderDetail=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.mallAutoAddress3.getContactId(),  SkuEnum.priceTestC.getSku(),5);
        String AOrderDetail=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.mallAutoAddress3.getContactId(),  SkuEnum.priceTestA.getSku(),5);
        String BOrderDetail=MallOrderUtil.preOrderWithToken("qa", token, AddressEnum.mallAutoAddress3.getContactId(),  SkuEnum.priceTestB.getSku(),5);
        String TimingOrderDetail=MallOrderUtil.preTimingWithToken("qa", token, MallUserEnum.mallAutoUser3.getPhone(), timingRuleId,5);


        Assert.isTrue(COrderDetail.contains("优惠券商品排除测试")&&!AOrderDetail.contains("优惠券商品排除测试"),"优惠券商品排除测试失败");
        Assert.isTrue(BOrderDetail.contains("优惠券商品包含测试")&&!COrderDetail.contains("优惠券商品包含测试"),"优惠券商品包含测试失败");
        Assert.isTrue(COrderDetail.contains("省心送不可用")&&!TimingOrderDetail.contains("省心送不可用"),"省心送不可用测试失败");
        Assert.isTrue(!COrderDetail.contains("仅省心送可用")&&TimingOrderDetail.contains("仅省心送可用"),"仅省心送可用测试失败");

        ds.close();
    }



    /*
    不校验到手价传""
     */
    public void checkSkuPrice(String token,String sku,int quantity,int contactId,String expectActualPrice,String expectPrePrice,String expectOrderPrice,String couponId){
        String actualPriceNow=getGoodsActualPrice(token,sku,quantity);
        String prePriceNow=getPreOrderPrice(token,sku,quantity,contactId);
        String orderPriceNow=getOrderPrice(token,sku,quantity,contactId,couponId);

        System.out.println("实际价格："+actualPriceNow);
        System.out.println("预下单价格："+prePriceNow);
        System.out.println("下单价格："+orderPriceNow);

        if(!expectActualPrice.equals("")){
            Assert.isTrue(expectActualPrice.equals(actualPriceNow),"列表到手价不匹配");
        }
        Assert.isTrue(expectPrePrice.equals(prePriceNow),"预下单价格不匹配");
        Assert.isTrue(expectOrderPrice.equals(orderPriceNow),"下单价格不匹配");
    }

    public static String getGoodsActualPrice(String token,String sku,int quantity){
        String url = "https://qah5.summerfarm.net/price/query/take-actual-price";
        Map<String, Object> orderMap = new HashMap<>();
        orderMap.put("suitId", 0);
        orderMap.put("sku", sku);
        orderMap.put("quantity", 1);

        Map<String, Object> body = new HashMap<>();
        body.put("minLadderPrice", quantity);
        body.put("orderNow", orderMap);
        String bodyStr = JSONObject.toJSONString(body);
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr)
                .execute();
        try{
            String actualPrice= JSONObject.parseObject(response.body()).getJSONArray("data").getJSONObject(0).getString("takeActualPrice");
            return actualPrice;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getPreOrderPrice(String token,String sku,int quantity,int contactId){
        JSONObject jsonObject=JSONObject.parseObject(MallOrderUtil.preOrderWithToken("qa", token, contactId, sku, quantity));
        try{
            return jsonObject.getJSONObject("data").getJSONArray("itemList").getJSONObject(0).getString("actualTotalPrice");
        }catch (Exception e){

        }
        return "";
    }

    public static String getOrderPrice(String token,String sku,int quantity,int contactId,String couponId){
        try{
            String thisOrderUrl = "https://" + "qa"+"h5.summerfarm.net/order/upsert/place-order/v3";
            Map<String, Object> body = new HashMap<>();
            body.put("contactId", contactId);
            body.put("payment",0);
            body.put("deliveryRulesType",1);
            List<Map<String, Object>> orderItemList=new ArrayList<>();
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("sku", sku);
            skuMap.put("quantity",quantity);
            orderItemList.add(skuMap);
            body.put("orderItemList", orderItemList);

            ArrayList<Map<String, Object>> usedCouponsList = new ArrayList<>();


            if(!couponId.equals("")){
                Map<String, Object> usedCouponMap = new HashMap<>();
                usedCouponMap.put("usedCouponId", couponId);
                usedCouponMap.put("couponType", 1);
                usedCouponsList.add(usedCouponMap);

            }
            body.put("usedCoupons",usedCouponsList);
            HttpResponse response = HttpRequest.post(thisOrderUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body(JSONObject.toJSONString(body))
                    .execute();
            String result =response.body();
            System.out.println(result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String orderNo=jsonObject.getJSONObject("data").getJSONArray("subOrderNos").get(0).toString();
            String masterOrderNo= jsonObject.getJSONObject("data").get("masterOrderNo").toString();
            String paymentUrl="https://" + "qa"+"h5.summerfarm.net/payment/pay";
            HttpResponse payResponse =HttpRequest.post(paymentUrl)
                    //.header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .body("masterOrderNo="+masterOrderNo+"&payChannel=1")
                    .execute();
            String orderDetailUrl="https://" + "qa"+"h5.summerfarm.net/order/query/detail/orderNo?orderNo="+orderNo;
            Map<String,String> map = new HashMap<>();
            map.put("orderNo", orderNo);
            HttpResponse orderDetailResponse =HttpRequest.post(orderDetailUrl)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                    .header("token", token)
                    .body(JSONObject.toJSONString(map))
                    .execute();
            return JSONObject.parseObject(orderDetailResponse.body()).getJSONObject("data").getJSONObject("orderVO")
                    .getJSONArray("orderItems").getJSONObject(0).getString("actualTotalPrice");
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }




}
