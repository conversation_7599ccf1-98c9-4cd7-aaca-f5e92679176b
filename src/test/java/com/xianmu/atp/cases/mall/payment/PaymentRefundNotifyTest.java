package com.xianmu.atp.cases.mall.payment;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("退款回调测试")
@Owner("zyl")
@Slf4j
public class PaymentRefundNotifyTest extends BaseTest {

    @Value("147")
    private int fireFaceAccount;

    @Value("153")
    private int dingAccount;

    @Value("5")
    private int wxAccount;

    @Autowired
    private DynamicDataSourceProperties properties;

    @Test
    @Story("火脸退款回调")
    public void testFireFaceRefundNotify() throws InterruptedException {
        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String sql1 = "update refund set status=4 where refund_no='01252QWMF30319155826MNPPWD'";
        String sql2 = "INSERT INTO xianmudb.refund_handle_event ( refund_no, status, retry_count, create_time, update_time) VALUES ( '01252QWMF30319155826MNPPWD', 1, 0, '2022-11-03 17:56:23', '2022-11-03 17:56:23')";

        jdbcTemplate.execute(sql1);
        jdbcTemplate.execute(sql2);
        Thread.sleep(2000);



        String env="qa";

        String formattedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        Map<String,Object> bodyMap = new HashMap<>();
        bodyMap.put("refundAmount",100);
        bodyMap.put("businessRefundNo","01252QWMF30319155826MNPPWD");
        bodyMap.put("businessOrderNo","02158555767025297");
        bodyMap.put("channelRefundNo","r2025030417340076210");
        bodyMap.put("merchantNo",MallUserEnum.mallAutoUser.getMid());
        bodyMap.put("orderNo", "02158555767025297");
        bodyMap.put("refundStatus",2);
        bodyMap.put("orderStatus",2);
        bodyMap.put("payWay","wechat");
        bodyMap.put("remark", SkuEnum.skuBillAfter.getSkuName());
        bodyMap.put("refundTime",formattedTime);
        bodyMap.put("topChannelRefundNo","50302502702025030424305510220");
        bodyMap.put("userId","oFQai6-9xyQrj5A_AoFMUy5Yv4o8");



        Map<String,Object> notifyMap = new HashMap<>();
        notifyMap.put("authCode","****************");
        notifyMap.put("code","200");
        notifyMap.put("message","请求成功");
        notifyMap.put("respBody",bodyMap);
        notifyMap.put("requestTime",formattedTime);

        notifyMap.put("resource", "api.hl.applet.b2b.preAppletB2b");
        notifyMap.put("sign","a04fbd9f0d215da4f517c71ef700c4c4");

        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/refund-notify/fire-face/"+fireFaceAccount)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(JSONObject.toJSONString(notifyMap))
                .execute();
        Thread.sleep(3000);
        //Assert.isTrue(response.body().contains("SUCCESS"),"火脸退款回调失败");
    }


    @Test
    @Story("智付退款回调")
    public void testDinRefundNotify() throws InterruptedException {

        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String sql1 = "update refund set status=4 where refund_no='012547WLWH0314113278A99AW9'";
        String sql2 = "INSERT INTO xianmudb.refund_handle_event ( refund_no, status, retry_count, create_time, update_time) VALUES ( '012547WLWH0314113278A99AW9', 1, 0, '2022-11-03 17:56:23', '2022-11-03 17:56:23')";

        jdbcTemplate.execute(sql1);
        jdbcTemplate.execute(sql2);
        Thread.sleep(2000);


        String env="qa";

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(false);
        String masterOrderNo = mallOrder.getMasterOrderNo();


        Map<String, Object> jsonMap = new HashMap<>();


        String formattedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        // 将JSON中的键值对存入Map

        jsonMap.put("refundFee", 100);
        jsonMap.put("payOrderNo", "M0125CNJNBO0304171526");
        jsonMap.put("refundNotifyDate", "*************");
        jsonMap.put("refundOrderAmount", 100);
        jsonMap.put("refundChannelOrderNum", "50103602562025030485456808718");
        jsonMap.put("refundOrderCompleteDate", formattedTime);
        jsonMap.put("refundFeeAccountAmt", 0);
        jsonMap.put("refundChannelNumber", "25030417170051602426309");
        jsonMap.put("paymentType", "WXPAY");
        jsonMap.put("controlType", "CONTROL");
        jsonMap.put("merchantId", "S10000000009651");
        jsonMap.put("refundOrderNo", "012547WLWH0314113278A99AW9");
        jsonMap.put("refundOrderStatus", "SUCCESS");
        jsonMap.put("refundUserAmount", 100);
        jsonMap.put("paymentMethods", "PUBLIC");
        jsonMap.put("currency", "CNY");
        jsonMap.put("refundAmount", 100);
        jsonMap.put("companyAccountId", dingAccount);

        String notifyStr="msg"+"="+"接口调用成功"+"&"+
                "code"+"="+"0000"+"&"+
                "msg"+"="+"接口调用成功"+"&"+
                "data"+"="+JSONObject.toJSONString(jsonMap)+"&"+
                "merchantId"+"="+"接口调用成功"+"&"+
                "sign"+"="+"mocksigntt6hRT/99i0L2Faxzc6nhLmp4cxbsp5MxBuFn/NBAiEAo0QFRcib8EC5NM1VipeRWaYU9wYtNRcJib5hHg8C9xI="+"&"+
                "signatureMethod"+"="+"SM3WITHSM2"+"&";


        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/refund-notify/din")
                .header(Header.CONTENT_TYPE,"application/x-www-form-urlencoded")
                .body(notifyStr)
                .execute();
        Thread.sleep(3000);
        //Assert.isTrue(response.body().contains("success"),"智付退款回调失败");

    }


    @Test
    @Story("招行退款回调")
    public void testCmbRefundNotify() throws InterruptedException {

        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String sql1 = "update refund set status=4 where refund_no='011557216475031374'";
        jdbcTemplate.execute(sql1);


        String env="qa";

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(false);
        String masterOrderNo = mallOrder.getMasterOrderNo();


        Map<String, Object> jsonMap = new HashMap<>();


        String formattedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String formattedData = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String formattedSecond = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        // 将JSON中的键值对存入Map
        jsonMap.put("merId", "3089991074200IY");
        jsonMap.put("orderId", "011557216475031374");
        jsonMap.put("cmbOrderId", "1006250304155452********");
        jsonMap.put("userId", "N003338026");
        jsonMap.put("currencyCode", "156");
        jsonMap.put("openId", "****************");
        jsonMap.put("txnTime", formattedTime);
        jsonMap.put("endDate", formattedData);
        jsonMap.put("endTime", formattedSecond);
        jsonMap.put("refundAmt", "10000");
        jsonMap.put("mchReserved", "107");
        jsonMap.put("dscAmt", "0");
        jsonMap.put("thirdOrderId", "682025030422001477921436938756");
        jsonMap.put("payBank", "ALIPAYACCOUNT");
        jsonMap.put("buyerLogonId", "156****8291");
        jsonMap.put("payChannel", "U");


        String notifyStr="encoding"+"="+"UTF-8"+"&"+
                "version"+"="+"0.0.1"+"&"+
                "biz_content"+"="+JSONObject.toJSONString(jsonMap)+"&"+
                "sign"+"="+"mocksigntt6hRT/99i0L2Faxzc6nhLmp4cxbsp5MxBuFn/NBAiEAo0QFRcib8EC5NM1VipeRWaYU9wYtNRcJib5hHg8C9xI="+"&"+
                "signMethod"+"="+"02";


        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/refund-notify/cmb")
                .header(Header.CONTENT_TYPE,"application/x-www-form-urlencoded")
                .body(notifyStr)
                .execute();
        Thread.sleep(3000);
        //Assert.isTrue(response.body().contains("SUCCESS"),"招行退款回调失败");
    }
}
