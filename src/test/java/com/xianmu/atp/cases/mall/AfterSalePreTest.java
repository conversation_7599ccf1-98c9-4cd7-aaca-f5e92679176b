package com.xianmu.atp.cases.mall;


import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.AfterSaleHelper;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

@Epic("mall")
@Feature("售后")
@Owner("zyl")
@Slf4j
public class AfterSalePreTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;



    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("单店未到货售后发起前链路")
    public void mallAfterSaleTest() throws InterruptedException {
        String env = "qa";
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuForActivityLimit,2);
        mallOrder.send(true);
        String orderNo = mallOrder.getOrderNo();
        System.out.println(orderNo);

        String accountStatusUrl = "https://"+env+"h5.summerfarm.net/sub-account/status?accountId=3383";
        String token= getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse accountResponse = HttpRequest.get(accountStatusUrl)
                .header("token", token)
                .execute();

        String preAfterSaleUrl="https://"+env+"h5.summerfarm.net/after-sale/pre";
        preAfterSaleUrl+="?sku="+SkuEnum.skuForActivityLimit.getSku()+"&orderNo="+orderNo;
        HttpResponse preAfterSaleResponse = HttpRequest.get(preAfterSaleUrl)
                .header("token", token)
                .execute();

        String deliveryUrl="https://"+env+"h5.summerfarm.net/after-sale/deliveryStatus";
        deliveryUrl+="?orderNo="+orderNo+"&isManage=false";
        HttpResponse deliveryResponse = HttpRequest.get(deliveryUrl)
                .header("token", token)
                .execute();

        String handTyepUrl="https://"+env+"h5.summerfarm.net/after-sale/getHandleType";
        handTyepUrl+="?orderNo="+orderNo+"&sku="+SkuEnum.skuForActivityLimit.getSku()+"&deliveryed=0&isManage=false";
        HttpResponse handTyepUrlResponse = HttpRequest.get(handTyepUrl)
                .header("token", token)
                .execute();

        String calQuantityUrl="https://"+env+"h5.summerfarm.net/after-sale/calculateQuantity";
        Map<String,Object> calQuantityBody=new HashMap<>();
        calQuantityBody.put("orderNo",orderNo);
        calQuantityBody.put("sku",SkuEnum.skuForActivityLimit.getSku());
        calQuantityBody.put("deliveryed",0);
        calQuantityBody.put("quantity","");
        calQuantityBody.put("suitId",0);
        calQuantityBody.put("handleType",2);
        HttpResponse calQuantityResponse = HttpRequest.post(calQuantityUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(calQuantityBody))
                .execute();

        String afterSaleMoneyUrl="https://"+env+"h5.summerfarm.net/after-sale/afterSaleMoney";
        Map<String,Object> afterSaleMoneyBody=new HashMap<>();
        afterSaleMoneyBody.put("orderNo",orderNo);
        afterSaleMoneyBody.put("sku",SkuEnum.skuForActivityLimit.getSku());
        afterSaleMoneyBody.put("deliveryed",0);
        afterSaleMoneyBody.put("quantity",2);
        afterSaleMoneyBody.put("suitId",0);
        afterSaleMoneyBody.put("handleType",2);

        HttpResponse afterSaleMoneyResponse =  HttpRequest.post(afterSaleMoneyUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(afterSaleMoneyBody))
                .execute();

        String checkDeliveryFeeUrl="https://"+env+"h5.summerfarm.net/after-sale/query/check-delivery-fee";
        HttpResponse checkDeliveryFeeResponse = HttpRequest.post(checkDeliveryFeeUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(JSONObject.toJSONString(afterSaleMoneyBody))
                .execute();

    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("账期大客户已到货售后发起前链路")
    public void billAfterSalePreTest() throws InterruptedException {
        String env = "qa";
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.bigUser);
        mallOrder.addAddress(AddressEnum.bigUserAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,2);
        mallOrder.send(true);
        String orderNo = mallOrder.getOrderNo();
        System.out.println(orderNo);

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        Thread.sleep(1000);
        AfterSaleHelper.setOrderReceivable(jdbcTemplate,orderNo);

        String accountStatusUrl = "https://"+env+"h5.summerfarm.net/sub-account/status?accountId=11260";
        String token= getXianmuMallUserToken(MallUserEnum.bigUser.getPhone(),env);
        HttpResponse accountResponse = HttpRequest.get(accountStatusUrl)
                .header("token", token)
                .execute();

        String preAfterSaleUrl="https://"+env+"h5.summerfarm.net/after-sale/pre";
        preAfterSaleUrl+="?sku="+SkuEnum.skuBillAfter.getSku()+"&orderNo="+orderNo;
        HttpResponse preAfterSaleResponse = HttpRequest.get(preAfterSaleUrl)
                .header("token", token)
                .execute();

        String deliveryUrl="https://"+env+"h5.summerfarm.net/after-sale/deliveryStatus";
        deliveryUrl+="?orderNo="+orderNo+"&isManage=false";
        HttpResponse deliveryResponse = HttpRequest.get(deliveryUrl)
                .header("token", token)
                .execute();

        String handTyepUrl="https://"+env+"h5.summerfarm.net/after-sale/getHandleType";
        handTyepUrl+="?orderNo="+orderNo+"&sku="+SkuEnum.skuBillAfter.getSku()+"&deliveryed=0&isManage=false";
        HttpResponse handTyepUrlResponse = HttpRequest.get(handTyepUrl)
                .header("token", token)
                .execute();

        String calQuantityUrl="https://"+env+"h5.summerfarm.net/after-sale/calculateQuantity";
        Map<String,Object> calQuantityBody=new HashMap<>();
        calQuantityBody.put("orderNo",orderNo);
        calQuantityBody.put("sku",SkuEnum.skuBillAfter.getSku());
        calQuantityBody.put("deliveryed",0);
        calQuantityBody.put("quantity","");
        calQuantityBody.put("suitId",0);
        calQuantityBody.put("handleType",2);
        HttpResponse calQuantityResponse = HttpRequest.post(calQuantityUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(calQuantityBody))
                .execute();

        String afterSaleMoneyUrl="https://"+env+"h5.summerfarm.net/after-sale/afterSaleMoney";
        Map<String,Object> afterSaleMoneyBody=new HashMap<>();
        afterSaleMoneyBody.put("orderNo",orderNo);
        afterSaleMoneyBody.put("sku",SkuEnum.skuBillAfter.getSku());
        afterSaleMoneyBody.put("deliveryed",0);
        afterSaleMoneyBody.put("quantity",2);
        afterSaleMoneyBody.put("suitId",0);
        afterSaleMoneyBody.put("handleType",3);

        HttpResponse afterSaleMoneyResponse =  HttpRequest.post(afterSaleMoneyUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(afterSaleMoneyBody))
                .execute();

        String checkDeliveryFeeUrl="https://"+env+"h5.summerfarm.net/after-sale/query/check-delivery-fee";
        HttpResponse checkDeliveryFeeResponse = HttpRequest.post(checkDeliveryFeeUrl)
                .header("token", token)
                .header(Header.CONTENT_TYPE, "application/json")
                .body(JSONObject.toJSONString(afterSaleMoneyBody))
                .execute();
        ds.close();

    }

}
