package com.xianmu.atp.cases.mall.dubbo;


import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.AfterOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Epic("mall")
@Feature("订单dubbo调用")
@Owner("zyl")
@Slf4j
public class OrderDubboTest extends BaseTest {
    //orderPlaceOrderV3 有一堆判错代码
    //getCloseFlag  过于麻烦，但覆盖行数很少

    @Resource
    private AfterOrderUtil afterOrderUtil;

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void closeOrderTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo",mallOrder.getOrderNo());
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.OrderProvider")
                .methodName("closeOrder")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.OrderReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void addOrderTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo",mallOrder.getOrderNo());
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.OrderProvider")
                .methodName("addOrder")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AddOrderReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }


    //注意后续的截单时间判断
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void updateDeliveryDateTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo",mallOrder.getOrderNo());
        paramMap.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        LocalDate localdate = LocalDate.now();
        paramMap.put("DeliveryTime",localdate.plusDays(3));
        paramMap.put("oldDeliveryTime",localdate.plusDays(1));
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.OrderProvider")
                .methodName("updateDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.UpdateDeliveryDateReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }


    //定时任务发样品，逻辑有点复杂，后面直接定时任务把
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void queryOrderInfoByAutoCreateSampleTaskTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        List<Long> mIdList=new ArrayList<>();
        mIdList.add(Long.valueOf(MallUserEnum.mallAutoUser.getMid()) );
        paramMap.put("mIdList",mIdList);

        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.OrderProvider")
                .methodName("queryOrderInfoByAutoCreateSampleTask")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.order.AutoCreateSampleReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    //@Test
    public void getCloseFlagTest() {
//        MallOrder mallOrder = new MallOrder();
//        mallOrder.addEnv(EnvEnum.qa);
//        mallOrder.addUser(MallUserEnum.mallAutoUser);
//        mallOrder.addAddress(AddressEnum.mallAutoAddress);
//        mallOrder.addSku(SkuEnum.skuBillAfter,1);
//        mallOrder.send(true);
//
//        Map<String, Object> paramMap=new HashMap<>();
//        paramMap.put("orderNo",mallOrder.getOrderNo());
//        //数据初始化
//        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
//                .env("qa")
//                .interfaceName("net.summerfarm.mall.client.provider.OrderProvider")
//                .methodName("getCloseFlag")
//                .paramTypes(new String[]{"net.summerfarm.mall.client.req.OrderVO", "java.util.List"})
//                .params(new Object[]{orderVO, planVOList})
//                .build();
//        //调用获取结果
//        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
//        log.info("调用结果：{}", atpDto);
//        //assert TODO: 2025/断言
    }
}
