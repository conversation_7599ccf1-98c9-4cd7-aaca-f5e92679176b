package com.xianmu.atp.cases.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.MerchantDao;
import com.xianmu.atp.dal.model.Merchant;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.generic.mall.SelftUsages.MerchantHelper;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

@Epic("mall")
@Feature("注销接口")
@Owner("zyl")
@Slf4j
public class MerchantCancelTest extends BaseTest {

    @Resource
    private MerchantDao merchantDao;

    @Autowired
    private DynamicDataSourceProperties properties;

    @Test(description = "注销重试",
            retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"5"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("注销接口")
    public void merchantCancelTest() throws InterruptedException{



        String env= EnvEnum.qa.getName();
        String applyCancelUrl="https://"+env
                +"h5.summerfarm.net/merchant/cancel/upsert/insert";

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        jdbcTemplate.execute("delete from merchant where phone='***********' ");
        jdbcTemplate.execute("delete from merchant_sub_account where phone='***********' ");


        //有订单的门店申请注销
        String token= getXianmuMallUserToken("***********",env);
        HttpResponse response = HttpRequest.post(applyCancelUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("{\"remake\":\"autotest\"}")
                .execute();
        Assert.isTrue(response.body().contains("存在未完成的订单\",\"账期门店暂不支持注销"),"未校验门店是否有订单");

        long mid= MerchantHelper.createMerchantToCancel();
        Thread.sleep(3000);
        jdbcTemplate.execute("update merchant set role_id=-1 where phone='***********' ");
        SqlExcutorUtil.checkSqlValue(jdbcTemplate,"select * from merchant where m_id="+mid, "islock", "0", 10);
        SqlExcutorUtil.checkSqlValue(jdbcTemplate,"select * from merchant where m_id="+mid, "role_id", "-1", 5);

        Thread.sleep(3000);
        //可注销门店
        String token2= getXianmuMallUserToken("***********",env);
        HttpResponse response2 = HttpRequest.post(applyCancelUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token2)
                .body("{\"remake\":\"autotest\"}")
                .execute();

        HttpResponse response99 = HttpRequest.post(applyCancelUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token2)
                .body("{\"remake\":\"autotest\"}")
                .execute();



        String queryInfoUrl="https://"+env +"h5.summerfarm.net/merchant/cancel/query/detail";
        HttpResponse response3 = HttpRequest.post(queryInfoUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token2)
                .execute();

        JSONObject json = JSONObject.parseObject(response3.body());
        log.info("json:{}",json);
        Allure.addAttachment("json:", json.toJSONString());
        String id= json.getJSONObject("data").getString("id");
        String confirmUrl="https://"+env +"h5.summerfarm.net/merchant/cancel/upsert/update-status";
        HttpResponse response4 = HttpRequest.post(confirmUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token2)
                .body("{\"remake\":\"autotest\",id:"+id+"}")
                .execute();
        ds.close();
        Assert.isTrue(response4.isOk(),"发起注销失败");
    }

    @Test
    @Story("主账号预校验")
    public void testMasterAccountCheck(){
        String env= EnvEnum.qa.getName();
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        String checkUrl = "https://"+env+"h5.summerfarm.net/merchant/cancel/upsert/check";
        HttpResponse response = HttpRequest.post(checkUrl)
                .header("token", token)
                .header("Content-Type", "application/json")
                .body("{}")
                .execute();
        Assert.isTrue(response.isOk(),"主账号预校验报错");
    }
}
