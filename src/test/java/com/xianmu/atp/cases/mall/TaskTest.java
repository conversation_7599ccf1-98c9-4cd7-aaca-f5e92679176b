package com.xianmu.atp.cases.mall;


import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.common.ExecuteJob;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Epic("mall定时任务测试")
@Owner("zyl")
@Slf4j
public class TaskTest extends BaseTest {

    @Resource
    private ExecuteJob executeJob;

    @Story("mall定时任务批量执行")
    @Test(description = "定时任务批量执行")
    public void test02() {
        String namespace = "qa";
        String groupId = "mall";
        Map<String, Long> map = new HashMap<>();
        map.put("每日重置商家弹窗", 2942335L);
        map.put("红包自动入账、失效", 2942336L);
        map.put("设置中银收款使用次数为0", 2942337L);
        map.put("同步推荐商品", 2942338L);
        map.put("向订阅了秒杀的推送消息", 2942339L);
        map.put("清除购物车换购已删除商品", 2942341L);
        map.put("优惠券过期通知", 2942342L);
        map.put("省心送即将退款预警通知", 4819445L);
        map.put("订单配送提醒通知", 6065862L);
        map.put("省心送退款短信通知", 6454962L);
        map.put("省心送自动售后", 6723686L);
        map.put("新人注册发放卡劵兜底方案", 9044179L);
        map.put("实际退款", 24591691L);
        for (Map.Entry<String, Long> entry : map.entrySet()){
            String result = executeJob.RunExecuteJob(namespace, groupId, entry.getValue());
            log.info("定时任务:{};执行结果：{}", entry.getKey(), result);
        }
    }
}
