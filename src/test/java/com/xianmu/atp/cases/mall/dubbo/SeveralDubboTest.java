package com.xianmu.atp.cases.mall.dubbo;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.json.JsonUtil;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Epic("mall")
@Feature("一些dubbo调用")
@Owner("zyl")
@Slf4j
public class SeveralDubboTest extends BaseTest {

    @Resource
    private DefaultDubboConfig defaultDubboConfig;


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("addressInfo")
    public void addressInfoTest() {

        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("areaNo", 1001);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.saas.provider.AddressInfoProvider")
                .methodName("getAddressInfo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.saas.req.AddressInfoReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"根据运营区域获取地址调用失败");



        Map<String, Object> paramMap2=new HashMap<>();
        List<Object> list = new ArrayList<>();
        Map<String, Object> addressInfoMap1 = new HashMap<>();
        addressInfoMap1.put("provinceName", "浙江省");
        addressInfoMap1.put("cityName", "杭州市");
        addressInfoMap1.put("areaName", "西湖区");

        Map<String, Object> addressInfoMap2 = new HashMap<>();
        addressInfoMap2.put("provinceName", "浙江省");
        addressInfoMap2.put("cityName", "台州市");
        addressInfoMap2.put("areaName", "温岭市");

        list.add(addressInfoMap1);
        list.add(addressInfoMap2);
        //paramMap.put("orderNo", timingOrderNo);
        paramMap2.put("addressInfoVOS", list);

        ATPRequestDto atpRequestDto2 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.saas.provider.AddressInfoProvider")
                .methodName("getAreaNoByAddress")
                .paramTypes(new String[]{"net.summerfarm.mall.client.saas.req.AreaNoByAddressReq"})
                .params(new Object[]{paramMap2})
                .build();
        //调用获取结果
        ATPDto atpDto2 = defaultDubboConfig.getDubboResult(atpRequestDto2);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto2.getDubboResponse().toString().contains("success"),"根据地址获取运营区域调用失败");
    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("AreaInfo")
    public void areaInfoTest() {
        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("status", 1);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.saas.provider.AreaInfoProvider")
                .methodName("getAll")
                .paramTypes(new String[]{"net.summerfarm.mall.client.saas.req.AreaInfoReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"根据状态获取全量运营区域失败");

    }



    //需要组合配送评价接口
    //暂时会报错
    public void deliveryEvaluationTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo", mallOrder.getOrderNo());
        paramMap.put("contactId", AddressEnum.mallAutoAddress.getContactId());
        paramMap.put("deliveryTime", LocalDate.now().plusDays(1));
        paramMap.put("pageIndex", 1);
        paramMap.put("pageSize", 6);
        paramMap.put("startRow", 0);
        paramMap.put("needPagination",0);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DeliveryEvaluationProvider")
                .methodName("getDeliveryEvaluation")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryEvaluationQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取评价失败");

    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("admin")
    //应该有数据才对
    public void adminTest() {


        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("pageIndex", 1);
        paramMap.put("pageSize", 6);
        paramMap.put("adminId", 4647);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AdminProvider")
                .methodName("getAdminDetail")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AdminReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"admin信息获取失败");

    }


//    @Test(retryAnalyzer = XMRetryAnalyzer.class,
//            attributes = {
//                    @CustomAttribute(name = "loop", values = {"3"}),
//                    @CustomAttribute(name = "interval", values = {"3"})
//            })
//    @Story("saasOrderareaInfoTest")
//    public void saasOrderareaInfoTest() {
//        Map<String, Object> paramMap=new HashMap<>();
//        //paramMap.put("orderNo", timingOrderNo);
//        paramMap.put("cosfoOrderNo", "OR173564047012762");
//        paramMap.put("pageIndex", 1);
//        paramMap.put("pageSize", 6);
//
//        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
//                .env("qa")
//                .interfaceName("net.summerfarm.mall.client.saas.provider.OutSideContactProvider")
//                .methodName("getOrderDetail")
//                .paramTypes(new String[]{"net.summerfarm.mall.client.saas.req.OutSideContactReq"})
//                .params(new Object[]{paramMap})
//                .build();
//        //调用获取结果
//        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
//        log.info("调用结果：{}", atpDto);
//        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"根据状态获取全量运营区域失败");
//
//    }
}
