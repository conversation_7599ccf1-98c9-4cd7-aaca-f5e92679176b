package com.xianmu.atp.cases.mall;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.JsonDealer;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.generic.mall.SelftUsages.ShopCart;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

@Epic("mall")
@Feature("大客户购物车")
@Owner("zyl")
@Slf4j
public class BigUserShopCartTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("账期大客户获取购物车价格信息")
    public void billBigUserShopCartTest() throws InterruptedException {
        ShopCart bigUserShopCart=new ShopCart();
        bigUserShopCart.addUser(MallUserEnum.bigUser);
        bigUserShopCart.addEnv(EnvEnum.qa);
        bigUserShopCart.addSku(SkuEnum.skuForActivityLimit,1);
        JSONObject jsonObject = bigUserShopCart.getCartInfo();
        int result=JsonDealer.findKeyInJson(jsonObject,"salePrice","15.00");

        MallOrder mallOrder = new MallOrder();
        mallOrder.addUser(MallUserEnum.bigUser);
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addSku(SkuEnum.skuForActivityLimit,1);
        mallOrder.addAddress(AddressEnum.bigUserAddress);

        mallOrder.send(false);
        mallOrder.payByBill();
        String orderNo = mallOrder.getOrderNo();
        bigUserShopCart.clearCart();
        bigUserShopCart.getCartInfo();

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        boolean orderPriceSuccess=SqlExcutorUtil.checkSqlValue(jdbcTemplate,"SELECT * FROM xianmudb.orders WHERE order_no='"+orderNo+"'","total_price","15.00",5);
        ds.close();

        Assert.isTrue(result==1,"大客户指定价报价单购物车价格获取错误");
        Assert.isTrue(orderPriceSuccess,"大客户指定价报价单下单价格获取错误");
    }
}
