package com.xianmu.atp.cases.mall;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;


@Epic("mall")
@Feature("抽奖活动")
@Owner("zyl")
@Slf4j
public class LuckyDrawTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("抽奖")
    public void luckyDrawTest() throws InterruptedException {
        String env= EnvEnum.qa.getName();
        String url= "https://" +env +"h5.summerfarm.net/luck/draw/activity/query/info";
        String token= MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);

        //红包雨信息
        String body="{\"type\":1}";
        HttpResponse response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body)
                .execute();
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        Assert.isTrue(jsonObject.getJSONObject("data").get("id").equals(144), "未返回红包雨");


        //抽奖信息
        String body2="{\"type\":2}";
        HttpResponse response2 = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body2)
                .execute();
        JSONObject jsonObject2 = JSONObject.parseObject(response2.body());
        Assert.isTrue(jsonObject2.getJSONObject("data").get("id").equals(145), "未返回抽奖活动");


        //抽红包雨
        String body3="{\"activityId\":144}";
        String drawUrl= "https://" +env +"h5.summerfarm.net/luck/draw/activity/upsert/start";
        HttpResponse response3 = HttpRequest.post(drawUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body3)
                .execute();


        //抽奖详情
        String body4="{\"id\":145,\"type\":2}";
        String detailUrl= "https://" +env +"h5.summerfarm.net/luck/draw/activity/query/detail";
        HttpResponse response4 = HttpRequest.post(detailUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body4)
                .execute();



        //抽抽奖
        String body6="{\"activityId\":145}";
        HttpResponse response6 = HttpRequest.post(drawUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body6)
                .execute();

        //奖项详情
        String body5="{\"id\":145,\"type\":2}";
        String prizeUrl= "https://" +env +"h5.summerfarm.net/luck/draw/activity/query/prize";
        HttpResponse response5 = HttpRequest.post(prizeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(body5)
                .execute();

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        String rewardsql="select count(1) as num from merchant_coupon where m_id="+MallUserEnum.mallAutoUser2.getMid();
        boolean getAllReawards= SqlExcutorUtil.checkSqlValue(jdbcTemplate,rewardsql,"num","4", 5);
        //重置数据
        jdbcTemplate.execute("delete from merchant_coupon where m_id="+MallUserEnum.mallAutoUser2.getMid());
        jdbcTemplate.execute("delete from lucky_draw_activity_record where m_id="+MallUserEnum.mallAutoUser2.getMid());
        jdbcTemplate.execute("update lucky_draw_activity_equity_package set surplus_quantity=100000 where activity_id in (144,145)");
        log.info("getAllReawards:{}", String.valueOf(getAllReawards));
        Allure.addAttachment("getAllReawards:", String.valueOf(getAllReawards));
        ds.close();
        Assert.isTrue(getAllReawards,"未抽中4个红包");


    }
}
