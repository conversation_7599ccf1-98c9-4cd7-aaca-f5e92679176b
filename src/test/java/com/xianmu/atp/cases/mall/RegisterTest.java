package com.xianmu.atp.cases.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPRedisRequestDto;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.JsonDealer;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.redis.DefaultRedisConfig;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Epic("mall")
@Feature("注册相关")
@Owner("zyl")
@Slf4j
public class RegisterTest extends BaseTest {

    @Resource
    private DefaultRedisConfig defaultRedisConfig;

    //预注册



    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private LoginHttp login;

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("验证码相关")
    public void registerCodeTest(){
        String env="qa";
        String getCodeUrl="https://"+env+"h5.summerfarm.net/register/VerificationCodeV2";
        String token=MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), env);
        HttpResponse response1 = HttpRequest.post(getCodeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("mobile=13773113731&popMerchant=true")
                .execute();

        HttpResponse response2 = HttpRequest.post(getCodeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("mobile=13773113731&popMerchant=false")
                .execute();

        Assert.isTrue(response1.isOk(), "pop验证码获取失败");
        Assert.isTrue(response2.isOk(), "商城验证码获取失败");

        String getCodeUrlv1="https://"+env+"h5.summerfarm.net/register/VerificationCode";
        HttpResponse response3 = HttpRequest.post(getCodeUrlv1)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("mobile=13773113731")
                .execute();
        Assert.isTrue(response3.isOk(), "老验证码获取失败");
        String yzmCode=JsonDealer.getKeyStringINJson(JSONObject.parseObject(response3.body()), "msg");

        String checkYZM="https://"+env+"h5.summerfarm.net/register/VerifyYZM";
        HttpResponse response4 = HttpRequest.post(checkYZM)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", token)
                .body("type=login&yzm="+112233)
                .execute();
        Assert.isTrue(response4.body().contains("请输入正确的验证码"), "验证码验证失败");
    }

    @Test()
    @Story("商城正向注册和重新绑定")
    public void registerTest() throws InterruptedException {

        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String deleteSql1="delete from merchant where mname='商城注册自动化名称'";
        String deletesql2="delete from merchant_sub_account where contact='商城注册自动化名称'";
        jdbcTemplate.execute(deleteSql1);
        jdbcTemplate.execute(deletesql2);

        JdbcTemplate saasJdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.cosfodb);
        String deleteSql3="delete from cosfodb.merchant_store where store_name='商城注册自动化名称'";
        saasJdbcTemplate.execute(deleteSql3);

        String token="1-5026d495154343bea198c30c198130e2";
        String key="login:mall:1-5026d495154343bea198c30c198130e2";
        Map<String,Object> map=new HashMap<>();
        map.put("loginWay","wechat_gzh");
        map.put("openId","oZbI26ROn4gvNgLwwWCIDzDppppp");

        String registerPhone="***********";

        //redisTemplate.opsForValue().set(key, JSON.toJSONString(map), 60*60*24, TimeUnit.SECONDS);

        ATPRedisRequestDto dto = ATPRedisRequestDto.builder()
                .dbIndex("5")
                .operation("set")
                .queryContent( JSON.toJSONString(map))
                .queryKey(key)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res = defaultRedisConfig.getRedisResult(dto);
        log.info(res);

        String phoneUrl = "https://qah5.summerfarm.net/register/Phone";
        HttpResponse response4 = HttpRequest.post(phoneUrl)
                .header("token", token)
                .contentType("application/x-www-form-urlencoded")
                .body("phone="+registerPhone)
                .execute();

        HttpResponse response5 = HttpRequest.post(phoneUrl)
                .header("token", token)
                .contentType("application/x-www-form-urlencoded")
                .body("phone="+MallUserEnum.mallAutoUser.getPhone())
                .execute();

        String env="qa";
        String registerUrl="https://"+env+"h5.summerfarm.net/register/VerifyBL";
        registerUrl+="?";
        registerUrl+="doorPic"+"="+"fe-biz/xm-mall/53m4nr0754q"+"&"+
                "mname"+"="+"商城注册自动化名称"+"&"+
                "mcontact"+"="+"商城注册自动化名称"+"&"+
                "province"+"="+"浙江"+"&"+
                "city"+"="+"杭州市"+"&"+
                "area"+"="+"西湖区"+"&"+
                "address"+"="+"浙大路1号 西湖区政协"+"&"+
                "phone"+"="+registerPhone+"&"+
                "invitecode"+"="+"seeegj"+"&"+
                "houseNumber"+"="+"33214"+"&"+
                "type"+"="+"咖啡"+"&"+
                "doorPic"+"="+"fe-biz/xm-mall/53m4nr0754q"+"&"+
                "poiNote"+"="+"120.13019,30.25964";
        HttpResponse response2 = HttpRequest.get(registerUrl)
                .header("token", token)
                .execute();

        Assert.isTrue(response2.isOk(), "注册失败");

        String deletesql3="update merchant set role_id=-1 where phone='"+registerPhone+"'";
        jdbcTemplate.execute(deletesql3);


        Thread.sleep(3000);
        String mallToken=MallOrder.getXianmuMallUserToken(registerPhone, env);

        String url = "https://qah5.summerfarm.net/merchant/urge-audit";
        HttpResponse response3 = HttpRequest.post(url)
                .header("token", mallToken)
                .execute();

        Map<String, Object> auditMap = new HashMap<>();

        // 将 JSON 数据的键值对添加到 auditMap 中
        auditMap.put("state", 0);
        auditMap.put("areaNo", 1001);
        auditMap.put("size", "大客户");
        auditMap.put("direct", 2);
        auditMap.put("type", "咖啡");
        auditMap.put("enterpriseScale", "未知");
        auditMap.put("adminId", 6012000);
        auditMap.put("server", 1);
        auditMap.put("mname", "商城注册自动化名称");
        auditMap.put("mcontact", "商城注册自动化名称");
        auditMap.put("phone", "***********");
        auditMap.put("skuShow", 1);
        auditMap.put("poiNote", "120.13019,30.25964");
        auditMap.put("address", "浙大路1号 西湖区政协");
        auditMap.put("showPrice", 1);
        auditMap.put("printOutTMSConfig", true);
        auditMap.put("cluePool", 1);
        auditMap.put("houseNumber", "33214");
        auditMap.put("merchantType", "普通");
        auditMap.put("outerMappings", new Object[]{}); // 空数组
        auditMap.put("displayButton", 1);
        auditMap.put("doorPic", "fe-biz/xm-mall/53m4nr0754q,fe-biz/xm-mall/53m4nr0754q");

        String adminToken= login.login();





        String newMid= SqlExcutorUtil.getOnlyValue(jdbcTemplate,"select m_id from merchant where mname='商城注册自动化名称'");
        System.out.println(newMid);

        String aduitUrl = "https://qaadmin.summerfarm.net/merchant/merchant-info-verify/"+newMid.toString();
        HttpResponse auditResponse = HttpRequest.post(aduitUrl)
                .header("token", adminToken)
                .contentType("application/json")
                .body(JSON.toJSONString(auditMap))
                .execute();

        boolean success = SqlExcutorUtil.checkSqlValue(jdbcTemplate, "select islock from merchant where mname='商城注册自动化名称'", "islock", "0", 5);
        Assert.isTrue(success, "商城注册失败");


        String token2="1-5026d495154343bea198c30c19813fff";
        String key2="login:mall:1-5026d495154343bea198c30c19813fff";
        Map<String,Object> map2=new HashMap<>();
        map2.put("loginWay","wechat_gzh");
        map2.put("openId","oZbI26ROn4gvNgLwwWCIDzDiufff");




        ATPRedisRequestDto dto2 = ATPRedisRequestDto.builder()
                .operation("set")
                .dbIndex("0")
                .queryContent(JSON.toJSONString(map2))
                .queryKey(key2)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res2 = defaultRedisConfig.getRedisResult(dto2);
        log.info(res2);
        ATPRedisRequestDto dto3 = ATPRedisRequestDto.builder()
                .operation("set")
                .dbIndex("5")
                .queryContent(JSON.toJSONString(map2))
                .queryKey(key2)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res3 = defaultRedisConfig.getRedisResult(dto3);
        log.info(res3);

        //要先验证码获取信息
        String url7 = "https://qah5.summerfarm.net/register/VerificationCode";
        String bodyStr = "mobile="+"***********";

        HttpResponse response = HttpRequest.post(url7)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token2)
                .body(bodyStr)
                .execute();
        String msg=JSONObject.parseObject(response.body()).getJSONObject("data").getString("msg");

        String url8 = "https://qah5.summerfarm.net/register/VerifyYZM";
        String bodyStr8 = "type="+"login"+
                "&yzm="+msg;

        HttpResponse response8 = HttpRequest.post(url8)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token2)
                .body(bodyStr8)
                .execute();



        String rebindOpenIdUrl="https://"+env+"h5.summerfarm.net/register/updateOpenId";
        HttpResponse response7 = HttpRequest.post(rebindOpenIdUrl)
                .header("token", token2)
                .body("{}")
                .execute();
        jdbcTemplate.execute("delete from account_change where m_id="+newMid);
        Assert.isTrue(response7.isOk(), "重新绑定失败");
    }

    @Test(dependsOnMethods = "registerTest",retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("无手机号重新绑定")
    public void rebindOpenIdNotTest() throws InterruptedException {
        String token2="1-5026d495154343bea198c30c19813xxx";
        String key2="login:mall:1-5026d495154343bea198c30c19813xxx";
        Map<String,Object> map2=new HashMap<>();
        map2.put("loginWay","wechat_gzh");
        map2.put("openId","oZbI26ROn4gvNgLwwWCIDzDiuxxx");

        ATPRedisRequestDto dto2 = ATPRedisRequestDto.builder()
                .operation("set")
                .dbIndex("0")
                .queryContent(JSON.toJSONString(map2))
                .queryKey(key2)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res2 = defaultRedisConfig.getRedisResult(dto2);
        log.info(res2);
        ATPRedisRequestDto dto3 = ATPRedisRequestDto.builder()
                .operation("set")
                .dbIndex("5")
                .queryContent(JSON.toJSONString(map2))
                .queryKey(key2)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res3 = defaultRedisConfig.getRedisResult(dto3);
        log.info(res3);

        //要先验证码获取信息
        String url7 = "https://qah5.summerfarm.net/register/VerificationCode";
        String bodyStr = "mobile="+"13744440988";

        HttpResponse response = HttpRequest.post(url7)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token2)
                .body(bodyStr)
                .execute();
        String msg=JSONObject.parseObject(response.body()).getJSONObject("data").getString("msg");

        String url8 = "https://qah5.summerfarm.net/register/VerifyYZM";
        String bodyStr8 = "type="+"login"+
                "&yzm="+msg;

        HttpResponse response8 = HttpRequest.post(url8)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token2)
                .body(bodyStr8)
                .execute();



        String rebindOpenIdUrl="https://"+"qa"+"h5.summerfarm.net/register/updateOpenIdNot";
        HttpResponse response7 = HttpRequest.post(rebindOpenIdUrl)
                .header("token", token2)
                .body("mname=无手机号店名&newContact=无手机号店名&oldPhone=***********")
                .execute();
        Assert.isTrue(response7.isOk(), "重新绑定失败");
    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("BD扫码正向注册")
    public void claimTest() throws InterruptedException {

        JdbcTemplate jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        String deleteSql1 = "delete from merchant where mname='商城BD扫码注册自动化'";
        String deletesql2 = "delete from merchant_sub_account where contact='商城BD扫码注册自动化'";
        jdbcTemplate.execute(deleteSql1);
        jdbcTemplate.execute(deletesql2);
        String deletesql3 = "update merchant_leads set status=1 where id=2781";
        jdbcTemplate.execute(deletesql3);

        Thread.sleep(3000);

        String bdRegisterPhone = "***********";
        String token = "1-5026d495154343bea198c30c198130e3";
        String key = "login:mall:1-5026d495154343bea198c30c198130e3";
        Map<String, Object> map = new HashMap<>();
        map.put("loginWay", "wechat_gzh");
        map.put("openId", "oZbI26ROn4gvNgLwwWCIDzDiuB3h");
        map.put("phone", bdRegisterPhone);

        ATPRedisRequestDto dto = ATPRedisRequestDto.builder()
                .operation("set")
                .dbIndex("5")
                .queryContent( JSON.toJSONString(map))
                .queryKey(key)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res = defaultRedisConfig.getRedisResult(dto);
        log.info(res);



        String env="qa";

        String claimUrl = "https://" + env + "h5.summerfarm.net/register/claim/" + "Mjc4MQ";

        Map<String, Object> phoneMap = new HashMap<>();
        map.put("yzm", null);
        map.put("type", null);
        map.put("phone", bdRegisterPhone);

        HttpResponse response2 = HttpRequest.post(claimUrl)
                .header("token", token)
                .contentType("application/json")
                .body(JSONObject.toJSONString(phoneMap))
                .execute();

        boolean success = SqlExcutorUtil.checkSqlValue(jdbcTemplate, "select islock from merchant where mname='商城BD扫码注册自动化'", "islock", "0", 7);
        Assert.isTrue(success, "BD扫码注册失败");
    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("茶百道大客户注册")
    public void registerToCBDTest() throws InterruptedException {

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        String deleteSql1 = "delete from merchant where mname='自动化预注册CBD店'";
        String deletesql2 = "delete from merchant_sub_account where phone='***********'";
        jdbcTemplate.execute(deleteSql1);
        jdbcTemplate.execute(deletesql2);

        String adminToken=login.login();
        String cbdSaveUrl="https://qaadmin.summerfarm.net/sf-mall-manage/merchant/upsert/batch-insert/cbd";


        // Excel 文件路径
        String filePath = "src/test/resources/data/autoCbdMerchants.xls";
        // 创建文件对象
        File file = new File(filePath);
        // 构建请求体，这里可以添加其他表单字段
        Map<String, Object> form = new HashMap<>();
        form.put("file", file); // 文件字段名根据服务器端要求设置

        // 使用 Hutool 的 HttpUtil 上传文件
        HttpResponse response9 = HttpRequest.post(cbdSaveUrl)
                .header("token", adminToken)
                .form(form) // true 表示以 multipart/form-data 格式上传，适用于文件上传
                .execute(); // 执行请求


        String mid=SqlExcutorUtil.getOnlyValue(jdbcTemplate, "select m_id from merchant where mname='自动化预注册CBD店'");

        Thread.sleep(3000);

        String cbdRegisterPhone = "***********";
        String token = "1-5026d495154343bea198c30c19813ppp";
        String key = "login:mall:1-5026d495154343bea198c30c19813ppp";
        Map<String, Object> map = new HashMap<>();
        map.put("loginWay", "wechat_gzh");
        map.put("openId", "oZbI26ROn4gvNgLwwWCIDzDiuppp");
        map.put("phone", cbdRegisterPhone);

        ATPRedisRequestDto dto = ATPRedisRequestDto.builder()
                .operation("set")
                .dbIndex("5")
                .queryContent( JSON.toJSONString(map))
                .queryKey(key)
                .timeout(1000)
                .timeUnit(TimeUnit.SECONDS)
                .build();
        String res = defaultRedisConfig.getRedisResult(dto);
        log.info(res);


        String phoneUrl = "https://qah5.summerfarm.net/register/Phone";
        HttpResponse response4 = HttpRequest.post(phoneUrl)
                .header("token", token)
                .contentType("application/x-www-form-urlencoded")
                .body("phone="+cbdRegisterPhone)
                .execute();

        Thread.sleep(3000);

        String url7 = "https://qah5.summerfarm.net/register/VerificationCode";
        String bodyStr = "mobile="+cbdRegisterPhone;

        HttpResponse response = HttpRequest.post(url7)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token)
                .body(bodyStr)
                .execute();
        String msg=JSONObject.parseObject(response.body()).getJSONObject("data").getString("msg");

        String url8 = "https://qah5.summerfarm.net/register/VerifyYZM";
        String bodyStr8 = "type="+"login"+
                "&yzm="+msg;

        HttpResponse response8 = HttpRequest.post(url8)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("token", token)
                .body(bodyStr8)
                .execute();





        String env="qa";

        String cbdUrl = "https://" + env + "h5.summerfarm.net/register/save/cbd";


        cbdUrl+="?doorPic="+"fe-biz/xm-mall/ssys784wldi"+
                "&mname="+"自动化预注册CBD店"+
                "&mcontact="+"自动化预注册CBD店"+
                "&address="+"浙大路1号 中共杭州市西湖区纪律检查委员会"+
                "&province="+"浙江"+
                "&city="+"杭州市"+
                "&phone="+cbdRegisterPhone+
                "&invitecode="+"seeegj"+
                "&houseNumber="+"3213"+
                "&poiNote="+"120.130188,30.259608"+
                "&type="+"咖啡"+
                "&mId="+mid+
                "&area="+"西湖区";

        HttpResponse response2 = HttpRequest.get(cbdUrl)
                .header("token", token)
                .execute();

        boolean success = SqlExcutorUtil.checkSqlValue(jdbcTemplate, "\n" +
                "select count(1) as num from merchant where mname='自动化预注册CBD店' and mcontact is not null", "num", "1", 7);
        ds.close();
        Assert.isTrue(success, "茶百道大客户注册失败");
    }



}
