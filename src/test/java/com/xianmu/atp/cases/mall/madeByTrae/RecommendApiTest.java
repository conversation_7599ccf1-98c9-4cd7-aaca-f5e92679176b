package com.xianmu.atp.cases.mall.madeByTrae;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

@Epic("mall")
@Feature("推荐接口")
@Owner("AI Test Engineer")
@Slf4j
public class RecommendApiTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    private String baseUrl;
    private String token;
    private JdbcTemplate jdbcTemplate;

    @BeforeClass
    public void setup() {
        String env = EnvEnum.qa.getName();
        baseUrl = "https://" + env + "h5.summerfarm.net";
        token = getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(), env);
        
        // 初始化数据库连接（使用主数据库）
        jdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.xianmudb);
        
        log.info("测试环境初始化完成，baseUrl: {}, token: {}", baseUrl, token);
    }

    /**
     * 用例ID: TC_API_RECOMMEND_NORMAL_001
     * 用例名称: 推荐接口正常分页查询功能验证
     * 优先级: P0
     * 前置条件: 用户已登录且token有效
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("推荐接口正常分页查询")
    public void testRecommendNormalPaging() {
        String recommendUrl = baseUrl + "/recommend/1/10";

        System.out.println("tttt:"+token);
        HttpResponse response = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();

        // 验证响应状态码
        Assert.isTrue(response.isOk(), "推荐接口请求失败，状态码: " + response.getStatus());
        
        // 验证响应体
        String responseBody = response.body();
        Assert.notEmpty(responseBody, "响应体不能为空");
        
        JSONObject jsonResponse = JSONObject.parseObject(responseBody);
        Assert.notNull(jsonResponse, "响应体JSON解析失败");
        
        log.info("推荐接口正常分页查询测试通过，响应: {}", responseBody);
    }

    /**
     * 用例ID: TC_API_RECOMMEND_BOUNDARY_002
     * 用例名称: 推荐接口边界值测试
     * 优先级: P1
     * 前置条件: 用户已登录且token有效
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"2"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("推荐接口边界值测试")
    public void testRecommendBoundaryValues() {
        // 测试最小分页参数
        String minPageUrl = baseUrl + "/recommend/1/1";
        HttpResponse minResponse = HttpRequest.get(minPageUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        Assert.isTrue(minResponse.isOk(), "最小分页参数请求失败");
        
        // 测试较大分页参数
        String maxPageUrl = baseUrl + "/recommend/1/100";
        HttpResponse maxResponse = HttpRequest.get(maxPageUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        Assert.isTrue(maxResponse.isOk(), "较大分页参数请求失败");
        
        log.info("推荐接口边界值测试通过");
    }

    /**
     * 用例ID: TC_API_RECOMMEND_INVALID_003
     * 用例名称: 推荐接口无效参数测试
     * 优先级: P1
     * 前置条件: 用户已登录且token有效
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"2"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("推荐接口无效参数测试")
    public void testRecommendInvalidParams() {
        // 测试零值参数
        String zeroPageUrl = baseUrl + "/recommend/0/0";
        HttpResponse zeroResponse = HttpRequest.get(zeroPageUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        // 根据业务逻辑，可能返回400或者有特定的错误处理
        log.info("零值参数测试，状态码: {}, 响应: {}", zeroResponse.getStatus(), zeroResponse.body());
        
        // 测试负数参数
        String negativePageUrl = baseUrl + "/recommend/-1/10";
        HttpResponse negativeResponse = HttpRequest.get(negativePageUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        log.info("负数参数测试，状态码: {}, 响应: {}", negativeResponse.getStatus(), negativeResponse.body());
        
        // 测试超大数值参数
        String largePageUrl = baseUrl + "/recommend/999999/999999";
        HttpResponse largeResponse = HttpRequest.get(largePageUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        log.info("超大数值参数测试，状态码: {}, 响应: {}", largeResponse.getStatus(), largeResponse.body());
    }

    /**
     * 用例ID: TC_API_RECOMMEND_AUTH_004
     * 用例名称: 推荐接口权限验证测试
     * 优先级: P0
     * 前置条件: 无
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"2"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("推荐接口权限验证测试")
    public void testRecommendAuthValidation() {
        String recommendUrl = baseUrl + "/recommend/1/10";
        
        // 测试无token访问
        HttpResponse noTokenResponse = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .timeout(3000)
                .execute();
        log.info("无token访问测试，状态码: {}, 响应: {}", noTokenResponse.getStatus(), noTokenResponse.body());
        
        // 测试无效token访问
        HttpResponse invalidTokenResponse = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", "invalid_token_123")
                .timeout(3000)
                .execute();
        log.info("无效token访问测试，状态码: {}, 响应: {}", invalidTokenResponse.getStatus(), invalidTokenResponse.body());
        
        // 测试空token访问
        HttpResponse emptyTokenResponse = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", "")
                .timeout(3000)
                .execute();
        log.info("空token访问测试，状态码: {}, 响应: {}", emptyTokenResponse.getStatus(), emptyTokenResponse.body());
    }

    /**
     * 用例ID: TC_API_RECOMMEND_DIFFERENT_USERS_005
     * 用例名称: 推荐接口不同用户类型测试
     * 优先级: P1
     * 前置条件: 不同类型用户已注册
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"2"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("推荐接口不同用户类型测试")
    public void testRecommendDifferentUsers() {
        String recommendUrl = baseUrl + "/recommend/1/10";
        String env = EnvEnum.qa.getName();
        
        // 测试普通用户
        String normalUserToken = getXianmuMallUserToken(MallUserEnum.mallAutoUser2.getPhone(), env);
        HttpResponse normalUserResponse = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", normalUserToken)
                .timeout(3000)
                .execute();
        Assert.isTrue(normalUserResponse.isOk(), "普通用户推荐接口请求失败");
        
        // 测试大客户用户
        String bigUserToken = getXianmuMallUserToken(MallUserEnum.bigUser.getPhone(), env);
        HttpResponse bigUserResponse = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", bigUserToken)
                .timeout(3000)
                .execute();
        Assert.isTrue(bigUserResponse.isOk(), "大客户用户推荐接口请求失败");
        
        // 测试POP用户
        String popUserToken = getXianmuMallUserToken(MallUserEnum.popUser.getPhone(), env);
        HttpResponse popUserResponse = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", popUserToken)
                .timeout(3000)
                .execute();
        Assert.isTrue(popUserResponse.isOk(), "POP用户推荐接口请求失败");
        
        log.info("不同用户类型推荐接口测试通过");
    }

    /**
     * 用例ID: TC_API_RECOMMEND_PAGINATION_006
     * 用例名称: 推荐接口分页逻辑验证测试
     * 优先级: P1
     * 前置条件: 用户已登录且token有效
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"2"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("推荐接口分页逻辑验证测试")
    public void testRecommendPaginationLogic() {
        // 测试第一页
        String firstPageUrl = baseUrl + "/recommend/1/5";
        HttpResponse firstPageResponse = HttpRequest.get(firstPageUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        Assert.isTrue(firstPageResponse.isOk(), "第一页请求失败");
        
        // 测试第二页
        String secondPageUrl = baseUrl + "/recommend/2/5";
        HttpResponse secondPageResponse = HttpRequest.get(secondPageUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        Assert.isTrue(secondPageResponse.isOk(), "第二页请求失败");
        
        // 验证分页数据一致性
        JSONObject firstPageJson = JSONObject.parseObject(firstPageResponse.body());
        JSONObject secondPageJson = JSONObject.parseObject(secondPageResponse.body());
        
        log.info("分页逻辑验证测试通过，第一页响应: {}", firstPageResponse.body());
        log.info("分页逻辑验证测试通过，第二页响应: {}", secondPageResponse.body());
    }

    /**
     * 用例ID: TC_API_RECOMMEND_PERFORMANCE_007
     * 用例名称: 推荐接口性能测试
     * 优先级: P2
     * 前置条件: 用户已登录且token有效
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            })
    @Story("推荐接口性能测试")
    public void testRecommendPerformance() {
        String recommendUrl = baseUrl + "/recommend/1/20";
        
        long startTime = System.currentTimeMillis();
        HttpResponse response = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        long endTime = System.currentTimeMillis();
        
        long responseTime = endTime - startTime;
        Assert.isTrue(response.isOk(), "推荐接口性能测试请求失败");
        Assert.isTrue(responseTime < 3000, "推荐接口响应时间超过3秒，实际响应时间: " + responseTime + "ms");
        
        log.info("推荐接口性能测试通过，响应时间: {}ms", responseTime);
    }

    /**
     * 用例ID: TC_API_RECOMMEND_DATA_VALIDATION_008
     * 用例名称: 推荐接口数据格式验证测试
     * 优先级: P1
     * 前置条件: 用户已登录且token有效
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"2"}),
                    @CustomAttribute(name = "interval", values = {"2"})
            })
    @Story("推荐接口数据格式验证测试")
    public void testRecommendDataValidation() {
        String recommendUrl = baseUrl + "/recommend/1/10";
        
        HttpResponse response = HttpRequest.get(recommendUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .timeout(3000)
                .execute();
        
        Assert.isTrue(response.isOk(), "推荐接口请求失败");
        
        String responseBody = response.body();
        Assert.notEmpty(responseBody, "响应体不能为空");
        
        // 验证JSON格式
        JSONObject jsonResponse = JSONObject.parseObject(responseBody);
        Assert.notNull(jsonResponse, "响应体必须是有效的JSON格式");
        
        // 验证响应头
        String contentType = response.header("Content-Type");
        Assert.notEmpty(contentType, "Content-Type响应头不能为空");
        
        log.info("推荐接口数据格式验证测试通过，Content-Type: {}", contentType);
    }

    /**
     * 用例ID: TC_API_RECOMMEND_DATABASE_QUERY_009
     * 用例名称: 推荐接口数据库查询验证测试
     * 优先级: P2
     * 前置条件: 数据库连接正常
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            })
    @Story("推荐接口数据库查询验证测试")
    public void testRecommendDatabaseQuery() {
        try {
            // 模拟查询interface_log表（如果存在的话）
            String sql = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_name = 'interface_log'";
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
            
            if (result != null && !result.isEmpty()) {
                Long count = (Long) result.get(0).get("count");
                if (count > 0) {
                    // 如果interface_log表存在，查询相关数据
                    String logSql = "SELECT * FROM interface_log WHERE interface_name LIKE '%recommend%' ORDER BY id DESC LIMIT 10";
                    List<Map<String, Object>> logResult = jdbcTemplate.queryForList(logSql);
                    log.info("查询到推荐接口相关日志记录数: {}", logResult.size());
                } else {
                    log.info("interface_log表不存在，跳过数据库验证");
                }
            }
            
            // 执行推荐接口请求
            String recommendUrl = baseUrl + "/recommend/1/10";
            HttpResponse response = HttpRequest.get(recommendUrl)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("token", token)
                    .timeout(3000)
                    .execute();
            
            Assert.isTrue(response.isOk(), "推荐接口请求失败");
            log.info("推荐接口数据库查询验证测试完成");
            
        } catch (Exception e) {
            log.warn("数据库查询验证测试异常: {}", e.getMessage());
            // 不影响主要测试流程，仅记录警告
        }
    }

    /**
     * 用例ID: TC_API_RECOMMEND_CONCURRENT_010
     * 用例名称: 推荐接口并发访问测试
     * 优先级: P2
     * 前置条件: 用户已登录且token有效
     */
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"1"})
            })
    @Story("推荐接口并发访问测试")
    public void testRecommendConcurrentAccess() {
        String recommendUrl = baseUrl + "/recommend/1/10";
        int concurrentCount = 5;
        int successCount = 0;
        
        for (int i = 0; i < concurrentCount; i++) {
            try {
                HttpResponse response = HttpRequest.get(recommendUrl)
                        .header(Header.CONTENT_TYPE, "application/json")
                        .header("token", token)
                        .timeout(3000)
                        .execute();
                
                if (response.isOk()) {
                    successCount++;
                }
                
                // 短暂间隔
                Thread.sleep(100);
            } catch (Exception e) {
                log.warn("并发请求异常: {}", e.getMessage());
            }
        }
        
        Assert.isTrue(successCount >= concurrentCount * 0.8, 
                String.format("并发访问成功率过低，期望至少80%%，实际: %d/%d", successCount, concurrentCount));
        
        log.info("推荐接口并发访问测试通过，成功率: {}/{}", successCount, concurrentCount);
    }
}