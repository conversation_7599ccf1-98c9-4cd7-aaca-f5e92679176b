package com.xianmu.atp.cases.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.MerchantDao;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.JsonDealer;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;

import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Epic("mall")
@Feature("省心送")
@Owner("zyl")
@Slf4j
public class TimingDeliveryPlanTest extends BaseTest {
    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private MerchantDao merchantDao;

    @Value("2985")
    private int timingRuleId;

    @Value("89")
    private int timingDays;

    public void changeTimingOrderDelievryPlan(){

    }

    @Story("省心送下单和设置配送计划")
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void setUnSetTimingDeliveryPlan() throws Exception {
        String env="qa";

        merchantDao.updateAmountByMerchantId(MallUserEnum.mallAutoUser.getMid(), 100000);

        //下单并支付，不作断言
        Map<String,Object> timingBodyMap = new HashMap<>();
        timingBodyMap.put("timingRuleId",timingRuleId);
        timingBodyMap.put("mphone", MallUserEnum.mallAutoUser.getPhone());
        timingBodyMap.put("contactId", AddressEnum.mallAutoAddress.getContactId());
        timingBodyMap.put("mcontact","dev");
        timingBodyMap.put("merchantCouponId","");
        timingBodyMap.put("quantity",6);
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/timing")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(timingBodyMap))
                .execute();
        JSONObject jsonObject= JSONObject.parseObject(response.body());
        String timingOrderNo=jsonObject.getString("data");

        Map<String,Object> payMap = new HashMap<>();
        payMap.put("paymentMethod",1);
        payMap.put("bizType",0);
        payMap.put("bizOrderNo",timingOrderNo);
        HttpResponse payResponse = HttpRequest.post("https://"+env+"h5.summerfarm.net/payment/union/pay")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(payMap))
                .execute();
        Thread.sleep(3000);




        //设置配送计划，并校验是否在OFC生成数据
        Map<String,Object> nextDeliervyMap = new HashMap<>();
        Map<String,Object> deliveryDetailMap = new HashMap<>();
        LocalDate specificDate =  LocalDate.now().plusDays(2);;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = specificDate.format(formatter);

        deliveryDetailMap.put("deliveryTime",formattedDate);
        deliveryDetailMap.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        deliveryDetailMap.put("quantity",2);

        JSONArray deliveryDetailArray = new JSONArray();
        deliveryDetailArray.add(deliveryDetailMap);
        nextDeliervyMap.put("insertPlanList",deliveryDetailArray);
        nextDeliervyMap.put("orderNo",timingOrderNo);

        HttpResponse insertResponse =  HttpRequest.post("https://"+env+"h5.summerfarm.net/delivery-plan/insert")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(nextDeliervyMap))
                .execute();

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.cosfodb);
        JdbcTemplate saasJdbcTemplate = new JdbcTemplate(ds);

        String sql="select A.fulfillment_status,A.fulfillment_time,B.amount from ofcdb.fulfillment_order A\n" +
                "left join ofcdb.fulfillment_item B\n" +
                "on A.fulfillment_no=B.fulfillment_no\n" +
                "where A.out_order_no='"+timingOrderNo+"'";
        List<Map<String, Object>> fulfillmentOrderDetails =  saasJdbcTemplate.queryForList(sql);
        int count=0;
        while(count<5){
            if(fulfillmentOrderDetails.size()==0){
                Thread.sleep(1000);
                fulfillmentOrderDetails =  saasJdbcTemplate.queryForList(sql);
                count++;
            }else{
                break;
            }
        }
        boolean isSuccessful =  fulfillmentOrderDetails.get(0).get("fulfillment_status").toString().equals("10")
                && fulfillmentOrderDetails.get(0).get("fulfillment_time").toString().equals(formattedDate)
                && fulfillmentOrderDetails.get(0).get("amount").toString().equals("2");
        Assert.isTrue(isSuccessful,"设置配送计划失败");

        //检查可配送间隔时间,获取配送计划ID
        HttpResponse deliveryResponse = HttpRequest.get("https://"+env+"h5.summerfarm.net/delivery-plan/"+timingOrderNo)
                .header("token", token)
                .execute();
        JSONObject deliveryJsonObject= JSONObject.parseObject(deliveryResponse.body());
        Long start= deliveryJsonObject.getJSONObject("data").getLong("deliveryStartTime");
        long end= deliveryJsonObject.getJSONObject("data").getLong("deliveryEndTime");
        Assert.isTrue(end-start==86400000L*timingDays,"可配送间隔时间不正确");
        JSONArray deliveryPlanList=deliveryJsonObject.getJSONObject("data").getJSONArray("deliveryPlen");
        int delivertId= deliveryPlanList.getJSONObject(0).getInteger("id");


        //删除配送计划并校验是否删除OFC数据

        Map<String,Object> deleteDeliervyMap = new HashMap<>();
        Map<String,Object> deleteDelievry = new HashMap<>();
        deleteDelievry.put("id",delivertId);
        JSONArray deleteDeliveryArray = new JSONArray();
        deleteDeliveryArray.add(deleteDelievry);
        deleteDeliervyMap.put("deletePlanList",deleteDeliveryArray);
        deleteDeliervyMap.put("orderNo",timingOrderNo);
        HttpResponse deleteResponse =  HttpRequest.post("https://"+env+"h5.summerfarm.net/delivery-plan/delete")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(deleteDeliervyMap))
                .execute();
        fulfillmentOrderDetails =  saasJdbcTemplate.queryForList(sql);

        count=0;
        boolean isDeleted=SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,sql,"fulfillment_status","50",5);

        Assert.isTrue(isDeleted,"删除配送计划失败");
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("省心送优惠相关测试")
    public void testTimingdiscount(){
        String env= EnvEnum.qa.getName();
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);

        HttpResponse listResponse = HttpRequest.get("https://"+env+"h5.summerfarm.net/timing-delivery/sku/1/8?type=0&frontCategoryId=202")
                .header("token", token)
                .execute();

        Assert.isTrue(listResponse.isOk(),"省心送列表返回失败");

        String timingGoodsUrl= "https://"+env+"h5.summerfarm.net/timing-delivery/sku/"+ SkuEnum.skuTiming.getSku() +"?ruleId="+SkuEnum.skuTiming.getExtraValue();
        HttpResponse timingGoodsResponse = HttpRequest.get(timingGoodsUrl)
                .header("token", token)
                .execute();

        String preTimingUrl= "https://"+env+"h5.summerfarm.net/order/checkTimingOrder";
        Map<String,Object> preTimingMap = new HashMap<>();
        preTimingMap.put("quantity",3);
        preTimingMap.put("mphone",MallUserEnum.mallAutoUser.getPhone());
        preTimingMap.put("timingRuleId",SkuEnum.skuTiming.getExtraValue());
        HttpResponse preTimingResponse = HttpRequest.post(preTimingUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(preTimingMap))
                .execute();

        String createTimingUrl= "https://"+env+"h5.summerfarm.net/order/timing";
        Map<String,Object> createTimingMap = new HashMap<>();
        createTimingMap.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        createTimingMap.put("merchantCouponId","");
        createTimingMap.put("mcontact","嘟嘟嘟");
        createTimingMap.put("quantity",3);
        createTimingMap.put("mphone",MallUserEnum.mallAutoUser.getPhone());
        createTimingMap.put("timingRuleId",SkuEnum.skuTiming.getExtraValue());
        HttpResponse createTimingResponse = HttpRequest.post(createTimingUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(preTimingMap))
                .execute();

        String timingOrderNo=JsonDealer.getKeyStringINJson(JSONObject.parseObject(createTimingResponse.body()),"data");


    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("省心送批量设置配送测试")
    public void testBatchSetPlan() throws InterruptedException {
        String env= EnvEnum.qa.getName();
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);

        Map<String,Object> timingBodyMap = new HashMap<>();
        timingBodyMap.put("timingRuleId",timingRuleId);
        timingBodyMap.put("mphone", MallUserEnum.mallAutoUser.getPhone());
        timingBodyMap.put("contactId", AddressEnum.mallAutoAddress.getContactId());
        timingBodyMap.put("mcontact","dev");
        timingBodyMap.put("merchantCouponId","");
        timingBodyMap.put("quantity",6);
        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/timing")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(timingBodyMap))
                .execute();
        JSONObject jsonObject= JSONObject.parseObject(response.body());
        String timingOrderNo1=jsonObject.getString("data");

        Map<String,Object> payMap = new HashMap<>();
        payMap.put("paymentMethod",1);
        payMap.put("bizType",0);
        payMap.put("bizOrderNo",timingOrderNo1);
        HttpResponse payResponse = HttpRequest.post("https://"+env+"h5.summerfarm.net/payment/union/pay")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(payMap))
                .execute();


        Map<String,Object> timingBodyMap2 = new HashMap<>();
        timingBodyMap2.put("timingRuleId",2985);
        timingBodyMap2.put("mphone", MallUserEnum.mallAutoUser.getPhone());
        timingBodyMap2.put("contactId", AddressEnum.mallAutoAddress.getContactId());
        timingBodyMap2.put("mcontact","dev");
        timingBodyMap2.put("merchantCouponId","");
        timingBodyMap2.put("quantity",60);
        HttpResponse response2 = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/timing")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(timingBodyMap2))
                .execute();
        JSONObject jsonObject2= JSONObject.parseObject(response2.body());
        String timingOrderNo2=jsonObject2.getString("data");

        Map<String,Object> payMap2 = new HashMap<>();
        payMap2.put("paymentMethod",1);
        payMap2.put("bizType",0);
        payMap2.put("bizOrderNo",timingOrderNo2);
        HttpResponse payResponse2 = HttpRequest.post("https://"+env+"h5.summerfarm.net/payment/union/pay")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(payMap2))
                .execute();
        Thread.sleep(3000);


        String url = "https://qah5.summerfarm.net/timing-delivery/query/timing-order-products";
        Map<String, Object> body = new HashMap<>();
        body.put("pageIndex", "1");
        body.put("pageSize", "999");
        body.put("orderStatus", "3");
        String bodyStr = JSONObject.toJSONString(body);
        HttpResponse response3 = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr)
                .execute();
        Assert.isTrue(response3.isOk(), "省心送品请求失败");


        String batchUrl = "https://qah5.summerfarm.net/delivery-plan/upsert/batch-operation";

        //批量新增

        List<Map<String,Object>> upsertPlanList=new ArrayList<>();
        List<Map<String,Object>> insertPlanList=new ArrayList<>();
        List<Map<String,Object>> deletePlanList=new ArrayList<>();

        Map<String,Object> insertMap1 = new HashMap<>();
        Map<String,Object> insertMap2 = new HashMap<>();

        insertMap1.put("deliveryTime",LocalDate.now().plusDays(3));
        insertMap1.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        insertMap1.put("quantity",2);
        insertMap1.put("orderNo",timingOrderNo1);
        insertMap1.put("sku","308366655042");

        insertMap2.put("deliveryTime",LocalDate.now().plusDays(3));
        insertMap2.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        insertMap2.put("quantity",2);
        insertMap2.put("orderNo",timingOrderNo2);
        insertMap2.put("sku","2154766511136");

        insertPlanList.add(insertMap1);
        insertPlanList.add(insertMap2);

        Map<String, Object> body2 = new HashMap<>();
        body2.put("upsertPlanList", upsertPlanList);
        body2.put("insertPlanList", insertPlanList);
        body2.put("deletePlanList", deletePlanList);
        String bodyStr2 = JSONObject.toJSONString(body2);
        HttpResponse response4 = HttpRequest.post(batchUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr2)
                .execute();

        Assert.isTrue(response4.isOk(), "省心送批量新增计划失败");

        //批量修改


        String queryIdUrl = "https://qah5.summerfarm.net/delivery-plan/query/delivery-plan-monthly";
        Map<String, Object> queryBody = new HashMap<>();
        queryBody.put("queryMonth", LocalDate.now().plusDays(3));
        String queryBodyStr = JSONObject.toJSONString(queryBody);
        HttpResponse queryResponse = HttpRequest.post(queryIdUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(queryBodyStr)
                .execute();

        JSONArray planList= JSONObject.parseObject(queryResponse.body()).getJSONArray("data");
        int id1=0;
        int id2=0;
        for(Object o: planList){
            JSONObject jo= JSONObject.parseObject(o.toString());
            if(jo.getString("orderNo").equals(timingOrderNo1)){
                id1=jo.getInteger("id");
            }
            if(jo.getString("orderNo").equals(timingOrderNo2)){
                id2=jo.getInteger("id");
            }
        }

        Map<String,Object> upsertMap1 = new HashMap<>();
        Map<String,Object> upsertMap2 = new HashMap<>();

        upsertMap1.put("deliveryTime",LocalDate.now().plusDays(3));
        upsertMap1.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        upsertMap1.put("quantity",3);
        upsertMap1.put("orderNo",timingOrderNo1);
        upsertMap1.put("sku","308366655042");
        upsertMap1.put("id",id1);

        upsertMap2.put("deliveryTime",LocalDate.now().plusDays(3));
        upsertMap2.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        upsertMap2.put("quantity",3);
        upsertMap2.put("orderNo",timingOrderNo2);
        upsertMap2.put("sku","2154766511136");
        upsertMap2.put("id",id2);

        upsertPlanList.add(upsertMap1);
        upsertPlanList.add(upsertMap2);

        Map<String, Object> body3 = new HashMap<>();
        body3.put("upsertPlanList", upsertPlanList);
        body3.put("insertPlanList", new ArrayList<>());
        body3.put("deletePlanList", new ArrayList<>());
        String bodyStr3 = JSONObject.toJSONString(body3);
        HttpResponse response5 = HttpRequest.post(batchUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr3)
                .execute();

        Assert.isTrue(response4.isOk(), "省心送批量修改计划失败");

        //批量删除

        HttpResponse queryResponse2 = HttpRequest.post(queryIdUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(queryBodyStr)
                .execute();

        JSONArray planList2= JSONObject.parseObject(queryResponse2.body()).getJSONArray("data");
        for(Object o: planList2){
            JSONObject jo= JSONObject.parseObject(o.toString());
            if(jo.getString("orderNo").equals(timingOrderNo1)){
                id1=jo.getInteger("id");
            }
            if(jo.getString("orderNo").equals(timingOrderNo2)){
                id2=jo.getInteger("id");
            }
        }

        Map<String,Object> deleteMap1 = new HashMap<>();
        Map<String,Object> deleteMap2 = new HashMap<>();

        deleteMap1.put("deliveryTime",LocalDate.now().plusDays(3));
        deleteMap1.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        deleteMap1.put("quantity",3);
        deleteMap1.put("orderNo",timingOrderNo1);
        deleteMap1.put("sku","308366655042");
        deleteMap1.put("status",3);
        deleteMap1.put("id",id1);

        deleteMap2.put("deliveryTime",LocalDate.now().plusDays(3));
        deleteMap2.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        deleteMap2.put("quantity",3);
        deleteMap2.put("orderNo",timingOrderNo2);
        deleteMap2.put("sku","2154766511136");
        deleteMap2.put("status",3);
        deleteMap2.put("id",id2);

        deletePlanList.add(deleteMap1);
        deletePlanList.add(deleteMap2);

        Map<String, Object> body4 = new HashMap<>();
        body4.put("upsertPlanList", new ArrayList<>());
        body4.put("insertPlanList", new ArrayList<>());
        body4.put("deletePlanList", deletePlanList);
        String bodyStr4 = JSONObject.toJSONString(body4);
        HttpResponse response6 = HttpRequest.post(batchUrl)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr4)
                .execute();

        Assert.isTrue(response6.isOk(), "省心送批量修改计划失败");



    }
}
