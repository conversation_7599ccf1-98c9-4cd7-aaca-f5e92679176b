package com.xianmu.atp.cases.mall;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.xianmu.atp.generic.mall.SelftUsages.MallOrder.getXianmuMallUserToken;

@Epic("mall")
@Feature("专题页")
@Owner("zyl")
@Slf4j
public class TopicFrameTest extends BaseTest {

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("专题页")
    public void testTopicFrame() {
        String env= EnvEnum.qa.getName();
        String frameUrl = "https://" + env+"h5.summerfarm.net/topic/page/query/frame?topicPageId=914";
        String token= getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse response = HttpRequest.post(frameUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .execute();

        String frameSkuUrl = "https://" + env+"h5.summerfarm.net/topic/page/query/sku";
        HttpResponse response2 = HttpRequest.post(frameSkuUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("{\"launchId\":17671,\"pageNo\":1,\"pageSize\":6}")
                .execute();


        String frameCouponUrl = "https://" + env+"h5.summerfarm.net/topic/page/query/coupon";
        HttpResponse response3 = HttpRequest.post(frameCouponUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("{\"launchId\":19343,\"pageNo\":1,\"pageSize\":1}")
                .execute();

        String lowLevelUrl = "https://" + env+"h5.summerfarm.net/topic/page/query/low-level";
        HttpResponse response4 = HttpRequest.post(frameCouponUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("{\"topicId\":914}")
                .execute();

        Assert.isTrue(response.isOk(),"专题页请求失败");
        Assert.isTrue(response2.isOk(),"专题页SKU请求失败");
        Assert.isTrue(response3.isOk(),"专题页优惠券请求失败");
        Assert.isTrue(response4.isOk(),"专题页低价请求失败");

        String oneClickUrl = "https://" + env+"h5.summerfarm.net/topic/page/upsert/coupon/one-click?launchId="+19343;
        HttpResponse response5 = HttpRequest.post(oneClickUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("{\n" +
                        "    \"launchId\": 19343\n" +
                        "  }")
                .execute();

    }

}
