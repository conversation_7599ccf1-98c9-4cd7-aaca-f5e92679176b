package com.xianmu.atp.cases.mall.dubbo;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.AfterOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("样品dubbo调用")
@Owner("zyl")
@Slf4j
public class SampleApplyDubboTest extends BaseTest {
    @Resource
    private AfterOrderUtil afterOrderUtil;

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Resource
    private LoginHttp login;

    @Test(description = "样品正常流程",retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("无异常流程")
    public void testSampleDubbo(){
        String env="qa";
        String adminToken = login.login();
        String createSamplyApply="https://qaadmin.summerfarm.net/crm-service/sampleApply/insert";
        String sampleApplyParam="{\n" +
                "  \"mId\": 344054,\n" +
                "  \"mname\": \"基德样品申请\",\n" +
                "  \"mphone\": \"34241241241\",\n" +
                "  \"mcontact\": \"deji8\",\n" +
                "  \"contactId\": 342497,\n" +
                "  \"remark\": \"基德自动化测试\",\n" +
                "  \"areaNo\": 1001,\n" +
                "  \"sampleSkuList\": [\n" +
                "    {\n" +
                "      \"amount\": 1,\n" +
                "      \"pdName\": \"测试的苹果修改\",\n" +
                "      \"sku\": \"5415253174\",\n" +
                "      \"weight\": \"精品/五句话(23233)\",\n" +
                "      \"categoryId\": 1448,\n" +
                "      \"afterSaleQuantity\": 12,\n" +
                "      \"categoryName\": \"测试3级-1\",\n" +
                "      \"extType\": 1,\n" +
                "      \"invId\": 41,\n" +
                "      \"maturity\": \"\",\n" +
                "      \"origin\": \"12\",\n" +
                "      \"pdId\": 8,\n" +
                "      \"picturePath\": \"test/jysc8sfkbsqbaateo.png\",\n" +
                "      \"quantity\": 12130,\n" +
                "      \"unit\": \"包\",\n" +
                "      \"volume\": \"0.01*0.01*0.01\",\n" +
                "      \"weightNum\": 1,\n" +
                "      \"checked\": true\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        HttpResponse samplyApplyrResponse =  HttpRequest.post(createSamplyApply)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", adminToken)
                .body(sampleApplyParam)
                .execute();
        String applyId = JSONObject.parseObject(samplyApplyrResponse.body()).getString("data");


        Map<String, Object> passParamMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        passParamMap.put("sampleId", applyId);
        passParamMap.put("status", 0);
        String passUrl="https://qaadmin.summerfarm.net/crm-service/sampleApplyReview/review";
        //审核通过
        HttpResponse passResponse =  HttpRequest.post(passUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", adminToken)
                .body(JSONObject.toJSONString(passParamMap))
                .execute();

        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("sampleId", applyId);
        //数据初始化
        System.out.println("dubbo请求1----------------------");
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SampleApplyProvider")
                .methodName("getSampleApply")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SampleApplyReq"})
                .params(new Object[]{paramMap})
                .build();
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"请求1调用失败");
        System.out.println("dubbo请求2----------------------");

        Map<String, Object> paramMap2=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap2.put("deliveryTime", LocalDate.now().plusDays(1));
        paramMap2.put("pageSize", 6);
        paramMap2.put("pageIndex", 1);
        //数据初始化
        ATPRequestDto atpRequestDto2 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SampleApplyProvider")
                .methodName("listSampleApplyOrderByDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryDateQueryReq"})
                .params(new Object[]{paramMap2})
                .build();
        ATPDto atpDto2=defaultDubboConfig.getDubboResult(atpRequestDto2);
        Assert.isTrue(atpDto2.getDubboResponse().toString().contains("success"),"请求2调用失败");


        Map<String, Object> paramMap6=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap6.put("sampleId",applyId );
        paramMap6.put("pageSize", 6);
        paramMap6.put("pageIndex", 1);
        //数据初始化
        System.out.println("样品信息请求---------");
        ATPRequestDto atpRequestDto6 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SampleSkuProvider")
                .methodName("getSampleSku")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SampleSkuReq"})
                .params(new Object[]{paramMap6})
                .build();
        ATPDto atpDto6 = defaultDubboConfig.getDubboResult(atpRequestDto6);
        Assert.isTrue(atpDto6.getDubboResponse().toString().contains("success"),"获取样品信息失败");


        System.out.println("dubbo请求3----------------------设置样品单配送时间");

        Map<String, Object> paramMap5=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap5.put("sampleId", applyId);
        paramMap5.put("deliveryTime", LocalDate.now().plusDays(2));
        //数据初始化
        ATPRequestDto atpRequestDto5 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SampleApplyProvider")
                .methodName("updateSampleApplyInfo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SampleApplyUpdateInfoReq"})
                .params(new Object[]{paramMap5})
                .build();
        ATPDto atpDto3=defaultDubboConfig.getDubboResult(atpRequestDto5);
        Assert.isTrue(atpDto3.getDubboResponse().toString().contains("success"),"请求3调用失败");

//        System.out.println("dubbo请求3----------------------审核通过样品");
//
//        String appoveUrl="https://qaadmin.summerfarm.net/crm-service/sampleApplyReview/review";
//        Map<String, Object> appoveParam=new HashMap<>();
//        //paramMap.put("orderNo", timingOrderNo);
//        appoveParam.put("sampleId", applyId);
//        appoveParam.put("status", 0);
//        HttpResponse appoveResponse =   HttpRequest.post(appoveUrl)
//                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
//                .header("token", adminToken)
//                .body(sampleApplyParam)
//                .execute();

        System.out.println("dubbo请求4----------------------切仓修改样品单");

        Map<String, Object> paramMap3=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap3.put("sampleId", applyId);
        paramMap3.put("oldStoreNo", 1);
        paramMap3.put("newStoreNo", 20);
        //数据初始化
        ATPRequestDto atpRequestDto3 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SampleApplyProvider")
                .methodName("updateSampleApplyStore")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SampleApplyUpdateStoreReq"})
                .params(new Object[]{paramMap3})
                .build();
        ATPDto atpDto4=defaultDubboConfig.getDubboResult(atpRequestDto3);
        Assert.isTrue(atpDto4.getDubboResponse().toString().contains("success"),"请求4调用失败");


        System.out.println("dubbo请求5----------------------取消样品单");

        Map<String, Object> paramMap4=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap4.put("sampleId", applyId);
        //数据初始化
        ATPRequestDto atpRequestDto4 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SampleApplyProvider")
                .methodName("cancelSampleApply")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SampleApplyCancelReq"})
                .params(new Object[]{paramMap4})
                .build();
        ATPDto atpDt05=defaultDubboConfig.getDubboResult(atpRequestDto4);
        Assert.isTrue(atpDt05.getDubboResponse().toString().contains("success"),"请求5调用失败");
    }


    @Test(description = "样品自提",retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("无异常流程")
    public void slefPickSampleSkuTest(){
        String env="qa";
        String adminToken = login.login();
        String createSamplyApply="https://qaadmin.summerfarm.net/crm-service/sampleApply/insert";
        String sampleApplyParam="{\n" +
                "  \"mId\": 344054,\n" +
                "  \"mname\": \"基德样品申请\",\n" +
                "  \"mphone\": \"34241241241\",\n" +
                "  \"mcontact\": \"deji8\",\n" +
                "  \"contactId\": 342497,\n" +
                "  \"remark\": \"基德自动化测试\",\n" +
                "  \"areaNo\": 1001,\n" +
                "  \"sampleSkuList\": [\n" +
                "    {\n" +
                "      \"amount\": 1,\n" +
                "      \"pdName\": \"测试的苹果修改\",\n" +
                "      \"sku\": \"5415253174\",\n" +
                "      \"weight\": \"精品/五句话(23233)\",\n" +
                "      \"categoryId\": 1448,\n" +
                "      \"afterSaleQuantity\": 12,\n" +
                "      \"categoryName\": \"测试3级-1\",\n" +
                "      \"extType\": 1,\n" +
                "      \"invId\": 41,\n" +
                "      \"maturity\": \"\",\n" +
                "      \"origin\": \"12\",\n" +
                "      \"pdId\": 8,\n" +
                "      \"picturePath\": \"test/jysc8sfkbsqbaateo.png\",\n" +
                "      \"quantity\": 12130,\n" +
                "      \"unit\": \"包\",\n" +
                "      \"volume\": \"0.01*0.01*0.01\",\n" +
                "      \"weightNum\": 1,\n" +
                "      \"checked\": true\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        HttpResponse samplyApplyrResponse =  HttpRequest.post(createSamplyApply)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", adminToken)
                .body(sampleApplyParam)
                .execute();
        String applyId = JSONObject.parseObject(samplyApplyrResponse.body()).getString("data");


        Map<String, Object> passParamMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        passParamMap.put("sampleId", applyId);
        passParamMap.put("status", 0);
        String passUrl="https://qaadmin.summerfarm.net/crm-service/sampleApplyReview/review";
        //审核通过
        HttpResponse passResponse =  HttpRequest.post(passUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", adminToken)
                .body(JSONObject.toJSONString(passParamMap))
                .execute();


        Map<String, Object> paramMap4=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap4.put("sampleId", applyId);
        //数据初始化
        ATPRequestDto atpRequestDto4 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SampleApplyProvider")
                .methodName("sampleApplySelfPickup")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SampleApplySelfPickupReq"})
                .params(new Object[]{paramMap4})
                .build();
        ATPDto atpDt05=defaultDubboConfig.getDubboResult(atpRequestDto4);
        Assert.isTrue(atpDt05.getDubboResponse().toString().contains("success"),"样品自提失败");
    }
}
