package com.xianmu.atp.cases.mall;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.xianmu.atp.dal.dao.MerchantCouponDao;
import com.xianmu.atp.dal.dao.MerchantDao;
import com.xianmu.atp.generic.MallField;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.xianmu.atp.util.mq.MQData;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.ResultDao;
import com.xianmu.atp.util.testng.XMAnnotationTransformer;
import io.qameta.allure.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.concurrent.ExecutorService;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;

import static org.testng.CommandLineArgs.THREAD_COUNT;

@Slf4j
@Epic("注解测试")
@Feature("一级测试分类")
@Owner("xianmu")
public class BaseCaseTest extends BaseTest {

//    private static final Logger log = LoggerFactory.getLogger(BaseCaseTest.class);

//    @Resource
//    private MqProducer mqProducer;

    @Resource
    private ResultDao resultDao;

    @Resource
    private MerchantCouponDao merchantCouponDao;

    @Resource
    private MerchantDao merchantDao;

    @Resource
    private MallField mallField;

    @Value("${app.manage-domain}")
    private String domain;

    @Story("二级测试分类")
    @Owner("xianmu")
    @Test(description = "测试用例", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
            @CustomAttribute(name = "loop", values = {"1"}),
            @CustomAttribute(name = "interval", values = {"10"})
    })
    public void case01() {
        log.info("测试用例");
//        String today= DateUtil.tomorrow().toString("yyyy-MM-dd");
//        String k = mallField.order("13764656494");
//        Console.log(today);
//        List<Integer> list = new ArrayList<>();
//        list.add(2);
//        list.stream().map(Integer -> Integer.intValue()+1);
//        String  i = resultDao.select();
//        Console.log("testcase:{}",i);
        Assert.assertTrue(true);
    }

    public void case02(){
        MQData mqData = new MQData();
        Map<String, String> data = new HashMap<>();
        data.put("batchNo","202408264615273002");
        data.put("cost", "10000000");
        data.put("outOrderNo","44338");
        data.put("outOrderType","1");
        data.put("sku","2150726531771");
        data.put("warehouseNo","1");
        mqData.setData(data);
//        try {
//            SendResultDTO dto = mqProducer.send(MQTopicConstant.DEV_POP_MQ, "tag_road_sale_cost", JSON.toJSONString(data));
//            log.info("返回消息{}", dto);
//        } catch (Exception e){
//            e.printStackTrace();
//        }
        log.info("在途成本变动MQ消息发送："+JSON.toJSONString(mqData));
        Assert.assertEquals(true, true);

    }

    @Test(description = "测试用例2", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"2"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void case03(){
//        resultDao.select();
        Assert.assertTrue(true);
    }


    private static final int TOTAL_REQUESTS = 4001;
    private static final int THREAD_COUNT = 40;

    public void removeEscapes() {
        String csvFile = "/Users/<USER>/Downloads/doorPic.csv"; // 替换为你的 CSV 文件路径
        String line;
        List<String> list = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(csvFile))) {
            while ((line = br.readLine()) != null) {
                // 使用逗号分割行数据
                // 处理数据，例如打印
                Console.log(line);
                System.out.println(); // 换行
                list.add(line.replace("\"", ""));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        String ocrUrl = "https://qaadmin.summerfarm.net/common-service/feishuOcr/query";
        String prefix = "https://azure.summerfarm.net/";
//        List<String> list = merchantDao.getMerchantDoorPicList();
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT); // 创建线程池


//        ExecutorService executor = ExecutorBuilder.create()
//                .setCorePoolSize(5)
//                .setMaxPoolSize(10)
//                .setWorkQueue(new LinkedBlockingQueue<>(100))
//                .build();
        long startTime = System.currentTimeMillis();
        Future<?>[] futures = new Future[TOTAL_REQUESTS];
        int i = 0;
        for (String s : list) {
            String picUrl = prefix + s;
            log.info("url:{}", picUrl);
            String requestBody = "{\"imageUrlAddress\":\"" + picUrl + "\"}";
            futures[i] = executor.submit(() -> {
                try {
                    HttpResponse response = HttpRequest.post(ocrUrl)
                           .header(Header.CONTENT_TYPE, "application/json")
                           .body(requestBody)
                           .execute();
                    Console.log("response:{}", response.body());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            i++;
        }
//        String picUrl = prefix + "/fe-biz/xm-mall/yr8lzelae8e";
//        String requestBody = "{\"imageUrlAddress\":\"" + picUrl + "\"}";
//        HttpResponse response = HttpRequest.post(ocrUrl)
//                           .header(Header.CONTENT_TYPE, "application/json")
//                           .body(requestBody)
//                           .execute();
//                    Console.log("response:{}", response.body());
        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                String s = (String) future.get();
                Console.log(s);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        long endTime = System.currentTimeMillis();
        long elapsedTime = endTime - startTime; // 计算总耗时
        double seconds = elapsedTime / 1000.0; // 转换为秒
        double qps = TOTAL_REQUESTS / seconds; // 计算 QPS
        Console.log("qps:{}",qps);
        executor.shutdown();
    }

}

