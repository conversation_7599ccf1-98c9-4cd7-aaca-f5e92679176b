package com.xianmu.atp.cases.mall.dubbo;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Epic("mall")
@Feature("运营区域dubbo调用")
@Owner("zyl")
@Slf4j
public class AreaAndWarehouseNameDubboTest extends BaseTest {

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Resource
    private LoginHttp login;


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void getAreaNameByAreaNoTest(){
        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("no", 1001);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AreaAndWarehouseNameProvider")
                .methodName("getAreaNameByAreaNo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AreaAndWarehouseNameQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取运营区域名称调用失败");
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void listAreaNameByAreaNosTest(){
        Map<String, Object> paramMap=new HashMap<>();
        List<Integer> noList=new ArrayList();
        noList.add(1001);
        noList.add(29380);
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("noList", noList);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AreaAndWarehouseNameProvider")
                .methodName("listAreaNameByAreaNos")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AreaAndWarehouseNameListQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"批量获取运营区域名称调用失败");
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void getStoreNameByStoreNoTest(){
        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("no", 1);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AreaAndWarehouseNameProvider")
                .methodName("getStoreNameByStoreNo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AreaAndWarehouseNameQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取城配仓名称调用失败");
    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void listStoreNameByStoreNosTest(){
        Map<String, Object> paramMap=new HashMap<>();
        List<Integer> noList=new ArrayList();
        noList.add(1);
        noList.add(176);
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("noList", noList);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AreaAndWarehouseNameProvider")
                .methodName("listStoreNameByStoreNos")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AreaAndWarehouseNameListQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"批量获取城配仓名称调用失败");
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void listWarehouseNameByWarehouseNosTest(){
        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        List<Integer> noList=new ArrayList();
        noList.add(1);
        noList.add(2);
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("noList", noList);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AreaAndWarehouseNameProvider")
                .methodName("listWarehouseNameByWarehouseNos")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AreaAndWarehouseNameListQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取库存仓名称调用失败");
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void listWarehouseNoBySkuListAndStoreNoTest(){
        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        List<String> skuList=new ArrayList();
        skuList.add(SkuEnum.skuBillAfter.getSku());
        skuList.add(SkuEnum.skuForSpuStartNum.getSku());
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("skuIdList", skuList);
        paramMap.put("storeNo",20);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AreaAndWarehouseNameProvider")
                .methodName("listWarehouseNoBySkuListAndStoreNo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SkuAndWarehouseNoMappingReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取城配仓名称调用失败");
    }

}
