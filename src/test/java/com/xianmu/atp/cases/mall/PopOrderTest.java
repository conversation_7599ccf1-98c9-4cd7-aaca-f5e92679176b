package com.xianmu.atp.cases.mall;

import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.dal.dao.MerchantDao;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.OrderUtils;
import org.testng.annotations.Test;

import javax.annotation.Resource;

@Epic("mall")
@Feature("pop下单")
@Owner("zyl")
@Slf4j
public class PopOrderTest extends BaseTest {


}
