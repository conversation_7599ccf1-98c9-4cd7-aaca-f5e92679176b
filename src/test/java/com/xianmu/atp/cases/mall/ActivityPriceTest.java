package com.xianmu.atp.cases.mall;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.enums.*;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.generic.mall.SelftUsages.ProductInfo;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import com.zaxxer.hikari.HikariDataSource;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


@Epic("mall")
@Feature("活动相关")
@Owner("zyl")
@Slf4j
public class ActivityPriceTest extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Resource
    private AreaStoreDao areaStoreDao;

    @Value("20")
    private String originalPrice;
    @Value("19")
    private String unit1Price;
    @Value("18")
    private String unit2Price;
    @Value("17")
    private String unit3Price;


    @Story("测试活动限购价格")
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void activityLimitPriceTest() throws InterruptedException {
        //

        HikariDataSource ds= SqlExcutorUtil.getDs(properties, DataSourceEnums.xianmudb);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(ds);
        //jdbcTemplate.execute("delete  from  orders  where m_id="+MallUserEnum.mallAutoUser.getMid());
        jdbcTemplate.execute("delete  from  active_sku_purchase_quantity  where m_id="+MallUserEnum.mallAutoUser.getMid());
        //jdbcTemplate.execute("delete  from delivery_plan where contact_id in (select contact.contact_id from contact where m_id="+MallUserEnum.mallAutoUser.getMid()+")");
        //活动限购数为6
        areaStoreDao.setOnlineQuantity(SkuEnum.skuForActivityLimit.getSku(),1);//1：杭州仓
        ProductInfo productInfo = new ProductInfo();
        productInfo.addUser(MallUserEnum.mallAutoUser);
        productInfo.addEnv(EnvEnum.qa);
        productInfo.addPdId(SkuEnum.skuForActivityLimit);
        productInfo.send();
        JSONObject unBoughtJson = productInfo.getJsonResult();
        BigDecimal unit1Price =  new BigDecimal(this.unit1Price);
        BigDecimal salePrice1 = unBoughtJson.getJSONArray("data").getJSONObject(0).getBigDecimal("salePrice");
        //Assert.isTrue(salePrice1.compareTo(unit1Price)==0,"商详页活动限购价格测试错误");

        MallOrder order = new MallOrder();
        order.addEnv(EnvEnum.qa);
        order.addAddress(AddressEnum.mallAutoAddress);
        order.addSku(SkuEnum.skuForActivityLimit,3);
        order.send(true);
        JSONObject orderDetail1 = JSONObject.parseObject(order.getOrderdetail());
        BigDecimal unit2Price =  new BigDecimal(this.unit2Price);
        BigDecimal orderItemPrice1= orderDetail1.getJSONObject("data").getJSONObject("orderVO").getJSONArray("orderItems").getJSONObject(0).getBigDecimal("price");


        MallOrder order2 = new MallOrder();
        order2.addEnv(EnvEnum.qa);
        order2.addAddress(AddressEnum.mallAutoAddress);
        order2.addSku(SkuEnum.skuForActivityLimit,5);
        order2.send(true);
        JSONObject orderDetail2 = JSONObject.parseObject(order2.getOrderdetail());
        BigDecimal orderItemPrice2= orderDetail2.getJSONObject("data").getJSONObject("orderVO").getJSONArray("orderItems").getJSONObject(0).getBigDecimal("price");

        //先清数据，再做断言
        jdbcTemplate.execute("delete  from  orders  where order_no in ('"+order.getOrderNo()+"','"+order2.getOrderNo()+"')");
        jdbcTemplate.execute("delete  from  active_sku_purchase_quantity  where m_id="+MallUserEnum.mallAutoUser.getMid());
        jdbcTemplate.execute("delete  from delivery_plan where order_no in ('"+order.getOrderNo()+"','"+order2.getOrderNo()+"')");

        Assert.isTrue(orderItemPrice1.compareTo(unit2Price)==0,"第一次活动价测试失败");

        Assert.isTrue(orderItemPrice2.compareTo(new BigDecimal("18.20"))==0,"第二次限购活动价测试失败");
        ds.close();
    }

    @Story("省心送到手价获取")
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void timingActivityPriceTest(){
        String env=EnvEnum.qa.getName();
        String phone=MallUserEnum.mallAutoUser.getPhone();
        String token= MallOrder.getXianmuMallUserToken(phone,env);

        String url = "https://qah5.summerfarm.net/price/query/take-actual-price";
        Map<String, Object> orderMap = new HashMap<>();
        orderMap.put("suitId", 0);
        orderMap.put("sku", "2154766511136");
        orderMap.put("quantity", 3);

        Map<String, Object> body = new HashMap<>();
        body.put("minLadderPrice", 0);
        body.put("timingSkuPrice", 1);
        body.put("timingRuleId", 3468);
        body.put("orderNow", orderMap);
        String bodyStr = JSONObject.toJSONString(body);
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("token", token)
                .body(bodyStr)
                .execute();
        Assert.isTrue(response.isOk()&&response.body().contains("price"),"省心送到手价获取失败");
    }

}
