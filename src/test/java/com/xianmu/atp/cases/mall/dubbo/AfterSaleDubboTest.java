package com.xianmu.atp.cases.mall.dubbo;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.dal.dao.AfterSaleOrderDao;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.AfterOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.json.JsonUtil;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.OrderUtils;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Epic("mall")
@Feature("售后dubbo调用")
@Owner("zyl")
@Slf4j
public class AfterSaleDubboTest extends BaseTest {

    @Resource
    private LoginHttp loginHttp;

    @Resource
    private AfterOrderUtil afterOrderUtil;

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Resource
    private AfterSaleOrderDao afterSaleOrderDao;

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void saveTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo",mallOrder.getOrderNo());
        paramMap.put("sku",SkuEnum.skuBillAfter.getSku());
        paramMap.put("revokeQuantity",1);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("save")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AfterSaleOrderSaveReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void listAfterSaleOrderByDeliveryDateTest() {


        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("deliveryTime",LocalDate.now());
        paramMap.put("pageSize",6);
        paramMap.put("pageIndex",1);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("listAfterSaleOrderByDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryDateQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void afterSaleCouponTest() throws InterruptedException {

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo",mallOrder.getOrderNo());
        paramMap.put("sku",SkuEnum.skuBillAfter.getSku());
        paramMap.put("quantity",1);
        paramMap.put("deliveryed",0);
        paramMap.put("suitId",0);
        paramMap.put("mId",MallUserEnum.mallAutoUser.getMid());
        paramMap.put("type",0);
        paramMap.put("deliveryId",6);
        paramMap.put("handleType",0);
        paramMap.put("afterSaleOrderNo",null);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("afterSaleCoupon")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.afterSale.CalcAfterSaleCouponReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void closeTest() {
        String afterSaleOrderNo = afterOrderUtil.AfterSaleResendOrder(EnvEnum.qa.getName(), MallUserEnum.mallAutoUser.getPhone(), AddressEnum.mallAutoAddress.getContactId(), SkuEnum.skuBillAfter.getSku(), 1);

        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("closeAfterSale")
                .paramTypes(new String[]{"java.lang.String"})
                .params(new String[]{afterSaleOrderNo})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void afterSaleDeliveryStatusTest() throws InterruptedException {
        //String afterSaleOrderNo = afterOrderUtil.AfterSaleResendOrder(EnvEnum.qa.getName(), MallUserEnum.mallAutoUser.getPhone(), AddressEnum.mallAutoAddress.getContactId(), SkuEnum.skuBillAfter.getSku(), 1);

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo",mallOrder.getOrderNo());
        paramMap.put("isManage",true);
        paramMap.put("deliveryId",null);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("afterSaleDeliveryStatus")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.afterSale.DeliveryStatusQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言

        paramMap.put("isManage",false);
        atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("afterSaleDeliveryStatus")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.afterSale.DeliveryStatusQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto2 = defaultDubboConfig.getDubboResult(atpRequestDto);
    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void afterSaleGetHandleTypeTest() throws InterruptedException {
        //String afterSaleOrderNo = afterOrderUtil.AfterSaleResendOrder(EnvEnum.qa.getName(), MallUserEnum.mallAutoUser.getPhone(), AddressEnum.mallAutoAddress.getContactId(), SkuEnum.skuBillAfter.getSku(), 1);

        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo",mallOrder.getOrderNo());
        paramMap.put("isManage",true);
        paramMap.put("sku",SkuEnum.skuBillAfter.getSku());
        paramMap.put("deliveryed",0);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("afterSaleGetHandleType")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.afterSale.HandleTypeQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言

        paramMap.put("isManage",false);
        atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("afterSaleGetHandleType")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.afterSale.HandleTypeQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto2 = defaultDubboConfig.getDubboResult(atpRequestDto);
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void queryAfterSaleReissueOrderTest() {
        String afterSaleOrderNo = afterOrderUtil.AfterSaleResendOrder(EnvEnum.qa.getName(), MallUserEnum.mallAutoUser.getPhone(), AddressEnum.mallAutoAddress.getContactId(), SkuEnum.skuBillAfter.getSku(), 1);



        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("afterSaleOrderNo",afterSaleOrderNo);
        paramMap.put("sku",SkuEnum.skuBillAfter.getSku());
        paramMap.put("mId",MallUserEnum.mallAutoUser.getMid());
        paramMap.put("addTime",LocalDate.now());
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("queryAfterSaleReissueOrder")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AfterSaleOrderQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }



    //还有问题
    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void batchSaveTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap = JsonUtil.toMap("{\n" +
                "  \"accountId\": 176947,\n" +
                "  \"afterSaleOrderStatus\": 1,\n" +
                "  \"afterSaleQuantity\": 0,\n" +
                "  \"afterSaleRemark\": \"\",\n" +
                "  \"afterSaleTime\": 0,\n" +
                "  \"categoryId\": 0,\n" +
                "  \"deliveryed\": 0,\n" +
                "  \"exchangeGoodList\": [\n" +
                "    \n" +
                "  ],\n" +
                "  \"handleNum\": 470.00,\n" +
                "  \"handleType\": 11,\n" +
                "  \"isManage\": true,\n" +
                "  \"mId\": 165524,\n" +
                "  \"needPagination\": true,\n" +
                "  \"orderNo\": \"0125GG18MM0304215634\",\n" +
                "  \"pageIndex\": 1,\n" +
                "  \"pageSize\": 20,\n" +
                "  \"quantity\": 8,\n" +
                "  \"refundType\": \"客户原因\",\n" +
                "  \"startRow\": 0,\n" +
                "  \"suitId\": 0,\n" +
                "  \"type\": 0\n" +
                "}");

        paramMap.put("orderNo",mallOrder.getOrderNo());
        paramMap.put("handleNum",new BigDecimal("100.00"));
        paramMap.put("quantity",1);
        paramMap.put("mId",MallUserEnum.mallAutoUser.getMid());
        paramMap.put("accountId",10295);
        List<Object> list= new ArrayList<>();
        list.add(paramMap);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleOrderProvider")
                .methodName("afterSaleBatchSave")
                .paramTypes(new String[]{"java.util.List"})
                .params(new Object[]{list})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void updateAfterSaleInfoTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.bigUser);
        mallOrder.addAddress(AddressEnum.bigUserAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,3);
        mallOrder.send(false);
        mallOrder.payByBill();
        String orderNo = mallOrder.getOrderNo();
        String adminToken=loginHttp.login();
        String bufaUrl="https://qaadmin.summerfarm.net/after-sale/order/save";
        String bufaBody="{\"orderNo\":\""+orderNo+"\",\"sku\":\"997578188103\",\"suitId\":0,\"isManage\":true,\"afterSaleUnit\":\"件\",\"handleRemark\":\"\",\"handleType\":7,\"proofPic\":\"test/nga40u772j96s3jha.jpeg\",\"proofVideo\":\"\",\"quantity\":1,\"handleNum\":0,\"deliveryed\":1,\"afterSaleRemarkType\":\"\",\"afterSaleRemark\":\"3\",\"recoveryType\":0,\"recoveryNum\":0,\"deliveryId\":\"\",\"afterSaleType\":\"包装问题\",\"carryingGoods\":0,\"applySecondaryRemark\":\"保质期,保质期和页面不符\"}";
        HttpResponse afterResponse =  HttpRequest.post(bufaUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .header("token", adminToken)
                .body(bufaBody)
                .execute();
        Thread.sleep(3000);

        String afterSaleOrderNo= afterSaleOrderDao.getAfterSaleOrder(orderNo).get(0).getAfterSaleOrderNo();


        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("afterSaleNo",afterSaleOrderNo);
        paramMap.put("oldStoreNo",20);
        paramMap.put("newStoreNo",1);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleDeliveryPathProvider")
                .methodName("updateAfterSaleDeliveryPathStore")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AfterSaleDeliveryPathUpdateStoreReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"售后更改城配仓失败");
        //assert TODO: 2025/断言

        Map<String, Object> paramMap2=new HashMap<>();
        paramMap2.put("afterSaleNo",afterSaleOrderNo);
        paramMap2.put("deliveryTime",LocalDate.now().plusDays(3));
        //数据初始化
        ATPRequestDto atpRequestDto2 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.AfterSaleDeliveryPathProvider")
                .methodName("updateAfterSaleDeliveryPathInfo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.AfterSaleDeliveryPathUpdateInfoReq"})
                .params(new Object[]{paramMap2})
                .build();
        //调用获取结果
        ATPDto atpDto2 = defaultDubboConfig.getDubboResult(atpRequestDto2);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"售后更改配送时间失败");
    }


}
