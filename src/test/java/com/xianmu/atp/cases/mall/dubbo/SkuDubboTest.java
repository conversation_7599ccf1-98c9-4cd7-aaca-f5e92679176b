package com.xianmu.atp.cases.mall.dubbo;


import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Epic("mall")
@Feature("sku的dubbo调用")
@Owner("zyl")
@Slf4j
public class SkuDubboTest extends BaseTest {

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("SaaS获取报价单信息")
    public void testSkuDubbo() {
        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("areaNo", 1001);
        paramMap.put("sku", "2234415700037");
        paramMap.put("adminId", 6010867);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SkuProvider")
                .methodName("queryMallPriceInfo4Saas")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.SkuBaseInfoReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取鲜沐报价信息失败");



        Map<String, Object> paramMap2=new HashMap<>();
        List<String> skuList = new ArrayList<>();
        skuList.add(SkuEnum.skuBillAfter.getSku());
        //paramMap.put("orderNo", timingOrderNo);
        paramMap2.put("mId", 350935);
        paramMap2.put("allSpuSku", true);
        paramMap2.put("querySkyPrice", true);
        paramMap2.put("skus", skuList);

        ATPRequestDto atpRequestDto2 = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.SkuProvider")
                .methodName("querMerchantOrderSku")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.order.SkuSpuOrderQuery"})
                .params(new Object[]{paramMap2})
                .build();
        //调用获取结果
        ATPDto atpDto2 = defaultDubboConfig.getDubboResult(atpRequestDto2);
        log.info("调用结果：{}", atpDto2);
        Assert.isTrue(atpDto2.getDubboResponse().toString().contains("success"),"querMerchantOrderSku调用失败");

    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("代下单商品列表")
    public void getHelpOrderProductListTest() {
        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("activityPrice", true);
        paramMap.put("mId", MallUserEnum.mallAutoUser.getMid());
        paramMap.put("orderType", 0);
        paramMap.put("pageIndex", 1);
        paramMap.put("pageSize", 10);
        paramMap.put("type", 0);
        paramMap.put("pdName", "测试");

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.ProductProvider")
                .methodName("getHelpOrderProductList")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.HelpOrderProductListQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取代下单商品失败");


    }



    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("活动商品列表")
    //目前是空的，后面看看原因
    public void getActiveSkuDetailTest() {

        String sku = SkuEnum.skuBillAfter.getSku();
        Integer areaNo=29380;
        Long mId= Long.valueOf(MallUserEnum.mallAutoUser.getMid());

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.ActiveSkuProvider")
                .methodName("getActiveSkuDetail")
                .paramTypes(new String[]{"java.lang.String", "java.lang.Integer", "java.lang.Long"})
                .params(new Object[]{sku,areaNo,mId})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取活动商品失败");


    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    @Story("购物车列表")
    //目前是空的，后面看看原因
    public void getShoppingCartTest() {

        Map<String, Object> paramMap=new HashMap<>();
        //paramMap.put("orderNo", timingOrderNo);
        paramMap.put("mId", MallUserEnum.mallAutoUser.getMid());
        paramMap.put("accountId", 10295);

        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.ShoppingCartProvider")
                .methodName("getShoppingCart")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.ShoppingCartReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取购物车失败");


    }
}
