package com.xianmu.atp.cases.mall.dubbo;


import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.generic.mall.AfterOrderUtil;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("直发采购dubbo调用")
@Owner("zyl")
@Slf4j
public class DirectPurchaseInfoDubboTest extends BaseTest {

    @Resource
    private AfterOrderUtil afterOrderUtil;

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Test
    public void testDirectPurchaseInfoDubbo() {
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("id", 1);
        paramMap.put("pageSize",6);
        paramMap.put("pageIndex",1);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DirectPurchaseInfoProvider")
                .methodName("getDirectPurchaseInfo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DirectPurchaseInfoQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取直发采购信息失败");
    }

    @Test
    public void testListDirectPurchaseInfoByDeliveryDate() {
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("deliveryTime", LocalDate.of(2020,4,4));
        paramMap.put("pageSize",6);
        paramMap.put("pageIndex",1);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DirectPurchaseInfoProvider")
                .methodName("listDirectPurchaseInfoByDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryDateQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"获取直发采购信息失败");
    }
}
