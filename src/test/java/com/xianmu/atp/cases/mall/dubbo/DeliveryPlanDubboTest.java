package com.xianmu.atp.cases.mall.dubbo;


import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.AfterOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Epic("mall")
@Feature("配送计划dubbo调用")
@Owner("zyl")
@Slf4j
public class DeliveryPlanDubboTest extends BaseTest {
    @Resource
    private AfterOrderUtil afterOrderUtil;

    @Resource
    private DefaultDubboConfig defaultDubboConfig;

    @Value("2985")
    private int timingRuleId;

    @Value("89")
    private int timingDays;


    @org.testng.annotations.Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void listNormalOrderDataByDeliveryDateTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("deliveryTime", LocalDate.now().plusDays(1));
        paramMap.put("pageSize",6);
        paramMap.put("pageIndex",1);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DeliveryPlanProvider")
                .methodName("listNormalOrderDataByDeliveryDate")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryDateQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @org.testng.annotations.Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    //删省心送的
    public void deleteDeliveryPlanTest() throws InterruptedException {
        String env="qa";
        Map<String,Object> timingBodyMap = new HashMap<>();
        timingBodyMap.put("timingRuleId",timingRuleId);
        timingBodyMap.put("mphone", MallUserEnum.mallAutoUser.getPhone());
        timingBodyMap.put("contactId", AddressEnum.mallAutoAddress.getContactId());
        timingBodyMap.put("mcontact","dev");
        timingBodyMap.put("merchantCouponId","");
        timingBodyMap.put("quantity",6);
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/timing")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(timingBodyMap))
                .execute();
        JSONObject jsonObject= JSONObject.parseObject(response.body());
        String timingOrderNo=jsonObject.getString("data");

        Map<String,Object> payMap = new HashMap<>();
        payMap.put("paymentMethod",1);
        payMap.put("bizType",0);
        payMap.put("bizOrderNo",timingOrderNo);
        HttpResponse payResponse = HttpRequest.post("https://"+env+"h5.summerfarm.net/payment/union/pay")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(payMap))
                .execute();
        Thread.sleep(3000);




        //设置配送计划，并校验是否在OFC生成数据
        Map<String,Object> nextDeliervyMap = new HashMap<>();
        Map<String,Object> deliveryDetailMap = new HashMap<>();
        LocalDate specificDate =  LocalDate.now().plusDays(3);;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = specificDate.format(formatter);

        deliveryDetailMap.put("deliveryTime",formattedDate);
        deliveryDetailMap.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        deliveryDetailMap.put("quantity",2);

        JSONArray deliveryDetailArray = new JSONArray();
        deliveryDetailArray.add(deliveryDetailMap);
        nextDeliervyMap.put("insertPlanList",deliveryDetailArray);
        nextDeliervyMap.put("orderNo",timingOrderNo);

        HttpResponse insertResponse =  HttpRequest.post("https://"+env+"h5.summerfarm.net/delivery-plan/insert")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(nextDeliervyMap))
                .execute();


        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo", timingOrderNo);
        paramMap.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        paramMap.put("deliveryTime",specificDate);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DeliveryPlanProvider")
                .methodName("deleteDeliveryPlan")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryPlanDeleteReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }


    @org.testng.annotations.Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void updateDeliveryPlanStoreTest() throws InterruptedException {
        MallOrder mallOrder = new MallOrder();
        mallOrder.addEnv(EnvEnum.qa);
        mallOrder.addUser(MallUserEnum.mallAutoUser);
        mallOrder.addAddress(AddressEnum.mallAutoAddress);
        mallOrder.addSku(SkuEnum.skuBillAfter,1);
        mallOrder.send(true);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo", mallOrder.getOrderNo());
        paramMap.put("deliveryTime",LocalDate.now().plusDays(1));
        paramMap.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        paramMap.put("oldStoreNo",20);
        paramMap.put("newStoreNo",1);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DeliveryPlanProvider")
                .methodName("updateDeliveryPlanStore")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryPlanUpdateStoreReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

    @org.testng.annotations.Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void queryDeliveryPlanTest() {
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("status", 3);
        paramMap.put("minDeliveryTime","2025-03-08");
//        List<String> areaList = new ArrayList<>();
//        areaList.add("29380");
        paramMap.put("areaList",null);
        paramMap.put("storeNo",176);
        paramMap.put("city","博尔塔拉蒙古自治州");
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DeliveryPlanProvider")
                .methodName("queryDeliveryPlan")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryPlanQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void insertDeliveryPlanTest() throws InterruptedException {
        String env="qa";
        Map<String,Object> timingBodyMap = new HashMap<>();
        timingBodyMap.put("timingRuleId",timingRuleId);
        timingBodyMap.put("mphone", MallUserEnum.mallAutoUser.getPhone());
        timingBodyMap.put("contactId", AddressEnum.mallAutoAddress.getContactId());
        timingBodyMap.put("mcontact","dev");
        timingBodyMap.put("merchantCouponId","");
        timingBodyMap.put("quantity",6);
        String token = MallOrder.getXianmuMallUserToken(MallUserEnum.mallAutoUser.getPhone(),env);
        HttpResponse response = HttpRequest.post("https://"+env+"h5.summerfarm.net/order/timing")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(timingBodyMap))
                .execute();
        JSONObject jsonObject= JSONObject.parseObject(response.body());
        String timingOrderNo=jsonObject.getString("data");

        Map<String,Object> payMap = new HashMap<>();
        payMap.put("paymentMethod",1);
        payMap.put("bizType",0);
        payMap.put("bizOrderNo",timingOrderNo);
        HttpResponse payResponse = HttpRequest.post("https://"+env+"h5.summerfarm.net/payment/union/pay")
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(JSONObject.toJSONString(payMap))
                .execute();
        Thread.sleep(3000);

        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("orderNo", timingOrderNo);
        paramMap.put("deliveryTime",LocalDate.now().plusDays(3));
        paramMap.put("contactId",AddressEnum.mallAutoAddress.getContactId());
        paramMap.put("quantity",3);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.DeliveryPlanProvider")
                .methodName("insertDeliveryPlan")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryPlanInsertReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        //assert TODO: 2025/断言
    }

//    @Test   不算mapper就1行
//    public void queryRecentTwoDeliveryPlanTest() {
//        Map<String, Object> paramMap=new HashMap<>();
//        paramMap.put("mId", MallUserEnum.mallAutoUser.getMid());
//        paramMap.put("orderTime","2025-03-20");
//        paramMap.put("orderNo","111");
//        paramMap.put("id",1);
////        List<String> areaList = new ArrayList<>();
////        areaList.add("29380");
//        //数据初始化
//        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
//                .env("qa")
//                .interfaceName("net.summerfarm.mall.client.provider.DeliveryPlanProvider")
//                .methodName("queryRecentTwoDeliveryPlan")
//                .paramTypes(new String[]{"net.summerfarm.mall.client.req.DeliveryPlayQueryReq"})
//                .params(new Object[]{paramMap})
//                .build();
//        //调用获取结果
//        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
//        log.info("调用结果：{}", atpDto);
//        //assert TODO: 2025/断言
//    }
}
