package com.xianmu.atp.cases.mall.dubbo;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.DTO.ATPDto;
import com.xianmu.atp.DTO.ATPRequestDto;
import com.xianmu.atp.enums.AddressEnum;
import com.xianmu.atp.enums.EnvEnum;
import com.xianmu.atp.enums.MallUserEnum;
import com.xianmu.atp.enums.SkuEnum;
import com.xianmu.atp.generic.mall.AfterOrderUtil;
import com.xianmu.atp.generic.mall.SelftUsages.MallOrder;
import com.xianmu.atp.util.dubbo.DefaultDubboConfig;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Epic("mall")
@Feature("门店dubbo调用")
@Owner("zyl")
@Slf4j
public class MerchantDubboTest extends BaseTest {

    @Resource
    private AfterOrderUtil afterOrderUtil;

    @Resource
    private DefaultDubboConfig defaultDubboConfig;


    @Test(retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"3"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
    public void subAccountInfoTest() {
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("accountId",10295);
        paramMap.put("pageIndex",1);
        paramMap.put("pageSize",6);
        //数据初始化
        ATPRequestDto atpRequestDto = ATPRequestDto.builder()
                .env("qa")
                .interfaceName("net.summerfarm.mall.client.provider.MerchantProvider")
                .methodName("subAccountInfo")
                .paramTypes(new String[]{"net.summerfarm.mall.client.req.merchant.SubAccountQueryReq"})
                .params(new Object[]{paramMap})
                .build();
        //调用获取结果
        ATPDto atpDto = defaultDubboConfig.getDubboResult(atpRequestDto);
        log.info("调用结果：{}", atpDto);
        Assert.isTrue(atpDto.getDubboResponse().toString().contains("success"),"子账户信息调用失败");
        //assert TODO: 2025/断言
    }
}
