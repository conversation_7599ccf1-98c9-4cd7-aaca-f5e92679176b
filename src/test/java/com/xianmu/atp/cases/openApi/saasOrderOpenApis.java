package com.xianmu.atp.cases.openApi;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class saasOrderOpenApis extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Value("https://dev-openapi.summerfarm.net/api?apiPath=saas.upsert.store.info")
    private String upsertStoreUrl;

    @Value("https://dev-openapi.summerfarm.net/api?apiPath=saas.customer.code.create.order")
    private String createOrderUrl;

    @Value("ningji2_app")
    private String appkeyOfNingji;

    @Value("b2a276873ef51cc837ec21f6afc38187")
    private String appSecretOfNingji;

    @Value("chagee_app")
    private String appkeyOfBawang;

    @Value("z8cd182f64ef0ecdbal20250414e9d5")
    private String appSecretOfBawang;

    public void testBawangOrder(){
        
        String body1=buildOrderBody("","2025-05-01",false);
    }


    public String createOrderBawang(String body){
        return createOrder(appkeyOfBawang,appSecretOfBawang,body);
    }


    public String createOrder(String appkey, String appSecret,String body){
        String DefaultJson="{\n" +
                "  \"storeNo\": \"outTest002\",\n" +
                "  \"customerOrderId\": \"ORauto+"+generateUniqueString()+"\",\n" +
                "  \"orderItemList\": [\n" +
                "    {\n" +
                "      \"skuCode\": \"bw909\",\n" +
                "      \"quantity\": 2,\n" +
                "      \"customerOrderItemId\": \"ITEM001\",\n" +
                "      \"itemType\": 0,\n" +
                "      \"customerSkuCode\": \"CUST_SKU001\",\n" +
                "      \"customerSkuTitle\": \"外部商品标题1\",\n" +
                "      \"customerSkuSpecification\": \"外部商品规格1\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"expectedDeliveryDate\": \"2025-05-01\",\n" +
                "  \"orderContactName\": \"张老板\",\n" +
                "  \"orderContactPhone\": \"13888888888\",\n" +
                "  \"orderPaymentTime\": \"2025-01-01 01:10:25\",\n" +
                "  \"overTimeOrder\": false,\n" +
                "  \"orderPriority\": 0\n" +
                "}";
        JSONObject json=JSONObject.parseObject(DefaultJson.toString());
        if(body!=null){
            json=JSONObject.parseObject(body);
        }

        HttpResponse response = HttpRequest.post(createOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("appkey", appkey)
                .header("appSecret", appSecret)
                .body(JSONObject.toJSONString(json))
                .execute();
        return response.body();

    }

    public static String buildOrderBody(String storeNo,String expectedDeliveryDate,Boolean overTimeOrder){
        String DefaultJson="{\n" +
                "  \"storeNo\": \"outTest002\",\n" +
                "  \"customerOrderId\": \"ORauto+"+generateUniqueString()+"\",\n" +
                "  \"orderItemList\": [\n" +
                "    {\n" +
                "      \"skuCode\": \"bw909\",\n" +
                "      \"quantity\": 2,\n" +
                "      \"customerOrderItemId\": \"ITEM001\",\n" +
                "      \"itemType\": 0,\n" +
                "      \"customerSkuCode\": \"CUST_SKU001\",\n" +
                "      \"customerSkuTitle\": \"外部商品标题1\",\n" +
                "      \"customerSkuSpecification\": \"外部商品规格1\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"expectedDeliveryDate\": \"2025-05-01\",\n" +
                "  \"orderContactName\": \"张老板\",\n" +
                "  \"orderContactPhone\": \"13888888888\",\n" +
                "  \"orderPaymentTime\": \"2025-01-01 01:10:25\",\n" +
                "  \"overTimeOrder\": false,\n" +
                "  \"orderPriority\": 0\n" +
                "}";
        JSONObject json=JSONObject.parseObject(DefaultJson.toString());
        if(storeNo!=null){
            json.put("storeNo", storeNo);
        }
        if(expectedDeliveryDate!=null){
            json.put("expectedDeliveryDate", expectedDeliveryDate);
        }
        if (overTimeOrder!=null){
            json.put("overTimeOrder", overTimeOrder);
        }
        return JSONObject.toJSONString(json);
    }

    public static String generateUniqueString() {
        long timestamp = System.currentTimeMillis(); // 当前时间的时间戳
        //int randomNum = new Random().nextInt(100000); // 生成一个随机数
        return String.valueOf(timestamp); // 组合时间戳和随机数
    }
}
