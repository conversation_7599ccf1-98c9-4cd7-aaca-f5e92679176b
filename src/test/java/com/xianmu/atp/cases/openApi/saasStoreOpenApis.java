package com.xianmu.atp.cases.openApi;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.enums.DataSourceEnums;
import com.xianmu.atp.generic.common.SqlExcutorUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.testng.annotations.Test;

public class saasStoreOpenApis extends BaseTest {

    @Autowired
    private DynamicDataSourceProperties properties;

    @Value("https://dev-openapi.summerfarm.net/api?apiPath=saas.upsert.store.info")
    private String upsertStoreUrl;

    @Value("https://dev-openapi.summerfarm.net/api?apiPath=saas.customer.code.create.order")
    private String createOrderUrl;

    @Value("ningji2_app")
    private String appkeyOfNingji;

    @Value("b2a276873ef51cc837ec21f6afc38187")
    private String appSecretOfNingji;

    @Value("chagee_app")
    private String appkeyOfBawang;

    @Value("z8cd182f64ef0ecdbal20250414e9d5")
    private String appSecretOfBawang;



    @Test
    public void createBawangStore() throws InterruptedException {
        String storeId = upsertBawangStore(null);
        JdbcTemplate saasJdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.cosfodb);
        String sql="select status from cosfodb.merchant_store where id="+storeId;
        Assert.isTrue(SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,sql,"status","0",5),"霸王茶姬创建门店失败");
    }

    @Test
    public void createNingjiStore() throws InterruptedException {
        String storeId = upsertNingjiStore(null);
        JdbcTemplate saasJdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.cosfodb);
        String sql="select status from cosfodb.merchant_store where id="+storeId;
        SqlExcutorUtil.getOnlyValue(saasJdbcTemplate,sql);
        Assert.isTrue(SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,sql,"status","1",5),"柠季节创建门店失败");
    }

    @Test
    public void updateBawangStore() throws InterruptedException {
        JdbcTemplate saasJdbcTemplate = SqlExcutorUtil.createJdbcTemplate(properties, DataSourceEnums.cosfodb);
        String resetSql="update cosfodb.merchant_store set status=1 where id="+166278;
        saasJdbcTemplate.update(resetSql);
        String updateBody="{\n" +
                "  \"storeName\": \"杭州旗舰店aaazzz1\",\n" +
                "  \"storeNo\": \"StoreNo1744784398887\",\n" +
                "  \"type\": 0,\n" +
                "  \"billSwitch\": 1,\n" +
                "  \"onlinePayment\": 1,\n" +
                "  \"remark\": \"欢迎光临我们的门店！\",\n" +
                "  \"contactList\": [\n" +
                "    {\n" +
                "      \"contactName\": \"张三\",\n" +
                "      \"phone\": \"***********\",\n" +
                "      \"defaultFlag\": 1,\n" +
                "      \"addressInfo\": {\n" +
                "        \"province\": \"浙江省\",\n" +
                "        \"city\": \"杭州市\",\n" +
                "        \"area\": \"西湖区\",\n" +
                "        \"address\": \"西湖文化广场"+generateUniqueString()+"\"\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"accountList\": [\n" +
                "    {\n" +
                "      \"accountName\": \"张店长\",\n" +
                "      \"phone\": \"***********\",\n" +
                "      \"type\": 0\n" +
                "    }\n" +
                "  ],\n" +
                "  \"balanceAuthority\": 1\n" +
                "}";
        upsertBawangStore(updateBody);

        String sql="select status from cosfodb.merchant_store where id="+166278;
        SqlExcutorUtil.getOnlyValue(saasJdbcTemplate,sql);
        Assert.isTrue(SqlExcutorUtil.checkSqlValue(saasJdbcTemplate,sql,"status","0",5),"霸王茶姬创建门店失败");
    }
    public String upsertNingjiStore(String body){
        if(body==null){
            return upsertStore(appkeyOfNingji,appSecretOfNingji,null);
        }
        return upsertStore(appkeyOfNingji,appSecretOfNingji,body);
    }

    public String upsertBawangStore(String body){
        if(body==null){
            return upsertStore(appkeyOfBawang,appSecretOfBawang,null);
        }
        return upsertStore(appkeyOfBawang,appSecretOfBawang,body);

    }


    public String upsertStore(String appkey, String appSecret,String body){
        String DefaultJson="{\n" +
                "  \"storeName\": \"杭州旗舰店aaazzz1\",\n" +
                "  \"storeNo\": \"HZ1234567\",\n" +
                "  \"type\": 0,\n" +
                "  \"billSwitch\": 1,\n" +
                "  \"onlinePayment\": 1,\n" +
                "  \"remark\": \"欢迎光临我们的门店！\",\n" +
                "  \"contactList\": [\n" +
                "    {\n" +
                "      \"contactName\": \"张三\",\n" +
                "      \"phone\": \"***********\",\n" +
                "      \"defaultFlag\": 1,\n" +
                "      \"addressInfo\": {\n" +
                "        \"province\": \"浙江省\",\n" +
                "        \"city\": \"杭州市\",\n" +
                "        \"area\": \"西湖区\",\n" +
                "        \"address\": \"西湖文化广场\"\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"accountList\": [\n" +
                "    {\n" +
                "      \"accountName\": \"张店长\",\n" +
                "      \"phone\": \"***********\",\n" +
                "      \"type\": 0\n" +
                "    }\n" +
                "  ],\n" +
                "  \"balanceAuthority\": 1\n" +
                "}";
        JSONObject json=JSONObject.parseObject(DefaultJson.toString());
        json.put("storeName", "门店创建自动化"+generateUniqueString());
        json.put("storeNo", "StoreNo"+generateUniqueString());
        if(body!=null){
            json=JSONObject.parseObject(body);
        }

        HttpResponse response = HttpRequest.post(upsertStoreUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("appkey", appkey)
                .header("appSecret", appSecret)
                .body(JSONObject.toJSONString(json))
                .execute();
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        return jsonObject.getJSONObject("data").getString("storeId");

    }

    public static String generateUniqueString() {
        long timestamp = System.currentTimeMillis(); // 当前时间的时间戳
        //int randomNum = new Random().nextInt(100000); // 生成一个随机数
        return String.valueOf(timestamp); // 组合时间戳和随机数
    }
}
