package com.xianmu.atp.cases.ofc;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSON;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.*;
import com.xianmu.atp.dal.model.DeliveryPlan;
import com.xianmu.atp.dal.model.OfcOrder;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.supply.GetDate;
import com.xianmu.atp.generic.supply.OfcOrderAssert;
import com.xianmu.atp.generic.supply.WmsDeliver;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMListener;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.*;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.*;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.text.SimpleDateFormat;
@Epic("注解测试")
@Feature("OFC商城订单用例")
@Owner("xianmu")
@Listeners({XMListener.class})
public class MallOrderTest  extends BaseTest {
    @Resource
    private  GetDate getDate;
    @Resource
    private OfcOrderDao ofcOrderDao;
    @Resource
    private DeliveryPlanDao deliveryPlanDao;
    @Resource
    private WmsStockTaskNoticeOrderDao wmsStockTaskNoticeOrderDao;
    @Resource
    private GoodsSupplyOrderDao goodsSupplyOrderDao;
    @Resource
    private OfcOrderAssert ofcOrderAssert;
    @Resource
    private WmsDeliver wmsDeliver;
    @Resource
    private WarehouseLogisticsCenterDao warehouseLogisticsCenterDao;
    @Resource
    private GoodsRecycleOrderDao goodsRecycleOrderDao;
    @Resource
    private AreaStoreDao areaStoreDao;
    Log log = LogFactory.get();
    @Resource
    private LoginHttp login;
    @Value("${app.manage-domain}")
    private String domain;
    @Value("${app.environment}")
    private String env;
    @Value("13175113333")
    private String phone;
    @Value("350801")
    private Integer contactId;
    @Value("2160107763766")
    private String sku;
    @Value("302")
    private Integer storeNo;
    @Value("2")
    private Integer quality;
    @Value("490")
    private Integer warehouseNo;
    @BeforeClass
    public  void setup() {
        warehouseLogisticsCenterDao.updateCloseTime("22:00:00",storeNo);
        areaStoreDao.setOnlineQuantity(sku,warehouseNo);
    }
    @Story("OFC的商城订单")
    @Owner("xianmu")
    @Test(description = "商城下单OFC断言", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void MallOrderToOfc() {
        String orderListUrl =domain+ "/summerfarm-ofc/fulfillment/query/list";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        Reporter.log("log-test");
        getDate.Sleep(2000);
        ofcOrderAssert.ofcOrderAssert(orderNo);
    }

    @Story("OFC的商城订单关单")
    @Owner("xianmu")
    @Test(description = "OFC关单", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void MallOrderOfcClose() {
        //先下单
        String orderListUrl =domain+
                "/summerfarm-ofc/fulfillment/query/list";
        String closeOrderUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/close-order";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        Reporter.log("log-test");
        ofcOrderAssert.ofcOrderAssert(orderNo);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        //再进入OFC详情进行关单操作
        String closeOrderBody = "{\n" +
                "\t\"remark\": \"测试关单\",\n" +
                "\t\"fulfillmentNo\": "+fulfillmentNo+"\n" +
                "}";
        HttpResponse response = HttpRequest.post(closeOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(closeOrderBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 关单返回："+response.body());
        getDate.Sleep(5000);
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
        ofcOrderAssert.closeOrderAssert(orderNo);
        //商城订单关单状态要单独判断
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getStatus().toString(),"14");

    }

    @Story("OFC修改配送日期用例")
    @Owner("xianmu")
    @Test(description = "OFC修改配送日期", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void MallOrderOfcDeliver() {
        //先下单
        String orderListUrl =domain+
                "/summerfarm-ofc/fulfillment/query/list";
        String updateOrderTimeUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/fulfillment-time";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        Reporter.log("log-test");
        getDate.Sleep(2000);
        ofcOrderAssert.ofcOrderAssert(orderNo);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        Date fulfillmentTime = ofcOrders.get(0).getFulfillmentTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(fulfillmentTime);
        // 当前日期加 1 天
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        // 获取加 1 天后的日期
        Date nextDate = calendar.getTime();

        // 格式化日期为 yyyy-MM-dd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormat.format(nextDate);
        String upsertBody = "{\n" +
                "\t\"fulfillmentNos\": [\""+fulfillmentNo+"\"],\n" +
                "\t\"fulfillmentTime\": \""+formattedDate+"\"\n" +
                "}";
        //再进入OFC详情修改配送日期操作
        HttpResponse response = HttpRequest.post(updateOrderTimeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(upsertBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 修改配送日期返回："+response.body());
        getDate.Sleep(5000);
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
        ofcOrderAssert.updateOrderAssert(orderNo,formattedDate);
        //商城订单配送日期状态要单独判断
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getDeliveryTime().toString(),formattedDate);

    }

    @Story("OFC修改为自提")
    @Owner("xianmu")
    @Test(description = "OFC自提", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void MallOrderOfcShelfPickUp() {
        String orderListUrl =domain+
                "/summerfarm-ofc/fulfillment/query/list";
        String shelfPickUpUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/self-pickup";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        Reporter.log("log-test");
        getDate.Sleep(2000);
        ofcOrderAssert.ofcOrderAssert(orderNo);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        String shelfPickUpBody = "{\"fulfillmentNo\": "+fulfillmentNo+"\n" +
                "}";
        HttpResponse response = HttpRequest.post(shelfPickUpUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(shelfPickUpBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 修改订单为自提返回："+response.body());
        getDate.Sleep(5000);
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
        ofcOrderAssert.shelfPickUpAssert(orderNo);
        //自提后商城那边的断言
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getDeliverytype().toString(),"1");
    }
    @Story("OFC发起拦截")
    @Owner("xianmu")
    @Test(description = "OFC发起拦截", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void MallOrderOfcIntercept() {
        String preCloseTime = warehouseLogisticsCenterDao.getCloseTime(storeNo).get(0).getCloseTime();
        warehouseLogisticsCenterDao.updateCloseTime(preCloseTime,storeNo);
        String InterceptUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/intercept";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        getDate.Sleep(2000);
        Reporter.log("log-test");
        ofcOrderAssert.ofcOrderAssert(orderNo);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        long taskId = wmsDeliver.outStockTask(orderNo,0,0);
        log.info("生成的出库任务 taskId:{}",taskId);
         //更新截单时间
        String newCloseTime = "00:00:01";
        warehouseLogisticsCenterDao.updateCloseTime(newCloseTime,storeNo);
        String InterceptBody = "{\n" +
                "\t\"reason\": \"0\",\n" +
                "\t\"remark\": \"1\",\n" +
                "\t\"fulfillmentNos\": [\""+fulfillmentNo+"\"]\n" +
                "}";
        HttpResponse response = HttpRequest.post(InterceptUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(InterceptBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 发起拦截："+response.body());
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
        getDate.Sleep(5000);
        ofcOrderAssert.interceptAssert(orderNo);
        //恢复截单时间
        warehouseLogisticsCenterDao.updateCloseTime(preCloseTime,storeNo);
        //上游商城订单那边的断言
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getInterceptFlag().toString(),"1");
    }

    @Story("OFC发起延迟配送")
    @Owner("xianmu")
    @Test(description = "OFC发起延迟配送", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void MallOrderOfcDelayDelivery() {
        String preCloseTime = warehouseLogisticsCenterDao.getCloseTime(storeNo).get(0).getCloseTime();
        String DelayDeliveryUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/delay-delivery";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        getDate.Sleep(2000);
        Reporter.log("log-test");
        ofcOrderAssert.ofcOrderAssert(orderNo);
        //更新截单时间
        String newCloseTime = "00:00:01";
        warehouseLogisticsCenterDao.updateCloseTime(newCloseTime,storeNo);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        Date fulfillmentTime = ofcOrders.get(0).getFulfillmentTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(fulfillmentTime);
        // 当前日期加 1 天
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        // 获取加 1 天后的日期
        Date nextDate = calendar.getTime();

        // 格式化日期为 yyyy-MM-dd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormat.format(nextDate);
        String DelayDeliveryBody = "{\n" +
                "\t\"fulfillmentNos\": ["+fulfillmentNo+"],\n" +
                "\t\"fulfillmentTime\": \""+formattedDate+"\"\n" +
                "}";
        HttpResponse response = HttpRequest.post(DelayDeliveryUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(DelayDeliveryBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 发起延迟配送："+response.body());
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
        warehouseLogisticsCenterDao.updateCloseTime(preCloseTime,storeNo);
        ofcOrderAssert.delayDeliveryAssert(orderNo,formattedDate);
        //商城订单延迟配送日期状态要单独判断
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getDeliveryTime().toString(),formattedDate);
    }

    @Test(description = "OFC发起延迟配送", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void MallOrderOfcAddOrder() {
        String preCloseTime = warehouseLogisticsCenterDao.getCloseTime(storeNo).get(0).getCloseTime();
        log.info("前置将截单时间调前5分钟，这样下单可以下单后的订单为T+2");
        Date now = new Date();
        // 使用 Calendar 来修改时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        // 减去 5 分钟
        calendar.add(Calendar.MINUTE, -5);
        // 获取 5 分钟前的时间
        Date fiveMinutesAgo = calendar.getTime();
        // 使用 SimpleDateFormat 格式化输出时分秒部分
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String formattedTime = sdf.format(fiveMinutesAgo);
        //更新截单时间
        warehouseLogisticsCenterDao.updateCloseTime(formattedTime,storeNo);
        String addOrderUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/add-order";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        getDate.Sleep(2000);
        Reporter.log("log-test");
        ofcOrderAssert.ofcOrderAssert(orderNo);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        log.info("进行加单操作");
        String addOrderBody = "{\"fulfillmentNo\": "+fulfillmentNo+"}";
        HttpResponse response = HttpRequest.post(addOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(addOrderBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 发起加单操作返回："+response.body());
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
        warehouseLogisticsCenterDao.updateCloseTime(preCloseTime,storeNo);
        Date currentDate = new Date();
        calendar.setTime(currentDate);
        // 当前日期加 1 天
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        // 获取加 1 天后的日期
        Date nextDate = calendar.getTime();
        // 格式化日期为 yyyy-MM-dd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormat.format(nextDate);
        ofcOrderAssert.addOrderAssert(orderNo,formattedDate);
        //商城另外断言
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getDeliveryTime().toString(),formattedDate);
    }
}
