package com.xianmu.atp.cases.ofc;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSON;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.dal.dao.OfcOrderDao;
import com.xianmu.atp.dal.dao.WarehouseLogisticsCenterDao;
import com.xianmu.atp.dal.model.OfcOrder;
import com.xianmu.atp.generic.mall.AfterOrderUtil;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.supply.GetDate;
import com.xianmu.atp.generic.supply.WmsDeliver;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMListener;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.*;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Epic("注解测试")
@Feature("OFC售后补发订单用例")
@Owner("xianmu")
@Listeners({XMListener.class})
public class AfterSaleResendOrderTest  extends BaseTest {
    Log log = LogFactory.get();
    @Resource
    private LoginHttp login;
    @Value("${app.manage-domain}")
    private String domain;
    @Value("${app.environment}")
    private String env;
    @Value("13175116666")
    private String phone;
    @Value("351833")
    private Integer contactId;
    @Value("2160675764440")
    private String sku;
    @Value("302")
    private Integer storeNo;
    @Value("2")
    private Integer quality;
    @Value("490")
    private Integer warehouseNo;
    @Resource
    private WarehouseLogisticsCenterDao warehouseLogisticsCenterDao;
    @Resource
    private AfterOrderUtil afterOrderUtil;
    @Resource
    private  GetDate getDate;
    @Resource
    private OfcOrderDao ofcOrderDao;
    @Resource
    private WmsDeliver wmsDeliver;
    @Resource
    private AreaStoreDao areaStoreDao;
    @BeforeClass
    public  void setup() {

        warehouseLogisticsCenterDao.updateCloseTime("22:00:00",storeNo);
        areaStoreDao.setOnlineQuantity(sku,warehouseNo);
        //库存恢复到999，这样每次都有充足的虚拟库存
    }
    @Story("OFC的补发订单")
    @Owner("xianmu")
    @Test(description = "商城下单OFC补发订单", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void ResendOrderOfc() {
        String ResendOrderNo = afterOrderUtil.AfterSaleResendOrder(env, phone, contactId, sku, quality);
        log.info("补发售后单号：{}",ResendOrderNo);
    }

    @Test(description = "商城补发订单，OFC修改配送日期", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void ResendOrderOfcDeliver() {
        String ResendOrderNo = afterOrderUtil.AfterSaleResendOrder(env, phone, contactId, sku, quality);
        log.info("补发售后单号：{}",ResendOrderNo);
        String updateOrderTimeUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/fulfillment-time";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        Reporter.log("log-test");

        getDate.Sleep(2000);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        String formattedDate =getDate.GetTomorrowDate();
        String upsertBody = "{\n" +
                "\t\"fulfillmentNos\": [\""+fulfillmentNo+"\"],\n" +
                "\t\"fulfillmentTime\": \""+formattedDate+"\"\n" +
                "}";
        //再进入OFC详情修改配送日期操作
        HttpResponse response = HttpRequest.post(updateOrderTimeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(upsertBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 修改配送日期返回："+response.body());
        getDate.Sleep(5000);
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
    }

    @Test(description = "OFC补发订单发起拦截", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void ResendOrderOfcIntercept() {
        String preCloseTime = warehouseLogisticsCenterDao.getCloseTime(storeNo).get(0).getCloseTime();
        warehouseLogisticsCenterDao.updateCloseTime(preCloseTime,storeNo);
        String InterceptUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/intercept";
        String token = login.login();
        log.info("token:{}", token);
        String ResendOrderNo = afterOrderUtil.AfterSaleResendOrder(env, phone, contactId, sku, quality);
        log.info("补发售后单号：{}",ResendOrderNo);
        getDate.Sleep(2000);
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(ResendOrderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();

        long taskId = wmsDeliver.outStockTask(ResendOrderNo,0,0);
        log.info("生成的出库任务 taskId:{}",taskId);
        //更新截单时间
        String newCloseTime = "00:00:01";
        warehouseLogisticsCenterDao.updateCloseTime(newCloseTime,storeNo);
        String InterceptBody = "{\n" +
                "\t\"reason\": \"0\",\n" +
                "\t\"remark\": \"1\",\n" +
                "\t\"fulfillmentNos\": [\""+fulfillmentNo+"\"]\n" +
                "}";
        HttpResponse response = HttpRequest.post(InterceptUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(InterceptBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 发起拦截："+response.body());
        Assert.assertEquals(JSON.parseObject(response.body()).get("status"),200);
        getDate.Sleep(5000);
        //恢复截单时间
        warehouseLogisticsCenterDao.updateCloseTime(preCloseTime,storeNo);
    }
    @Test(description = "商城补发订单关闭售后单", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void ResendOrderClose() {
        String ResendOrderNo = afterOrderUtil.AfterSaleResendOrder(env, phone, contactId, sku, quality);
        log.info("补发售后单号：{}",ResendOrderNo);
        String closetUrl =domain+
                "/after-sale/close/"+ResendOrderNo;
        String token = login.login();
        log.info("token:{}", token);
        HttpResponse response = HttpRequest.post(closetUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body("")
                .execute();
        Reporter.log("log-test");
        log.info("商城关闭售后单返回："+response.body());
        Assert.assertEquals(JSON.parseObject(response.body()).get("code"),"SUCCESS");
    }

    @Test(description = "商城下单OFC补发订单", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void RecycleOrderOfc() {
        String ResendOrderNo = afterOrderUtil.AfterSaleRecycle(env, phone, contactId, sku, quality);
        log.info("售后单号：{}",ResendOrderNo);
    }

}
