package com.xianmu.atp.cases.ofc;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSON;
import com.xianmu.atp.BaseTest;
import com.xianmu.atp.dal.dao.AreaStoreDao;
import com.xianmu.atp.dal.dao.DeliveryPlanDao;
import com.xianmu.atp.dal.dao.OfcOrderDao;
import com.xianmu.atp.dal.dao.WarehouseLogisticsCenterDao;
import com.xianmu.atp.dal.model.DeliveryPlan;
import com.xianmu.atp.dal.model.OfcOrder;
import com.xianmu.atp.generic.common.ExecuteJob;
import com.xianmu.atp.generic.mall.MallOrderUtil;
import com.xianmu.atp.generic.supply.GetDate;
import com.xianmu.atp.generic.supply.OfcOrderAssert;
import com.xianmu.atp.util.http.LoginHttp;
import com.xianmu.atp.util.testng.XMListener;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.*;
import org.springframework.beans.factory.annotation.Value;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Epic("注解测试")
@Feature("OFC代销不入仓订单用例")
@Owner("xianmu")
@Listeners({XMListener.class})
public class AgentOrderTest   extends BaseTest{
    Log log = LogFactory.get();
    @Resource
    private LoginHttp login;
    @Value("${app.manage-domain}")
    private String domain;
    @Value("${app.environment}")
    private String env;
    @Value("13175115555")
    private String phone;
    @Value("350821")
    private Integer contactId;
    @Value("2160087156081")
    private String sku;
    @Value("302")
    private Integer storeNo;
    @Value("2")
    private Integer quality;
    @Value("490")
    private Integer warehouseNo;

    @Resource
    private WarehouseLogisticsCenterDao warehouseLogisticsCenterDao;
    @Resource
    private OfcOrderAssert ofcOrderAssert;
    @Resource
    private DeliveryPlanDao deliveryPlanDao;
    @Resource
    private OfcOrderDao ofcOrderDao;
    @Resource
    private ExecuteJob executeJob;
    @Resource
    private GetDate getDate;
    @Resource
    private AreaStoreDao areaStoreDao;
    @BeforeClass
    public  void setup() {
        warehouseLogisticsCenterDao.updateCloseTime("22:00:00",storeNo);
        //areaStoreDao.updateOnlineQuantity(sku,warehouseNo);
        areaStoreDao.setOnlineQuantity(sku,warehouseNo);
        //库存恢复到999，这样每次都有充足的虚拟库存
    }
    @Story("OFC的代销不入仓订单")
    @Owner("xianmu")
    @Test(description = "商城下单OFC断言代销不入仓订单", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void agentOrderToOfc() {
        String orderListUrl =domain+ "/summerfarm-ofc/fulfillment/query/list";
        String skuAvailableUrl = domain
                +"/summerfarm-wms/notice/tool/test_query_av";
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String skuAvailableBody ="{ \"sku\":\""+sku+"\",\"warehouseNo\":"+warehouseNo+"}";
        HttpResponse skuAvailableResponse = HttpRequest.post(skuAvailableUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(skuAvailableBody)
                .execute();
        Integer num = Integer.valueOf(JSON.parseObject(skuAvailableResponse.body()).getJSONObject("data").get("availableNum").toString());
        if (num>0)
            quality = num +quality;
        String orderNo = MallOrderUtil.send(env,phone,contactId,sku,quality);
        Reporter.log("log-test");
        getDate.Sleep(3000);
        log.info("token:{}", token);
        //查询履约表
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        //查看履约列表数据
        String orderListBody = "{\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 10,\n" +
                "\t\"outOrderNo\": \""+orderNo+"\"\n" +
                "}";
        HttpResponse response = HttpRequest.post(orderListUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(orderListBody)
                .execute();
        Reporter.log("log-test");
        log.info(JSON.parseObject(response.body()).getJSONObject("data").toString());
        Assert.assertEquals(JSON.parseObject(response.body()).getJSONObject("data").get("total").toString() , "1");
        //履约单断言
        List<DeliveryPlan> deliveryOrder = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrder.get(0).getDeliveryTime().toString(),ofcOrders.get(0).getFulfillmentTime().toString());
        Assert.assertEquals(ofcOrders.get(0).getFulfillmentStatus(),10);
        Assert.assertEquals(ofcOrders.get(0).getOutContactId().toString(),deliveryOrder.get(0).getContactId().toString());

        log.info("OFC销转采购操作");
        String saleToPurchaseUrl =domain+
                "/summerfarm-ofc/saleToPurchase/debug";

        String saleToPurchaseBody = "{\"pickupDate\": \""+getDate.GetTomorrowDate()+"\",\"warehouseNo\": "+warehouseNo+"}";
        HttpResponse saleToPurchaseResponse = HttpRequest.post(saleToPurchaseUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(saleToPurchaseBody)
                .execute();
        Reporter.log("log-test");
        log.info(saleToPurchaseResponse.body());
        Assert.assertEquals(JSON.parseObject(saleToPurchaseResponse.body()).get("status"),200);
    }
    @Test(description = "代销不入仓销转采后关闭订单", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void agentOrderToSaleClose(){
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = ofcOrderAssert.AgentOrderToOfc(phone,contactId,sku,quality,warehouseNo);
        //OFC关闭代销不入仓销转采后的订单
        String closeOrderUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/close-order";
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        //再进入OFC详情进行关单操作
        String closeOrderBody = "{\n" +
                "\t\"remark\": \"测试关单\",\n" +
                "\t\"fulfillmentNo\": "+fulfillmentNo+"\n" +
                "}";
        HttpResponse closeResponse = HttpRequest.post(closeOrderUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(closeOrderBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 关单返回："+closeResponse.body());
        getDate.Sleep(5000);
        Assert.assertEquals(JSON.parseObject(closeResponse.body()).get("status"),200);
        //商城订单关单状态要单独判断
        List<DeliveryPlan> deliveryOrders = deliveryPlanDao.getDeliveryOrder(orderNo);
        Assert.assertEquals(deliveryOrders.get(0).getStatus().toString(),"14");

    }
    @Test(description = "代销不入仓销转采后延迟配送", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void agentOrderToSaleDeliver() {
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = ofcOrderAssert.AgentOrderToOfc(phone, contactId, sku, quality, warehouseNo);
        String updateOrderTimeUrl =domain+
                "/summerfarm-ofc/fulfillment/upsert/fulfillment-time";
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();

        String formattedDate =getDate.GetDesignatedDate(4);
        String upsertBody = "{\n" +
                "\t\"fulfillmentNos\": [\""+fulfillmentNo+"\"],\n" +
                "\t\"fulfillmentTime\": \""+formattedDate+"\"\n" +
                "}";
        //再进入OFC详情修改配送日期操作
        HttpResponse response = HttpRequest.post(updateOrderTimeUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(upsertBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 修改配送日期返回："+response.body());
    }

    @Test(description = "代销不入仓销转采后商城未到货退款操作", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void agentOrderToSaleAfterSale() {
        String token = login.login();
        log.info("token:{}", token);
        Allure.label("定位符：", token);
        String orderNo = ofcOrderAssert.AgentOrderToOfc(phone, contactId, sku, quality, warehouseNo);
        String AfterSaleUrl =domain+
                "/after-sale/order/save";
        String afterSaleBody = "{\n" +
                "    \"orderNo\": \""+orderNo+"\",\n" +
                "    \"sku\": \""+sku+"\",\n" +
                "    \"suitId\": 0,\n" +
                "    \"isManage\": true,\n" +
                "    \"afterSaleUnit\": \"条\",\n" +
                "    \"handleRemark\": \"\",\n" +
                "    \"handleType\": 2,\n" +
                "    \"proofPic\": \"\",\n" +
                "    \"proofVideo\": \"\",\n" +
                "    \"quantity\": "+quality+",\n" +
                "    \"handleNum\": "+quality+",\n" +
                "    \"deliveryed\": 0,\n" +
                "    \"afterSaleRemarkType\": \"\",\n" +
                "    \"afterSaleRemark\": \"\",\n" +
                "    \"recoveryType\": 0,\n" +
                "    \"recoveryNum\": 0,\n" +
                "    \"deliveryId\": \"\",\n" +
                "    \"refundType\": \"缺货\",\n" +
                "    \"applySecondaryRemark\": \"错拍\"\n" +
                "}";
        HttpResponse response = HttpRequest.post(AfterSaleUrl)
                .header(Header.CONTENT_TYPE, "application/json")
                .header("token", token)
                .body(afterSaleBody)
                .execute();
        Reporter.log("log-test");
        log.info("OFC 修改配送日期返回："+response.body());
        Assert.assertEquals(JSON.parseObject(response.body()).get("code"),"SUCCESS");
        List<OfcOrder> ofcOrders = ofcOrderDao.getFulfillmentOrder(orderNo);
        Long fulfillmentNo = ofcOrders.get(0).getFulfillmentNo();
        getDate.Sleep(2000);
        ofcOrderAssert.AfterSaleAssert(orderNo);
    }

    @Test(description = "test", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void test(){
        String status = executeJob.RunExecuteJob(env,"summerfarm-wms",23507699L);
        log.info("定时任务返回的状态为:{}",status);
    }


}
