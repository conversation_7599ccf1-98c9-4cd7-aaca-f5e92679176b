package com.xianmu.atp.cases.ofc;

import com.xianmu.atp.BaseTest;
import com.xianmu.atp.generic.mall.PopOrderUtil;
import com.xianmu.atp.util.testng.XMRetryAnalyzer;
import io.qameta.allure.Owner;
import io.qameta.allure.Story;
import org.springframework.beans.factory.annotation.Value;
import org.testng.annotations.CustomAttribute;
import org.testng.annotations.Test;

import javax.annotation.Resource;

public class PopOrderTest extends BaseTest {
    @Resource
    private PopOrderUtil popOrderUtil;
    @Value("${app.environment}")
    private String env;
    @Value("18768143561")
    private String phone;
    @Value("348132")
    private Integer contactId;
    @Value("2185440823444")
    private String sku;
    @Value("481")
    private Integer warehouse;
    @Story("OFC的順路達订单")
    @Owner("xianmu")
    @Test(description = "POP下单OFC断言,T+1", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"10"})
            })
    public void PopOrderToOfc() {
       String orderNo =  popOrderUtil.PopOrder( env,  phone,  contactId,  sku, 5, warehouse);

    }
}
