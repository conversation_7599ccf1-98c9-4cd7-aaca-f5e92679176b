package com.xianmu.atp.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 类<code>Doc</code>用于：TODO
 * 重试配置
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @date 2024-11-18
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface DeployConfig {
    /**
     * 依赖顺序
     */
    String sequence();
}
