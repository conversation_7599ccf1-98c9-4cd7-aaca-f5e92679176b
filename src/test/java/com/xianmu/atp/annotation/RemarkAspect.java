package com.xianmu.atp.annotation;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 切面类，用于拦截DAL层方法执行前后来添加备注信息
 */
@Aspect
@Component
public class RemarkAspect {

    private static ThreadLocal<Map<String, Object>> contextHolder = new ThreadLocal<>();

    /**
     * 环绕通知，拦截DAL层方法执行，添加备注信息到上下文中
     *
     * @param joinPoint 切入点对象，包含被拦截方法的信息
     * @return 执行的目标方法的结果
     * @throws Throwable 目标方法执行过程中可能抛出的异常
     */
    @Around("execution(* com.xianmu.atp.dal.mapper..*(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Map<String, Object> context = new HashMap<>();
        if (method.isAnnotationPresent(Remark.class)) {
            Remark remark = method.getAnnotation(Remark.class);
            context.put("remark", remark.value());
            contextHolder.set(context);
        }
        try {
            return joinPoint.proceed();
        }
        finally {
            // 清除当前线程的context值
            contextHolder.remove();
        }
    }

    public static Map<String, Object> getCurrentContext() {
        return contextHolder.get();
    }
}