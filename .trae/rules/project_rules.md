java版本是java8，请按照以下规则为我生成测试用例代码：

# 测试用例生成规则

## 1. 基础信息规范

### 1.1 用例标识
- **用例ID**: TC_API_{模块}_{接口名}_{序号}
- **用例名称**: 简洁明确描述测试目标
- **用例级别**: P0(核心)、P1(重要)、P2(一般)、P3(辅助)
- **测试类型**: 功能测试
- **内容**: 解释测试用例的步骤和内容
- **Author**: AI Test Engineer

### 1.2 接口信息
- **接口名称**: RESTful API命名规范
- **请求方法**: GET、POST、PUT、DELETE、PATCH等
- **接口地址**: 完整的URL路径
- **协议版本**: HTTP/1.1、HTTP/2等

## 2. 测试数据设计原则

### 2.1 等价类划分
- **有效等价类**: 符合业务规则的输入数据
- **无效等价类**: 违反业务规则的输入数据
- **边界值**: 最小值、最大值、临界值

### 2.2 参数类型覆盖
- **必填参数**: 验证缺失时的错误处理
- **可选参数**: 验证默认值和空值处理
- **数据类型**: 字符串、数字、布尔值、数组、对象
- **特殊字符**: SQL注入、XSS、Unicode字符

## 3. 测试场景分类

### 3.1 正向测试场景
- **基本功能验证**: 标准业务流程
- **参数组合测试**: 多参数有效组合
- **数据格式验证**: JSON、XML格式正确性
- **业务逻辑验证**: 符合业务规则的操作

### 3.2 异常测试场景
- **参数缺失**: 必填参数为空
- **参数类型错误**: 类型不匹配
- **参数值超范围**: 超出允许范围
- **无效参数组合**: 互斥参数同时存在
- **权限验证**: 未授权访问
- **数据不存在**: 查询不存在的资源

### 3.3 边界测试场景
- **字符串长度**: 最小长度、最大长度、超长
- **数值范围**: 最小值、最大值、负数、零
- **数组大小**: 空数组、单元素、最大容量
- **时间范围**: 过去时间、未来时间、时区处理

## 4. 用例结构模板

### 4.1 标准格式
```
**用例ID**: TC_API_USER_LOGIN_001
**用例名称**: 用户正常登录功能验证
**优先级**: P0
**前置条件**: 用户已注册且状态正常
**测试步骤**:
1. 发送POST请求到/api/v1/login
2. 传入有效的用户名和密码
**预期结果**: 
- 返回状态码200
- 返回用户信息和token
- token格式正确且有效期符合要求
**实际结果**: [执行时填写]
**测试结论**: [Pass/Fail]
```

### 4.2 详细字段说明
- **请求头**: Content-Type、Authorization等
- **请求体**: 完整的JSON数据
- **响应验证**: 状态码、响应体结构、数据准确性
- **性能要求**: 响应时间阈值

## 5. 断言验证规则

### 5.1 状态码验证
- **2xx**: 成功响应的具体状态码
- **4xx**: 客户端错误的准确分类
- **5xx**: 服务器错误的处理

### 5.2 响应体验证
- **数据结构**: JSON Schema验证
- **字段存在性**: 必需字段不能为空
- **数据类型**: 每个字段的数据类型正确
- **数据范围**: 枚举值、数值范围验证
- **业务逻辑**: 计算结果、状态变更正确性

## 6. 测试环境要求(可以不参考)

### 6.1 环境配置
- **基础URL**: 区分开发、测试、预生产环境
- **数据库状态**: 确保测试数据一致性
- **第三方依赖**: Mock服务或真实服务配置

### 6.2 数据准备
- **基础数据**: 用户、权限、配置数据
- **测试数据**: 针对具体用例的数据
- **清理策略**: 测试后的数据清理

## 7. 自动化测试考虑

### 7.1 用例设计
- **独立性**: 用例间无依赖关系
- **可重复性**: 多次执行结果一致
- **数据驱动**: 参数化测试数据

### 7.2 维护性
- **模块化**: 公共方法抽取
- **配置外置**: 环境配置独立管理
- **报告详细**: 失败原因清晰可追溯

## 8. 质量评估标准

### 8.1 覆盖率指标
- **功能覆盖**: 所有API功能点覆盖
- **代码覆盖**: 业务代码执行覆盖率
- **场景覆盖**: 正常、异常、边界场景

### 8.2 通过标准
- **功能正确性**: 100%核心功能正常
- **性能要求**: 符合SLA要求
- **安全合规**: 通过安全扫描
- **稳定性**: 连续执行无随机失败

## 9. 禁止规则
- 禁止生成安全测试相关用例
- 禁止生成性能测试相关用例

## 10. 额外要求
1. 每个测试用例都要有明确的验证点
2. 考虑参数之间的关联性和依赖关系
3. 包含数据边界和异常情况的测试
4. 提供可执行的具体测试数据
5. 按优先级排序，重要功能优先
6. http请求要加上超时时间timeout：HttpRequest.post(configUrl).header("Content-Type","application/json").timeout(3000).execute();

请确保测试用例覆盖全面、逻辑清晰、可执行性强。

# 执行流程
1.识别场景 → 调用相关规则
2.读取示例代码 → 作为生成参考
3.执行强制/禁止行为 → 确保代码质量

## 质量保障
所有规则必须100%执行，重点关注强制行为和禁止规则
