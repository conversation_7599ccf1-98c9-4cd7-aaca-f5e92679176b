### SummerFarm-XM ATP framework || 自动化测试平台框架介绍
#### 1.框架目录

    └── src
    ├── main
    │   ├── java
    │   │   └── com
    │   │       └── xianmu
    │   │           └── atp
    │   │               └── AtpApplication.java————（启动文件）
    │   └── resources
    │       ├── application-dev.yml————（dev本地开发环境配置）
    │       ├── application-qa.yml————（流水线运行环境配置）
    │       ├── application.yml————（应用环境配置，通过activate选择具体的环境配置文件启动）
    │       └── logback-spring.xml————（日志配置文件）
    └── test
        ├── java
        │   └── com
        │       └── xianmu
        │           └── atp
        │               ├── BaseTest.java————（勿动！基准测试文件，所有的用例需要继承该基础类）
        │               ├── annotation
        │               │   └── DeployConfig.java————（无需关注）
        │               ├── cases————（具体的业务自动化场景用例，按应用组织package）
        │               │   ├── mall
        │               │   │   ├── BaseCaseTest.java
        │               │   ├── tms
        │               │   │   └── PerformanceReviewTest.java
        │               │   └── wms
        │               │       ├── PurchaseReceiveTest.java
        │               │       └── stockTransferTest.java
        │               ├── config————（一些中间件使用配置）
        │               │   └── RedisConfig.java————（redis的配置）
        │               ├── contexts
        │               │   └── MQTopicConstant.java————（消息的常量配置）
        │               ├── dal————（Data Access Layer）
        │               │   ├── dao————（Data Access Object，封装对数据源的访问来简化数据操作）
        │               │   │   ├── AreaSkuDao.java
        │               │   │   ├── AreaStoreDao.java
        │               │   │   ├── ContactDao.java
        │               │   ├── mapper————（mybaits-plus接口）
        │               │   │   ├── AreaSkuMapper.java
        │               │   │   ├── AreaStoreMapper.java
        │               │   │   ├── ContactMapper.java
        │               │   └── model————（数据实例）
        │               │       ├── AreaSku.java
        │               │       ├── AreaStore.java
        │               │       ├── Contact.java
        │               ├── enums————（一些枚举类）
        │               │   ├── AddressEnum.java
        │               │   ├── CouponEnum.java
        │               │   ├── EnvEnum.java
        │               ├── generic————（业务场景的通用代码，按照应用组织package）
        │               │   ├── mall
        │               │   ├── saas
        │               │   └── supply
        │               │       ├── Purchase.java
        │               │       └── WmsDeliver.java
        │               ├── interceptor————（无需关注，框架http请求与响应的探针，用于日志记录）
        │               │   ├── RequestInterceptor.java
        │               │   └── ResponseInterceptor.java
        │               └── util————（与业务场景无关的通用方法，按照中间件或者协议等组织package）
        │                   ├── Constants.java
        │                   ├── MyBatisPlusGenerator.java
        │                   ├── ResultUtil
        │                   │   └── JsonDealer.java
        │                   ├── http
        │                   │   ├── CustomRequest.java
        │                   │   └── LoginHttp.java
        │                   ├── mq
        │                   │   ├── MQData.java
        │                   │   └── XMMqListener.java
        │                   └── testng————（无需关注，testng框架的一些通用方法）
        │                       ├── CsvDataProvider.java
        │                       ├── HeartbeatHandler.java
        │                       ├── XMAnnotationTransformer.java
        │                       ├── XMListener.java
        │                       ├── XMRetryAnalyzer.java
        │                       └── XMSuiteListener.java
        └── resources————（用例的配置文件等）
            ├── TestCase
            │   └── stockTransfer
            │       ├── stockTransferTest.stockTransfer.csv
            ├── mall.xml
            └── testng.xml
#### 2.框架使用
##### -2.1 环境配置
- 本地开发请使用application-dev.yml文件启动（在application.yml中激活）
- ```
  spring:
  profiles:
    active:
      dev
  ```
##### -2.2 用例编写
###### -2.2.1 数据库访问
- 数据库访问使用mybatis-plus框架，具体使用方法可以直接使用生成器，也可以按照如下步骤编写
- -1. 编写实体类接口（数据表），如果使用datagrip桌面管理工具可以通过"数据表右键”->“Tools”->“Scripted-Extension”->“Generate POJOs groovy”
- -2. 在实例类上添加@Data和@TableName注解
- -3. 编写mapper接口，继承BaseMapper，e.g.```public interface AreaSkuMapper extends BaseMapper<AreaSku> {
  }```
- -4. 编写dao类
###### -2.2.2 http请求
- 框架使用hutool工具包进行http请求，具体使用方法可以参考
```java
HttpResponse response = HttpRequest.post(ocrUrl)
                           .header(Header.CONTENT_TYPE, "application/json")
                           .body(JSONString)
                           .execute();
                    Console.log("response:{}", response.body());
```
- 自定义http工具，参考src/test/java/com/xianmu/atp/generic/common/Request.java的main方法
- 支持2种请求方式，分别对应post，get
- 支持contentType为application/json，application/x-www-form-urlencoded
- 响应体HttpResponseWrapper
```java
    public static void main(String[] args) throws IOException, InterruptedException {
  String url = "https://qaadmin.summerfarm.net/authentication/auth/username/login";
  String method = "Post";
  String contentType = "application/json";

  // requestBody可接收以下三种类型
  String requestBody = "{\"username\":\"<EMAIL>\",\"password\":\"123456\"}";
//        String requestBody = "username=<EMAIL>&password=123456";
//        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("valid", "1");
//        HashMap<String, String> requestBody = new HashMap<>();
//        requestBody.put("username", "<EMAIL>");
//        requestBody.put("password", "123456");
  Request requestInstance = new Request();
  requestInstance.setToken("admin__5496b7b9-0904-4fa7-a354-1af2fd959d98");
  HttpResponseWrapper res =requestInstance.sendRequest (url, method, contentType, requestBody);

  // 处理响应
  if (res != null) {
    System.out.println("Status code: " + res.getStatusCode());
    System.out.println("response headers: " + res.getHeaders());
    System.out.println("Response body: " + res.getBody());
    System.out.println("Response: " + res.toString());
  } else {
    System.out.println("请求失败，未收到有效响应");
  }
}
```
###### -2.2.3 assert断言
- 框架使用testng自带的assert断言，具体使用方法按需断言
###### -2.2.4 数据驱动
- 框架使用testng自带的dataProvider数据驱动，具体使用方法参考(也可以参考testng官方文件)
```java
 @Test(description = "测试用例", retryAnalyzer = XMRetryAnalyzer.class,
        attributes = {
                @CustomAttribute(name = "loop", values = {"1"}),
                @CustomAttribute(name = "interval", values = {"10"})
        },dataProvider = "CsvDataProvider", dataProviderClass = CsvDataProvider.class)
public void stockTransfer(String caseNum,String sku){
```
###### -2.2.5 重试机制
- 框架改进了testng自带的retryAnalyzer重试机制，在Test注解中添加两个属性loop和interval，分别表示循环次数和间隔时间（秒），具体使用方法参考：
```java
  @Test(description = "测试用例2", retryAnalyzer = XMRetryAnalyzer.class,
            attributes = {
                    @CustomAttribute(name = "loop", values = {"1"}),
                    @CustomAttribute(name = "interval", values = {"3"})
            })
```
##### -2.3 通用方法
- 业务场景的通用方法请在generic包下编写，按照中间件或者协议等组织package，例如登录、下单等等


#### 3.编织用例场景
##### -3.1 依赖关系
- 如果你的用例场景依赖于其他用例场景，请在Test注解中指定依赖的方法，例如：
```java
@Test(description = "xx任务", dependsOnMethods = "casexxx");
//指定多个方法：
@Test(description = "xx任务", dependsOnMethods={"test5","xxx"});

```
##### -3.2 suite集成
- 如果你是新增应用的用例，请在resources目录下新增对应应用的xml文件，例如：xxapp.xml
- 在xml文件中引用用例，例如：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="套件">
    <test name="测试执行">
        <packages>
            <package name="com.xianmu.atp.cases.mall" />
        </packages>
    </test> <!-- NewMedia -->
</suite> <!-- Default Suite -->
```
- 使用package标签后，会自动执行该package下的所有用例，后续在该package下新增的用例会自动集成进该suite
##### -3.3 执行
- 本地执行
```shell
mvn clean test -Dsurefire.suiteXmlFiles=src/test/resources/mall.xml
```

#### 4.流水线
##### 4.1 流水线配置
- 在业务应用的流水线配置中，在需要触发的步骤添加卡片“atp自动化回归测试”即可
##### 4.2 测试报告
- 流水线集成了allure测试报告，可以直接在卡片上点击查看测试报告，或者在oss上查询
##### 4.3 卡点
- 暂未集成

