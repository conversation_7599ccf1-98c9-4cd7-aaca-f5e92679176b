
import mysql.connector
import yaml

def get_db_config():
    with open('src/main/resources/application-local.yml', 'r') as f:
        config = yaml.safe_load(f)
    return config['spring']['datasource']['dynamic']['datasource']

def execute_sql(database_name: str, sql_query: str) -> str:
    """
    Connects to a database, executes a SQL query, and returns the result.

    :param database_name: The name of the database to connect to (e.g., 'master', 'offline').
    :param sql_query: The SQL query to execute.
    :return: The result of the SQL query as a string.
    """
    db_config = get_db_config()
    if database_name not in db_config:
        return f"Database '{database_name}' not found in configuration."

    config = db_config[database_name]
    try:
        db_url = config['url']
        print(f"Original DB URL: {db_url}")
        db_name = db_url.split('?')[0].split('/')[-1]
        print(f"Attempting to connect to database: {db_name}")
        conn = mysql.connector.connect(
            host=config['url'].split('//')[1].split('/')[0].split(':')[0],
            port=int(config['url'].split('//')[1].split('/')[0].split(':')[1]),
            user=config['username'],
            password=config['password'],
            database=db_name
        )
        cursor = conn.cursor()
        cursor.execute(sql_query)
        result = cursor.fetchall()
        conn.close()
        return str(result)
    except Exception as e:
        return f"Error executing SQL query: {e}"

# This is a placeholder for the AI plugin decorator.
# You would replace this with the actual decorator provided by your AI plugin framework.
def ai_plugin_tool(func):
    def wrapper(*args, **kwargs):
        # Here you would add the logic to make this function available to the AI plugin.
        # For example, registering it with a tool registry.
        print(f"Calling tool '{func.__name__}' with args: {args}, kwargs: {kwargs}")
        return func(*args, **kwargs)
    return wrapper

@ai_plugin_tool
def query_database(database_name: str, sql_query: str) -> str:
    return execute_sql(database_name, sql_query)

